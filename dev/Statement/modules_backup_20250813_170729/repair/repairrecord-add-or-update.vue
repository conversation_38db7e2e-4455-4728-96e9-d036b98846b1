<template>
  <el-dialog :visible.sync="visible" :title="!dataForm.id ? $t('add') : $t('update')" :close-on-click-modal="false" :close-on-press-escape="false" append-to-body>
    <el-form :model="dataForm" :rules="dataRule" ref="dataForm" @keyup.enter.native="dataFormSubmitHandle()" :label-width="$i18n.locale === 'en-US' ? '120px' : '80px'">
      <div class="container">
                                                <el-form-item label="领取人Id" prop="receiver">
              <el-input v-model="dataForm.receiver" placeholder="领取人Id"></el-input>
            </el-form-item>
                                        <el-form-item label="领取人" prop="receiverName">
              <el-input v-model="dataForm.receiverName" placeholder="领取人"></el-input>
            </el-form-item>
                                        <el-form-item label="领取数量" prop="receiveNumber">
              <el-input v-model="dataForm.receiveNumber" placeholder="领取数量"></el-input>
            </el-form-item>
                                        <el-form-item label="领取时间" prop="receiveTime">
              <el-input v-model="dataForm.receiveTime" placeholder="领取时间"></el-input>
            </el-form-item>
                                        <el-form-item label="领取次id" prop="receiveSubBatchId">
              <el-input v-model="dataForm.receiveSubBatchId" placeholder="领取次id"></el-input>
            </el-form-item>
                                        <el-form-item label="次批号" prop="subBatchNumber">
              <el-input v-model="dataForm.subBatchNumber" placeholder="次批号"></el-input>
            </el-form-item>
                                        <el-form-item label="次批剩余数量" prop="remainingQuantity">
              <el-input v-model="dataForm.remainingQuantity" placeholder="次批剩余数量"></el-input>
            </el-form-item>
                                        <el-form-item label="当前状态(0: 作业中,1: 作业完成)" prop="currentState">
              <el-input v-model="dataForm.currentState" placeholder="当前状态(0: 作业中,1: 作业完成)"></el-input>
            </el-form-item>
                                                                                                                  </div>
    </el-form>
    <template slot="footer">
      <el-button @click="visible = false">{{ $t('cancel') }}</el-button>
      <el-button type="primary" @click="dataFormSubmitHandle()">{{ $t('confirm') }}</el-button>
    </template>
  </el-dialog>
</template>

<script>
import debounce from 'lodash/debounce'
import mixinViewModule from '@/mixins/view-module'
export default {
  mixins:[mixinViewModule],
  data () {
    return {
      visible: false,
      //创建该页面时，不调用数据查询接口
      mixinViewModuleOptions: {
        createdIsNeed: false,
      },
      dataForm: {
        id: '',
        receiver: '',
        receiverName: '',
        receiveNumber: '',
        receiveTime: '',
        receiveSubBatchId: '',
        subBatchNumber: '',
        remainingQuantity: '',
        currentState: '',
        disabled: '',
        creator: '',
        createDate: '',
        updater: '',
        updateDate: ''
      }
    }
  },
  computed: {
    dataRule () {
      return {
          id: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          receiver: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          receiverName: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          receiveNumber: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          receiveTime: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          receiveSubBatchId: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          subBatchNumber: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          remainingQuantity: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          currentState: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
                }
    }
  },
  methods: {
    init () {
      this.visible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].resetFields()
        if (this.dataForm.id) {
          this.getInfo()
        }
      })
    },
    // 获取信息
    getInfo () {
      this.$http.get(`/repair/repairrecord/${this.dataForm.id}`).then(({ data: res }) => {
        if (res.code !== 0) {
          return this.$message.error(res.msg)
        }
        this.dataForm = {
          ...this.dataForm,
          ...res.data
        }
      }).catch(() => {})
    },
    // 表单提交
    dataFormSubmitHandle: debounce(function () {
      this.$refs['dataForm'].validate((valid) => {
        if (!valid) {
          return false
        }
        let message = '';
        if(this.dataForm.id){
          message = '此操作将修改该数据'
        }else {
          message = '此操作将新增一条数据'
        }
        this.$confirm(message+', 是否继续?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
          center: true
        }).then(() => {
          this.$http[!this.dataForm.id ? 'post' : 'put']('/repair/repairrecord/', this.dataForm).then(({ data: res }) => {
            if (res.code !== 0) {
            return this.$message.error(res.msg)
            }
            this.$message({
          message: this.$t('prompt.success'),
            type: 'success',
                    duration: 500,
                    onClose: () => {
              this.visible = false
              this.$emit('refreshDataList')
            }
          })
          }).catch(() => {})
        }).catch(() => {
          this.$message({
            type: 'info',
            message: '已取消'
          });
        });
      })
    }, 1000, { 'leading': true, 'trailing': false })
  }
}
</script>
