<template>
  <el-card shadow="never" class="aui-card--fill">
    <div class="mod-fabricate__fabricate-part}">
      <el-form :inline="true" :model="dataForm" @keyup.enter.native="getDataList()" :key="dataForm.id">
        <el-tooltip content="请输入客户代码或者部品番号" placement="top">
          <el-form-item>
            <el-input v-model="dataForm.paramStr" placeholder="关键字查询" @input="throttleFunction" clearable @clear="clearValue">
                <el-button slot="append" icon="el-icon-search" @click="getDataList()"></el-button>
            </el-input>
          </el-form-item>
        </el-tooltip>
        <el-form-item>
          <el-select v-model="dataForm.type" placeholder="材质" @change="getDataList()" :clearable="true">
            <el-option
                v-for="item in type"
                :key="item.value"
                :label="item.label"
                :value="item.value">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-select v-model="dataForm.productionCategory" placeholder="订单别" @change="getDataList()" :clearable="true">
            <el-option
                v-for="item in productionCategory"
                :key="item.value"
                :label="item.label"
                :value="item.value">
            </el-option>
          </el-select>
        </el-form-item>

        <el-form-item>
          <el-button v-if="$hasPermission('fabricate:part:save')" type="primary" @click="callFunction()">
            {{ $t('add') }}
          </el-button>
        </el-form-item>
        <el-form-item>
          <el-button v-if="$hasPermission('fabricate:part:save')" type="primary"
                     @click="addOrUpdateHandle()">新增部品
          </el-button>
        </el-form-item>
        <el-form-item>
          <el-button type="primary"
                     @click="printHomePagePdf">流程卡pdf
          </el-button>
        </el-form-item>
        <el-dropdown  style="float:right;">
          <el-button type="primary">
            更多功能<i class="el-icon-arrow-down el-icon--right"></i>
          </el-button>
          <el-dropdown-menu slot="dropdown">
            <el-dropdown-item>
              <el-form-item>
                <el-button type="info" @click="exportHandle()">{{ $t('export') }}</el-button>
              </el-form-item>
            </el-dropdown-item>
            <el-dropdown-item>
              <el-form-item>
                <el-button v-if="$hasPermission('fabricate:part:export')" type="info" @click="exportTemplateHandle()">
                  {{ $t('exportTemplate') }}
                </el-button>
              </el-form-item>
            </el-dropdown-item>
            <el-dropdown-item>
              <el-form-item>
                <el-tooltip content="下载模板填入信息后导入" placement="top">
                  <el-upload
                      v-if="$hasPermission('fabricate:part:save')"
                      class="upload-demo"
                      :action="mixinViewModuleOptions.addImageUrl+currentRowId"
                      :headers="headers"
                      :multiple="false"
                      :show-file-list="false"
                      :file-list="fileList"
                      :before-upload="beforeUpload"
                      :on-success="resultHandle"
                      :on-change="handleChange"
                      accept="'.xlsx','.xls'">
                    <el-button type="success">{{ $t('addBatch') }}</el-button>
                  </el-upload>
                </el-tooltip>
                <el-button type="text" v-show="unsuccessfulNameList" @click="dialogVisible = true">导入失败列表</el-button>
              </el-form-item>
            </el-dropdown-item>
            <el-dropdown-item>
              <el-form-item>
                <el-button v-if="$hasPermission('fabricate:part:delete')" type="danger" @click="deleteHandle()">
                  {{ $t('deleteBatch') }}
                </el-button>
              </el-form-item>
            </el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown>
      </el-form>
      <el-table v-loading="dataListLoading" height="750px" :data="dataList" border @selection-change="dataListSelectionChangeHandle"
                style="width: 100%;">
        <el-table-column type="selection" header-align="center" align="center" :min-width="20" width="auto"></el-table-column>
        <el-table-column label="品号代码"  header-align="center" align="center" :min-width="80" width="auto">
          <template slot-scope="scope">
            <div @click="updatePart(scope.row.id)">
              <span v-if="scope.row.code">{{scope.row.code}}</span>
              <span v-else>无编号</span>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="部品图片" header-align="center" :min-width="80" width="auto" align="center">
          <template slot-scope="scope">
            <div class="block" @click="openDialog(scope.row.documentId,scope.row.documentType)" v-if="scope.row.documentId != null">
              <el-avatar shape="square" v-if="scope.row.documentId" :size="40">
                <img :src="$filePath+scope.row.documentId+scope.row.documentType"
                     style="width: 100%; height: 100%; object-fit: contain; max-width: 100%; max-height: 100%;" />
              </el-avatar>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="customerCode" label="客户代码" header-align="center" align="center" :min-width="100" width="auto">
          <template slot-scope="scope">
            <div @click="openDialogCustomer(scope.row.customerId)">{{scope.row.customerCode}}</div>
          </template>
        </el-table-column>
        <el-table-column label="客户品号" header-align="center" align="center" :min-width="200" width="auto">
          <template slot-scope="scope">
            <div @click="selectBasicsulFuraddition(scope.row.id,scope.row.customerId,scope.row.type)">{{scope.row.designation}}</div>
          </template>
        </el-table-column>
        <el-table-column prop="productName" label="客户品名"  header-align="center" align="center" :min-width="100" width="auto">
          <template slot-scope="scope">
            <div>
              <span v-if="scope.row.productName">{{scope.row.productName}}</span>
              <span v-else>无品名</span>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="type" label="材质" :formatter="sizeCategoryFormat" header-align="center"
                         align="center" :min-width="80" width="auto"></el-table-column>
        <el-table-column prop="productionCategory" label="生产别" :formatter="productionCategoryFormat"
                         header-align="center" align="center" :min-width="80" width="auto"></el-table-column>
        <el-table-column prop="nickname" label="昵称" header-align="center" align="center" :min-width="80" width="auto"></el-table-column>
        <el-table-column :label="$t('handle')" fixed="right" header-align="center" align="center" :min-width="50" width="auto">
          <template slot-scope="scope">
            <el-dropdown>
              <el-button type="primary">
                功能<i class="el-icon-arrow-down el-icon--right"></i>
              </el-button>
              <el-dropdown-menu slot="dropdown">
                <el-dropdown-item>
                  <el-button v-if="$hasPermission('fabricate:part:update')" type="text" size="small"
                             @click="addOrUpdateHandle(scope.row.id)">{{ $t('update') }}
                  </el-button>
                </el-dropdown-item>
                <el-dropdown-item>
<!--                  <input type="file" style="display:none;" :name="scope.row.id" ref="fileInput" @change="handleFileChange"></input>
                  <el-button  v-if="$hasPermission('fabricate:part:save')" type="text" size="small"
                              @click="chooseFile(scope.row.id,scope.row.documentId)">上传照片</el-button>-->

                  <el-upload
                      v-if="$hasPermission('fabricate:part:save')"
                      :action="mixinViewModuleOptions.addImageUrl+scope.row.id"
                      class="upload-demo"
                      :show-file-list="false"
                      :headers="headers"
                      :multiple="false"
                      :on-success="handleUploadSuccess"
                      :before-upload="beforeUploadFile"
                      :accept="'image/*'">
                    <el-button
                        v-if="$hasPermission('fabricate:part:save')"
                        type="text"
                        size="small"
                        @click="chooseFile(scope.row.id,scope.row.documentId)"
                    >上传照片</el-button>
                  </el-upload>
                </el-dropdown-item>
                <el-dropdown-item>
                  <el-button v-if="$hasPermission('fabricate:part:delete')" type="text" size="small"
                             @click="deleteHandle(scope.row.id)">{{ $t('delete') }}
                  </el-button>
                </el-dropdown-item>

              </el-dropdown-menu>
            </el-dropdown>
          </template>
        </el-table-column>
      </el-table>
      <el-pagination
          :current-page="page"
          :page-sizes="[10, 20, 50, 100]"
          :page-size="limit"
          :total="total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="pageSizeChangeHandle"
          @current-change="pageCurrentChangeHandle">
      </el-pagination>
      <!-- 弹窗, 新增 / 修改 -->
      <add-or-update v-if="addOrUpdateVisible" ref="addOrUpdate" @refreshDataList="getDataList"></add-or-update>
      <basicsulfuraddition-add-or-update v-if="basicsulFuradditionVisible" ref="basicsulFuradditionAddOrUpdate"  @refreshDataList="getDataList"></basicsulfuraddition-add-or-update>
      <!-- 弹出框 -->
      <el-dialog title=图片显示 :visible.sync="dialogVisible" @closed="closeDialog">
        <img :src="$filePath+imagePath+documentType" alt="未上传图片" style="width: 40vw;height:  60vh">
      </el-dialog>
      <el-dialog title=客户信息 :visible.sync="dialogVisibleCustomer" @closed="closeDialogCustomer">
        <el-form :model="tableData">
        <el-form-item label="代号:" prop="code">
          <div style="font-size: 16px; color: black;font-weight: bold;">
            {{tableData.code}}
          </div>
        </el-form-item>
        <el-form-item label="中文名称" prop="chineseName">
          <div style="font-size: 16px; color: black;font-weight: bold;">
            {{tableData.chineseName}}
          </div>
        </el-form-item>
        <el-form-item label="英文名称" prop="englishName">
          <div style="font-size: 16px; color: #1c0101;font-weight: bold;">
            {{tableData.englishName}}
          </div>
        </el-form-item>
      </el-form>
      </el-dialog>

      <el-dialog title=更新品番信息 :visible.sync="updatePartVisible" @closed="closeDialogCustomer">
        <el-form :model="part" ref="part" :label-width="$i18n.locale === 'en-US' ? '120px' : '80px'">
          <el-form-item label="部品编号" prop="code">
            <el-input v-model="part.head" placeholder="客户品名" style="width: 80px;"></el-input>
            <el-select v-model="part.gum" placeholder="请选择" style="width: 80px">
              <el-option
                  v-for="item in gum"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value">
              </el-option>
            </el-select>
            <el-select v-model="part.represent" placeholder="请选择"  style="width: 80px">
              <el-option
                  v-for="item in represent"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value">
              </el-option>
            </el-select>
            <el-input v-model="part.serialNumber" placeholder="编号流水号" style="width: 200px"></el-input>
          </el-form-item>
          <el-form-item label="客户品名" prop="productName">
            <el-input v-model="part.productName" placeholder="客户品名"></el-input>
          </el-form-item>
        </el-form>
        <template slot="footer">
          <el-button @click="updatePartVisible = false">{{ $t('cancel') }}</el-button>
          <el-button type="primary" @click="updateSubmit()">{{ $t('confirm') }}</el-button>
        </template>
      </el-dialog>

      <el-dialog :visible.sync="dialogVisibleBasicsulFuraddition" @closed="closeVulcanization">
        <el-form :model="basicSulfurAdditionData" ref="dataForm"
                 :label-width="$i18n.locale === 'en-US' ? '200px' : '120px'">

          <el-descriptions class="margin-top" :column="column" :content-style="content_style" :label-style="label_style" border>
            <el-descriptions-item label="部品名称">
              <el-input v-model="basicSulfurAdditionData.productName" placeholder="部品名称"></el-input>
            </el-descriptions-item>
            <el-descriptions-item label="材质">
              <el-input v-model="basicSulfurAdditionData.material" placeholder="材质"></el-input>
            </el-descriptions-item>
            <el-descriptions-item>
              <template slot="label">
                <span class="required">*</span>
                部品番号
              </template>
              <part-number-component v-model="basicSulfurAdditionData.partId" placeholder="部品番号"></part-number-component>
            </el-descriptions-item>
            <el-descriptions-item label="配方编号" prop="formulationId">
              <template slot="label">
                <span class="required">*</span>
                配方编号
              </template>
              <formulation-component v-model="basicSulfurAdditionData.formulationId" placeholder="模具编号"></formulation-component>
            </el-descriptions-item>
            <el-descriptions-item label="版本">
              <el-input v-model="basicSulfurAdditionData.version" placeholder="版本"></el-input>
            </el-descriptions-item>
            <el-descriptions-item label="文件编号">
              <el-input v-model="basicSulfurAdditionData.standardBookId" placeholder="加硫作业标准书编号"></el-input>
            </el-descriptions-item>
            <el-descriptions-item label="成型机型(吨)">
              <el-input v-model="basicSulfurAdditionData.formingMachine" placeholder="成型机型(吨)"></el-input>
            </el-descriptions-item>
            <el-descriptions-item label="机台编号">
              <machine-component v-model="basicSulfurAdditionData.machineList" :multiple="true" placeholder="机台编号"></machine-component>
            </el-descriptions-item>
            <el-descriptions-item label="模具">
              <el-input v-model="basicSulfurAdditionData.moldHole" placeholder="金型(X孔)" class="input_width_half"></el-input>
              [孔] *
              <el-input v-model="basicSulfurAdditionData.moldSurface" placeholder="金型(X面)" class="input_width_half"></el-input>
              [面]
            </el-descriptions-item>
            <el-descriptions-item label="模具尺寸">
              <el-input v-model="basicSulfurAdditionData.moldDimensionsOne" placeholder="金型寸法(X mm)" class="input_width_onethird"></el-input>
              X
              <el-input v-model="basicSulfurAdditionData.moldDimensionsTwo" placeholder="金型寸法(X mm)" class="input_width_onethird"></el-input>
              X
              <el-input v-model="basicSulfurAdditionData.moldDimensionsThree" placeholder="金型寸法(X mm)" class="input_width_onethird"></el-input>
              <span class="font_size">mm</span>
            </el-descriptions-item>
            <el-descriptions-item label="材料尺寸">
              <el-input v-model="basicSulfurAdditionData.materialDimensionsLong" placeholder="材料寸法(长 mm)" class="input_width_half"></el-input>
              ±
              <el-input v-model="basicSulfurAdditionData.moldDimensionsThreeLongMan" placeholder="材料寸法(长士 mm)" class="input_width_half"></el-input>
              <span class="font_size">mm</span><br/>
              <el-input v-model="basicSulfurAdditionData.materialDimensionsThickness" placeholder="材料寸法(厚 mm)" class="input_width_half"></el-input>
              ±
              <el-input v-model="basicSulfurAdditionData.materialDimensionsAtsushi" placeholder="材料寸法(厚士 mm)" class="input_width_half"></el-input>
              <span class="font_size">mm</span>
            </el-descriptions-item>
            <el-descriptions-item label="材料形状">
              <el-input v-model="basicSulfurAdditionData.materialShape" placeholder="材料形状"></el-input>
            </el-descriptions-item>
            <el-descriptions-item label="材料重量">
              <el-input v-model="basicSulfurAdditionData.materialWeightOne" placeholder="材料重量1" class="input_width_half"></el-input>
              -
              <el-input v-model="basicSulfurAdditionData.materialWeightTwo" placeholder="材料重量2" class="input_width_half"></el-input>
              <span class="font_size">g/条</span>
            </el-descriptions-item>
            <el-descriptions-item label="共排">
              <el-input v-model="basicSulfurAdditionData.rowTogether" placeholder="共排" class="input_width_half"></el-input>
              <span class="font_size">条</span>
            </el-descriptions-item>
            <el-descriptions-item label="模具实际温度">
              上
              <el-input v-model="basicSulfurAdditionData.moldActualTemperatureSuperiorOne" placeholder="模具实际温度上1(℃)" class="input_width_half"></el-input>
              <el-input v-model="basicSulfurAdditionData.moldActualTemperatureSuperiorTwo" placeholder="模具实际温度下2(℃)" class="input_width_half"></el-input>
              <span class="font_size">℃</span><br/>
              /
              <el-input v-model="basicSulfurAdditionData.moldActualTemperaturePreviewOne" placeholder="模具实际温度预览1(℃)" class="input_width_half"></el-input>
              <el-input v-model="basicSulfurAdditionData.moldActualTemperaturePreviewTwo" placeholder="模具实际温度预览2(℃)" class="input_width_half"></el-input>
              <span class="font_size">℃</span><br/>
              下
              <el-input v-model="basicSulfurAdditionData.moldActualTemperatureDownOne" placeholder="模具实际温度上1(℃)" class="input_width_half"></el-input>
              <el-input v-model="basicSulfurAdditionData.moldActualTemperatureDownTwo" placeholder="模具实际温度下2(℃)" class="input_width_half"></el-input>
              <span class="font_size">℃</span>
            </el-descriptions-item>
            <el-descriptions-item label="排气">
              <el-input v-model="basicSulfurAdditionData.exhaust" placeholder="排气" class="input_width_half"></el-input>
              <span class="font_size">次</span>
            </el-descriptions-item>
            <el-descriptions-item label="成形压力">
              <el-input v-model="basicSulfurAdditionData.formingPressureHead" class="input_width_half"></el-input>
              -
              <el-input v-model="basicSulfurAdditionData.formingPressureEnd" class="input_width_half"></el-input>
              kgf/cm2
            </el-descriptions-item>
            <el-descriptions-item label="抽真空压力">
              <el-input v-model="basicSulfurAdditionData.vacuumPressure" class="input_width_half"></el-input>
              cmHg
            </el-descriptions-item>
            <el-descriptions-item label="加热治具温度">
              设定
              <el-input v-model="basicSulfurAdditionData.heatingFixtureTemperatureOne" class="input_width_half"></el-input>
              ±
              <el-input v-model="basicSulfurAdditionData.heatingFixtureTemperatureTwo" class="input_width_half"></el-input>
              ℃
            </el-descriptions-item>
            <el-descriptions-item label="模具清洗">
              <el-input v-model="basicSulfurAdditionData.moldCleaning" placeholder="模具清洗"></el-input>
            </el-descriptions-item>
            <el-descriptions-item label="清洗频率">
              <el-input v-model="basicSulfurAdditionData.cleaningFrequency" placeholder="清洗频率"></el-input>
            </el-descriptions-item>
            <el-descriptions-item label="硫化时间">
              <el-input v-model="basicSulfurAdditionData.vulcanizationTimeOne" placeholder="硫化时间1" class="input_width_half"></el-input>
              <el-input v-model="basicSulfurAdditionData.vulcanizationTimeTwo" placeholder="硫化时间1" class="input_width_half"></el-input>
              秒
            </el-descriptions-item>
            <el-descriptions-item label="离型剂">
              <el-input v-model="basicSulfurAdditionData.releaseAgent" placeholder="离型剂"></el-input>
            </el-descriptions-item>
            <el-descriptions-item label="冷模超时报警">
              <el-input v-model="basicSulfurAdditionData.coldMoldTimeoutAlarmHead" placeholder="冷模超时报警1(秒)" class="input_width_half"></el-input>
              /
              <el-input v-model="basicSulfurAdditionData.coldMoldTimeoutAlarmEnd" placeholder="冷模超时报警2(秒)" class="input_width_half"></el-input>
            </el-descriptions-item>
            <el-descriptions-item label="离型剂使用频率">
              <el-input v-model="basicSulfurAdditionData.frequency" placeholder="频率"></el-input>
            </el-descriptions-item>
            <el-descriptions-item label="半成品装箱数">
              <el-input v-model="basicSulfurAdditionData.semiFinishedProductNumberBoxes" placeholder="半成品装箱数(周转箱)"></el-input>
            </el-descriptions-item>
            <el-descriptions-item label="节拍时间">
              <el-input v-model="basicSulfurAdditionData.taktTime" placeholder="节拍时间(秒)"></el-input>
            </el-descriptions-item>
            <el-descriptions-item label="产效目标">
              <el-input v-model="basicSulfurAdditionData.takeEffect" placeholder="产效目标(模/小时)"></el-input>
            </el-descriptions-item>
            <el-descriptions-item label="不良率">
              <el-input v-model="basicSulfurAdditionData.defectiveRate" placeholder="不良率(%)"></el-input>
            </el-descriptions-item>
            <el-descriptions-item label="单品重量">
              <el-input v-model="basicSulfurAdditionData.singleProductWeight" placeholder="单品重量"></el-input>
            </el-descriptions-item>
            <el-descriptions-item label="注意事项" :span="2">
              <el-input
                  type="textarea"
                  :autosize="{ minRows: 4, maxRows: 6}"
                  placeholder="请输入内容"
                  v-model="basicSulfurAdditionData.precautions">
              </el-input>
            </el-descriptions-item>
          </el-descriptions>
        </el-form>
     </el-dialog>
    </div>
  </el-card>
</template>

<script>
import mixinViewModule from '@/mixins/view-module'
import AddOrUpdate from './part-add-or-update'
import Cookies from "js-cookie";
import BasicsulfuradditionAddOrUpdate from "../vulcanization/basicsulfuraddition-add-or-update.vue";
import {addDynamicRoute} from '@/router'

// 节流函数
const delay = (function() {
  let timer = 0;
  return function(callback, ms) {
    clearTimeout(timer);
    timer = setTimeout(callback, ms);
  };
})();
export default {
  mixins: [mixinViewModule],
  data() {
    return {
      fileInputs: [],
      showFileInput:false,
      updatePartVisible:false,
      column: 2,
      windowWidth: window.innerWidth,
      content_style: {
        'min-width': '200px',
        'word-break': 'break-all'
      },
      label_style: {
        'color': '#000',
        'text-align': 'center',
        'font-weight': '600',
        'height': '40px',
        'background-color': '#d6dbe1',
        'min-width': '100px',
        'word-break': 'keep-all'
      },
      part:{
        id:'',
        code:'',
        productName:'',
        head: 'MG',
        gum: '',
        represent: '',
        serialNumber: '',
      },
      basicSulfurAdditionData: {
        id: '',
        customerId: '',
        customerCode: '',
        partId: '',
        designation: '',
        productName: '',
        moldId: '',
        documentType: '',
        standardBookId: '',
        version: 'A0',
        formulationId: '',
        formulationCode: '',
        material: '',
        formingMachine: '',
        machineId: '',
        moldHole: '',
        moldSurface: '',
        moldDimensionsOne: '',
        moldDimensionsTwo: '',
        moldDimensionsThree: '',
        materialDimensionsLong: '',
        moldDimensionsThreeLongMan: '',
        materialDimensionsThickness: '',
        materialDimensionsAtsushi: '',
        materialWeightOne: '',
        materialWeightTwo: '',
        finishedProductWeight: '',
        materialShape: '',
        moldTemperatureTop: '',
        moldTemperatureBelow: '',
        vulcanizationTimeOne: '',
        vulcanizationTimeTwo: '',
        rowTogether: '',
        exhaust: '',
        machinePressure: '',
        moldCleaning: '',
        cleaningFrequency: '',
        releaseAgent: '',
        kind: '',
        frequency: '',
        plating: '',
        precautions: '',
        precautionsPictureOne: '',
        precautionsPictureTow: '',
        precautionsPictureThree: '',
        semiFinishedProductNumberBoxes: '',
        takeEffect: '',
        defectiveRate: '',
        remark: '',
        disabled: '',
        creator: '',
        createDate: '',
        updater: '',
        updateDate: ''
      },
      basicsulFuradditionVisible:false,
      addOrUpdateVisible:false,
      dialogVisibleBasicsulFuraddition:false,
      imagePath: '',
      documentType: '',
      disabled: false,
      tableData: '',
      dialogVisibleCustomer: false,
      visible: false,
      content: '点击关闭提示框功能',
      textPrompt:'',
      chineseName:'',
      dialogVisible:false,
      currentRowId:'',
      imgSrc: '',
      headers: {'token': Cookies.get('token')},
      fileList: [{name: '', url: ''}],
      unsuccessfulNameList: '',//  导入失败名称列表
      mixinViewModuleOptions: {
        getDataListURL: '/fabricate/part/page',
        getDataListIsPage: true,
        exportURL: '/fabricate/part/export',
        deleteURL: '/fabricate/part',
        deleteIsBatch: true,
        exportTemplateURL: '/fabricate/part/export/template',
        addBatchUrl: window.SITE_CONFIG['apiURL'] + '/fabricate/part/batchSave',
        addImageUrl: window.SITE_CONFIG['apiURL'] + '/fabricate/part/api/upload?id=',
      },
      dataForm: {
        id: '',
        designation: '',
        paramStr: this.$route.query.parameter != null ? this.$route.query.parameter:'',
        customerCode: '',
        productionCategory: '',
        type:'',
      },
      // 判断是否还在继续输入
      timer: null,
      type: [{
        value: 1,
        label: '硅胶'
      }, {
        value: 2,
        label: '橡胶'
      }, {
        value: 3,
        label: '液态'
      }, {
        value: 4,
        label: '其他'
      }],
      material:[{
        value: 1,
        label: '硅胶'
      }, {
        value: 2,
        label: '橡胶'
      }, {
        value: 3,
        label: '液态'
      }, {
        value: 4,
        label: '铁件'
      }, {
        value: 5,
        label: '其他'
      }],
      productionCategory: [{
        value: 1,
        label: '样品'
      }, {
        value: 2,
        label: '试模'
      }, {
        value: 3,
        label: '量产'
      }, {
        value: 4,
        label: '打切'
      }],
      gum: [{
        value: 'R',
        label: 'R (橡胶)'
      }, {
        value: 'S',
        label: 'S (硅胶)'
      }, {
        value: 'L',
        label: 'L (液态)'
      }],
      represent: [{
        value: 'J',
        label: 'J (射出)'
      }, {
        value: 'P',
        label: 'P (平板)'
      }, {
        value: 'X',
        label: 'X (模心)'
      }, {
        value: 'U',
        label: 'U (上模)'
      }, {
        value: 'D',
        label: 'D (下模)'
      }],
    }
  },
  components: {
    AddOrUpdate,
    BasicsulfuradditionAddOrUpdate,
  },
  mounted() {
    window.addEventListener('resize', this.handleResize);
  },
  beforeDestroy() {
    window.removeEventListener('resize', this.handleResize)
  },
  methods: {
    printHomePagePdf(){
      // 路由参数
      const routeParams = {
        routeName: `${this.$route.name}__printhomepagepdf`,
        title: `打印操作-首页`,
        path: 'pdf/printhomepagepdf',
      }
      // 动态路由
      addDynamicRoute(routeParams, this.$router)
    },
    updatePart(id){
      this.updatePartVisible = true
      this.$nextTick(() =>{
        this.$refs['part'].resetFields()
        if (id) {
          this.part.id = id
          this.getInfo(id)
        }
      })
    },
    updateSubmit(){
      if (this.dataForm.gum && this.dataForm.represent && this.dataForm.serialNumber){
        this.dataForm.code = this.dataForm.head+this.dataForm.gum+this.dataForm.represent+this.dataForm.serialNumber
      }
      this.$http['put']('/fabricate/part/', this.part).then(({ data: res }) => {
        console.log(res)
        if (res.code !== 0) {
          return this.$message.error(res.msg)
        }
        this.$message({
          message: this.$t('prompt.success'),
          type: 'success',
          duration: 500,
          onClose: () => {
            this.updatePartVisible = false
            this.getDataList()
          }
        })
      }).catch(() => {})
    },
    // 获取信息
    getInfo(id) {
      this.$http.get(`/fabricate/part/${id}`).then(({data: res}) => {
        if (res.code !== 0) {
          return this.$message.error(res.msg)
        }
        this.part = {
          ...this.part,
          ...res.data
        }
        if (this.part.code){
          this.part.head = this.part.code.slice(0,2)
          this.part.gum = this.part.code.slice(2,3)
          this.part.represent = this.part.code.slice(3,4)
          this.part.serialNumber = this.part.code.slice(4,10)
        }
      }).catch(() => {
      })
    },
    //节流函数
    throttleFunction() {
      // 如果timer不为空就表明在持续输入,每次改变都清除定时，这样就可以实现只输入最后一次结果(注意每次间隔如果超过500毫秒就会输出)
      if (this.timer !== null) clearTimeout(this.timer)
      this.timer = setTimeout(() => {
        this.getDataList()
      }, 500)
    },
    clearValue(){
      this.dataForm.paramStr = '';
      //重新导航到当前路由，并将参数 parameter 设置为 null
      this.$router.replace({ query: { parameter: null } });
    },
    selectBasicsulFuraddition(partId,customerId,type){
      this.dialogVisibleBasicsulFuraddition=true
      this.$http.get(`/vulcanization/basicsulfuraddition/getBasicSulfurAddition?partId=`+partId+'&customerId='+customerId+'&type='+type).then(({data: res}) => {
        console.log("res:",res)
        if (res.code !== 0) {
          this.basicSulfurAdditionData=''
          return this.$message.error(res.msg)
        }
        if(res.data != null){
          this.basicSulfurAdditionData = res.data
        }
      }).catch(() => {
        this.dialogVisibleBasicsulFuraddition = false
      })
    },
    closeVulcanization() { // 关闭对话框
      this.dialogVisibleBasicsulFuraddition = false;
    },
    callFunction (id) {
      this.basicsulFuradditionVisible = true
      this.$nextTick(() => {
        this.$refs.basicsulFuradditionAddOrUpdate.dataForm.partId = id
        this.$refs.basicsulFuradditionAddOrUpdate.init()
      })
    },
    chooseFile(id, documentId) {
      this.currentRowId = documentId
    },
    beforeUploadFile(file) {
      if (!file.type.startsWith('image/')) {
        this.$message.error('只能上传图片文件')
        return false
      }
    },
    handleUploadSuccess(response) {
      console.log(response)
      if (response.code === 200) {
        if(this.currentRowId){
          this.$http.delete('/fabricate/part/deleteImage?id=' + this.currentRowId)
        }
        this.$message.success('上传成功')
      } else {
        this.$message.error('上传失败')
      }
      this.query()
    },
    handleResize() {
      this.windowWidth = window.innerWidth
      console.log(this.windowWidth)
      if (this.windowWidth < 1500){
        this.column = 1
      }else {
        this.column = 2
      }
      // 在这里更新组件状态或执行其他的逻辑
    },
    openDialogCustomer(customerId) { // 打开对话框

      this.selectCustomer(customerId)
      this.dialogVisibleCustomer = true;
    },
    closeDialogCustomer() { // 关闭对话框
      this.dialogVisibleCustomer = false;
      this.updatePartVisible = false;
    },
    selectCustomer(id){
      this.$http.get(`/customer/customer/${id}`).then(({data: res}) => {
        console.log("res:",res)
        if (res.code !== 0) {
          this.tableData = ''
          return this.$message.error(res.msg)
        }
        this.tableData = res.data
        this.tableData.isWeighing = this.sfktHouse(this.tableData)
      }).catch(() => {
        this.dataListLoading = false
      })
    },
    openDialog(documentId,documentType) { // 打开对话框
      this.imagePath = documentId
      this.documentType = documentType
      this.dialogVisible = true;
    },
    closeDialog() { // 关闭对话框
      this.dialogVisible = false;
    },

    beforeUpload(file) {
      this.message = this.$message({
        message: '上传中，请勿操作',
        center: true,
        duration: 0,
      });
    },
    handleChange(file, fileList) {
      this.fileList = fileList.slice(-3);

    },
    resultHandle(res) {
      if (res.code !== 0) {
        return this.$message.error(res.msg)
      }
      this.$message({
        message: this.$t('prompt.success'),
        type: 'success',
        duration: 500,
        onClose: () => {
          if (res.data) {
            this.unsuccessfulNameList = res.data
          }
          this.visible = false
          this.query()
        }
      })
    },
    productionCategoryFormat(row, column) {
      switch (row.productionCategory) {
        case 1:
          return '样品'
        case 2:
          return '试模'
        case 3:
          return '量产'
        case 4:
          return '打切'
      }
    },
    sizeCategoryFormat(row, column) {
      switch (row.type) {
        case 1:
          return '硅胶'
        case 2:
          return '橡胶'
        case 3:
          return '液态'
        case 4:
          return "铁件"
        case 5:
          return "其他"
      }
    },
  }
}
</script>
<style scoped>
.container {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  margin: 10px;
}
.box {
  width: calc(50% - 5px);
  height: 150px;
  margin-bottom: 10px;
}
@media screen and (max-width: 767px) {
  .box {
    width: 100%;
  }
}
.modal {
  display: none;
  position: fixed;
  z-index: 1;
  padding-top: 100px;
  left: 0;
  top: 0;
  width: 50%;
  height: 50%;
  overflow: auto;
  background-color: white;
}
.modal-content {
  margin: auto;
  display: block;
  width: 50%;
  max-width: 800px;
}
.close {
  position: absolute;
  top: 15px;
  right: 35px;
  color: #f1f1f1;
  font-size: 40px;
  font-weight: bold;
  transition: 0.3s;
}
.close:hover,
.close:focus {
  color: #bbb;
  text-decoration: none;
  cursor: pointer;
}

.u-tag {
  display: inline-block;
  padding: 6px 12px;
  border-radius: 15px;
  font-size: 14px;
  font-weight: 400;
  line-height: 20px;
  cursor: pointer;
}
.u-info {
  display: inline-block;
  font-size: 16px;
}
.mt150 {
  margin-top: 150px;
}
.yellow {
  color: #FF9000;
  background: #FFF9F0;
}
.blue {
  color: #0079DD;
  background: #F0F7FD;
}
</style>
