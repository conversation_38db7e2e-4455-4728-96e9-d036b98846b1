<template>
  <el-dialog :visible.sync="visible" :title="!dataForm.id ? '新增部品信息' : '修改部品信息'" :close-on-click-modal="false"
             :close-on-press-escape="false" append-to-body :append-to-body="true">
    <el-form :model="dataForm" :rules="dataRule" ref="dataForm" @keyup.enter.native="dataFormSubmitHandle()"
             :label-width="$i18n.locale === 'en-US' ? '120px' : '80px'">
      <el-form-item label="部品编号" prop="code">
        <span class="font_size">{{dataForm.head}}</span>
        <el-select v-model="dataForm.gum" placeholder="请选择" style="width: 100px;margin-left: 8px">
          <el-option
              v-for="item in gum"
              :key="item.value"
              :label="item.label"
              :value="item.value">
          </el-option>
        </el-select>
        <el-select v-model="dataForm.represent" placeholder="请选择"  style="width: 100px; margin-left: 8px">
          <el-option
              v-for="item in represent"
              :key="item.value"
              :label="item.label"
              :value="item.value">
          </el-option>
        </el-select>
        <el-input v-model="dataForm.serialNumber" placeholder="编号流水号" style="width: 130px; margin-left: 8px"></el-input>
      </el-form-item>
      <el-form-item label="客户品名" prop="productName">
        <el-input v-model="dataForm.productName" placeholder="客户品名"></el-input>
      </el-form-item>
      <el-form-item label="客户代码" prop="customerId">
        <customer-component v-model="dataForm.customerId" placeholder="请选择客户代码"></customer-component>
      </el-form-item>
      <el-form-item label="客户品号" prop="designation">
        <el-input v-model="dataForm.designation" placeholder="客户品号"></el-input>
      </el-form-item>
      <el-form-item label="材质" prop="type">
        <el-select v-model="dataForm.type" placeholder="材质">
          <el-option
              v-for="item in type"
              :key="item.value"
              :label="item.label"
              :value="item.value">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="生产阶段" prop="productionCategory">
        <el-select v-model="dataForm.productionCategory" placeholder="生产阶段">
          <el-option
              v-for="item in productionCategory"
              :key="item.value"
              :label="item.label"
              :value="item.value">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="生产地" prop="location">
        <el-input v-model="dataForm.location" placeholder="所在地"></el-input>
      </el-form-item>
<!--      <el-form-item label="昵称" prop="nickname">-->
<!--        <el-input v-model="dataForm.nickname" placeholder="昵称"></el-input>-->
<!--      </el-form-item>-->
      <el-form-item label="备注" prop="remark">
        <el-input v-model="dataForm.remark" placeholder="备注"></el-input>
      </el-form-item>
    </el-form>
    <template slot="footer">
      <el-button @click="visible = false">{{ $t('cancel') }}</el-button>
      <el-button type="primary" @click="dataFormSubmitHandle()">{{ $t('confirm') }}</el-button>
    </template>
  </el-dialog>
</template>

<script>
import debounce from 'lodash/debounce'
import axios from "axios";

export default {
  data() {
    return {
      imageUrl: '',
      visible: false,
      dataForm: {
        id: '',
        designation: '',
        productName: '',
        head: 'PH',
        gum: '',
        represent: '',
        serialNumber: '',
        code:'',
        documentId: '',
        type: '',
        customerId: '',
        customerCode: '',
        chineseName:'',
        location: '',
        productionCategory: '',
        sizeCategory: '',
        nickname: '',
        remark: '',
      },
      type: [{
        value: 1,
        label: '硅胶'
      }, {
        value: 2,
        label: '橡胶'
      }, {
        value: 3,
        label: '液态'
      },{
        value: 4,
        label: '铁件'
      }, {
        value: 5,
        label: '其他'
      }],
      productionCategory: [{
        value: 1,
        label: '样品'
      }, {
        value: 2,
        label: '试模'
      }, {
        value: 3,
        label: '量产'
      }, {
        value: 4,
        label: '打切'
      }],
      gum: [{
        value: 'R',
        label: 'R (橡胶)'
      }, {
        value: 'S',
        label: 'S (硅胶)'
      }, {
        value: 'L',
        label: 'L (液态)'
      }, {
        value: 'I',
        label: 'I (铁件)'
      }],
      represent: [{
        value: 'J',
        label: 'J (射出)'
      }, {
        value: 'P',
        label: 'P (平板)'
      }, {
        value: 'X',
        label: 'X (模心)'
      }, {
        value: 'U',
        label: 'U (上模)'
      }, {
        value: 'D',
        label: 'D (下模)'
      }],
    }

  },
  computed: {
    dataRule() {
      return {
        customerId: [
          {required: true, message: this.$t('validate.required'), trigger: 'blur'}
        ],
        designation: [
          {required: true, message: this.$t('validate.required'), trigger: 'blur'}
        ],
        type: [
          {required: true, message: this.$t('validate.required'), trigger: 'blur'}
        ],
        productionCategory: [
          {required: true, message: this.$t('validate.required'), trigger: 'blur'}
        ],
      }
    }
  },
  watch:{

  },
  methods: {
    init() {
      this.visible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].resetFields()
        if (this.dataForm.id) {
          this.getInfo()
        }
      })
    },
    // 获取信息
    getInfo() {
      this.$http.get(`/fabricate/part/${this.dataForm.id}`).then(({data: res}) => {
        if (res.code !== 0) {
          return this.$message.error(res.msg)
        }
        this.dataForm = {
          ...this.dataForm,
          ...res.data
        }
        // this.dataForm.head = this.dataForm.code.slice(0,2)
        this.dataForm.gum = this.dataForm.code.slice(2,3)
        this.dataForm.represent = this.dataForm.code.slice(3,4)
        // this.dataForm.serialNumber = this.dataForm.code.slice(4,10)
        this.dataForm.serialNumber = this.dataForm.code.slice(4,this.dataForm.code.length)
      }).catch(() => {
      })
    },
    // 表单提交
    dataFormSubmitHandle: debounce(function () {
      if (this.dataForm.gum && this.dataForm.represent && this.dataForm.serialNumber){
        this.dataForm.code = this.dataForm.head+this.dataForm.gum+this.dataForm.represent+this.dataForm.serialNumber
      }
      this.$refs['dataForm'].validate((valid) => {
        if (!valid) {
          return false
        }
        this.$http[!this.dataForm.id ? 'post' : 'put']('/fabricate/part/', this.dataForm).then(({data: res}) => {
          if (res.code !== 0) {
            return this.$message.error(res.msg)
          }
          this.$message({
            message: this.$t('prompt.success'),
            type: 'success',
            duration: 500,
            onClose: () => {
              this.visible = false
              this.$emit('refreshDataList')
            }
          })
        }).catch(() => {
        })
      })
    }, 1000, {'leading': true, 'trailing': false})
  }
}
</script>
