<template>
  <el-dialog :visible.sync="visible" :title="!dataForm.id ? $t('add') : $t('update')" :close-on-click-modal="false" :close-on-press-escape="false" append-to-body>
    <el-form :model="dataForm" :rules="dataRule" ref="dataForm" @keyup.enter.native="dataFormSubmitHandle()" :label-width="$i18n.locale === 'en-US' ? '120px' : '80px'">
      <div class="container">
        <div class="box">
          <el-form-item label="部品番号" prop="partId">
            <part-number-component v-model="dataForm.partId" @change-event="changeEvent" placeholder="请选择品番" v-if="!dataForm.id" clearable></part-number-component>
            <div class="font_size" v-else>
              {{$getPartList(dataForm.partId)}}
            </div>
          </el-form-item>
        </div>
      </div>
      <div class="container">
        <div class="item">
          <el-form-item label="成形数" prop="uncheckedNum">
            <el-input-number :controls="false" v-model="dataForm.uncheckedNum" placeholder="成形" class="inputSize"></el-input-number>
          </el-form-item>
        </div>
        <div class="item">
          <el-form-item label="品修数" prop="qualityRepairNum">
            <el-input-number :controls="false" v-model="dataForm.qualityRepairNum" placeholder="品修" class="inputSize"></el-input-number>
          </el-form-item>
        </div>
        <div class="item">
          <el-form-item label="品检数" prop="qualityInspectionNum">
            <el-input-number :controls="false" v-model="dataForm.qualityInspectionNum" placeholder="品检" class="inputSize"></el-input-number>
          </el-form-item>
        </div>
      </div>
      <div class="container">
        <div class="item">
          <el-form-item label="待出货数" prop="transferredNum">
            <el-input-number :controls="false" v-model="dataForm.toBeShipped" placeholder="待出货数" class="inputSize"></el-input-number>
          </el-form-item>
        </div>
        <div class="item">
          <el-form-item label="待包装数" prop="numberOfReserves">
            <el-input-number :controls="false" v-model="dataForm.toBePacked" placeholder="待包装数" class="inputSize"></el-input-number>
          </el-form-item>
        </div>
      </div>
      <div class="container">
        <div class="item">
          <el-form-item label="转入数" prop="transferredNum">
            <el-input-number :controls="false" v-model="dataForm.transferredNum" placeholder="转入数" class="inputSize"></el-input-number>
          </el-form-item>
        </div>
        <div class="item">
          <el-form-item label="无订单数" prop="numberOfReserves">
            <el-input-number :controls="false" v-model="dataForm.numberOfReserves" placeholder="无订单数" class="inputSize"></el-input-number>
          </el-form-item>
        </div>
        <div class="item">
          <el-form-item label="隔离数" prop="isolationNumber">
            <el-input-number :controls="false" v-model="dataForm.isolationNumber" placeholder="隔离数"></el-input-number>
          </el-form-item>
        </div>
      </div>
      <el-form-item label="调节人" prop="moderator">
        <employee-component v-model="dataForm.moderator" placeholder="调节人(工号)"></employee-component>
      </el-form-item>
      <el-form-item label="调节原因" prop="reasonForAdjustment">
        <el-input v-model="dataForm.reasonForAdjustment" placeholder="调节原因"></el-input>
      </el-form-item>
<!--      <el-form-item label="审核人" prop="reviewer">
        <employee-component v-model="dataForm.reviewer" :default-value="false" placeholder="审核人"></employee-component>
      </el-form-item>
          <el-form-item label="核准" prop="approved">
          <el-input v-model="dataForm.approved" placeholder="核准"></el-input>
      </el-form-item>-->
          <el-form-item label="备注" prop="remark">
          <el-input v-model="dataForm.remark" placeholder="备注"></el-input>
      </el-form-item>
      </el-form>
    <template slot="footer">
      <el-button @click="visible = false">{{ $t('cancel') }}</el-button>
      <el-button type="primary" @click="dataFormSubmitHandle()">{{ $t('confirm') }}</el-button>
    </template>
  </el-dialog>
</template>

<script>
import debounce from 'lodash/debounce'
import mixinViewModule from '@/mixins/view-module'
export default {
  mixins: [mixinViewModule],
  data () {
    return {
      visible: false,
      dataForm: {
        id: '',
        partId: '',
        designation:'',
        uncheckedNum: 0,
        qualityRepairNum: 0,
        qualityInspectionNum: 0,
        toBeShipped: 0,
        toBePacked: 0,
        endProductNum: 0,
        semiFinishedProduct: 0,
        inventoryNum: 0,
        transferredNum: 0,
        isolationNumber: 0,
        numberOfReserves: 0,
        reasonForAdjustment: '',
        moderator: '',
        reviewer: '',
        approved: '',
        remark: '',
      }
    }
  },
  computed: {
    dataRule () {
      return {
        partId: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        reasonForAdjustment: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
      }
    }
  },
  watch:{
    visible(value){
      if(!value){
        this.$refs['dataForm'].resetFields()
      }
    }
  },
  methods: {
    changeEvent(){
      if(this.dataForm.partId){
        this.$http.get(`fabricate/productInventory/queryProductInventory/`+this.dataForm.partId).then(({data:res}) => {
          if (res.code !== 0) {
            return this.$message.error(res.msg)
          }
          this.dataForm = {
            ...this.dataForm,
            ...res.data
          }
          this.dataForm.id = ''
        })
      }
    },
    //  获取量产类型部品列表
    getPartList(queryString, cb) {
      this.$http.get(`/fabricate/part/getMPAll/` + queryString).then(({data: res}) => {
        if (res.code !== 0) {
          return this.$message.error(res.msg)
        }
        const list = res.data.map((obj) => {
          console.log(obj)
          return {
            value: obj.designation + '(' + obj.designation + ')',
            partId: obj.id,
            designation: obj.designation
          }
        })
        this.timeout = setTimeout(() => {
          cb(list);
        }, 300 * Math.random());
      }).catch(() => {
      })
    },
    //  输出选择日志
    partSelect(item) {
      this.dataForm.designation = item.designation
      this.dataForm.partId = item.partId
    },
    init () {
      this.visible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].resetFields()
        if (this.dataForm.id) {
          this.getInfo()
        }
      })
    },
    // 获取信息
    getInfo () {
      this.$http.get(`/fabricate/inventoryadjustment/${this.dataForm.id}`).then(({ data: res }) => {
        if (res.code !== 0) {
          return this.$message.error(res.msg)
        }
        this.dataForm = {
          ...this.dataForm,
          ...res.data
        }
      }).catch(() => {})
    },
    // 表单提交
    dataFormSubmitHandle: debounce(function () {
      this.$refs['dataForm'].validate((valid) => {
        if (!valid) {
          return false
        }
        this.$http[!this.dataForm.id ? 'post' : 'put']('/fabricate/inventoryadjustment/', this.dataForm).then(({ data: res }) => {
          if (res.code !== 0) {
            return this.$message.error(res.msg)
          }
          this.$message({
            message: this.$t('prompt.success'),
            type: 'success',
            duration: 500,
            onClose: () => {
              this.visible = false
              this.$emit('refreshDataList')
            }
          })
        }).catch(() => {})
      })
    }, 1000, { 'leading': true, 'trailing': false })
  }
}
</script>
