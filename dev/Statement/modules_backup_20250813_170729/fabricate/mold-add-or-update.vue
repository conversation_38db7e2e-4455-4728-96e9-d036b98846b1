<template>
  <el-dialog :visible.sync="visible" :title="!dataForm.id ? $t('add') : $t('update')" :close-on-click-modal="false"
             :close-on-press-escape="false">
    <el-form :model="dataForm" :rules="dataRule" ref="dataForm" @keyup.enter.native="dataFormSubmitHandle()"
             :label-width="$i18n.locale === 'en-US' ? '120px' : '100px'">
      <div style="display: flex">
        <div class="form-item">
          <el-form-item  label="模具编码">
            <span class="font_size">{{ dataForm.head }}</span>
            <el-select v-model="dataForm.gum" placeholder="请选择" style="width: 5vw; margin-left: 6px">
              <el-option v-for="item in gum"
                         :key="item.value"
                         :label="item.label"
                         :value="item.value"></el-option>
            </el-select>
              <el-select v-model="dataForm.represent" placeholder="请选择" style="width: 5vw; margin-left: 6px">
                <el-option v-for="item in represent"
                           :key="item.value"
                           :label="item.label"
                           :value="item.value"></el-option>
              </el-select>
              <el-input v-model="dataForm.serialNumber" placeholder="编号流水号" style="width: 8vw; margin-left: 6px" @input="serialNumberValidateInput">
              </el-input>
              <el-input v-model="dataForm.groupId" placeholder="模具组编号" style="width: 5vw; margin-left: 6px" @input="groupIdValidateInput">
              </el-input>
          </el-form-item>
        </div>
        <div>
          <el-form-item  label="模具组别" >
           <el-select v-model="dataForm.moldGroup" style="width: 120px">
            <el-option
                v-for="(item,index) in moldGroupOptions"
                :key="index"
                :label="item.label"
                :value="item.value"
            ></el-option>
           </el-select>
          </el-form-item>
        </div>
      </div>
      <div class="container">
        <div class="item">
          <el-form-item label="品号" prop="partId">
            <el-select v-model="dataForm.partId" @input="changeReturn" @visible-change="handleVisibleChange" filterable placeholder="请选择"  style="width: 10vw">
              <el-option
                  v-for="(item,index) in options"
                  :key="index"
                  :label="item.label"
                  :value="item.value">
              </el-option>
            </el-select>
          </el-form-item>
        </div>
<!--        <div class="item">-->
<!--          <el-form-item label="配方编号" prop="formulationId">-->
<!--            <el-input v-model="dataForm.formulationId" placeholder="配方编号"></el-input>-->
<!--          </el-form-item>-->
<!--        </div>-->
        <div class="item">
          <el-form-item label="模具孔数" prop="numberOfHoles">
            <el-input v-model="dataForm.numberOfHoles" placeholder="模具孔数"></el-input>
          </el-form-item>
        </div>
      </div>
      <div class="container">
        <div class="item">
          <el-form-item label="模具尺寸" prop="moldSize">
            <el-input v-model="dataForm.moldSize" placeholder="模具尺寸"></el-input>
          </el-form-item>
        </div>
        <div class="item">
          <el-form-item label="文件编号" prop="documentNumber">
            <el-input v-model="dataForm.documentNumber" placeholder="文件编号"></el-input>
          </el-form-item>
        </div>
        <div class="item">
          <el-form-item label="不良率目标" prop="defectRateTarget">
            <el-input v-model="dataForm.defectRateTarget" placeholder="不良率目标"></el-input>
          </el-form-item>
        </div>
      </div>
      <div class="container">
        <div class="item">
          <el-form-item label="成形时间" prop="formingTime">
            <el-input v-model="dataForm.formingTime" placeholder="成形时间(秒)"></el-input>
          </el-form-item>
        </div>
        <div class="item">
          <el-form-item label="胶料重量" prop="rubberWeight">
            <el-input v-model="dataForm.rubberWeight" placeholder="胶料重量"></el-input>
          </el-form-item>
        </div>
        <div class="item">
          <el-form-item label="成形单重" prop="formedUnitWeight">
            <el-input v-model="dataForm.formedUnitWeight" placeholder="成形单重"></el-input>
          </el-form-item>
        </div>
      </div>
      <div class="container">
        <div class="item">
          <el-form-item label="产效目标" prop="productivityTargets">
            <el-input v-model="dataForm.productivityTargets" placeholder="产效目标(模/小时)"></el-input>
          </el-form-item>
        </div>
        <div class="item">
          <el-form-item label="品修时效" prop="qualityRepairTimeliness">
            <el-input v-model="dataForm.qualityRepairTimeliness" placeholder="品修时效(模/小时)"></el-input>
          </el-form-item>
        </div>
        <div class="item">
          <el-form-item label="品检时效" prop="qualityInspectionTimeliness">
            <el-input v-model="dataForm.qualityInspectionTimeliness" placeholder="品检时效(模/小时)"></el-input>
          </el-form-item>
        </div>
      </div>
      <div class="container">
        <div class="item">
          <el-form-item label="二次加硫">
            <el-radio v-model="dataForm.vulcanization" :label=0>否</el-radio>
            <el-radio v-model="dataForm.vulcanization" :label=1>是</el-radio>
          </el-form-item>
        </div>
<!--        <el-form-item label="半成品装箱数量" prop="packingQuantity">-->
<!--          <el-input v-model="dataForm.packingQuantity" placeholder="半成品装箱数量(模/每箱)"></el-input>-->
<!--        </el-form-item>-->
<!--        <el-form-item label="模具制作" prop="moldMaking">-->
<!--          <el-input v-model="dataForm.moldMaking" placeholder="模具制作"></el-input>-->
<!--        </el-form-item>-->
<!--        <el-form-item label="发行日期" prop="issueDate">-->
<!--          <el-date-picker-->
<!--              class="datePicker"-->
<!--              v-model="dataForm.issueDate"-->
<!--              type="date"-->
<!--              value-format="yyyy-MM-dd"-->
<!--              placeholder="发行日期">-->
<!--          </el-date-picker>-->
<!--        </el-form-item>-->
<!--        <el-form-item label="模具构造" prop="moldConstruction">-->
<!--          <ren-select v-model="dataForm.moldConstruction" dict-type="mold_construction"></ren-select>-->
<!--        </el-form-item>-->
<!--        <el-form-item label="电镀" prop="electroplating">-->
<!--          <el-input v-model="dataForm.electroplating" placeholder="电镀"></el-input>-->
<!--        </el-form-item>-->
<!--        <el-form-item label="模具清洗频率" prop="moldCleaningFrequency">-->
<!--          <el-input v-model="dataForm.moldCleaningFrequency" placeholder="模具清洗频率"></el-input>-->
<!--        </el-form-item>-->
<!--        <el-form-item label="目前位置" prop="storageLocation">-->
<!--          <el-input v-model="dataForm.storageLocation" placeholder="目前位置"></el-input>-->
<!--        </el-form-item>-->
      </div>

      <el-form-item label="备注" >
        <el-input v-model="dataForm.remark" placeholder="备注"></el-input>
      </el-form-item>
    </el-form>
    <template slot="footer">
      <el-button @click="clearDataFrom">{{ $t('cancel') }}</el-button>
      <el-button type="primary" @click="dataFormSubmitHandle()">{{ $t('confirm') }}</el-button>
    </template>
  </el-dialog>
</template>

<script>
import debounce from 'lodash/debounce'
import mixinViewModule from '@/mixins/view-module'

export default {
  data() {
    return {
      visible: false,
      //创建该页面时，不调用数据查询接口
      mixinViewModuleOptions: {
        createdIsNeed: false,
      },
      dataForm: {
        id: '',
        moldGroup:'',
        partId: '',
        head: 'MJ',
        serialNumber: '',
        gum: '',
        represent: '',
        groupId: '',
        moldCode: '',
        numberOfHoles: '',
        moldSize: '',
        documentNumber: '',
        defectRateTarget: '',
        formingTime: '',
        rubberWeight: '',
        formedUnitWeight: '',
        productivityTargets: '',
        qualityRepairTimeliness: '',
        qualityInspectionTimeliness: '',
        packingQuantity: '',
        moldMaking: '',
        issueDate: '',
        moldConstruction: '',
        electroplating: '',
        vulcanization:0,
        formulationId: null,
        moldCleaningFrequency: '',
        cumulativeModulus: 0,
        storageLocation: '',
        remark: '',
        disabled: '',
        creator: '',
        createDate: '',
        updater: '',
        updateDate: ''
      },
      moldGroupOptions:[
        {
          label:'A',
          value:'A'
        },
        {
          label:'B',
          value:'B'
        },{
          label:'C',
          value:'C'
        },{
          label:'D',
          value:'D'
        },{
          label:'E',
          value:'E'
        },{
          label:'F',
          value:'F'
        },{
          label:'G',
          value:'G'
        },{
          label:'H',
          value:'H'
        },{
          label:'I',
          value:'I'
        }
      ],
      //partInfoList:[],
      options: [],
      gum: [{
        value: 'R',
        label: 'R (橡胶)'
      }, {
        value: 'S',
        label: 'S (硅胶)'
      }, {
        value: 'L',
        label: 'L (液态)'
      }],
      represent: [{
        value: 'S',
        label: 'S (射出)'
      }, {
        value: 'P',
        label: 'P (平板)'
      },],
    }
  },
  created() {
    //this.getPartInfoList()
    this.getPartList()
  },
  computed: {
    dataRule() {
      return {
        id: [
          {required: true, message: this.$t('validate.required'), trigger: 'blur'}
        ],
        partId: [
          {required: true, message: this.$t('validate.required'), trigger: 'blur'}
        ],
        numberOfHoles: [
          {required: true, message: this.$t('validate.required'), trigger: 'blur'}
        ],
        gum: [
            {required: true, message: '请选择模具编码', trigger: 'blur'}
        ],
        represent: [
            {required: true, message: '请选择模具编码', trigger: 'blur'}
        ],
        serialNumber: [
          {required: true, message: '请输入编号流水号', trigger: 'blur'},
          {pattern: /^[A-Z0-9]{5}$/, message: '编号流水号必须是5位英文大写或阿拉伯数字', trigger: 'blur'}
        ],
        groupId: [
          {required: true, message: '请输入模具组编号', trigger: 'blur'},
          {pattern: /^[A-Z0-9]{2}$/, message: '模具组编号必须是2位英文大写或阿拉伯数字', trigger: 'blur'}
        ],
        // moldSize: [
        //   {required: true, message: this.$t('validate.required'), trigger: 'blur'}
        // ],
        // documentNumber: [
        //   {required: true, message: this.$t('validate.required'), trigger: 'blur'}
        // ],
        // defectRateTarget: [
        //   {required: true, message: this.$t('validate.required'), trigger: 'blur'}
        // ],
        // formingTime: [
        //   {required: true, message: this.$t('validate.required'), trigger: 'blur'}
        // ],
        // rubberWeight: [
        //   {required: true, message: this.$t('validate.required'), trigger: 'blur'}
        // ],
        // formedUnitWeight: [
        //   {required: true, message: this.$t('validate.required'), trigger: 'blur'}
        // ],
        // productivityTargets: [
        //   {required: true, message: this.$t('validate.required'), trigger: 'blur'}
        // ],
        // qualityRepairTimeliness: [
        //   {required: true, message: this.$t('validate.required'), trigger: 'blur'}
        // ],
        // qualityInspectionTimeliness: [
        //   {required: true, message: this.$t('validate.required'), trigger: 'blur'}
        // ],
        // packingQuantity: [
        //   {required: true, message: this.$t('validate.required'), trigger: 'blur'}
        // ],
        // moldMaking: [
        //   {required: true, message: this.$t('validate.required'), trigger: 'blur'}
        // ],
        // issueDate: [
        //   {required: true, message: this.$t('validate.required'), trigger: 'blur'}
        // ],
        // moldConstruction: [
        //   {required: true, message: this.$t('validate.required'), trigger: 'blur'}
        // ],
        // electroplating: [
        //   {required: true, message: this.$t('validate.required'), trigger: 'blur'}
        // ],
        // formulationId: [
        //   {required: true, message: this.$t('validate.required'), trigger: 'blur'}
        // ],
        // moldCleaningFrequency: [
        //   {required: true, message: this.$t('validate.required'), trigger: 'blur'}
        // ],
        // storageLocation: [
        //   {required: true, message: this.$t('validate.required'), trigger: 'blur'}
        // ],
        // remark: [
        //   {required: true, message: this.$t('validate.required'), trigger: 'blur'}
        // ],
      }
    }
  },
  methods: {
    changeReturn(value) {
      this.getPartList(value)
    },
    handleVisibleChange(visible) {
      if (visible) {
        // 下拉框显示时执行的操作
        console.log('下拉框显示了');
        // 这里可以触发相应的事件或执行其他操作
        this.getPartList();
      }
    },
    //查询匹配品番列表
    getPartList(designation) {
      // 根据参数是否存在来确定发送的请求
      const url = designation ? `/fabricate/part/getPartListByDesignation?designation=${designation}` : '/fabricate/part/getPartListByDesignation';
      console.log('请求参数:' + url)
      this.$http.get(url).then(({data: res}) => {
        if (res.code !== 0) {
          return this.$message.error(res.msg);
        }
        this.options = res.data.map((obj) => {
          return {
            label: obj.designation,
            value: obj.id,
            data: obj
          };
        });
      }).catch(() => {
        // 错误处理
      });
    },
    serialNumberValidateInput() {
      // 只允许输入5个大写字母或数字
      this.dataForm.serialNumber = this.dataForm.serialNumber.replace(/[^A-Z0-9]/g, '').toUpperCase().slice(0, 5);
    },
    groupIdValidateInput() {
      // 只允许输入两个数字或大写字母
      this.dataForm.groupId = this.dataForm.groupId.replace(/[^A-Z0-9]/g, '').toUpperCase().slice(0, 2);
    },

    // getPartInfoList(){
    //     this.$http.get(`/fabricate/part/getPartListByDesignation`).then(({data: res}) => {
    //       if (res.code !== 0) {
    //         return this.$message.error(res.message)
    //       }
    //       this.partInfoList = res.data;
    //     })
    // },
    clearDataFrom() {
      this.dataForm = {
        id: '',
        partId: '',
        head: 'MG',
        serialNumber: '',
        gum: '',
        represent: '',
        groupId: '',
        moldCode: '',
        numberOfHoles: '',
        moldSize: '',
        documentNumber: '',
        defectRateTarget: '',
        formingTime: '',
        rubberWeight: '',
        formedUnitWeight: '',
        productivityTargets: '',
        qualityRepairTimeliness: '',
        qualityInspectionTimeliness: '',
        packingQuantity: '',
        moldMaking: '',
        issueDate: '',
        moldConstruction: '',
        electroplating: '',
        formulationId: null,
        moldCleaningFrequency: '',
        cumulativeModulus: 0,
        storageLocation: '',
        vulcanization:0,
        remark: '',
        disabled: '',
        creator: '',
        createDate: '',
        updater: '',
        updateDate: ''
      }
      this.visible = false;
    },
    init() {
      this.visible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].resetFields()
        if (this.dataForm.id) {
          this.getInfo()
        }
      })
    },
    // gumVerify(){
    //   if(this.dataForm.gum == ''){
    //
    //   }
    // }
    // 获取信息
    getInfo() {
      this.$http.get(`/fabricate/mold/${this.dataForm.id}`).then(({data: res}) => {
        if (res.code !== 0) {
          return this.$message.error(res.msg)
        }
        this.dataForm = {
          ...this.dataForm,
          ...res.data
        }
        if (this.dataForm.moldCode !== '') {
          console.log('模具编码: ' + this.dataForm.moldCode)
          console.log('模具编码开始赋值')
          this.dataForm.head = this.dataForm.moldCode.slice(0, 2)
          this.dataForm.gum = this.dataForm.moldCode.slice(2, 3)
          this.dataForm.represent = this.dataForm.moldCode.slice(3, 4)
          this.dataForm.serialNumber = this.dataForm.moldCode.slice(4, 9)
          this.dataForm.groupId = this.dataForm.moldCode.slice(10, 12)
        }
      }).catch(() => {
      })
    },
    // 表单提交
    dataFormSubmitHandle: debounce(function () {
      this.$refs['dataForm'].validate((valid) => {
        if (!valid) {
          return false
        }
        if ((!this.dataForm.gum ||
            !this.dataForm.represent ||
            !(this.dataForm.serialNumber && /^[A-Z0-9]{5}$/.test(this.dataForm.serialNumber)) ||
            !(this.dataForm.groupId && /^[A-Z0-9]{2}$/.test(this.dataForm.groupId)))){
          return this.$message.error('模具编码必须填写完整')
        }
        this.dataForm.moldCode = this.dataForm.head+this.dataForm.gum + this.dataForm.represent + this.dataForm.serialNumber + '-' + this.dataForm.groupId
        this.$http[!this.dataForm.id ? 'post' : 'put']('/fabricate/mold/', this.dataForm).then(({data: res}) => {
          if (res.code !== 0) {
            return this.$message.error(res.msg)
          }
          this.$message({
            message: this.$t('prompt.success'),
            type: 'success',
            duration: 500,
            onClose: () => {
              this.visible = false
              this.$emit('refreshDataList')
            }
          })
        }).catch(() => {
        })
      })
    }, 1000, {'leading': true, 'trailing': false})
  }
}
</script>
<style>

</style>