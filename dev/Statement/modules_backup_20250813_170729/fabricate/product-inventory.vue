<template>
  <el-card shadow="never" class="aui-card--fill">
    <div class="mod-_fabricate_productInventory}">
      <el-form :inline="true" :model="dataForm" @keyup.enter.native="getDataList()">
        <el-tooltip content="请输入品号或者客户代码" placement="top">
          <el-form-item>
            <el-input v-model="dataForm.paramStr" placeholder="关键字信息" clearable @clear="onClear">
              <el-button slot="append" icon="el-icon-search" @click="getDataList()"></el-button>
            </el-input>
          </el-form-item>
        </el-tooltip>
<!--        <el-form-item>-->
<!--          <div class="block">-->
<!--            <el-date-picker-->
<!--                v-model="startToEnd"-->
<!--                @change="handleDateChange"-->
<!--                type="daterange"-->
<!--                range-separator="至"-->
<!--                start-placeholder="开始日期"-->
<!--                value-format="yyyy-MM-dd"-->
<!--                end-placeholder="结束日期">-->
<!--            </el-date-picker>-->
<!--          </div>-->
<!--        </el-form-item>-->

<!--        <el-form-item>-->
<!--          <el-button v-if="$hasPermission('fabricate:productInventory:save')" type="primary" @click="addOrUpdateHandle()">{{ $t('add') }}</el-button>-->
<!--&lt;!&ndash;          <el-button type="info">出货</el-button>&ndash;&gt;-->
<!--        </el-form-item>-->

        <el-dropdown  style="float:right;">
          <el-button type="primary">
            更多功能<i class="el-icon-arrow-down el-icon--right"></i>
          </el-button>
          <el-dropdown-menu slot="dropdown">
            <el-dropdown-item>
              <el-form-item>
                <el-button type="info" @click="exportHandle()">{{ $t('export') }}</el-button>
              </el-form-item>
            </el-dropdown-item>
            <el-dropdown-item>
              <el-form-item>
                <el-button v-if="$hasPermission('fabricate:productInventory:export')" type="info" @click="exportTemplateHandle()">
                  {{ $t('exportTemplate') }}
                </el-button>
              </el-form-item>
            </el-dropdown-item>
            <el-dropdown-item>
              <el-form-item>
                <el-tooltip content="下载模板填入信息后导入" placement="top">
                  <el-upload
                      v-if="$hasPermission('fabricate:productInventory:save')"
                      class="upload-demo"
                      :action="mixinViewModuleOptions.addBatchUrl"
                      :headers="headers"
                      :multiple="false"
                      :show-file-list="false"
                      :file-list="fileList"
                      :before-upload="beforeUpload"
                      :on-success="resultHandle"
                      :on-change="handleChange"
                      accept="'.xlsx','.xls'">
                    <el-button type="success">{{ $t('addBatch') }}</el-button>
                  </el-upload>
                </el-tooltip>
                <el-button type="text" v-show="unsuccessfulNameList" @click="dialogVisible = true">导入失败列表</el-button>
              </el-form-item>
            </el-dropdown-item>
            <el-dropdown-item>
              <el-form-item>
                <el-button v-if="$hasPermission('fabricate:productInventory:delete')" type="danger" @click="deleteHandle()">{{ $t('deleteBatch') }}</el-button>
              </el-form-item>
            </el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown>
      </el-form>
      <el-table v-loading="dataListLoading" height="600px" :data="dataList" :cell-style="columnStyle" border @selection-change="dataListSelectionChangeHandle" style="width: 100%;">
        <el-table-column type="selection" header-align="center" align="center" width="50"></el-table-column>
        <el-table-column prop="partCode" label="品号代码" header-align="center" align="center" width="130"></el-table-column>
        <el-table-column prop="partDesignation" label="品号" header-align="center" align="center" width="130" >
          <template slot-scope="scope">
            <div @click="openDialogImage(scope.row.documentId,scope.row.documentType)">{{scope.row.partDesignation}}</div>
          </template>
        </el-table-column>
        <el-table-column label="库存总数" header-align="center" align="center" :formatter="thousandSeparator">
          <template slot-scope="scope">
            <div @click="openDialog(scope.row.id)">{{ scope.row.inventoryNum | numberWithCommas}}</div>
          </template>
        </el-table-column>
        <el-table-column prop="numberOfReserves" label="无订单数" header-align="center" align="center" :formatter="thousandSeparator"></el-table-column>
<!--        <el-table-column prop="isolationNumber" label="隔离数" header-align="center" align="center" :formatter="thousandSeparator"></el-table-column>-->
        <el-table-column prop="endProductNum" label="成品总数" header-align="center" align="center" :formatter="thousandSeparator"></el-table-column>
        <el-table-column prop="semiFinishedProduct" label="半成品总数" header-align="center" align="center" :formatter="thousandSeparator"></el-table-column>
        <el-table-column prop="toBePacked" label="待包装数" header-align="center" align="center" :formatter="thousandSeparator"></el-table-column>
        <el-table-column prop="toBeShipped" label="待出货数" header-align="center" align="center" :formatter="thousandSeparator"></el-table-column>
        <el-table-column prop="uncheckedNum" label="成形数" header-align="center" align="center" :formatter="thousandSeparator"></el-table-column>
        <el-table-column prop="qualityRepairNum" label="品修数" header-align="center" align="center" :formatter="thousandSeparator"></el-table-column>
        <el-table-column prop="qualityInspectionNum" label="品检数" header-align="center" align="center" :formatter="thousandSeparator"></el-table-column>
        <el-table-column prop="transferredNum" label="转入数" header-align="center" align="center" :formatter="thousandSeparator"></el-table-column>
        <el-table-column prop="remark" label="备注" header-align="center" align="center"></el-table-column>
        <el-table-column :label="$t('functions')" fixed="right" header-align="center" align="center" width="150">
          <template slot-scope="scope">
            <el-dropdown>
              <el-button type="primary">
                功能<i class="el-icon-arrow-down el-icon--right"></i>
              </el-button>
              <el-dropdown-menu slot="dropdown">
                <el-dropdown-item>
                  <el-button v-if="$hasPermission('fabricate:productInventory:update')" type="text" size="small" @click="addOrUpdateHandle(scope.row.id)">{{ $t('update') }}</el-button>
                </el-dropdown-item>
                <el-dropdown-item>
                  <el-button v-if="$hasPermission('fabricate:productInventory:update')" type="text" size="small" @click="">出货</el-button>
                </el-dropdown-item>
                <el-dropdown-item>
                  <el-button v-if="$hasPermission('fabricate:productInventory:delete')" type="text" size="small" @click="deleteHandle(scope.row.id)">{{ $t('delete') }}</el-button>
                </el-dropdown-item>
              </el-dropdown-menu>
            </el-dropdown>


          </template>
        </el-table-column>
      </el-table>
      <el-pagination
        :current-page="page"
        :page-sizes="[10, 20, 50, 100]"
        :page-size="limit"
        :total="total"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="pageSizeChangeHandle"
        @current-change="pageCurrentChangeHandle">
      </el-pagination>
      <!-- 弹窗, 新增 / 修改 -->
      <add-or-update v-if="addOrUpdateVisible" ref="addOrUpdate" @refreshDataList="getDataList"></add-or-update>
      <!--弹窗形式显示-->
      <el-dialog title="部品库存明细资料" :visible.sync="dialogVisible" @closed="closeDialog">
        <el-form :model="displayData" ref="dataForm" @keyup.enter.native="dataFormSubmitHandle()"
                 :label-width="$i18n.locale === 'en-US' ? '80px' : '120px'">
          <div style="display: flex">
              <el-form-item label="品号代码：" prop="partCode">
                <div class="left-align" style="text-align: left">
                  {{displayData.partCode}}
                </div>
              </el-form-item>
              <el-form-item label="品号：" prop="partDesignation">
                <div class="left-align" style="text-align: left">
                  {{displayData.partDesignation}}
                </div>
              </el-form-item>
          </div>
          <div class="container">
            <div class="item" style="background-color:#bcbec2">
              <div>
                <el-form-item label="库存总数：" prop="inventoryNum">
                  <div class="left-align">
                    {{displayData.inventoryNum | numberWithCommas}}
                  </div>
                </el-form-item>
              </div>
              <div style="background-color: #13ce66">
                <el-form-item label="订单数量:" prop="orderQuantity">
                  <div class="left-align">
                    {{displayData.orderQuantity === null ? 0 : displayData.orderQuantity}}
                  </div>
                </el-form-item>
                <el-form-item label="预示库存:" prop="indicativeInventory">
                  <div class="left-align">
                    {{displayData.indicativeInventory === null ? 0 : displayData.indicativeInventory}}
                  </div>
                </el-form-item>
                <el-form-item label="无订单数:" prop="numberOfReserves">
                  <div class="left-align">
                    {{displayData.numberOfReserves === null ? 0 : displayData.numberOfReserves}}
                  </div>
                </el-form-item>
              </div>
              <div>
                <el-form-item label="转入数：" prop="transferredNum">
                  <div class="left-align">
                    {{displayData.transferredNum | numberWithCommas}}
                  </div>
                </el-form-item>
                <el-form-item label="其他数：" prop="other">
                  <div class="left-align">
                    {{displayData.other === null ? 0 : displayData.other}}
                  </div>
                </el-form-item>
              </div>
            </div>
            <div class="item" style="background-color: #dfe6e9">
              <div>
                <el-form-item label="成品总数："  prop="endProductNum">
                  <div class="left-align">
                    {{displayData.endProductNum | numberWithCommas}}
                  </div>
                </el-form-item>
              </div>
              <div>
                <el-form-item label="待包装数：" prop="endProductNum">
                  <div class="left-align">
                    {{displayData.toBePacked | numberWithCommas}}
                  </div>
                </el-form-item>
              </div>
              <div>
                <el-form-item label="待出货数：" prop="endProductNum">
                  <div class="left-align">
                    {{displayData.toBeShipped | numberWithCommas}}
                  </div>
                </el-form-item>
              </div>
            </div>
            <div class="item" style="background-color: #f1f2f6">
              <div>
                <el-form-item label="半成品数："  prop="semiFinishedProduct">
                  <div class="left-align">
                    {{displayData.semiFinishedProduct | numberWithCommas}}
                  </div>
                </el-form-item>
              </div>
              <div>
                <el-form-item label="成形数：" prop="uncheckedNum">
                  <div class="left-align">
                    {{displayData.uncheckedNum | numberWithCommas}}
                  </div>
                </el-form-item>
              </div>
              <div>
                <el-form-item label="品修数：" prop="qualityInspectionNum">
                  <div class="left-align">
                    {{displayData.qualityRepairNum | numberWithCommas}}
                  </div>
                </el-form-item>
              </div>
              <div>
                <el-form-item label="品检数：" prop="qualityInspectionNum">
                  <div class="left-align">
                    {{displayData.qualityInspectionNum | numberWithCommas}}
                  </div>
                </el-form-item>
              </div>
            </div>
            <div style="width: 100%">
              <el-form-item label="更新时间：" prop="updateDate">
                <div style="width: auto;border: #5daf34">
                  {{displayData.updateDate}}
                </div>
              </el-form-item>
            </div>
          </div>
          <div class="container">
            <div class="item">
              <el-button @click="openDialogImage(displayData.documentId,displayData.documentType)">图片显示</el-button>
            </div>
            <div class="item">
              <el-button type="info" @click="viewInventoryDetails">查看详情</el-button>
            </div>
          </div>
        </el-form>
      </el-dialog>


      <el-dialog title=图片显示 :visible.sync="imageDisplay" @closed="closeDialogImage">
        <el-image :src="'http://192.168.10.113/files/'+imagePath+documentType" fit="contain" style="width: 45vw; height: 65vh" alt="未上传图片"></el-image>
      </el-dialog>

      <el-dialog title="库存详情" :visible.sync="visible1">
        <el-table
            :data="inventoryByMoldData"
            border
            style="width: 100%">
          <el-table-column prop="moldGroup" label="模具组别">
            <template slot-scope="scope">
              {{scope.row.moldGroup === 'default' ? '无模具信息' : scope.row.moldGroup}}
            </template>
          </el-table-column>
          <el-table-column prop="inventoryNum" label="库存总数" :formatter="formatNumber">
          </el-table-column>
          <el-table-column prop="endProductNum" label="成品总数" :formatter="formatNumber">
          </el-table-column>
          <el-table-column prop="semiFinishedProduct" label="半成品总数" :formatter="formatNumber">
          </el-table-column>
        </el-table>
      </el-dialog>
      <el-dialog title="出货" :visible.sync="outDialog">

      </el-dialog>
    </div>
  </el-card>
</template>

<script>
import mixinViewModule from '@/mixins/view-module'
import AddOrUpdate from './product-inventory-add-or-update'
import Cookies from "js-cookie";
import debounce from "lodash/debounce";
export default {
  mixins: [mixinViewModule],
  data () {
    return {
      startToEnd: [],
      imagePath: '',
      documentType:'',
      tableData: '',
      dialogVisibleCustomer: false,
      visible1: false,
      outDialog: false,
      imageDisplay: false,
      //按钮
      currentCommand:'',
      message:'',
      dialogVisible: false, // 对话框是否显示
      headers: {'token': Cookies.get('token')},
      fileList: [{name: '', url: ''}],
      unsuccessfulNameList: '',//  导入失败名称列表
      mixinViewModuleOptions: {
        getDataListURL: '/fabricate/productInventory/page',
        getDataListIsPage: true,
        exportURL: '/fabricate/productInventory/export',
        deleteURL: '/fabricate/productInventory',
        deleteIsBatch: true,
        exportTemplateURL: '/fabricate/productInventory/export/template',
        addBatchUrl: window.SITE_CONFIG['apiURL'] + '/fabricate/productInventory/batchSave',
      },
      dataForm: {
        partDesignation: '',
        customerCode: '',
        customerId: '',
        paramStr: '',
        startDate:'',
        endDate:'',
      },
      displayData:{
        id: '',
        partId: '',
        partDesignation: '',
        customerCode: '',
        documentId: '',
        documentType:'',
        uncheckedNum: 0,
        numberOfReserves: 0,
        orderQuantity:0,
        noOrderNumber:0,
        indicativeInventory:0,
        other:0,
        qualityRepairNum: 0,
        qualityInspectionNum: 0,
        semiFinishedProduct: 0,
        endProductNum: 0,
        inventoryNum: 0,
        transferredNum: 0,
        toBeShipped: 0,
        toBePacked: 0,
        remark: '',
      },
      inventoryByMoldData:[],
    }
  },
  components: {
    AddOrUpdate
  },
  created() {
  /*  const endDate = new Date();
    const startDate = new Date();
    startDate.setMonth(endDate.getMonth() - 6);
    this.dataForm.startDate = startDate
    this.dataForm.endDate = endDate*/
  },
  filters:{
    // 自定义千位分隔符
    numberWithCommas (value) {
      // 将数字转换为字符串并使用正则表达式添加千位分隔符
      return String(value).replace(/\B(?=(\d{3})+(?!\d))/g, ',')
    },

  },
  methods:{
    columnStyle({ row, column, rowIndex, columnIndex }) {
      if(columnIndex  <  8) {
        return 'background:	#FFF8DC;'
      }else {
        return 'background:	#fffff;'
      }
    },
    formatNumber(row, column, cellValue) {
      const num = Number(cellValue);
      if (isNaN(num)) return '0';
      return num.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',');
    },
    // 将获取的时间转换成日期
    dateOfConversion(date){
      let year = date.getFullYear();
      let month = date.getMonth() + 1;
      let day = date.getDate();
      return `${year}-${month}-${day}`;
    },
    handleDateChange(value){
      if(!value){

        return
      }
      this.dataForm.startDate = value[0]
      this.dataForm.endDate = value[1]
      this.getDataList()
    },
    //查看库存详情
    viewInventoryDetails(){
      this.$http.get(`fabricate/productInventory/queryProductInventoryGroupByMold/${this.displayData.partId}`).then(({data: res}) =>{
        if(res.code !== 0){
          return this.$message.error(res.msg)
        }
        this.inventoryByMoldData = res.data
        this.visible1 = true
      })
    },
    //清空后重新查询
    onClear(){
      this.query()
    },
    openDialogImage(documentId,documentType) { // 打开对话框
      this.imagePath = documentId
      this.documentType = documentType
      this.imageDisplay = true;
    },
    closeDialogImage() { // 关闭对话框
      this.imageDisplay = true;
      this.imageDisplay = false;
    },
    selectCustomer(id){
      this.$http.get(`/customer/customer/${id}`).then(({data: res}) => {
        console.log("res:",res)
        if (res.code !== 0) {
          this.tableData = ''
          return this.$message.error(res.msg)
        }
        this.tableData = res.data
        this.tableData.isWeighing = this.sfktHouse(this.tableData)
      }).catch(() => {
        this.dataListLoading = false
      })
    },
    // 自定义千位分隔符
    thousandSeparator(row, column, cellValue) {
      return cellValue.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ",");
    },
    handleClick(inventoryNum){
    },
    selectHandle(id){
      this.dialogVisible = true;
      this.$http.get(`/fabricate/productInventory/${id}`).then(({data: res}) => {
        console.log("res:",res)
        if (res.code !== 0) {
          this.displayData = ''
          return this.$message.error(res.msg)
        }
        this.displayData = res.data
        this.imagePath = res.data.documentId
        this.displayData.isWeighing = this.sfktHouse(this.displayData)
        this.dataForm = {
          ...this.dataForm,
        }
      }).catch(() => {
        this.dataListLoading = false
      })
    },
    openDialog(id) { // 打开对话框
      this.dialogVisible = true;
      this.selectHandle(id)
    },
    closeDialog() { // 关闭对话框
      this.dialogVisible = false;
    },
    beforeUpload(file) {
      this.message = this.$message({
        message: '上传中，请勿操作',
        center: true,
        duration: 0,
      });
    },
    handleChange(file, fileList) {
      this.fileList = fileList.slice(-3);

    },
    resultHandle(res) {
      if (res.code !== 0) {
        return this.$message.error(res.msg)
      }
      this.$message({
        message: this.$t('prompt.success'),
        type: 'success',
        duration: 500,
        onClose: () => {
          if (res.data) {
            this.unsuccessfulNameList = res.data
          }
          this.visible = false
          this.query()
        }
      })
    },
    dataFormSubmitHandle: debounce(function () {
      this.dataForm.uncheckedNum = this.dataForm.uncheckedNum || 0;
      this.dataForm.qualityRepairNum = this.dataForm.qualityRepairNum || 0;
      this.dataForm.qualityInspectionNum = this.dataForm.qualityInspectionNum || 0;
      this.dataForm.endProductNum = this.dataForm.endProductNum || 0;
      this.dataForm.inventoryNum = this.dataForm.inventoryNum || 0;
      this.dataForm.transferredNum = this.dataForm.transferredNum || 0;
      this.$refs['dataForm'].validate((valid) => {
        if (!valid) {
          return false
        }
        this.$http[!this.dataForm.id ? 'post' : 'put']('/fabricate/productInventory/', this.dataForm).then(({data: res}) => {
          if (res.code !== 0) {
            return this.$message.error(res.msg)
          }
          this.$message({
            message: this.$t('prompt.success'),
            type: 'success',
            duration: 500,
            onClose: () => {
              this.visible = false
              this.$emit('refreshDataList')
            }
          })
        }).catch(() => {
        })
      })
    }, 1000, {'leading': true, 'trailing': false})
  }
}
</script>


<style scoped>
.left-align {
  font-size: 16px;
  color: black;
  font-weight: bold;
}
.container {
  display: flex;
  flex-wrap: wrap;
}
.boxs {
  width: calc(50% - 10px);
  margin: 5px;
}

@media screen and (max-width: 768px) {
  .container {
    display: block;  /* Change container display to block */
  }
  .boxs {
    width: 100%;
  }
}
.item {
  width: calc(33.33% - 10px);
  margin: 5px;
}

@media screen and (max-width: 300px) {
  .container {
    display: block;  /* Change container display to block */
  }
  .item {
    width: 100%;  /* Change item width to 100% */
  }
}

@media (min-width: 768px) and (max-width: 1023px) {
  .container {
    display: block;  /* Change container display to block */
  }
  .item {
    width: calc(50% - 10px);
  }
}
</style>
