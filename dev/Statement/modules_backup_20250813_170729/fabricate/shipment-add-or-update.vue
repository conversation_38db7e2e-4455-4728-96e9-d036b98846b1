<template>
  <el-dialog :visible.sync="visible" :title="!dataForm.id ? '新增出货记录' : $t('update')" :close-on-click-modal="false"
             :close-on-press-escape="false" append-to-body @close="closeDialog">
    <el-form :model="dataForm" :rules="dataRule" ref="dataForm"
             :label-width="$i18n.locale === 'en-US' ? '120px' : '80px'">
<!--      <div class="container" v-for="(item,index) in dataForm.shipmentList" :key="index">-->
<!--        <el-form-item label="次批号"  style="width: 20vw">-->
<!--          <el-select v-model="item.subBatchId" style="width: 15vw" @change="selectSubBatch(index)" filterable clearable>-->
<!--            <el-option v-for="(data,index1) in subBatchOption"-->
<!--                       :key="index1"-->
<!--                       :label="data.label + '(' + data.data.designation + ')'"-->
<!--                       :value="data.value"></el-option>-->
<!--          </el-select>-->
<!--        </el-form-item>-->
<!--        <el-form-item label="出货数量">-->
<!--          <el-input type="number"  v-model="item.number" placeholder="出货数量"></el-input>-->
<!--        </el-form-item>-->
<!--        <el-button  type="danger" style="width: 4vw; height: 4vh; margin-left: 2vw" icon="el-icon-delete" @click="clearByIndex(index)"></el-button>-->
<!--      </div>-->
      <el-row>
        <el-col :span="12">
      <el-form-item label="客户代码" prop="customerId">
        <customer-component v-model="dataForm.customerId" :query-name="true" @customerData="customerData" placeholder="客户代码"></customer-component>
      </el-form-item>
        </el-col>
        <el-col :span="12">
      <el-form-item label="客户名称" prop="customerName">
        <span class="font_size">{{dataForm.customerName}}</span>
      </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="12">
      <el-form-item label="订单号" prop="orderId">
        <business-order-component v-model="dataForm.orderId" :query-name="true" @orderData="orderData" placeholder="客户代码"></business-order-component>
      </el-form-item>
        </el-col>
        <el-col :span="12">
      <el-form-item label="应出货量" prop="quantity">
        <el-input v-model="dataForm.quantity" style="width: 13vw" disabled></el-input>
      </el-form-item>
        </el-col>
      </el-row>
      <el-form-item label="订单交期" prop="deliveryDate">
        <el-input v-model="dataForm.deliveryDate" style="width: 13vw" disabled></el-input>
      </el-form-item>
      <el-form-item label="交货地址" prop="tradingLocations">
<!--        <el-input v-model="dataForm.tradingLocations" style="width: 13vw" disabled></el-input>-->
<span>{{dataForm.tradingLocations}}</span>
      </el-form-item>
<!--      <el-form-item label="单价" prop="unitPrice">-->
<!--        <el-input v-model="dataForm.unitPrice" style="width: 13vw" disabled></el-input>-->
<!--      </el-form-item>-->
<!--      <el-form-item label="总金额" prop="transactionAmount">-->
<!--        <el-input v-model="dataForm.transactionAmount" style="width: 13vw" disabled></el-input>-->
<!--      </el-form-item>-->
<!--      <el-form-item label="币别" prop="currency">-->
<!--        <template slot-scope="scope">-->
<!--          {{scope.row.currency===0? '人民币' :-->
<!--            scope.row.currency===1?'欧元':-->
<!--                scope.row.currency===2?'英镑':-->
<!--                    scope.row.currency===3?'日元':-->
<!--                        scope.row.currency===4?'澳元':-->
<!--                            scope.row.currency===5?'加元':-->
<!--                                scope.row.currency===6?'瑞士法郎':-->
<!--                                    scope.row.currency===7?'美元':-->
<!--                                        scope.row.currency===8?'印度卢比':-->
<!--                                            scope.row.currency===9?'新加坡元':-->
<!--                                                scope.row.currency===10?'港元':'未知'-->
<!--          }}-->
<!--        </template>-->
<!--        <el-input v-model="dataForm.currency" style="width: 13vw" disabled></el-input>-->
<!--        <span>{{dataForm.currency===0? '人民币' :-->
<!--          dataForm.currency===1?'欧元':-->
<!--              dataForm.currency===2?'英镑':-->
<!--                  dataForm.currency===3?'日元':-->
<!--                      dataForm.currency===4?'澳元':-->
<!--                          dataForm.currency===5?'加元':-->
<!--                              dataForm.currency===6?'瑞士法郎':-->
<!--                                  dataForm.currency===7?'美元':-->
<!--                                      dataForm.currency===8?'印度卢比':-->
<!--                                          dataForm.currency===9?'新加坡元':-->
<!--                                              dataForm.currency===10?'港元':'未知'-->
<!--        }}</span>-->
<!--      </el-form-item>-->
<!--      <el-form-item label="次批号">-->
<!--      <el-cascader-->
<!--          v-model="dataForm.shipmentList"-->
<!--          class="cascader_top"-->
<!--          :options="subBatchOption"-->
<!--          :props="props"-->
<!--          @change="handleChange"-->
<!--          ref="editList"-->
<!--          filterable-->
<!--          clearable></el-cascader>-->
<!--      </el-form-item>-->
      <el-table v-loading="dataListLoading" height="500px" :data="tableData" border @selection-change="dataListSelectionChangeHandle" style="width: 100%;">
        <el-table-column type="selection" header-align="center" align="center" width="50"></el-table-column>
        <!--        <el-table-column prop="id" label="主键id" header-align="center" align="center"></el-table-column>-->
        <el-table-column prop="boxCode" label="箱号" header-align="center" align="center">
          <template slot-scope="scope">
            <el-link @click="showPackDetail(scope.row.id)" type="primary">{{scope.row.boxCode}}</el-link>
          </template>
        </el-table-column>
        <!--        <el-table-column prop="partId" label="品号id" header-align="center" align="center"></el-table-column>-->
        <el-table-column prop="designation" label="品号" header-align="center" align="center"></el-table-column>
        <el-table-column prop="customerCode" label="客户代码" header-align="center" align="center"></el-table-column>
        <!--        <el-table-column prop="peSpecification" label="PE袋规格" header-align="center" align="center"></el-table-column>-->
        <!--        <el-table-column prop="boxSpecification" label="纸箱规格" header-align="center" align="center"></el-table-column>-->
        <!--        <el-table-column prop="quantityPerBag" label="每袋数量" header-align="center" align="center"></el-table-column>-->
        <!--        <el-table-column prop="quantityOfEachCarton" label="每箱数量" header-align="center" align="center"></el-table-column>-->
        <el-table-column prop="pieceWeight" label="单重" header-align="center" align="center">
          <template slot-scope="scope">
            {{scope.row.pieceWeight}}g
          </template>
        </el-table-column>
        <el-table-column prop="suttle" label="净重" header-align="center" align="center">
          <template slot-scope="scope">
            {{scope.row.suttle}}kg
          </template>
        </el-table-column>
        <el-table-column prop="grossWeight" label="毛重" header-align="center" align="center">
          <template slot-scope="scope">
            {{scope.row.grossWeight}}kg
          </template>
        </el-table-column>
        <el-table-column prop="quantity" label="数量" header-align="center" align="center">

        </el-table-column>
        <el-table-column prop="shippingStatus" label="当前状态" header-align="center" align="center">
          <template slot-scope="scope">
            <el-tag
                :type="scope.row.shippingStatus === 0 ? '' : scope.row.shippingStatus === 1 ? 'success' : 'info'"
            >{{scope.row.shippingStatus === 0 ? '待包装' : scope.row.shippingStatus === 1 ? '已包装' : '已出货'}}
            </el-tag>
          </template>
        </el-table-column>
        <!--        <el-table-column prop="packerId" label="包装者id" header-align="center" align="center"></el-table-column>-->
        <el-table-column prop="packer" label="包装者" header-align="center" align="center"></el-table-column>
        <el-table-column prop="packingTime" label="包装时间" header-align="center" align="center"></el-table-column>
        <el-table-column prop="storageLocation" label="库位" header-align="center" align="center"></el-table-column>
        <!--        <el-table-column prop="remark" label="备注" header-align="center" align="center"></el-table-column>-->
        <!--        <el-table-column prop="disabled" label="是否失效 0:有效; 1:失效 (逻辑删除)" header-align="center" align="center"></el-table-column>-->
        <!--        <el-table-column prop="creator" label="创建者" header-align="center" align="center"></el-table-column>-->
        <!--        <el-table-column prop="createDate" label="创建时间" header-align="center" align="center"></el-table-column>-->
        <!--        <el-table-column prop="updater" label="更新者" header-align="center" align="center"></el-table-column>-->
        <!--        <el-table-column prop="updateDate" label="更新时间" header-align="center" align="center"></el-table-column>-->
<!--        <el-table-column :label="$t('handle')" fixed="right" header-align="center" align="center" width="150">-->
<!--          <template slot-scope="scope">-->
<!--            <el-dropdown>-->
<!--              <el-button type="primary">-->
<!--                功能<i class="el-icon-arrow-down el-icon&#45;&#45;right"></i>-->
<!--              </el-button>-->
<!--              <el-dropdown-menu slot="dropdown">-->
<!--                &lt;!&ndash;                <el-dropdown-item>&ndash;&gt;-->
<!--                &lt;!&ndash;                  <el-button v-if="$hasPermission('packing:packagingbox:update')" type="text" size="small" @click="addOrUpdateHandle(scope.row.id)">{{ $t('update') }}</el-button>&ndash;&gt;-->
<!--                &lt;!&ndash;                </el-dropdown-item>&ndash;&gt;-->
<!--                <el-dropdown-item>-->
<!--                  <el-button v-if="$hasPermission('packing:packagingbox:delete')" type="text" size="small" @click="deleteRow(scope.row.id)">{{ $t('delete') }}</el-button>-->
<!--                </el-dropdown-item>-->
<!--              </el-dropdown-menu>-->
<!--            </el-dropdown>-->
<!--          </template>-->
<!--        </el-table-column>-->
      </el-table>
      <el-form-item label="出货数量" prop="customerName">
        <el-input type="number"  v-model="dataForm.shipmentQuantity" placeholder="出货数量"></el-input>
      </el-form-item>
      <el-form-item label="出货金额" prop="customerName">
        <el-input type="number"  v-model="dataForm.outAmount" placeholder="出货数量"></el-input>
      </el-form-item>
      <el-form-item label="快递单号" prop="shipmentNumber">
        <el-input type="string"  v-model="dataForm.shipmentNumber" placeholder="快递单号"></el-input>
      </el-form-item>
<!--      <div class="container" v-for="(item,index) in dataOption">-->
<!--        <span>次批号:{{item[2].batchNumber + "-" + item[2].subBatchNumber}}</span>-->
<!--      </div>-->
<!--      <el-button @click="add"  type="info" style="width: 4vw; height: 4vh;margin-bottom: 2vh">新增</el-button>-->
      <div class="container">
        <el-form-item label="出货时间" prop="shipmentTime">
          <el-date-picker
              v-model="dataForm.shipmentTime"
              type="datetime"
              value-format="yyyy-MM-dd HH:mm:ss"
              placeholder="选择日期时间"
              default-time="18:00:00">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="记录者" prop="shipperId">
          <el-select v-model="dataForm.shipperId" @change="selectEmployee">
            <el-option v-for="(item,index) in employeesOption"
                       :key="index"
                       :label="item.label"
                       :value="item.value"></el-option>
          </el-select>
        </el-form-item>
      </div>
    </el-form>
    <template slot="footer">
      <el-button @click="visible = false">{{ $t('cancel') }}</el-button>
      <el-button type="primary" @click="dataFormSubmitHandle()">{{ $t('confirm') }}</el-button>
    </template>
    <el-dialog :visible.sync="visible1" title="包装详情" width="550px" center :append-to-body="true">
      <div style="display: flex; justify-content: center;">
        <el-table :data="detailData" style="width: 480px;">
          <el-table-column prop="subBatchNumber" label="批次号" width="240"></el-table-column>
          <el-table-column prop="quantity" label="数量" width="230"></el-table-column>
        </el-table>
      </div>
    </el-dialog>
  </el-dialog>

</template>

<script>
import debounce from 'lodash/debounce'
import Cookies from "js-cookie";
import mixinViewModule from '@/mixins/view-module'

export default {
  mixins: [mixinViewModule],
  data() {
    return {
      visible2: false,
      visible1: false,
      visible: false,
      //创建该页面时，不调用数据查询接口
      mixinViewModuleOptions: {
        createdIsNeed: false,
      },
      props: { label: 'label', value: 'value', children: 'children',multiple:true},
      dataForm: {
        multipleBoxId: '',
        orderId: '',
        orderNumber: '',
        multiplePartAndBatch: '',
        subBatchId: '',
        subBatchNumber: '',
        number:'',
        quantity:'',
        deliveryDate: '',
        tradingLocations: '',
        unitPrice: '',
        transactionAmount: '',
        currency: '',
        outAmount: '',
        shipmentQuantity:'',
        shipmentNumber:'',
        customerId: '',
        customerCode: '',
        customerName: '',
        partId:'',
        designation:'',
        batchId:'',
        shipmentList: [
          {
            id: '',
            partId: '',
            designation: '',
            batchId:'',
            subBatchId: '',
            subBatchNumber: '',
            number: '',
          }
        ],
        shipmentTime: '',
        shipperId: '',
        shipper: '',
      },
      dataOption:[
        {
          partId: '',
          designation: '',
          subBatchId: '',
          subBatchNumber: '',
          number: '',
        }
      ],
      deptList:['1641701552458743810'],
      subBatchOption:[],
      subBatchTempOption:[],
      employeesOption:[],
      tableData: [],
      dateTime:[{
        label: '2021-01-01 00:00:00',
        value: '2021-01-01 00:00:00'
      },{
        label: '2021-01-02 00:00:00',
        value: '2021-01-02 00:00:00'
      },{
        label: '2021-01-03 00:00:00',
        value: '2021-01-03 00:00:00'
      },{
        label: '2021-01-04 00:00:00',
        value: '2021-01-04 00:00:00'
      }
      ],
      selectedRows: [],
      detailData:[],
    }
  },
  computed: {
    dataRule() {
      return {
        id: [
          {required: true, message: this.$t('validate.required'), trigger: 'blur'}
        ],
        partId: [
          {required: true, message: this.$t('validate.required'), trigger: 'blur'}
        ],
        designation: [
          {required: true, message: this.$t('validate.required'), trigger: 'blur'}
        ],
        subBatchId: [
          {required: true, message: this.$t('validate.required'), trigger: 'blur'}
        ],
        subBatchNumber: [
          {required: true, message: this.$t('validate.required'), trigger: 'blur'}
        ],
        shipmentTime: [
          {required: true, message: this.$t('validate.required'), trigger: 'blur'}
        ],
        shipperId: [
          {required: true, message: this.$t('validate.required'), trigger: 'blur'}
        ],
        shipper: [
          {required: true, message: this.$t('validate.required'), trigger: 'blur'}
        ],
      }
    }
  },
  methods: {
    //查看包装详情
    showPackDetail(boxId){
      //获取该包装详情
      this.visible1 = true
      this.searchPackDetail(boxId)
    },
    //查找包装详情
    searchPackDetail(boxId){
      this.$http.get(`packing/packagingboxdetail/getBoxDetailData?boxId=${boxId}`).then(({data: res}) => {
        if (res.code !== 0) {
          return this.$message.error(res.msg)
        }
        this.detailData = res.data
      })
    },
    dataListSelectionChangeHandle(selection) {
      var totoQuantity = 0;
      var multipleBoxId = '';
      this.selectedRows = selection; // 更新选中的行数据数组
      console.log('selection:', selection);
      if (selection.length > 0) {
        for(let i = 0; i < selection.length; i++){
          totoQuantity += selection[i].quantity
          multipleBoxId += selection[i].id  + ','+ selection[i].boxCode +','+selection[i].quantity+ ';'
        }
      }
      this.dataForm.shipmentQuantity = totoQuantity
      console.log('this.dataForm.shipmentQuantity:', this.dataForm.shipmentQuantity)
      this.dataForm.outAmount= this.dataForm.shipmentQuantity * this.dataForm.unitPrice
      this.dataForm.multipleBoxId = multipleBoxId
    },
    // 客户数据
    customerData(data){
      console.log('customerData:', data)
      this.dataForm.customerId = data.id
      this.dataForm.customerCode = data.code
      this.dataForm.customerName = data.chineseName
    },
    orderData(data){
      console.log('orderData:', data)
      this.dataForm.orderId = data.id
      this.dataForm.orderNumber = data.orderNumber
      this.dataForm.quantity = data.quantity
      this.dataForm.unitPrice = data.unitPrice
      this.dataForm.transactionAmount = data.transactionAmount
      this.dataForm.currency = data.currency
      this.dataForm.deliveryDate = data.deliveryDate
      this.dataForm.tradingLocations = data.tradingLocations
      this.getTableDataList()
    },
//获取包装数据列表
    getTableDataList(){
      this.$http.get(`/packing/packagingbox/page1`,{params:{

          customerCode: this.dataForm.customerCode,
        }}).then(({data:res}) => {
        if (res.code !== 0) {
          return this.$message.error(res.msg)
        }
        console.log('tableData:', res.data)
        this.tableData = res.data.list
      })
    },
    //获取数据
    getDataList(){
      this.$http.get('batch/subbatch/getFormingStatistics',{params:{
          begin: this.dataFrom.dateTime[0],
          end: this.dataFrom.dateTime[1],
          type:this.dataFrom.type
        }}).then(({data: res}) =>{
        if (res.code !== 0) {
          return this.$message.error(res.msg)
        }
        this.dataX = res.data["1"]
        this.dataY = res.data["2"]
        this.initChart()
      })
    },
    add(){
    let last =  {
      partId: '',
      designation: '',
      subBatchId: '',
      subBatchNumber: '',
      number: '',
    };
    this.dataForm.shipmentList.push(last)
    },
    clearByIndex(index){
      // 检查是否只剩下一个元素
      if (this.dataForm.shipmentList.length > 1) {
        // 删除指定下标的元素
        this.dataForm.shipmentList.splice(index, 1);
      } else {
        // 提示用户不能删除最后一个元素
        this.$message({
          message: '无法删除最后一项',
          type: 'warning'
        });
      }
    },
    handleChange(value) {
      console.log('pzChangeList:', value); // 输出名称数组
      console.log('shipmentList:', this.dataForm.shipmentList); // 输出名称数组
      console.log('pzChangeList length:', value.length);
      console.log('11:', this.dataForm.shipmentList.length+"==="+this.dataForm.shipmentList[0][2].id+"==="+this.dataForm.shipmentList[0][2].designation);
      var multiplePartAndBatch=''
      for(var index=0;index<value.length;index++){
        multiplePartAndBatch=multiplePartAndBatch+value[index][2].designation+","+value[index][2].batchNumber+"-"+value[index][2].subBatchNumber+";"
      }
      this.dataForm.multiplePartAndBatch=multiplePartAndBatch
//       var pzLabelList=this.$refs['editList'].getCheckedNodes().map(item=>{
//         var obj={label:item.label,id:item.value}
//         return obj
//       })
//       console.log(pzLabelList,'pzLabelList')
// this.dataForm.shipmentList=pzLabelList
      console.log(">>>>>>>>>>"+this.dataForm.shipmentList[0][2].partId);
      // console.log(">>>>>>>>>>"+pzLabelList[0].id.partId); // 当前选中的值
    },
    //选择批号时的动作
    selectSubBatch(index){
      console.log('----------------');
      if(this.dataForm.shipmentList[index].subBatchId){
        let filter = this.dataForm.shipmentList.filter(item => item.subBatchId === this.dataForm.shipmentList[index].subBatchId);
        if(filter.length > 1){
          this.dataForm.shipmentList[index].subBatchId = ''
          return  this.$message({
            message: '不能选择重复的批号',
            type: 'warning'
          });
        }
        let find = this.subBatchOption.find(item => item.value === this.dataForm.shipmentList[index].subBatchId);
        this.dataForm.shipmentList[index].subBatchNumber = find.label
        this.dataForm.shipmentList[index].number = find.data.toBeShipped
        this.dataForm.shipmentList[index].partId = find.data.partId
        this.dataForm.shipmentList[index].designation = find.data.designation
      }else {
        this.dataForm.shipmentList[index].subBatchNumber = ''
        this.dataForm.shipmentList[index].number = ''
        this.dataForm.shipmentList[index].partId = ''
        this.dataForm.shipmentList[index].designation = ''
      }
    },
    //选择出货人时的动作
    selectEmployee(event) {

      console.log("item.value=="+this.dataForm.shipperId)
      if(this.dataForm.shipperId) {
        let find = this.employeesOption.find(item => item.value === this.dataForm.shipperId);
        this.dataForm.shipper = find.label
        console.log("find.label=="+find.label)
      }else {
        this.dataForm.shipper = ''
      }
    },
    //关闭对话框的回调
    closeDialog(){
      this.dataForm = {
        shipmentList: [
          {
            partId: '',
            designation: '',
            subBatchId: '',
            subBatchNumber: '',
            number: '',
          }
        ],
        shipmentTime: '',
        shipperId: '',
        shipper: '',
        customerId: '',
        orderId:'',
        shipmentQuantity: '',
        shipmentNumber: '',
        outAmount:  '',
      }
      this.tableData  = []
    },
    //获取可出货次批列表
    getShipmentSubBatchList(){
      this.$http.get('batch/subbatch/getShipmentSubBatchList').then(({data:res}) => {
        if (res.code !== 0) {
          return this.$message.error(res.msg)
        }
      this.subBatchOption = res.data
      })
    },
    //获取出货人员列表
    getShipmentEmployeesList(){
      this.$http.post('/sys/user/getUserListByDeptId',this.deptList).then(({data: res}) => {
      if(res.code !== 0){
        return this.$message.error(res.msg)
        }
        this.employeesOption = res.data.map((obj) => {
          return{
            label:obj.userCode + '(' + obj.username + ')',
            value:obj.id
          }
        })
      })
    },
    init() {
      this.visible = true
      this.getShipmentSubBatchList()
      this.getShipmentEmployeesList()
      this.$nextTick(() => {
        this.$refs['dataForm'].resetFields()
        if (this.dataForm.id) {
          this.getInfo()
        }
        this.dataOption = []
        this.dataForm.shipmentTime = this.formatDate(new Date())
        // this.dataForm.shipperId = Cookies.get('userId')
        // this.dataForm.shipper = Cookies.get('userName')
      })
    },
    //日期处理方法
    formatDate(date) {
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, '0');
      const day = String(date.getDate()).padStart(2, '0');
      const hours = String(date.getHours()).padStart(2, '0');
      const minutes = String(date.getMinutes()).padStart(2, '0');
      const seconds = String(date.getSeconds()).padStart(2, '0');
      return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
    },
    // 获取信息
    getInfo() {
      this.$http.get(`/fabricate/shipment/${this.dataForm.id}`).then(({data: res}) => {
        if (res.code !== 0) {
          return this.$message.error(res.msg)
        }
        this.dataForm = {
          ...this.dataForm,
          ...res.data
        }
      }).catch(() => {
      })
    },
    // 表单提交
    dataFormSubmitHandle: debounce(function () {
      // console.log("-----"+this.dataOption.length+'--'+this.dataOption[0][2].batchId);
      // let k = this.dataForm.shipmentList.some(item => !item.subBatchId);
      let k = this.dataForm.shipmentList.some(item => !item.batchId);
      // console.log(this.dataForm.shipmentList[0][2].partId+"========="+this.dataForm.shipmentList.length+'===='+this.dataOption.length);
      if(this.dataForm.shipmentList.length==0){
        return this.$message({
          message: '请将次批信息填写完整',
          type: 'warning'
        });
      }
      // let some = this.dataForm.shipmentList.some(item => !item.number);
      let some = this.dataForm.shipmentList.some(item => !item.subBatchNumber);
      if(this.dataForm.shipmentQuantity<=0){
        return this.$message({
          message: '请将出货数量填写完整',
          type: 'warning'
        });
      }
      this.$refs['dataForm'].validate((valid) => {
        if (!valid) {
          return false
        }
        let message = '';
        if (this.dataForm.id) {
          message = '此操作将修改该数据'
        } else {
          message = '此操作将新增一条数据'
        }
        this.$confirm(message + ', 是否继续?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
          center: true
        }).then(() => {
          console.log("this.dataForm=="+JSON.stringify(this.dataForm));
          // this.dataForm.batchId=this.dataForm.shipmentList[0][2].batchId;
          // this.dataForm.partId=this.dataForm.shipmentList[0][2].partId;
          // this.dataForm.designation=this.dataForm.shipmentList[0][2].designation;
          this.dataForm.designation=this.tableData[0].designation;
          this.dataForm.number=this.dataForm.shipmentQuantity;
          // this.dataForm.subBatchId=this.dataForm.shipmentList[0][2].id;
          // this.dataForm.subBatchNumber=this.dataForm.shipmentList[0][2].batchNumber+"-"+this.dataForm.shipmentList[0][2].subBatchNumber;
          // this.dataForm.shipperId=this.dataForm.shipperId;
          // this.dataForm.shipper=this.dataForm.shipper;
          this.dataForm.deliveryDate=this.dataForm.deliveryDate+" 00:00:00";
          console.log("this.dataForm=="+JSON.stringify(this.dataForm));
          this.$http[!this.dataForm.id ? 'post' : 'put']('/fabricate/shipment/', this.dataForm).then(({data: res}) => {
            if (res.code !== 0) {
              return this.$message.error(res.msg)
            }
            this.$message({
              message: this.$t('prompt.success'),
              type: 'success',
              duration: 500,
              onClose: () => {
                // closeDialog()
                console.log('closeDialog')
                this.closeDialog();
                this.visible = false
                this.$emit('refreshDataList')
              }
            })
          }).catch(() => {
          })
        }).catch(() => {
          this.$message({
            type: 'info',
            message: '已取消'
          });
        });
      })
    }, 1000, {'leading': true, 'trailing': false})
  }
}
</script>

<style scoped>
.cascader_top ::v-deep .el-input__inner{
  width: 30vw;
}
.el-dialog {
  overflow: auto; /* 或者 visible */
}
</style>
