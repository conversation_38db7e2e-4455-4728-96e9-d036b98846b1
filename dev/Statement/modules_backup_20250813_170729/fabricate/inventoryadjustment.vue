<template>
  <el-card shadow="never" class="aui-card--fill">
    <div class="mod-fabricate__inventoryadjustment}">
      <el-form :inline="true" :model="dataForm" @keyup.enter.native="getDataList()">
        <el-form-item>
          <el-input v-model="dataForm.id" placeholder="id" clearable @clear="onClear">
            <el-button slot="append" icon="el-icon-search" @click="getDataList()"></el-button>
          </el-input>
        </el-form-item>
<!--        <el-form-item>
          <el-button @click="getDataList()">{{ $t('query') }}</el-button>
        </el-form-item>-->
        <el-form-item>
          <el-button type="info" @click="exportHandle()">{{ $t('export') }}</el-button>
        </el-form-item>
        <el-form-item>
        <el-button v-if="$hasPermission('fabricate:inventoryadjustment:export')" type="info" @click="exportTemplateHandle()">
          {{ $t('exportTemplate') }}
        </el-button>
        </el-form-item>
        <el-form-item>
          <el-tooltip content="下载模板填入信息后导入" placement="top">
            <el-upload
                v-if="$hasPermission('fabricate:inventoryadjustment:save')"
                class="upload-demo"
                :action="mixinViewModuleOptions.addBatchUrl"
                :headers="headers"
                :multiple="false"
                :show-file-list="false"
                :file-list="fileList"
                :before-upload="beforeUpload"
                :on-success="resultHandle"
                :on-change="handleChange"
                accept="'.xlsx','.xls'">
              <el-button type="success">{{ $t('addBatch') }}</el-button>
            </el-upload>
          </el-tooltip>
          <el-button type="text" v-show="unsuccessfulNameList" @click="dialogVisible = true">导入失败列表</el-button>
        </el-form-item>
        <el-form-item>
          <el-button v-if="$hasPermission('fabricate:inventoryadjustment:save')" type="primary" @click="addOrUpdateHandle()">{{ $t('add') }}</el-button>
        </el-form-item>
        <el-form-item>
          <el-button v-if="$hasPermission('fabricate:inventoryadjustment:delete')" type="danger" @click="deleteHandle()">{{ $t('deleteBatch') }}</el-button>
        </el-form-item>
      </el-form>
      <el-table v-loading="dataListLoading" :data="dataList" border @selection-change="dataListSelectionChangeHandle" style="width: 100%;">
        <el-table-column type="selection" header-align="center" align="center" width="50"></el-table-column>
        <el-table-column prop="designation" label="部品番号" header-align="center" align="center"></el-table-column>
        <el-table-column prop="uncheckedNum" label="成形数" header-align="center" align="center"></el-table-column>
        <el-table-column prop="qualityRepairNum" label="品修数" header-align="center" align="center"></el-table-column>
        <el-table-column prop="qualityInspectionNum" label="品检数" header-align="center" align="center"></el-table-column>
        <el-table-column prop="toBePacked" label="待包装数" header-align="center" align="center"></el-table-column>
        <el-table-column prop="toBeShipped" label="待出货数" header-align="center" align="center"></el-table-column>
        <el-table-column prop="transferredNum" label="转入数" header-align="center" align="center"></el-table-column>
        <el-table-column prop="isolationNumber" label="隔离数" header-align="center" align="center"></el-table-column>
        <el-table-column prop="numberOfReserves" label="无订单数" header-align="center" align="center"></el-table-column>
        <el-table-column prop="reasonForAdjustment" label="调节原因" header-align="center" align="center"></el-table-column>
        <el-table-column prop="moderator" label="调节人" header-align="center" align="center">
          <template slot-scope="scope">
            <div>
              {{$getEmployeesList(scope.row.moderator)}}
            </div>
          </template>
        </el-table-column>
<!--        <el-table-column prop="reviewer" label="审核人" header-align="center" align="center">
          <template slot-scope="scope">
            <div>
              {{$getEmployeesList(scope.row.reviewer)}}
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="approved" label="核准" header-align="center" align="center"></el-table-column>-->
        <el-table-column prop="remark" label="备注" header-align="center" align="center"></el-table-column>
        <el-table-column :label="$t('handle')" fixed="right" header-align="center" align="center" width="150">
          <template slot-scope="scope">
            <el-dropdown>
              <el-button type="primary">
                功能<i class="el-icon-arrow-down el-icon--right"></i>
              </el-button>
              <el-dropdown-menu slot="dropdown">
                <el-dropdown-item>
                  <el-button v-if="$hasPermission('fabricate:inventoryadjustment:update')" type="text" size="small" @click="addOrUpdateHandle(scope.row.id)">{{ $t('update') }}</el-button>
                </el-dropdown-item>
                <el-dropdown-item>
                  <el-button v-if="$hasPermission('fabricate:inventoryadjustment:delete')" type="text" size="small" @click="deleteHandle(scope.row.id)">{{ $t('delete') }}</el-button>
                </el-dropdown-item>
              </el-dropdown-menu>
            </el-dropdown>

          </template>
        </el-table-column>
      </el-table>
      <el-pagination
        :current-page="page"
        :page-sizes="[10, 20, 50, 100]"
        :page-size="limit"
        :total="total"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="pageSizeChangeHandle"
        @current-change="pageCurrentChangeHandle">
      </el-pagination>
      <!-- 弹窗, 新增 / 修改 -->
      <add-or-update v-if="addOrUpdateVisible" ref="addOrUpdate" @refreshDataList="getDataList"></add-or-update>
    </div>
  </el-card>
</template>

<script>
import mixinViewModule from '@/mixins/view-module'
import AddOrUpdate from './inventoryadjustment-add-or-update'
import Cookies from "js-cookie";
export default {
  mixins: [mixinViewModule],
  data () {
    return {
      headers: {'token': Cookies.get('token')},
      fileList: [{name: '', url: ''}],
      mixinViewModuleOptions: {
        getDataListURL: '/fabricate/inventoryadjustment/page',
        getDataListIsPage: true,
        exportURL: '/fabricate/inventoryadjustment/export',
        deleteURL: '/fabricate/inventoryadjustment',
        deleteIsBatch: true,
        exportTemplateURL: '/fabricate/inventoryadjustment/template',
        addBatchUrl: window.SITE_CONFIG['apiURL'] + '/fabricate/inventoryadjustment/batchSave',
      },
      dataForm: {
        id: ''
      }
    }
  },
  components: {
    AddOrUpdate
  },
  methods:{
    //清空后重新查询
    onClear(){
      this.query()
    },
    beforeUpload(file) {
      this.message = this.$message({
        message: '上传中，请勿操作',
        center: true,
        duration: 0,
      });
    },
    handleChange(file, fileList) {
      this.fileList = fileList.slice(-3);

    },
    resultHandle(res) {
      if (res.code !== 0) {
        return this.$message.error(res.msg)
      }
      this.$message({
        message: this.$t('prompt.success'),
        type: 'success',
        duration: 500,
        onClose: () => {
          if (res.data) {
            this.unsuccessfulNameList = res.data
          }
          this.visible = false
          this.query()
        }
      })
    },
  }
}
</script>
