<template>
  <el-card shadow="never" class="aui-card--fill">
    <div class="mod-fabricate__manufacturingOrder}">
      <el-form :inline="true" :model="dataForm" @keyup.enter.native="getDataList()">
        <el-tooltip content="请输入制造指令或者部品番号" placement="top">
          <el-form-item>
            <el-input v-model="dataForm.paramStr" placeholder="关键字查询" clearable @clear="onClear">
              <el-button slot="append" icon="el-icon-search" @click="getDataList()"></el-button>
            </el-input>
          </el-form-item>
        </el-tooltip>
<!--        <el-form-item>
          <el-button @click="getDataList()">{{ $t('query') }}</el-button>
        </el-form-item>-->
        <el-form-item>
          <el-button type="info" @click="exportHandle()">{{ $t('export') }}</el-button>
        </el-form-item>
        <el-form-item>
          <el-button v-if="$hasPermission('fabricate:manufacturingOrder:save')" type="primary"
                     @click="addOrUpdateHandle()">{{ $t('add') }}
          </el-button>
        </el-form-item>
        <el-form-item>
          <el-button v-if="$hasPermission('fabricate:manufacturingOrder:delete')" type="danger" @click="deleteHandle()">
            {{ $t('deleteBatch') }}
          </el-button>
        </el-form-item>
      </el-form>
      <el-table v-loading="dataListLoading" :data="dataList" height="600px" border @selection-change="dataListSelectionChangeHandle"
                style="width: 100%;">
        <el-table-column type="selection" header-align="center" align="center" width="50"></el-table-column>
        <el-table-column prop="orderNumber" label="订单号" header-align="center"
                         align="center"></el-table-column>
        <el-table-column prop="manufacturingInstructions" label="制造指令" header-align="center"
                         align="center"></el-table-column>
        <el-table-column prop="partDesignation" label="部品番号" header-align="center" align="center"></el-table-column>
        <el-table-column prop="remark" label="备注" header-align="center" align="center"></el-table-column>
        <el-table-column :label="$t('handle')" fixed="right" header-align="center" align="center" width="150">
          <template slot-scope="scope">
            <el-dropdown>
              <el-button type="primary">
                功能<i class="el-icon-arrow-down el-icon--right"></i>
              </el-button>
              <el-dropdown-menu slot="dropdown">
                <el-dropdown-item>
                  <el-button v-if="$hasPermission('fabricate:manufacturingOrder:update')" type="text" size="small"
                             @click="addOrUpdateHandle(scope.row.id)">{{ $t('update') }}
                  </el-button>
                </el-dropdown-item>
                <el-dropdown-item>
                  <el-button v-if="$hasPermission('fabricate:manufacturingOrder:delete')" type="text" size="small"
                             @click="deleteHandle(scope.row.id)">{{ $t('delete') }}
                  </el-button>
                </el-dropdown-item>
              </el-dropdown-menu>
            </el-dropdown>


          </template>
        </el-table-column>
      </el-table>
      <el-pagination
          :current-page="page"
          :page-sizes="[10, 20, 50, 100]"
          :page-size="limit"
          :total="total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="pageSizeChangeHandle"
          @current-change="pageCurrentChangeHandle">
      </el-pagination>
      <!-- 弹窗, 新增 / 修改 -->
      <add-or-update v-if="addOrUpdateVisible" ref="addOrUpdate" @refreshDataList="getDataList"></add-or-update>
    </div>
  </el-card>
</template>

<script>
import mixinViewModule from '@/mixins/view-module'
import AddOrUpdate from './manufacturing-order-add-or-update'

export default {
  mixins: [mixinViewModule],
  data() {
    return {
      mixinViewModuleOptions: {
        getDataListURL: '/fabricate/manufacturingOrder/page',
        getDataListIsPage: true,
        exportURL: '/fabricate/manufacturingOrder/export',
        deleteURL: '/fabricate/manufacturingOrder',
        deleteIsBatch: true
      },
      dataForm: {
        manufacturingInstructions: '',
        partDesignation: '',
        customerCode: '',
        paramStr: '',
      }
    }
  },
  components: {
    AddOrUpdate
  },
  methods:{
    //清空后重新查询
    onClear(){
      this.query()
    },
  }
}
</script>
