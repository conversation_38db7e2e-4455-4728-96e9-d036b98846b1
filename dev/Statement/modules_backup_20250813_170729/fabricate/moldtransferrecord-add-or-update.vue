<template>
  <el-dialog :visible.sync="visible" :title="!dataForm.id ? '新增模具转移记录' : '修改模具转移记录'" :close-on-click-modal="false" :close-on-press-escape="false" append-to-body>
    <el-form :model="dataForm" :rules="dataRule" ref="dataForm" @keyup.enter.native="dataFormSubmitHandle()" :label-width="$i18n.locale === 'en-US' ? '120px' : '80px'">
      <div class="container">
        <el-form-item label="模具" prop="moldId">
          <machine-component v-model="dataForm.moldId" placeholder="模具"></machine-component>
        </el-form-item>
        <el-form-item label="客户" prop="customerId">
          <customer-component v-model="dataForm.customerId" placeholder="客户"></customer-component>
        </el-form-item>
        <el-form-item label="转移日期" prop="transferDate">
          <el-date-picker
              class="datePicker"
              v-model="dataForm.transferDate"
              type="date"
              value-format="yyyy-MM-dd"
              placeholder="转移日期">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="保管地点" prop="storageLocation">
          <el-input v-model="dataForm.storageLocation" placeholder="保管地点"></el-input>
        </el-form-item>
        <el-form-item label="确认人" prop="confirmPerson">
          <employee-component v-model="dataForm.confirmPerson" placeholder="确认人"></employee-component>
        </el-form-item>
      </div>
      <el-form-item label="备注" prop="remark">
        <el-input v-model="dataForm.remark" placeholder="备注"></el-input>
      </el-form-item>
    </el-form>
    <template slot="footer">
      <el-button @click="visible = false">{{ $t('cancel') }}</el-button>
      <el-button type="primary" @click="dataFormSubmitHandle()">{{ $t('confirm') }}</el-button>
    </template>
  </el-dialog>
</template>

<script>
import debounce from 'lodash/debounce'
import mixinViewModule from '@/mixins/view-module'
export default {
 data () {
    return {
      visible: false,
      //创建该页面时，不调用数据查询接口
      mixinViewModuleOptions: {
        createdIsNeed: false,
      },
      dataForm: {
        id: '',
        moldId: '',
        customerId: '',
        transferDate: '',
        storageLocation: '',
        confirmPerson: '',
        remark: '',
        disabled: '',
        creator: '',
        createDate: '',
        updater: '',
        updateDate: ''
      }
    }
  },
  computed: {
    dataRule () {
      return {
          id: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          moldId: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          customerId: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          transferDate: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          storageLocation: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          confirmPerson: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          remark: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
      }
    }
  },
  methods: {
    init () {
      this.visible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].resetFields()
        if (this.dataForm.id) {
          this.getInfo()
        }
      })
    },
    // 获取信息
    getInfo () {
      this.$http.get(`/fabricate/moldtransferrecord/${this.dataForm.id}`).then(({ data: res }) => {
        if (res.code !== 0) {
          return this.$message.error(res.msg)
        }
        this.dataForm = {
          ...this.dataForm,
          ...res.data
        }
      }).catch(() => {})
    },
    // 表单提交
    dataFormSubmitHandle: debounce(function () {
      this.$refs['dataForm'].validate((valid) => {
        if (!valid) {
          return false
        }
        this.$http[!this.dataForm.id ? 'post' : 'put']('/fabricate/moldtransferrecord/', this.dataForm).then(({ data: res }) => {
          if (res.code !== 0) {
            return this.$message.error(res.msg)
          }
          this.$message({
            message: this.$t('prompt.success'),
            type: 'success',
            duration: 500,
            onClose: () => {
              this.visible = false
              this.$emit('refreshDataList')
            }
          })
        }).catch(() => {})
      })
    }, 1000, { 'leading': true, 'trailing': false })
  }
}
</script>
