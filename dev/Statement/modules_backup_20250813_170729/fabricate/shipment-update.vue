<template>
  <el-dialog :visible.sync="visible" :title="!dataForm.id ? '新增出货记录' : '修改出货记录'" :close-on-click-modal="false"
             :close-on-press-escape="false" append-to-body @close="closeDialog">
    <el-form :model="dataForm" :rules="dataRule" ref="dataForm"
             :label-width="$i18n.locale === 'en-US' ? '120px' : '80px'">
      <div class="container">
<!--        <el-form-item label="次批号" prop="subBatchNumber">-->
<!--          <el-input v-model="dataForm.subBatchNumber" style="width: 13vw" disabled></el-input>-->
<!--        </el-form-item>-->
        <el-form-item label="出货数量" prop="number">
          <el-input type="number" v-model="dataForm.number" placeholder="出货数量"></el-input>
        </el-form-item>
      </div>
      <div class="container">
        <el-form-item label="出货时间" prop="shipmentTime">
          <el-date-picker
              style="width: 13vw"
              v-model="dataForm.shipmentTime"
              type="datetime"
              value-format="yyyy-MM-dd HH:mm:ss"
              placeholder="选择日期时间"
              default-time="18:00:00">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="出货人" prop="shipper">
          <el-input v-model="dataForm.shipper" disabled></el-input>
        </el-form-item>
      </div>
    </el-form>
    <template slot="footer">
      <el-button @click="visible = false">{{ $t('cancel') }}</el-button>
      <el-button type="primary" @click="dataFormSubmitHandle()">{{ $t('confirm') }}</el-button>
    </template>
  </el-dialog>
</template>

<script>
import debounce from 'lodash/debounce'
import mixinViewModule from '@/mixins/view-module'

export default {
  mixins: [mixinViewModule],
  data() {
    return {
      visible: false,
      //创建该页面时，不调用数据查询接口
      mixinViewModuleOptions: {
        createdIsNeed: false,
      },
      dataForm: {
        id: '',
        partId: '',
        designation: '',
        subBatchId: '',
        subBatchNumber: '',
        number: '',
        shipmentTime: '',
        shipperId: '',
        shipper: '',
      },
      subBatchOption: [],
      employeesOption: [
        {
          label: 'TQ00121(贾林)',
          value: '1641729347844198402'
        }
      ]
    }
  },
  computed: {
    dataRule() {
      return {
        id: [
          {required: true, message: this.$t('validate.required'), trigger: 'blur'}
        ],
        partId: [
          {required: true, message: this.$t('validate.required'), trigger: 'blur'}
        ],
        designation: [
          {required: true, message: this.$t('validate.required'), trigger: 'blur'}
        ],
        subBatchId: [
          {required: true, message: this.$t('validate.required'), trigger: 'blur'}
        ],
        subBatchNumber: [
          {required: true, message: this.$t('validate.required'), trigger: 'blur'}
        ],
        number: [
          {required: true, message: this.$t('validate.required'), trigger: 'blur'}
        ],
        shipmentTime: [
          {required: true, message: this.$t('validate.required'), trigger: 'blur'}
        ],
        shipperId: [
          {required: true, message: this.$t('validate.required'), trigger: 'blur'}
        ],
        shipper: [
          {required: true, message: this.$t('validate.required'), trigger: 'blur'}
        ],
      }
    }
  },
  methods: {
    //关闭对话框的回调
    closeDialog() {
      this.dataForm = {
        id: '',
        partId: '',
        designation: '',
        subBatchId: '',
        subBatchNumber: '',
        number: '',
        shipmentTime: '',
        shipperId: '',
        shipper: '',
      }
    },
    //获取可出货次批列表
    getShipmentSubBatchList() {
      this.$http.get('batch/subbatch/getShipmentSubBatchList').then(({data: res}) => {
        if (res.code !== 0) {
          return this.$message.error(res.msg)
        }
        this.subBatchOption = res.data.map((obj) => {
          return {
            label: obj.subBatchNumber,
            value: obj.id,
            data: obj
          }
        })
      })
    },
    init() {
      this.visible = true
      this.getShipmentSubBatchList()
      this.$nextTick(() => {
        this.$refs['dataForm'].resetFields()
        if (this.dataForm.id) {
          this.getInfo()
        }
      })
    },
    // 获取信息
    getInfo() {
      this.$http.get(`/fabricate/shipment/${this.dataForm.id}`).then(({data: res}) => {
        if (res.code !== 0) {
          return this.$message.error(res.msg)
        }
        this.dataForm = {
          ...this.dataForm,
          ...res.data
        }
      }).catch(() => {
      })
    },
    // 表单提交
    dataFormSubmitHandle: debounce(function () {
      this.$refs['dataForm'].validate((valid) => {
        if (!valid) {
          return false
        }
        let message = '';
        if (this.dataForm.id) {
          message = '此操作将修改该数据'
        } else {
          message = '此操作将新增一条数据'
        }
        this.$confirm(message + ', 是否继续?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
          center: true
        }).then(() => {
          this.$http[!this.dataForm.id ? 'post' : 'put']('/fabricate/shipment/', this.dataForm).then(({data: res}) => {
            if (res.code !== 0) {
              return this.$message.error(res.msg)
            }
            this.$message({
              message: this.$t('prompt.success'),
              type: 'success',
              duration: 500,
              onClose: () => {
                this.visible = false
                this.$emit('refreshDataList')
              }
            })
          }).catch(() => {
          })
        }).catch(() => {
          this.$message({
            type: 'info',
            message: '已取消'
          });
        });
      })
    }, 1000, {'leading': true, 'trailing': false})
  }
}
</script>
