<template>
  <el-card shadow="never" class="aui-card--fill">
    <div class="mod-fabricate__mold}">
      <el-form :inline="true" :model="dataForm" @keyup.enter.native="getDataList()">
        <el-form-item>
          <el-tooltip content="请输入查询关键字" placement="top">
            <el-input v-model="dataForm.paramStr" placeholder="查询关键字" @input="throttleFunction" clearable>
              <el-button slot="append" icon="el-icon-search" @click="getDataList()"></el-button>
            </el-input>
          </el-tooltip>
        </el-form-item>
        <el-form-item>
          <el-button v-if="$hasPermission('fabricate:mold:save')" type="primary" @click="addOrUpdateHandle()">{{ $t('add') }}</el-button>
        </el-form-item>
        <el-dropdown  style="float:right;">
          <el-button type="primary">
            更多功能<i class="el-icon-arrow-down el-icon--right"></i>
          </el-button>
          <el-dropdown-menu slot="dropdown">
            <el-dropdown-item>
              <el-form-item>
                <el-button type="info" @click="exportHandle()">{{ $t('export') }}</el-button>
              </el-form-item>
            </el-dropdown-item>
            <el-dropdown-item>
              <el-form-item>
                <el-button v-if="$hasPermission('fabricate:mold:export')" type="info" @click="exportTemplateHandle()">
                  {{ $t('exportTemplate') }}
                </el-button>
              </el-form-item>
            </el-dropdown-item>
            <el-dropdown-item>
              <el-form-item>
                <el-tooltip content="下载模板填入信息后导入" placement="top">
                  <el-upload
                          v-if="$hasPermission('fabricate:mold:save')"
                          class="upload-demo"
                          :action="mixinViewModuleOptions.addBatchUrl"
                          :headers="headers"
                          :multiple="false"
                          :show-file-list="false"
                          :file-list="fileList"
                          :before-upload="beforeUpload"
                          :on-success="resultHandle"
                          :on-change="handleChange"
                          accept="'.xlsx','.xls'">
                    <el-button type="success">{{ $t('addBatch') }}</el-button>
                  </el-upload>
                </el-tooltip>
                <el-button type="text" v-show="unsuccessfulNameList" @click="dialogVisible = true">导入失败列表</el-button>
              </el-form-item>
            </el-dropdown-item>
            <el-dropdown-item>
              <el-form-item>
                <el-button v-if="$hasPermission('fabricate:mold:delete')" type="danger" @click="deleteHandle()">{{ $t('deleteBatch') }}</el-button>
              </el-form-item>
            </el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown>
      </el-form>
      <el-table v-loading="dataListLoading" height="600px" :data="dataList" border @selection-change="dataListSelectionChangeHandle" style="width: 100%;">
        <el-table-column type="selection" header-align="center" align="center" width="50"></el-table-column>
        <el-table-column prop="designation" label="品号" header-align="center" align="center" width="130"></el-table-column>
        <el-table-column prop="moldGroup" label="组别" header-align="center" align="center" width="50"></el-table-column>
<!--        <el-table-column prop="formulationCode" label="配方代码" header-align="center" align="center"></el-table-column>-->
        <el-table-column prop="moldCode" label="模具代码" header-align="center" align="center"></el-table-column>
        <el-table-column prop="numberOfHoles" label="模具孔数" header-align="center" align="center"></el-table-column>
<!--        <el-table-column prop="moldSize" label="模具尺寸" header-align="center" align="center"></el-table-column>-->
<!--        <el-table-column prop="documentNumber" label="文件编号" header-align="center" align="center"></el-table-column>-->
<!--        <el-table-column prop="defectRateTarget" label="不良率目标" header-align="center" align="center"></el-table-column>-->
        <el-table-column prop="formingTime" label="成形时间" header-align="center" align="center"></el-table-column>
        <el-table-column prop="rubberWeightFloot" label="胶料重量下限" header-align="center" align="center"></el-table-column>
        <el-table-column prop="rubberWeightLimit" label="胶料重量上限" header-align="center" align="center"></el-table-column>
        <el-table-column prop="formedUnitWeight" label="成形单重" header-align="center" align="center"></el-table-column>
        <el-table-column prop="productivityTargets" label="产效目标" header-align="center" align="center"></el-table-column>
        <el-table-column prop="qualityRepairTimeliness" label="品修时效" header-align="center" align="center"></el-table-column>
<!--        <el-table-column prop="qualityInspectionTimeliness" label="品检时效" header-align="center" align="center"></el-table-column>-->
<!--        <el-table-column prop="packingQuantity" label="半成品装箱数量" header-align="center" align="center"></el-table-column>-->
<!--        <el-table-column prop="moldMaking" label="模具制作" header-align="center" align="center"></el-table-column>-->
<!--        <el-table-column prop="issueDate" label="发行日期" header-align="center" align="center"></el-table-column>-->
<!--        <el-table-column prop="moldConstruction" label="模具构造" header-align="center" align="center">-->
<!--          <template slot-scope="scope">-->
<!--            {{$getDictLabel("mold_construction",scope.row.moldConstruction)}}-->
<!--          </template>-->
<!--        </el-table-column>-->
<!--        <el-table-column prop="electroplating" label="电镀" header-align="center" align="center"></el-table-column>-->
<!--        <el-table-column prop="moldCleaningFrequency" label="模具清洗频率" header-align="center" align="center"></el-table-column>-->
<!--        <el-table-column prop="storageLocation" label="目前位置" header-align="center" align="center"></el-table-column>-->
        <el-table-column prop="remark" label="备注" header-align="center" align="center"></el-table-column>
        <el-table-column :label="$t('handle')" fixed="right" header-align="center" align="center" width="150">
          <template slot-scope="scope">
            <el-dropdown>
              <el-button type="primary">
                功能<i class="el-icon-arrow-down el-icon--right"></i>
              </el-button>
              <el-dropdown-menu slot="dropdown">
                <el-dropdown-item>
                  <el-button v-if="$hasPermission('fabricate:mold:update')" type="text" size="small" @click="addOrUpdateHandle(scope.row.id)">{{ $t('update') }}</el-button>
                </el-dropdown-item>
                <el-dropdown-item>
                  <el-button v-if="$hasPermission('fabricate:mold:delete')" type="text" size="small" @click="deleteHandle(scope.row.id)">{{ $t('delete') }}</el-button>
                </el-dropdown-item>
              </el-dropdown-menu>
            </el-dropdown>
          </template>
        </el-table-column>
      </el-table>
      <el-pagination
        :current-page="page"
        :page-sizes="[10, 20, 50, 100]"
        :page-size="limit"
        :total="total"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="pageSizeChangeHandle"
        @current-change="pageCurrentChangeHandle">
      </el-pagination>
      <!-- 弹窗, 新增 / 修改 -->
      <add-or-update v-if="addOrUpdateVisible" ref="addOrUpdate" @refreshDataList="getDataList"></add-or-update>
    </div>
  </el-card>
</template>

<script>
import mixinViewModule from '@/mixins/view-module'
import AddOrUpdate from './mold-add-or-update'
import Cookies from "js-cookie";
export default {
  mixins: [mixinViewModule],
  data () {
    return {
      message:'',
      headers: {'token': Cookies.get('token')},
      fileList: [{name: '', url: ''}],
      unsuccessfulNameList: '',//  导入失败名称列表
      mixinViewModuleOptions: {
        getDataListURL: '/fabricate/mold/page',
        getDataListIsPage: true,
        exportURL: '/fabricate/mold/export',
        deleteURL: '/fabricate/mold',
        deleteIsBatch: true,
        exportTemplateURL: '/fabricate/mold/export/template',
        addBatchUrl: window.SITE_CONFIG['apiURL'] + '/fabricate/mold/batchSave',
      },
      dataForm: {
        id: '',
        paramStr: '',
      },
      // 判断是否还在继续输入
      timer: null,
    }
  },
  components: {
    AddOrUpdate
  },
  methods:{
    //节流函数
    throttleFunction() {
      // 如果timer不为空就表明在持续输入,每次改变都清除定时，这样就可以实现只输入最后一次结果(注意每次间隔如果超过500毫秒就会输出)
      if (this.timer !== null) clearTimeout(this.timer)
      this.timer = setTimeout(() => {
        this.getDataList()
      }, 500)
    },
    beforeUpload(file) {
      this.message = this.$message({
        message: '上传中，请勿操作',
        center: true,
        duration: 0,
      });
    },
    handleChange(file, fileList) {
      this.fileList = fileList.slice(-3);

    },
    resultHandle(res) {
      if (res.code !== 0) {
        this.message.close()
      return this.$message.error(res.msg)
      }
      this.message.close()
      this.$message({
        message: this.$t('prompt.success'),
        type: 'success',
        duration: 500,
        onClose: () => {
          if (res.data) {
            this.unsuccessfulNameList = res.data
          }
          this.visible = false
          this.query()
        }
      })
    },
  }
}
</script>
