<template>
  <el-dialog :visible.sync="visible" :title="!dataForm.id ? '新增制造指令' : '修改制造指令信息'" :close-on-click-modal="false"
             :close-on-press-escape="false" append-to-body>
    <el-form :model="dataForm" :rules="dataRule" ref="dataForm" @keyup.enter.native="dataFormSubmitHandle()"
             :label-width="$i18n.locale === 'en-US' ? '120px' : '80px'">
      <el-form-item label="订单号" prop="orderNumber">
        <el-input v-model="dataForm.orderNumber" placeholder="订单号"></el-input>
      </el-form-item>
      <div class="container">
        <div class="item">
          <el-form-item label="指令编码" prop="instructionNumber">
            <ren-select v-model="dataForm.instructionNumber" dict-type="instruction_number"></ren-select>
          </el-form-item>
        </div>
      </div>
      <el-form-item label="部品id" prop="partId">
        <el-input v-model="dataForm.partId" placeholder="部品id" disabled></el-input>
      </el-form-item>
      <el-form-item label="部品番号" prop="partDesignation">
        <el-autocomplete
            class="inline-input"
            v-model="dataForm.partDesignation"
            :fetch-suggestions="getPartList"
            placement="bottom"
            placeholder="请输入内容"
            :trigger-on-focus="false"
            @select="handleSelect"
        ></el-autocomplete>
      </el-form-item>
      <el-form-item label="炼胶日期" prop="refiningTime">
        <el-date-picker
            class="datePicker"
            v-model="dataForm.refiningTime"
            type="date"
            value-format="yyyy-MM-dd"
            placeholder="选择日期">
        </el-date-picker>
      </el-form-item>
      <el-form-item label="交货日期" prop="deliveryTime">
        <el-date-picker
            class="datePicker"
            v-model="dataForm.deliveryTime"
            type="date"
            value-format="yyyy-MM-dd"
            placeholder="选择日期">
        </el-date-picker>
      </el-form-item>
      <el-form-item label="备注" prop="remark">
        <el-input v-model="dataForm.remark" placeholder="备注"></el-input>
      </el-form-item>
    </el-form>
    <template slot="footer">
      <el-button @click="visible = false">{{ $t('cancel') }}</el-button>
      <el-button type="primary" @click="dataFormSubmitHandle()">{{ $t('confirm') }}</el-button>
    </template>
  </el-dialog>
</template>

<script>
import debounce from 'lodash/debounce'
import mixinViewModule from '@/mixins/view-module'
import {getDictValue} from "@/utils/index"

export default {
  mixins:[mixinViewModule],
  data() {
    return {
      visible: false,
      dataForm: {
        id: '',
        paramStr: '',
        manufacturingInstructions: '',
        instructionNumber: 0,
        orderNumber: '',
        partDesignation: '',
        refiningTime: this.formatDates(new Date()),
        deliveryTime: this.formatDates(new Date()),
        customerCode: '',
        partId: '',
        remark: ''
      },
      restaurants: []
    }
  },
  computed: {
    dataRule() {
      return {
        orderNumber: [
          {required: true, message: this.$t('validate.required'), trigger: 'blur'}
        ],
        partId: [
          {required: false, message: this.$t('validate.required'), trigger: 'blur'}
        ],
      }
    }
  },
  methods: {
    //  获取量产类型部品列表
    getPartList(queryString, cb) {
      this.$http.get(`/fabricate/part/getMPAll/` + queryString).then(({data: res}) => {
        if (res.code !== 0) {
          return this.$message.error(res.msg)
        }
        const list = res.data.map((obj) => {
          return {
            value: obj.designation,
            partId: obj.id,
            partDesignation: obj.designation
          }
        })
        this.timeout = setTimeout(() => {
          cb(list);
        }, 300 * Math.random());
      }).catch(() => {
      })
    },
    //  输出选择日志
    handleSelect(item) {
      this.dataForm.partDesignation = item.partDesignation
      this.dataForm.partId = item.partId
    },
    init() {
      this.visible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].resetFields()
        if (this.dataForm.id) {
          this.getInfo()
        }
      })
    },
    // 获取信息
    getInfo() {
      this.$http.get(`/fabricate/manufacturingOrder/${this.dataForm.id}`).then(({data: res}) => {
        if (res.code !== 0) {
          return this.$message.error(res.msg)
        }
        this.dataForm = {
          ...this.dataForm,
          ...res.data
        }
        console.log('导致鳄梨')
        console.log(this.dataForm)
        this.dataForm.instructionNumber = getDictValue("instruction_number",res.data.manufacturingInstructions.slice(0,2));
        this.dataForm.manufacturingInstructions = res.data.manufacturingInstructions.slice(2,10)
        console.log('到')
      }).catch(() => {
      })
    },
    // 表单提交
    dataFormSubmitHandle: debounce(function () {
      this.$refs['dataForm'].validate((valid) => {
        if (!valid) {
          return false
        }
        this.$http[!this.dataForm.id ? 'post' : 'put']('/fabricate/manufacturingOrder/', this.dataForm).then(({data: res}) => {
          if (res.code !== 0) {
            return this.$message.error(res.msg)
          }
          this.$message({
            message: this.$t('prompt.success'),
            type: 'success',
            duration: 500,
            onClose: () => {
              this.visible = false
              this.$emit('refreshDataList')
            }
          })
        }).catch(() => {
        })
      })
    }, 1000, {'leading': true, 'trailing': false})
  }
}
</script>
