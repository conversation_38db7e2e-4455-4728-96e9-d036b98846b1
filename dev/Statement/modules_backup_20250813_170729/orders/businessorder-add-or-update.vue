<template>
  <el-dialog :visible.sync="visible" :title="!dataForm.id ? $t('add') : $t('update')" :close-on-click-modal="false" :close-on-press-escape="false" append-to-body>
    <el-form :model="dataForm" :rules="dataRule" ref="dataForm" @keyup.enter.native="dataFormSubmitHandle()" :label-width="$i18n.locale === 'en-US' ? '120px' : '80px'">
      <el-divider content-position="center">
        <span style="font-size: 20px; font-weight: bold;">建立新订单</span>
      </el-divider>
      <div class="container">
        <el-form-item label="客户代码" prop="customerId">
          <customer-component v-model="dataForm.customerId" :query-name="true" @customerData="customerData" placeholder="客户代码"></customer-component>
        </el-form-item>
        <el-form-item label="客户名称" prop="customerName">
          <span class="font_size">{{dataForm.customerName}}</span>
        </el-form-item>
        <el-form-item label="客户订单" prop="orderNumber">
          <el-input v-model="dataForm.orderNumber" placeholder="订单/预示号"></el-input>
        </el-form-item>
        <el-form-item label="下单日期" prop="orderDate">
          <el-date-picker
              v-model="dataForm.orderDate"
              type="date"
              format="yyyy 年 MM 月 dd 日"
              value-format="yyyy-MM-dd"
              placeholder="订单/预示日期">
          </el-date-picker>
        </el-form-item>
<!--        <el-form-item label="上传订单" prop="orderDate">
          <el-input v-model="dataForm.orderDate" placeholder="订单/预示日期"></el-input>
        </el-form-item>-->
<!--        <el-form-item label="交货地点" prop="deliveryLocationCode">
          <el-input v-model="dataForm.deliveryLocationCode" placeholder="交货地代码"></el-input>
        </el-form-item>-->
        <el-form-item label="交货地址" prop="tra">
          <el-input v-model="dataForm.tradingLocations" placeholder="交货地址"></el-input>
        </el-form-item>
        <el-form-item label="联络人" prop="contactPerson">
          <el-input v-model="dataForm.contactPerson" placeholder="联络人"></el-input>
        </el-form-item>
        <el-form-item label="联络电话" prop="contactPhone">
          <el-input v-model="dataForm.contactPhone" placeholder="联络人电话"></el-input>
        </el-form-item>
        <el-form-item label="联络邮箱" prop="contactEmail">
          <el-input v-model="dataForm.contactEmail" placeholder="联络人邮箱"></el-input>
        </el-form-item>
        <el-form-item label="做成" prop="make">
          <employee-component v-model="dataForm.make" placeholder="做成"></employee-component>
        </el-form-item>
      </div>
      <div v-show="showHoldOrder" class="rounded-div" :style="{backgroundColor:backgroundColor}">
        <br/>
        <div class="container" v-if="checkOrder">
          <div v-if="showOrders === 0" @click="pushOrder">
            <el-button type="success" icon="el-icon-plus" circle></el-button>
            <span class="font_size">新增新订单</span>
          </div>
          <div class="margins" @click="viewOrders(0)">
            <el-button type="primary">查看订单</el-button>
          </div>
        </div>
        <br/>
        <div class="container">
            <el-form-item label="订单类型" prop="orderType">
              <ren-select v-model="dataForm.orderType" placeholder="订单类型" dict-type="business_order_type"></ren-select>
            </el-form-item>
            <el-form-item label="部品id" prop="partId">
              <part-number-component v-model="dataForm.partId" :customer-id="dataForm.customerId" placeholder="部品id"></part-number-component>
            </el-form-item>
            <el-form-item label="品名代码" prop="partNameCode">
              <el-input v-model="dataForm.partNameCode" placeholder="品名代码"></el-input>
            </el-form-item>
            <el-form-item label="材质" prop="materialQuality">
              <el-input v-model="dataForm.materialQuality" placeholder="材质"></el-input>
            </el-form-item>
            <el-form-item label="交制数量" prop="quantity">
              <el-input v-model="dataForm.quantity" placeholder="交制数量"></el-input>
            </el-form-item>
            <el-form-item label="客户交期" prop="deliveryDate">
              <el-date-picker
                  v-model="dataForm.deliveryDate"
                  type="date"
                  format="yyyy 年 MM 月 dd 日"
                  value-format="yyyy-MM-dd"
                  placeholder="客户交期">
              </el-date-picker>
            </el-form-item>
            <el-form-item label="币别" prop="currency">
              <ren-select v-model="dataForm.currency" placeholder="币别" dict-type="currency"></ren-select>
            </el-form-item>
            <el-form-item label="单价" prop="unitPrice">
              <el-input v-model="dataForm.unitPrice" placeholder="单价"></el-input>
            </el-form-item>
            <el-form-item label="汇率" prop="exchangeRate">
              <el-input v-model="dataForm.exchangeRate" placeholder="汇率"></el-input>
            </el-form-item>
            <el-form-item label="税率" prop="taxRate">
              <el-input v-model="dataForm.taxRate" placeholder="税率"></el-input>
            </el-form-item>
            <el-form-item label="交易金额" prop="transactionAmount">
              <el-input v-model="dataForm.transactionAmount" placeholder="交易金额"></el-input>
            </el-form-item>
            <el-form-item label="摘要说明" prop="summaryDescription">
              <el-input v-model="dataForm.summaryDescription" placeholder="摘要说明"></el-input>
            </el-form-item>
            <el-form-item label="交货地点" prop="tradingLocations">
              <el-input v-model="dataForm.tradingLocations" placeholder="交货地点"></el-input>
            </el-form-item>
          <el-form-item label="报价单号" prop="quotationNumber">
              <el-input v-model="dataForm.quotationNumber" placeholder="报价单号"></el-input>
            </el-form-item>
          </div>
      </div>
      <div v-show="!showHoldOrder">
        <el-button type="primary" @click="viewOrders">添加订单</el-button>
        <el-table height="600px" :data="dataFromList" border style="width: 100%;">
          <el-table-column prop="orderType" label="订单类型" header-align="center" align="center">
            <template slot-scope="scope">
              {{ $getDictLabel("business_order_type", scope.row.orderType) }}
            </template>
          </el-table-column>
          <el-table-column prop="partId" label="部品番号" fixed header-align="center" align="center"></el-table-column>
          <el-table-column prop="materialQuality" label="材质" header-align="center" align="center"></el-table-column>
          <el-table-column prop="quantity" label="数量" header-align="center" align="center"></el-table-column>
          <el-table-column :label="$t('handle')" fixed="right" header-align="center" align="center" width="150">
            <template slot-scope="scope">
              <el-button  @click="assignment(scope.row.index)" type="text">修改</el-button>
<!--              <el-button  @click="deleteOrder(scope.row)" type="text">删除</el-button>-->
            </template>
          </el-table-column>
        </el-table>
        <el-pagination
            class="containers"
            background
            layout="prev, pager, next"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            :current-page="page"
            :page-sizes="[10, 20, 50, 100]"
            :page-size="limit"
            :total="total">
        </el-pagination>
      </div>
    </el-form>
    <template slot="footer">
      <el-button v-if="showHoldOrder"  @click="clearData"  type="primary">清除</el-button>
      <el-button v-if="updateShow"  @click="updateHoldOrder"  type="primary">修改</el-button>
      <el-button type="primary" @click="dataFormSubmitHandle()">{{ $t('confirm') }}</el-button>
      <el-button @click="visible = false">{{ $t('cancel') }}</el-button>
    </template>
  </el-dialog>
</template>

<script>
import debounce from 'lodash/debounce'
import mixinViewModule from '@/mixins/view-module'
import {getUUID} from "../../../utils";
export default {
  mixins:[mixinViewModule],
  data () {
    return {
      visible: false,
      showHoldOrder: false,
      checkOrder: true, // 查看订单
      updateShow: false,
      showOrders:0,
      total:0,
      limit:50,
      page:1,//页数
      length:0,
      backgroundColor:'#a3f1c6',
      dataFromList:[],
      //创建该页面时，不调用数据查询接口
      mixinViewModuleOptions: {
        createdIsNeed: false,
      },
      dataForm: {
        id: '',
        index: '',
        orderType: 0,
        sysUserId: '',
        orderControl: '',
        trackingNumber: '',
        productionOrder: '',
        customerId: '',
        customerName: '',
        partId: '',
        partNameCode: '',
        materialQuality: '',
        summaryDescription: '',
        orderNumber: '',
        orderLink: '',
        orderDate: this.formatDates(),
        quantity: 0,
        deliveryDate: this.formatDates(),
        completionDay: '',
        forecastBalance: '',
        deliveryLocationCode: '',
        contactPerson:'',
        contactPhone:'',
        contactEmail:'',
        shippingMethod: '',
        shipmentQuantity: '',
        outageQuantity: '',
        unitPrice: 0,
        currency: 0,
        exchangeRate: '',
        taxRate: '',
        transactionAmount: 0,
        amountReceivable: '',
        paidFor: '',
        notCollected: '',
        confirm: '',
        make: '',
        tradingLocations: '',
        quotationNumber: '',
        clearanceDate: '',
        paymentDate: '',
        remark: '',
        disabled: '',
        creator: '',
        createDate: '',
        updater: '',
        updateDate: ''
      }
    }
  },
  computed: {
    dataRule () {
      return {
          orderType: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          orderControl: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          trackingNumber: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          productionOrder: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          customerId: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          partId: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ]
      }
    }
  },
  watch:{
    "dataForm.unitPrice"(){
      this.dataForm.transactionAmount = this.dataForm.unitPrice * this.dataForm.quantity
    },
    "dataForm.orderType"(type){
      this.baseColorMatching(type)
    },
    "dataForm.quantity"(){
      this.dataForm.transactionAmount = this.dataForm.unitPrice * this.dataForm.quantity
    },
    "dataForm.deliveryLocationCode"(){
      if(!this.dataForm.id){
        this.dataForm.tradingLocations = this.dataForm.deliveryLocationCode
      }
    },
  },
  methods: {
    // 更换底色
    baseColorMatching(orderType){
      switch (Number(orderType)) {
        case 0:
          this.backgroundColor = '#a3f1c6';
          break;
        case 1:
          this.backgroundColor = '#a6c6fa';
          break;
        case 2:
          this.backgroundColor = '#a6c6fa';
          break;
        case 3:
          this.backgroundColor = '#e5edfa';
          break;
        case 4:
          this.backgroundColor = '#e8e889';
          break;
        case 5:
          this.backgroundColor = '#db89e8';
          break;
        case 5:
          this.backgroundColor = '#a3f1c6';
          break;
        case 6:
          this.backgroundColor = '#a3f1c6';
          break;
        case 7:
          this.backgroundColor = '#a3f1c6';
          break;
      }
    },
    // 查看订单
    viewOrders(item){
      if(item){
        this.showHoldOrder = true
      }else {
        this.showHoldOrder = false
      }
    },
    deleteOrder(data){
      this.$http.post(`/orders/businessorder/deleteOrder`,data).then(({ data: res }) => {
        if (res.code !== 0) {
          return this.$message.error(res.msg)
        }
        this.$message({
          message: '删除成功',
          type: 'success'
        });
        this.dataFromList = res.data
      }).catch(() => {})
    },
    assignment(index){
      this.showHoldOrder = true
      this.dataForm = {
        ...this.dataForm,
        ...this.dataFromList[index]
      }
      this.updateShow = true
    },
    // 修改订单
    updateHoldOrder(){
      this.$http.put(`/orders/businessorder/modifyPendingOrder`,this.dataForm).then(({ data: res }) => {
        if (res.code !== 0) {
          return this.$message.error(res.msg)
        }
        this.$message({
          message: '修改成功',
          type: 'success'
        });
        this.dataFromList = res.data
        this.updateShow = false
      }).catch(() => {})
    },
    handleSizeChange(val) {
      console.log(`每页 ${val} 条`);
    },
    handleCurrentChange(val) {
      if(val-1 < this.dataFromList.length){
        this.dataForm = {
          ...this.dataForm,
          ...this.dataFromList[val-1]
        }
      }
    },
    // 客户数据
    customerData(data){
      this.dataForm.customerName = data.chineseName
    },
    //判断是否添加数据到数组中
    judgingParameters(){
      console.log('判断必填项')
      //判读必填项是否为空
      return new Promise((resolve,reject) => {
        this.$refs['dataForm'].validate((valid) => {
          if (!valid) {
            resolve(false)
          }
          resolve(true)
        })
      })
    },
    // 清除
    clearData(){
      let keyList = ['orderType','partId','partNameCode', 'materialQuality',
        'quantity','deliveryDate','currency','unitPrice',
        'exchangeRate','taxRate','transactionAmount','summaryDescription','quotationNumber','index']
      for (let keys of keyList) {
        this.dataForm[keys] = ''
      }
    },
    // 添加订单
    pushOrder() {
      this.$refs['dataForm'].validate((valid) => {
        if (!valid) {
          return false
        }
        this.$http.post(`/orders/businessorder/holdOrder`,this.dataForm).then(({ data: res }) => {
          if (res.code !== 0) {
            return this.$message.error(res.msg)
          }
          this.$message({
            message: '添加成功',
            type: 'success'
          });
          this.dataFromList = res.data
          this.length = res.data.length
          this.clearData()
        }).catch(() => {})
      })
    },
    // 修改订单
    revise(){
      this.$http['put']('/orders/businessorder/', this.dataForm).then(({ data: res }) => {
        if (res.code !== 0) {
          return this.$message.error(res.msg)
        }
        this.$message({
          message: this.$t('prompt.success'),
          type: 'success',
          duration: 500,
          onClose: () => {
            this.visible = false
            this.$emit('refreshDataList')
          }
        })
      }).catch(() => {})
    },
    // 保存订单
    preserve(){
      this.dataFromList.push(this.dataForm)
      this.$http['post']('/orders/businessorder/batchOrderSave', this.dataFromList).then(({ data: res }) => {
        if (res.code !== 0) {
          return this.$message.error(res.msg)
        }
        this.$message({
          message: this.$t('prompt.success'),
          type: 'success',
          duration: 500,
          onClose: () => {
            this.visible = false
            this.$emit('refreshDataList')
          }
        })
      }).catch(() => {})
    },
    init () {
      this.visible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].resetFields()
        if (this.dataForm.id) {
          this.getInfo()
        }else {
          this.dataForm.sysUserId = window.SITE_CONFIG['users'].id
          this.getOrder()
        }
      })
    },
    // 获取信息
    getInfo () {
      // this.$http.get(`/orders/businessorder/${this.dataForm.id}`).then(({ data: res }) => {
      this.$http.get(`/orders/businessorder/getBusinessOrderInfoByOrderId/${this.dataForm.id}`).then(({ data: res }) => {
        if (res.code !== 0) {
          return this.$message.error(res.msg)
        }
        this.dataForm = {
          ...this.dataForm,
          ...res.data
        }
      }).catch(() => {})
    },
    // 获取订单信息
    getOrder () {
      this.$http.get(`/orders/businessorder/getOrderList/${this.dataForm.sysUserId}`).then(({ data: res }) => {
        if (res.code !== 0) {
          return this.$message.error(res.msg)
        }
        this.dataFromList = res.data
        if(this.dataFromList.length == 0){
          this.showHoldOrder = true
        }
      }).catch(() => {})
    },
    // 表单提交
    dataFormSubmitHandle: debounce(function () {
      this.$refs['dataForm'].validate((valid) => {
        if (!valid) {
          return false
        }
        let message = '';
        if(this.dataForm.id){
          message = '此操作将修改该数据'
        }else {
          message = '此操作将新增数据'
        }
        this.$confirm(message+', 是否继续?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
          center: true
        }).then(() => {
          if(this.dataForm.id){
            this.revise()
          }else {
            this.preserve()
          }
        }).catch(() => {
          this.$message({
            type: 'info',
            message: '已取消'
          });
        });
      })
    }, 1000, { 'leading': true, 'trailing': false })
  }
}
</script>
