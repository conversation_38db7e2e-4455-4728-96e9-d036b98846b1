<template>
  <el-dialog :visible.sync="visible" title="查询" :close-on-click-modal="false" :close-on-press-escape="false" append-to-body>
    <el-form :model="dataForm" ref="dataForm" @keyup.enter.native="dataFormSubmitHandle()" :label-width="$i18n.locale === 'en-US' ? '120px' : '80px'">
      <div>
        <p class="font_size">下单日期：</p>
        <div>
          <span class="font_size">按日期查询</span>
          <el-date-picker
              v-model="orderDay"
              type="daterange"
              value-format="yyyy-MM-dd"
              align="right"
              unlink-panels
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              :picker-options="pickerOptionsDay">
          </el-date-picker>
        </div>
        <div>
          <span class="font_size">按月份查询</span>
          <el-date-picker
              v-model="orderMonth"
              type="monthrange"
              value-format="yyyy-MM"
              align="right"
              unlink-panels
              range-separator="至"
              start-placeholder="开始月份"
              end-placeholder="结束月份"
              :picker-options="pickerOptionsMonth">
          </el-date-picker>
        </div>

        <p class="font_size">客户交期：</p>
        <div>
          <span class="font_size">按日期查询</span>
          <el-date-picker
              v-model="deliveryDay"
              type="daterange"
              value-format="yyyy-MM-dd"
              align="right"
              unlink-panels
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              :picker-options="pickerOptionsDay">
          </el-date-picker>
        </div>
        <div>
          <span class="font_size">按月份查询</span>
          <el-date-picker
              v-model="deliveryMonth"
              type="monthrange"
              value-format="yyyy-MM"
              align="right"
              unlink-panels
              range-separator="至"
              start-placeholder="开始月份"
              end-placeholder="结束月份"
              :picker-options="pickerOptionsMonth">
          </el-date-picker>
        </div>
      </div>
    </el-form>
    <template slot="footer">
      <el-button @click="visible = false">{{ $t('cancel') }}</el-button>
      <el-button type="primary" @click="inquire()">查询</el-button>
    </template>
  </el-dialog>
</template>

<script>
export default {
 data () {
    return {
      visible: false,
      dataForm: {
        customerId: '',
        orderNumber: '',
        thisItemNumber: '',
        undertaker: '',
        quotationNo: '',
        orderDate: '',
        orderStartDate: '',
        orderEndDate: '',
        deliveryStartDate: '',
        deliveryEndDate: '',
      },
      mixinViewModuleOptions: {
        exportURL: ''
      },
      accordingTo: '',
      orderDay: '',
      deliveryDay: '',
      orderMonth: '',
      deliveryMonth: '',
      year: '',
      pickerOptionsDay: {
        shortcuts: [{
          text: '最近一周',
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
            picker.$emit('pick', [start, end]);
          }
        }, {
          text: '最近一个月',
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
            picker.$emit('pick', [start, end]);
          }
        }, {
          text: '最近三个月',
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
            picker.$emit('pick', [start, end]);
          }
        }]
      },
      pickerOptionsMonth: {
        shortcuts: [{
          text: '本月',
          onClick(picker) {
            picker.$emit('pick', [new Date(), new Date()]);
          }
        }, {
          text: '今年至今',
          onClick(picker) {
            const end = new Date();
            const start = new Date(new Date().getFullYear(), 0);
            picker.$emit('pick', [start, end]);
          }
        }, {
          text: '最近六个月',
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setMonth(start.getMonth() - 6);
            picker.$emit('pick', [start, end]);
          }
        }]
      },
    }
  },
  watch:{
    orderDay(newVal) {
      if (newVal) {
        this.orderMonth = '';
        this.splitArray(newVal,'orderStartDate','orderEndDate')
      }
    },
    orderMonth(newVal) {
      if (newVal) {
        this.orderDay = '';
        this.splitArray(newVal,'orderStartDate','orderEndDate')
      }
    },
    deliveryDay(newVal) {
      if (newVal) {
        this.deliveryMonth = '';
        this.splitArray(newVal,'deliveryStartDate','deliveryEndDate')
      }
    },
    deliveryMonth(newVal) {
      if (newVal) {
        this.deliveryDay = '';
        this.splitArray(newVal,'deliveryStartDate','deliveryEndDate')
      }
    },
  },
  methods: {
    inquire(){
      this.$emit("inquire",this.dataForm)
      this.visible = false
    },
    splitArray (array,start,end){
      this.dataForm[start] = array[0];
      this.dataForm[end] = array[1];
    },
    init () {
      this.visible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].resetFields()
      })
    },
  }
}
</script>
