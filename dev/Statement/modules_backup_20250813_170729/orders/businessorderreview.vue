<template>
  <el-card shadow="never" class="aui-card--fill">
    <div class="mod-orders__businessorderreview}">
      <el-form :inline="true" :model="dataForm" @keyup.enter.native="getDataList()">
        <el-form-item>
          <el-tooltip content="请输入查询关键字" placement="top">
            <el-input v-model="dataForm.id" placeholder="查询关键字" @input="throttleFunction" clearable>
              <el-button slot="append" icon="el-icon-search" @click="getDataList()"></el-button>
            </el-input>
          </el-tooltip>
        </el-form-item>
        <el-form-item>
          <el-button v-if="$hasPermission('orders:businessorderreview:save')" type="primary"
                     @click="addOrUpdateHandle()">{{ $t('add') }}
          </el-button>
        </el-form-item>
        <el-dropdown style="float:right;">
          <el-button type="primary">
            更多功能<i class="el-icon-arrow-down el-icon--right"></i>
          </el-button>
          <el-dropdown-menu slot="dropdown">
            <el-dropdown-item>
              <el-form-item>
                <el-button type="info" @click="exportHandle()">{{ $t('export') }}</el-button>
              </el-form-item>
            </el-dropdown-item>
            <el-dropdown-item>
              <el-form-item>
                <el-button v-if="$hasPermission('orders:businessorderreview:export')" type="info"
                           @click="exportTemplateHandle()">
                  {{ $t('exportTemplate') }}
                </el-button>
              </el-form-item>
            </el-dropdown-item>
            <el-dropdown-item>
              <el-form-item>
                <el-tooltip content="下载模板填入信息后导入" placement="top">
                  <el-upload
                      v-if="$hasPermission('orders:businessorderreview:save')"
                      class="upload-demo"
                      :action="mixinViewModuleOptions.addBatchUrl"
                      :headers="headers"
                      :multiple="false"
                      :show-file-list="false"
                      :file-list="fileList"
                      :before-upload="beforeUpload"
                      :on-success="resultHandle"
                      :on-change="handleChange"
                      accept="'.xlsx','.xls'">
                    <el-button type="success">{{ $t('addBatch') }}</el-button>
                  </el-upload>
                </el-tooltip>
                <el-button type="text" v-show="unsuccessfulNameList" @click="dialogVisible = true">导入失败列表
                </el-button>
              </el-form-item>
            </el-dropdown-item>
            <el-dropdown-item>
              <el-form-item>
                <el-button v-if="$hasPermission('orders:businessorderreview:delete')" type="danger"
                           @click="deleteHandle()">{{ $t('deleteBatch') }}
                </el-button>
              </el-form-item>
            </el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown>
      </el-form>
      <el-table v-loading="dataListLoading" height="500px" :data="dataList" border
                @selection-change="dataListSelectionChangeHandle" style="width: 100%;">
        <el-table-column type="selection" header-align="center" align="center" width="50"></el-table-column>
        <el-table-column prop="customerCode" label="客户代码" header-align="center" align="center"></el-table-column>
        <el-table-column prop="designation" label="客户品名" header-align="center" align="center"></el-table-column>
        <el-table-column prop="orderType" label="订单类型" header-align="center" align="center">
          <template slot-scope="scope">
            {{ $getDictLabel("business_order_type", scope.row.orderType) }}
          </template>
        </el-table-column>
        <el-table-column prop="orderNumber" label="客户订单" header-align="center" align="center">
          <template slot-scope="scope">
            <div @click="viewCustomerOrders(scope.row.businessOrderId,'')">
              {{ scope.row.orderNumber }}
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="trackingNumber" label="制交单号" header-align="center" align="center">
          <template slot-scope="scope">
            <div @click="viewCustomerOrders('',scope.row.trackingNumber)">
              {{ scope.row.trackingNumber }}
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="materialQuality" label="材质" header-align="center" align="center"></el-table-column>
        <!--        <el-table-column prop="numberOfHoles" label="孔数" header-align="center" align="center"></el-table-column>-->
        <el-table-column prop="quantity" label="生产数量" header-align="center" align="center"></el-table-column>
        <el-table-column prop="orderDate" label="下单日期" header-align="center" align="center"></el-table-column>
        <el-table-column prop="originOfProduction" label="生产地点" header-align="center"
                         align="center"></el-table-column>
        <el-table-column prop="stockStatus" label="库存状态" header-align="center" align="center">
          <template slot-scope="scope">
            {{ $getDictLabel("stock_status", scope.row.stockStatus) }}
          </template>
        </el-table-column>
        <!--        <el-table-column prop="packagingRegulations" label="包装规定" header-align="center"
                                 align="center"></el-table-column>-->
        <el-table-column prop="approve" label="内签" header-align="center" align="center">
          <template slot-scope="scope">
            <div @click="updateApprove(scope.row.orderApprovalId,scope.row)">
             <span v-if="scope.row.approve">
                {{ $getDictLabel("approve", scope.row.approve) }}
             </span>
            <span v-else>
              已核准
            </span>
            </div>

          </template>
        </el-table-column>
        <el-table-column prop="countersign" label="会签" header-align="center" align="center">
          <template slot-scope="scope">
            <div @click="updateCountersign(scope.row.orderCountersignedId,scope.row)">
              <span v-if="scope.row.countersign">
                {{ $getDictLabel("countersign", scope.row.countersign) }}
              </span>
              <span v-else>
                已完成
              </span>
            </div>

          </template>
        </el-table-column>
        <el-table-column prop="productionConfirm" label="生产确认" header-align="center" align="center">
          <template slot-scope="scope">
            <div @click="confirmProduction(scope.row.id,scope.row)">
              {{ $getDictLabel("production_confirm", scope.row.productionConfirm) }}
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="deliveryDate" label="出货日期" header-align="center" align="center"></el-table-column>
        <el-table-column prop="countersigningOpinions" show-overflow-tooltip label="会签意见" header-align="center"
                         align="center"></el-table-column>
        <el-table-column prop="remark" label="备注" header-align="center" align="center"></el-table-column>
        <el-table-column :label="$t('handle')" fixed="right" header-align="center" align="center" width="150">
          <template slot-scope="scope">
            <el-dropdown>
              <el-button type="primary">
                功能<i class="el-icon-arrow-down el-icon--right"></i>
              </el-button>
              <el-dropdown-menu slot="dropdown">
                <el-dropdown-item>
                  <el-button v-if="$hasPermission('orders:businessorderreview:update')" type="text" size="small"
                             @click="addOrUpdateHandle(scope.row.id)">{{ $t('update') }}
                  </el-button>
                </el-dropdown-item>
                <el-dropdown-item>
                  <el-button v-if="$hasPermission('orders:orderapproval:save')" type="text" size="small"
                             @click="updateApprove(scope.row.orderApprovalId,scope.row,'update')">内签
                  </el-button>
                </el-dropdown-item>
                <el-dropdown-item>
                  <el-button v-if="$hasPermission('orders:ordercountersigned:save')" type="text" size="small"
                             @click="updateCountersign(scope.row.orderCountersignedId,scope.row,'update')">会签
                  </el-button>
                </el-dropdown-item>
                <el-dropdown-item>
                  <el-button v-if="$hasPermission('orders:businessorderreview:delete')" type="text" size="small"
                             @click="deleteHandle(scope.row.id)">{{ $t('delete') }}
                  </el-button>
                </el-dropdown-item>
              </el-dropdown-menu>
            </el-dropdown>
          </template>
        </el-table-column>
      </el-table>
      <el-pagination
          :current-page="page"
          :page-sizes="[10, 20, 50, 100]"
          :page-size="limit"
          :total="total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="pageSizeChangeHandle"
          @current-change="pageCurrentChangeHandle">
      </el-pagination>
      <!-- 弹窗, 新增 / 修改 -->
      <div>
        <add-or-update v-if="addOrUpdateVisible" ref="addOrUpdate" @refreshDataList="getDataList"></add-or-update>
        <customer-orders v-if="checkOrderVisible" ref="checkOrder"></customer-orders>
        <order-approval-add-or-update v-if="orderApprovalVisible" ref="orderApproval"
                                      @refreshDataList="getDataList"></order-approval-add-or-update>
        <order-countersigned-add-or-update v-if="orderCountersignedVisible" ref="orderCountersigned"
                                           @refreshDataList="getDataList"></order-countersigned-add-or-update>
      </div>
    </div>
  </el-card>
</template>

<script>
import mixinViewModule from '@/mixins/view-module'
import AddOrUpdate from './businessorderreview-add-or-update'
import CustomerOrders from "./customer-orders.vue";
import OrderApprovalAddOrUpdate from "./orderapproval-add-or-update.vue";
import OrderCountersignedAddOrUpdate from "./ordercountersigned-add-or-update.vue";
import Cookies from "js-cookie";
import row from "element-ui/packages/row";

export default {
  computed: {
    row() {
      return row
    }
  },
  mixins: [mixinViewModule],
  data() {
    return {
      message: '',
      headers: {'token': Cookies.get('token')},
      fileList: [{name: '', url: ''}],
      unsuccessfulNameList: '',//  导入失败名称列表
      mixinViewModuleOptions: {
        getDataListURL: '/orders/businessorderreview/page',
        getDataListIsPage: true,
        exportURL: '/orders/businessorderreview/export',
        deleteURL: '/orders/businessorderreview',
        deleteIsBatch: true,
        exportTemplateURL: '/orders/businessorderreview/export/template',
        addBatchUrl: window.SITE_CONFIG['apiURL'] + '/orders/businessorderreview/batchSave',
      },
      dataForm: {
        id: '',
        paramStr: '',
      },
      // 判断是否还在继续输入
      timer: null,
      checkOrderVisible: false,
      orderApprovalVisible: false,
      orderCountersignedVisible: false,
    }
  },
  components: {
    AddOrUpdate,
    CustomerOrders,
    OrderApprovalAddOrUpdate,
    OrderCountersignedAddOrUpdate
  },
  methods: {
    newObject(data) {
      const {customerId, partId, businessOrderId, trackingNumber, approve, countersign, countersigningOpinions} = data
      const newObj = {
        customerId,
        partId,
        businessOrderId,
        trackingNumber,
        approve,
        countersign,
        countersigningOpinions
      };
      return newObj;
    },
    // 内签
    updateApprove(orderApprovalId, data, handle) {
      console.log(orderApprovalId, data)
      this.orderApprovalVisible = true
      this.$nextTick(() => {
        this.$refs.orderApproval.dataForm.id = orderApprovalId;
        if (orderApprovalId) {
          this.$refs.orderApproval.displayComponent = false;
        }
        if (handle && orderApprovalId) {
          this.$refs.orderApproval.displayComponent = true;
          this.$refs.orderApproval.init();
        } else {
          let newObject = this.newObject(data);
          this.$refs.orderApproval.dataInit(newObject);
        }
      })
    },
    // 会签
    updateCountersign(orderCountersignedId, data, handle) {
      this.orderCountersignedVisible = true
      this.$nextTick(() => {
        this.$refs.orderCountersigned.dataForm.id = orderCountersignedId;
        if (orderCountersignedId) {
          this.$refs.orderCountersigned.displayComponent = false;
        }
        if (handle && orderCountersignedId) {
          this.$refs.orderCountersigned.displayComponent = true;
          this.$refs.orderCountersigned.init();
        } else {
          let newObject = this.newObject(data);
          this.$refs.orderCountersigned.dataInit(newObject);
        }
      })
    },
    // 确认生产
    confirmProduction(id, data) {
      if (data.orderApprovalId && data.orderCountersignedId) {
        let message = ''
        let productionConfirm = ''
        if (data.productionConfirm === 1) {
          message = '是否确认生产？';
          productionConfirm = 0;
        } else {
          message = '是否取消生产？';
          productionConfirm = 1;
        }
        this.$confirm(message, '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
          center: true
        }).then(() => {
          this.$http['post']('/orders/businessorderreview/productionConfirm', {
            id: id,
            productionConfirm: productionConfirm
          }).then(({data: res}) => {
            if (res.code !== 0) {
              return this.$message.error(res.msg)
            }
            this.$message({
              message: '修改成功',
              type: 'success',
              duration: 500,
              onClose: () => {
                this.getDataList()
              }
            })
          }).catch(() => {
          })
        }).catch(() => {
          this.$message({
            type: 'info',
            message: '已取消操作'
          });
        });
      } else {
        this.$message({
          type: 'info',
          message: '您还未确认内签或会签'
        });
      }
    },
    // 查看客户订单
    viewCustomerOrders(businessOrderId, trackingNumber) {
      this.checkOrderVisible = true
      this.$nextTick(() => {
        if (trackingNumber) {
          this.$refs.checkOrder.trackingNumber = trackingNumber
          this.$refs.checkOrder.oneOrMore = false
          this.$refs.checkOrder.title = '所有订单内容'
        } else if (businessOrderId) {
          this.$refs.checkOrder.oneOrMore = true
          this.$refs.checkOrder.title = '订单内容'
          this.$refs.checkOrder.businessOrderId = businessOrderId
        }
        this.$refs.checkOrder.init();
      })
    },
    //节流函数
    throttleFunction() {
      // 如果timer不为空就表明在持续输入,每次改变都清除定时，这样就可以实现只输入最后一次结果(注意每次间隔如果超过500毫秒就会输出)
      if (this.timer !== null) clearTimeout(this.timer)
      this.timer = setTimeout(() => {
        this.getDataList()
      }, 500)
    },
    beforeUpload(file) {
      this.message = this.$message({
        message: '上传中，请勿操作',
        center: true,
        duration: 0,
      });
    },
    handleChange(file, fileList) {
      this.fileList = fileList.slice(-3);

    },
    resultHandle(res) {
      if (res.code !== 0) {
        return this.$message.error(res.msg)
      }
      this.$message({
        message: this.$t('prompt.success'),
        type: 'success',
        duration: 500,
        onClose: () => {
          if (res.data) {
            this.unsuccessfulNameList = res.data
          }
          this.visible = false
          this.query()
        }
      })
    },
  }
}
</script>
