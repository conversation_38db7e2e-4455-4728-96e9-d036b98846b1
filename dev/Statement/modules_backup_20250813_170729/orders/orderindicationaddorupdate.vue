<template>
  <div class="containers">
    <el-form :model="dataForm" :rules="dataRule" ref="dataForm" @keyup.enter.native="dataFormSubmitHandle()" :label-width="$i18n.locale === 'en-US' ? '120px' : '80px'">
      <el-divider content-position="center">
        <span style="font-size: 20px; font-weight: bold;">订单建立</span>
      </el-divider>
      <div class="container">
        <el-form-item label="客户代码" prop="customerId">
          <customer-component v-model="dataForm.customerId" placeholder="客户代码"></customer-component>
        </el-form-item>
        <el-form-item label="订单号" prop="orderNumber">
          <el-input v-model="dataForm.orderNumber" placeholder="订单号"></el-input>
        </el-form-item>
        <el-form-item label="订单日期" prop="orderDate">
          <el-date-picker
              class="datePicker"
              v-model="dataForm.orderDate"
              type="date"
              value-format="yyyy-MM-dd"
              placeholder="订单日期">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="作成" prop="make">
          <el-input v-model="dataForm.make" placeholder="作成"></el-input>
        </el-form-item>
      </div>
      <div class="containers div">
        <div @click="addOrder(0)" class="margins">
          <el-button type="success" icon="el-icon-plus" circle></el-button>
          <span class="font_size">新增新订单</span>
        </div>
        <div @click="addOrder(1)" class="margins">
          <el-button type="primary" icon="el-icon-plus" circle></el-button>
          <span class="font_size">预示订单</span>
        </div>
        <div @click="addOrder(2)" class="margins">
          <el-button type="danger" icon="el-icon-plus" circle></el-button>
          <span class="font_size">新品订单</span>
        </div>
        <div @click="addOrder(3)">
          <el-button type="warning" icon="el-icon-plus" circle></el-button>
          <span class="font_size">模具订单</span>
        </div>
      </div>
      <div v-show="visible" class="rounded-div" :style="{backgroundColor:showOrders === 1 ? '':'#a3f1c6'}">
        <div class="container">
          <div v-if="showOrders === 0" @click="pushOrder">
            <el-button type="success" icon="el-icon-plus" circle></el-button>
            <span class="font_size">新增新订单</span>
          </div>
          <div v-else-if="showOrders === 1" @click="pushOrder">
            <el-button type="primary" icon="el-icon-plus" circle></el-button>
            <span class="font_size">预示订单</span>
          </div>
          <div v-else-if="showOrders === 2">
            <el-button type="danger" icon="el-icon-plus" circle></el-button>
            <span class="font_size">新品订单</span>
          </div>
          <div v-else>
            <el-button type="warning" icon="el-icon-plus" circle></el-button>
            <span class="font_size">模具订单</span>
          </div>
          <div class="margins" @click="preserve" v-if="$hasPermission('orders:orderindication:save')">
            <el-button type="primary">保存</el-button>
          </div>
          <div class="margins" @click="viewOrders(0)">
            <el-button type="primary">查看订单</el-button>
          </div>
        </div>
        <div>
          <div class="container">
            <el-form-item label="订单类型" prop="orderType">
              <el-input v-model="dataForm.orderType" placeholder="订单类型"></el-input>
            </el-form-item>
          </div>
          <div class="container">
            <el-form-item label="部品番号" prop="partId">
              <part-number-component v-model="dataForm.partId" :customer-id="dataForm.customerId" placeholder="部品番号"></part-number-component>
            </el-form-item>
            <el-form-item label="品名代码" prop="partNameCode">
              <el-input v-model="dataForm.partNameCode" placeholder="品名代码"></el-input>
            </el-form-item>
            <el-form-item label="材质" prop="materialQuality">
              <el-input v-model="dataForm.materialQuality" placeholder="材质"></el-input>
            </el-form-item>
            <el-form-item label="摘要说明" prop="summaryDescription">
              <el-input v-model="dataForm.summaryDescription" placeholder="摘要说明"></el-input>
            </el-form-item>
          </div>
          <div class="container">
            <el-form-item label="数量" prop="quantity">
              <el-input v-model="dataForm.quantity" placeholder="数量"></el-input>
            </el-form-item>
            <el-form-item label="单价" prop="unitPrice">
              <el-input v-model="dataForm.unitPrice" placeholder="单价"></el-input>
            </el-form-item>
            <el-form-item label="币别" prop="currency">
              <el-input v-model="dataForm.currency" placeholder="币别"></el-input>
            </el-form-item>
            <el-form-item label="交易金额" prop="transactionAmount">
              <el-input v-model="dataForm.transactionAmount" placeholder="交易金额"></el-input>
            </el-form-item>
          </div>
          <div class="container">
            <el-form-item v-if="showOrders === 1" label="完成日期" prop="completionDate">
              <el-date-picker
                  class="datePicker"
                  v-model="dataForm.completionDate"
                  type="date"
                  value-format="yyyy-MM-dd"
                  placeholder="完成日期">
              </el-date-picker>
            </el-form-item>
            <el-form-item v-if="showOrders === 0" label="交期" prop="deliveryDate">
              <el-date-picker
                  class="datePicker"
                  v-model="dataForm.deliveryDate"
                  type="date"
                  value-format="yyyy-MM-dd"
                  placeholder="交期">
              </el-date-picker>
            </el-form-item>
            <el-form-item label="交货地代码" prop="deliveryLocationCode">
              <el-input v-model="dataForm.deliveryLocationCode" placeholder="交货地代码"></el-input>
            </el-form-item>
            <el-form-item label="交货地" prop="deliveryLocation">
              <el-input v-model="dataForm.deliveryLocation" placeholder="交货地"></el-input>
            </el-form-item>
            <el-form-item  v-if="showOrders === 0" label="报价单号" prop="quotationNumber">
              <el-input v-model="dataForm.quotationNumber" placeholder="报价单号"></el-input>
            </el-form-item>
          </div>
          <el-form-item label="备注" prop="remark">
            <el-input v-model="dataForm.remark" placeholder="备注"></el-input>
          </el-form-item>
        </div>
      </div>
      <div v-show="!visible">
        <el-button type="primary" @click="viewOrders">添加订单</el-button>
        <el-table height="600px" :data="dataFromList" border style="width: 100%;">
          <el-table-column prop="orderType" label="订单类型" header-align="center" align="center"></el-table-column>
          <el-table-column prop="partId" label="部品番号" fixed header-align="center" align="center"></el-table-column>
          <el-table-column prop="materialQuality" label="材质" header-align="center" align="center"></el-table-column>
          <el-table-column prop="quantity" label="数量" header-align="center" align="center"></el-table-column>
          <el-table-column :label="$t('handle')" fixed="right" header-align="center" align="center" width="150"></el-table-column>
        </el-table>
        <el-pagination
            class="containers"
            background
            layout="prev, pager, next"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            :current-page="page"
            :page-sizes="[10, 20, 50, 100]"
            :page-size="limit"
            :total="total">
        </el-pagination>
      </div>
    </el-form>
<!--    <div v-if="false">
      <el-form-item label="制交单号" prop="trackingNumber">
        <el-input v-model="dataForm.trackingNumber" placeholder="制交单号"></el-input>
      </el-form-item>
      <el-form-item label="预定出货日" prop="preShipmentDate">
        <el-input v-model="dataForm.preShipmentDate" placeholder="预定出货日"></el-input>
      </el-form-item>
      <el-form-item label="预示单号" prop="indicationNumber">
        <el-input v-model="dataForm.indicationNumber" placeholder="预示单号"></el-input>
      </el-form-item>
      <el-form-item label="预示日期" prop="predictTheDate">
        <el-input v-model="dataForm.predictTheDate" placeholder="预示日期"></el-input>
      </el-form-item>
      <el-form-item label="库存数量" prop="inventoryQuantity">
        <el-input v-model="dataForm.inventoryQuantity" placeholder="库存数量"></el-input>
      </el-form-item>
      <el-form-item label="入库日期" prop="warehousingDate">
        <el-input v-model="dataForm.warehousingDate" placeholder="入库日期"></el-input>
      </el-form-item>
      <el-form-item label="出货方式" prop="shippingMethod">
        <el-input v-model="dataForm.shippingMethod" placeholder="出货方式"></el-input>
      </el-form-item>
      <el-form-item label="出货数量" prop="shipmentQuantity">
        <el-input v-model="dataForm.shipmentQuantity" placeholder="出货数量"></el-input>
      </el-form-item>
      <el-form-item label="欠货数量" prop="outageQuantity">
        <el-input v-model="dataForm.outageQuantity" placeholder="欠货数量"></el-input>
      </el-form-item>
      <el-form-item label="清款日" prop="clearanceDate">
        <el-input v-model="dataForm.clearanceDate" placeholder="清款日"></el-input>
      </el-form-item>
      <el-form-item label="收款日" prop="paymentDate">
        <el-input v-model="dataForm.paymentDate" placeholder="收款日"></el-input>
      </el-form-item>
      <el-form-item label="是否收款" prop="collection">
        <el-input v-model="dataForm.collection" placeholder="是否收款"></el-input>
      </el-form-item>
    </div>-->
    <template slot="footer">
      <el-button @click="visible = false">{{ $t('cancel') }}</el-button>
      <el-button type="primary" @click="dataFormSubmitHandle()">{{ $t('confirm') }}</el-button>
    </template>
  </div>
</template>

<script>
import debounce from 'lodash/debounce'
import mixinViewModule from '@/mixins/view-module'
export default {
  mixins:[mixinViewModule],
  data () {
    return {
      visible: true,
      //创建该页面时，不调用数据查询接口
      mixinViewModuleOptions: {
        createdIsNeed: false,
      },
      showOrders:0,
      total:0,
      limit:50,
      page:1,//页数
      dataFromList:[],
      dataForm: {
        id: '',
        orderType: '',
        trackingNumber: '',
        customerId: '',
        partId: '',
        partNameCode: '',
        materialQuality: '',
        summaryDescription: '',
        orderNumber: '',
        orderDate: '',
        quantity: '',
        deliveryDate: '',
        preShipmentDate: '',
        indicationNumber: '',
        predictTheDate: '',
        inventoryQuantity: '',
        completionDate: '',
        deliveryLocationCode: '',
        warehousingDate: '',
        shippingMethod: '',
        shipmentQuantity: '',
        outageQuantity: '',
        deliveryLocation: '',
        unitPrice: '',
        currency: '',
        transactionAmount: '',
        make: '',
        quotationNumber: '',
        clearanceDate: '',
        paymentDate: '',
        collection: '',
        remark: '',
        disabled: '',
        creator: '',
        createDate: '',
        updater: '',
        updateDate: ''
      },
      initialData:{
        id: '',
        orderType: '',
        trackingNumber: '',
        partId: '',
        partNameCode: '',
        materialQuality: '',
        summaryDescription: '',
        quantity: '',
        deliveryDate: '',
        preShipmentDate: '',
        indicationNumber: '',
        predictTheDate: '',
        inventoryQuantity: '',
        completionDate: '',
        deliveryLocationCode: '',
        warehousingDate: '',
        shippingMethod: '',
        shipmentQuantity: '',
        outageQuantity: '',
        deliveryLocation: '',
        unitPrice: '',
        currency: '',
        transactionAmount: '',
        quotationNumber: '',
        clearanceDate: '',
        paymentDate: '',
        collection: '',
        remark: '',
        disabled: '',
        creator: '',
        createDate: '',
        updater: '',
        updateDate: ''
      }
    }
  },
  computed: {
    dataRule () {
      return {
          orderType: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          customerId: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          partId: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          orderNumber: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        materialQuality: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
      }
    }
  },
  methods: {
    viewOrders(item){
      if(item){
        this.visible = true
      }else {
        this.visible = false
      }
    },
    //暂存数据
    stagingData(){
      // 将数组转换为字符串
      let recordsString = JSON.stringify(this.dataFromList);
      // 将数据存到localStorage中
      sessionStorage.setItem('dataFromList',recordsString)
    },
    //获取暂存数据
    getStagingData(){
      // 获取到暂存数据
      let recordsString = sessionStorage.getItem("dataFromList");
      // 将字符串转换为数组
      let recordsArray = JSON.parse(recordsString);
      this.dataFromList = recordsArray
    },
    preserve(){
      this.judgingParameters().then(result => {
        if(result){
          this.dataFromList.push(this.dataForm)
          this.stagingData()
          this.dataFormSubmitHandle()
        }
      }).catch(error => {
        // 处理错误情况
        console.error(error);
      });
    },
    //判断是否添加数据到数组中
    judgingParameters(){
      //判读必填项是否为空
      return new Promise((resolve,reject) => {
        this.$refs['dataForm'].validate((valid) => {
          if (!valid) {
            resolve(false)
          }
          if(this.dataFromList.length !== 0){
            //判断客户id,部品id,材质是否都一致
            const hasMatchingData = this.dataFromList.some(item =>{
              return (
                  item.partId === this.dataForm.partId &&
                  item.customerId === this.dataForm.customerId &&
                  item.materialQuality === this.dataForm.materialQuality
              );
            });
            resolve(!hasMatchingData);
          }else {
            resolve(true)
          }
        })
      })
    },
    handleSizeChange(val) {
      console.log(`每页 ${val} 条`);
    },
    //计算下标，并根据下标修改数组
    modifyArray(){
      //计算下标
      let number = (this.page - 1) * this.limit;
      this.dataFromList[number] = {
        ...this.dataFromList[number],
        ...this.dataForm
      }
    },
    handleCurrentChange(val) {
      this.dataForm = {
        ...this.dataForm,
        ...this.dataFromList[val-1]
      }
    },
    pushOrder() {
      this.judgingParameters().then(result => {
        if(result){
          this.dataFromList.push(this.dataForm)
          this.stagingData()
          this.$message({
            message: '添加成功',
            type: 'success'
          });
          this.initializationSection()
        }else {
        }
      }).catch(error => {
        // 处理错误情况
        console.error(error);
      });

    },
    //初始化部分属性
    initializationSection(){
      this.dataForm = {
        ...this.dataForm,
        ...this.initialData,
      }
    },
    addOrder(item){
      switch (item) {
        case 0:
          this.showOrders = 0
          break;
        case 1:
          this.showOrders = 1
          break;
        case 2:
          this.showOrders = 2
          break;
        case 3:
          this.showOrders = 3
          break;
      }
    },
    init () {
      this.visible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].resetFields()
        this.dataFromList = []
        if (this.dataForm.id) {
          this.getInfo()
        }
      })
    },
    // 获取信息
    getInfo () {
      this.$http.get(`/order/orderindication/${this.dataForm.id}`).then(({ data: res }) => {
        if (res.code !== 0) {
          return this.$message.error(res.msg)
        }
        this.dataForm = {
          ...this.dataForm,
          ...res.data
        }
      }).catch(() => {})
    },
    // 表单提交
    dataFormSubmitHandle: debounce(function () {
      this.$refs['dataForm'].validate((valid) => {
        if (!valid) {
          return false
        }
        let message = '';
        if(this.dataForm.id){
          message = '此操作将修改该数据'
        }else {
          message = '此操作将新增数据'
        }
        this.$confirm(message+', 是否继续?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
          center: true
        }).then(() => {
          this.$http[!this.dataForm.id ? 'post' : 'put']('/orders/orderindication/saveOrderIndication', this.dataFromList).then(({ data: res }) => {
            if (res.code !== 0) {
              return this.$message.error(res.msg)
            }
            this.$message({
              message: this.$t('prompt.success'),
              type: 'success',
              duration: 500,
              onClose: () => {
                this.$emit('refreshDataList')
                this.dataFromList = []
                this.$refs['dataForm'].resetFields()
                this.$router.replace('orders-orderindication');
              }
            })
          }).catch(() => {})
        }).catch(() => {
          this.$message({
            type: 'info',
            message: '已取消'
          });
        });
      })
    }, 1000, { 'leading': true, 'trailing': false })
  }
}
</script>

<style scoped>
.div{
  margin-top: 50px;
  margin-bottom: 20px;
}
.margins{
  margin-right: 100px;
}
.containers {
  display: flex;
  justify-content: center;
  align-items: center;
}
.rounded-div {
 /* border-radius: 10px;*/
}
</style>
