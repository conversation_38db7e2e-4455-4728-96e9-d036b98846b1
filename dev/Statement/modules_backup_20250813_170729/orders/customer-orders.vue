<template>
  <el-dialog :visible.sync="visible" :title="title" :close-on-click-modal="false" :close-on-press-escape="false" append-to-body>
    <div v-if="oneOrMore">
      <el-form :model="dataForm" :label-width="$i18n.locale === 'en-US' ? '120px' : '80px'">
        <div class="container">
          <el-form-item label="客户代码" prop="customerId">
            <customer-component v-model="dataForm.customerId" :query-name="true" placeholder="客户代码"></customer-component>
          </el-form-item>
          <el-form-item label="客户名称" prop="customerName">
            <span class="font_size">{{dataForm.customerName}}</span>
          </el-form-item>
          <el-form-item label="客户订单" prop="orderNumber">
            <el-input v-model="dataForm.orderNumber" placeholder="订单/预示号"></el-input>
          </el-form-item>
          <el-form-item label="下单日期" prop="orderDate">
            <el-date-picker
                v-model="dataForm.orderDate"
                type="date"
                format="yyyy 年 MM 月 dd 日"
                value-format="yyyy-MM-dd"
                placeholder="订单/预示日期">
            </el-date-picker>
          </el-form-item>
          <el-form-item label="上传订单" prop="orderDate">
            <el-input v-model="dataForm.orderDate" placeholder="订单/预示日期"></el-input>
          </el-form-item>
          <el-form-item label="交货地点" prop="deliveryLocationCode">
            <el-input v-model="dataForm.deliveryLocationCode" placeholder="交货地代码"></el-input>
          </el-form-item>
          <el-form-item label="联络人" prop="deliveryLocationCode">
            <el-input v-model="dataForm.deliveryLocationCode" placeholder="联络人"></el-input>
          </el-form-item>
          <el-form-item label="联络电话" prop="deliveryLocationCode">
            <el-input v-model="dataForm.deliveryLocationCode" placeholder="联络人电话"></el-input>
          </el-form-item>
          <el-form-item label="联络邮箱" prop="deliveryLocationCode">
            <el-input v-model="dataForm.deliveryLocationCode" placeholder="联络人邮箱"></el-input>
          </el-form-item>
          <el-form-item label="做成" prop="make">
            <employee-component v-model="dataForm.make" placeholder="做成"></employee-component>
          </el-form-item>
        </div>
        <div class="container">
          <el-form-item label="订单类型" prop="orderType">
            <ren-select v-model="dataForm.orderType" placeholder="订单类型" dict-type="business_order_type"></ren-select>
          </el-form-item>
          <el-form-item label="部品id" prop="partId">
            <part-number-component v-model="dataForm.partId" :customer-id="dataForm.customerId" placeholder="部品id"></part-number-component>
          </el-form-item>
          <el-form-item label="品名代码" prop="partNameCode">
            <el-input v-model="dataForm.partNameCode" placeholder="品名代码"></el-input>
          </el-form-item>
          <el-form-item label="材质" prop="materialQuality">
            <el-input v-model="dataForm.materialQuality" placeholder="材质"></el-input>
          </el-form-item>
          <el-form-item label="交制数量" prop="quantity">
            <el-input v-model="dataForm.quantity" placeholder="交制数量"></el-input>
          </el-form-item>
          <el-form-item label="客户交期" prop="deliveryDate">
            <el-date-picker
                v-model="dataForm.deliveryDate"
                type="date"
                format="yyyy 年 MM 月 dd 日"
                value-format="yyyy-MM-dd"
                placeholder="客户交期">
            </el-date-picker>
          </el-form-item>
          <el-form-item label="币别" prop="currency">
            <ren-select v-model="dataForm.currency" placeholder="币别" dict-type="currency"></ren-select>
          </el-form-item>
          <el-form-item label="单价" prop="unitPrice">
            <el-input v-model="dataForm.unitPrice" placeholder="单价"></el-input>
          </el-form-item>
          <el-form-item label="汇率" prop="exchangeRate">
            <el-input v-model="dataForm.exchangeRate" placeholder="汇率"></el-input>
          </el-form-item>
          <el-form-item label="税率" prop="taxRate">
            <el-input v-model="dataForm.taxRate" placeholder="税率"></el-input>
          </el-form-item>
          <el-form-item label="交易金额" prop="transactionAmount">
            <el-input v-model="dataForm.transactionAmount" placeholder="交易金额"></el-input>
          </el-form-item>
          <el-form-item label="摘要说明" prop="summaryDescription">
            <el-input v-model="dataForm.summaryDescription" placeholder="摘要说明"></el-input>
          </el-form-item>
          <el-form-item label="交货地点" prop="tradingLocations">
            <el-input v-model="dataForm.tradingLocations" placeholder="交货地点"></el-input>
          </el-form-item>
          <el-form-item label="报价单号" prop="quotationNumber">
            <el-input v-model="dataForm.quotationNumber" placeholder="报价单号"></el-input>
          </el-form-item>
        </div>
      </el-form>
    </div>
    <div v-else>
      <el-table height="500px" :data="dataFromList" border style="width: 100%;">
        <el-table-column prop="orderDate" label="下单日期" header-align="center" align="center"></el-table-column>
        <el-table-column prop="orderType" label="订单类型" header-align="center" align="center">
          <template slot-scope="scope">
            {{ $getDictLabel("business_order_type", scope.row.orderType) }}
          </template>
        </el-table-column>
        <el-table-column prop="orderNumber" label="订单/预示号" header-align="center" align="center"></el-table-column>
<!--        <el-table-column prop="orderControl" label="订单管制" header-align="center" align="center"></el-table-column>-->
        <el-table-column prop="trackingNumber" label="制交单号" header-align="center" align="center"></el-table-column>
<!--        <el-table-column prop="productionOrder" label="生产工单号" header-align="center" align="center"></el-table-column>-->
        <el-table-column prop="customerCode" label="客户代码" header-align="center" align="center"></el-table-column>
        <el-table-column prop="designation" label="部品番号" header-align="center" align="center"></el-table-column>
        <el-table-column prop="partNameCode" label="品名代码" header-align="center" align="center"></el-table-column>
        <el-table-column prop="quantity" label="交制数量" header-align="center" align="center"></el-table-column>
        <el-table-column prop="materialQuality" label="材质" header-align="center" align="center"></el-table-column>
      </el-table>
    </div>
  </el-dialog>
</template>

<script>
export default {
 data () {
    return {
      visible: false,
      oneOrMore: true,
      title:'',
      dataForm: {
        id: '',
        index: '',
        orderType: 0,
        sysUserId: '',
        orderControl: '',
        trackingNumber: '',
        productionOrder: '',
        customerId: '',
        customerName: '',
        partId: '',
        partNameCode: '',
        materialQuality: '',
        summaryDescription: '',
        orderNumber: '',
        orderLink: '',
        orderDate: '',
        quantity: 0,
        deliveryDate: '',
        completionDay: '',
        forecastBalance: '',
        deliveryLocationCode: '',
        shippingMethod: '',
        shipmentQuantity: '',
        outageQuantity: '',
        unitPrice: 0,
        currency: 0,
        exchangeRate: '',
        taxRate: '',
        transactionAmount: 0,
        amountReceivable: '',
        paidFor: '',
        notCollected: '',
        confirm: '',
        make: '',
        tradingLocations: '',
        quotationNumber: '',
        clearanceDate: '',
        paymentDate: '',
        remark: '',
        disabled: '',
        creator: '',
        createDate: '',
        updater: '',
        updateDate: ''
      },
      dataFromList:[],
      businessOrderId:'',
      trackingNumber:''
    }
  },
  methods: {
    // 获取订单信息
    getOrder () {
      this.$http.get(`/orders/businessorder/${this.businessOrderId}`).then(({ data: res }) => {
        if (res.code !== 0) {
          return this.$message.error(res.msg)
        }
        this.dataForm = {
          ...this.dataForm,
          ...res.data
        }
      }).catch(() => {})
    },
    // 根据制交单号获取相关订单信息
    getRelatedOrder () {
      this.$http.get(`/orders/businessorder/trackingNumberMatching/${this.trackingNumber}`).then(({ data: res }) => {
        if (res.code !== 0) {
          return this.$message.error(res.msg)
        }
        this.dataFromList = res.data
      }).catch(() => {})
    },
    init () {
      this.visible = true
      this.$nextTick(() => {
        if(this.oneOrMore){
          this.getOrder()
        }else {
          this.getRelatedOrder()
        }
      })
    },
  }
}
</script>
