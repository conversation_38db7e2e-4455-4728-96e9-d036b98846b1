<template>
  <el-dialog :visible.sync="visible" :title="!dataForm.id ? '新增会签内容' : '修改会签内容'" :close-on-click-modal="false" :close-on-press-escape="false" append-to-body>
    <el-form :model="dataForm" :rules="dataRule" ref="dataForm" @keyup.enter.native="dataFormSubmitHandle()" :label-width="$i18n.locale === 'en-US' ? '120px' : '80px'">
      <div class="container">
        <el-form-item label="客户代码" prop="customerId">
          <customer-component v-model="dataForm.customerId" placeholder="客户代码"></customer-component>
        </el-form-item>
        <el-form-item label="部品品番" prop="partId">
          <part-number-component v-model="dataForm.partId" placeholder="部品品番"></part-number-component>
        </el-form-item>
        <el-form-item label="客户订单" prop="businessOrderId">
          <business-order-component v-model="dataForm.businessOrderId"
                                    placeholder="客户订单"></business-order-component>
        </el-form-item>
        <el-form-item label="制交单号" prop="trackingNumber">
          <el-input v-model="dataForm.trackingNumber" placeholder="制交单号"></el-input>
        </el-form-item>
        <el-form-item label="会签" prop="countersign">
          <ren-select v-model="dataForm.countersign" placeholder="会签 0:未完成 1:已完成" dict-type="countersign"></ren-select>
        </el-form-item>
        <el-form-item label="会签人" prop="countersigner">
          <employee-component v-model="dataForm.countersigner" placeholder="会签人"></employee-component>
        </el-form-item>
        <el-form-item label="会签部门" prop="countersignDept">
          <ren-dept-tree v-model="dataForm.countersignDept" placeholder="会签部门" :dept-name.sync="dataForm.deptName"></ren-dept-tree>
        </el-form-item>
        <el-form-item label="会签意见" prop="countersigningOpinions">
          <el-input v-model="dataForm.countersigningOpinions" placeholder="会签意见"></el-input>
        </el-form-item>
      </div>
      <el-form-item label="备注" prop="remark">
        <el-input v-model="dataForm.remark" placeholder="备注"></el-input>
      </el-form-item>
    </el-form>
    <template v-if="displayComponent" slot="footer">
      <el-button @click="visible = false">{{ $t('cancel') }}</el-button>
      <el-button type="primary" @click="dataFormSubmitHandle()">{{ $t('confirm') }}</el-button>
    </template>
  </el-dialog>
</template>

<script>
import debounce from 'lodash/debounce'
import mixinViewModule from '@/mixins/view-module'
export default {
  mixins:[mixinViewModule],
  data () {
    return {
      visible: false,
      //创建该页面时，不调用数据查询接口
      mixinViewModuleOptions: {
        createdIsNeed: false,
      },
      dataForm: {
        id: '',
        customerId: '',
        partId: '',
        businessOrderId: '',
        trackingNumber: '',
        countersign: 0,
        countersigner: '',
        countersignDept:'',
        deptName: '',
        countersigningOpinions: '',
        remark: '',
        disabled: '',
        creator: '',
        createDate: '',
        updater: '',
        updateDate: ''
      },
      displayComponent:true
    }
  },
  computed: {
    dataRule () {
      return {
          id: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          customerId: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          partId: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          businessOrderId: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          trackingNumber: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          countersign: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          countersigner: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          countersignDept: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          countersigningOpinions: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],}
    }
  },
  methods: {
    dataInit (data) {
      this.visible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].resetFields()
        this.dataForm = {
          ...this.dataForm,
          ...data
        }
        let user = JSON.parse(sessionStorage.getItem('users'));
        this.dataForm.countersignDept =  window.SITE_CONFIG['users'].deptId
        this.dataForm.deptName =  user.deptName
      })
    },
    init () {
      this.visible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].resetFields()
        if (this.dataForm.id) {
          this.getInfo()
        }
      })
    },
    // 获取信息
    getInfo () {
      this.$http.get(`/orders/ordercountersigned/${this.dataForm.id}`).then(({ data: res }) => {
        if (res.code !== 0) {
          return this.$message.error(res.msg)
        }
        this.dataForm = {
          ...this.dataForm,
          ...res.data
        }
      }).catch(() => {})
    },
    // 表单提交
    dataFormSubmitHandle: debounce(function () {
      this.$refs['dataForm'].validate((valid) => {
        if (!valid) {
          return false
        }
        let message = '';
        if(this.dataForm.id){
          message = '此操作将修改该数据'
        }else {
          message = '此操作将新增一条数据'
        }
        this.$confirm(message+', 是否继续?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
          center: true
        }).then(() => {
          this.$http[!this.dataForm.id ? 'post' : 'put']('/orders/ordercountersigned/', this.dataForm).then(({ data: res }) => {
            if (res.code !== 0) {
            return this.$message.error(res.msg)
            }
            this.$message({
          message: this.$t('prompt.success'),
            type: 'success',
                    duration: 500,
                    onClose: () => {
              this.visible = false
              this.$emit('refreshDataList')
            }
          })
          }).catch(() => {})
        }).catch(() => {
          this.$message({
            type: 'info',
            message: '已取消'
          });
        });
      })
    }, 1000, { 'leading': true, 'trailing': false })
  }
}
</script>
