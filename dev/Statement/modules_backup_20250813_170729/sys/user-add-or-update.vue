<template>
  <el-dialog :visible.sync="visible" :title="!dataForm.id ? $t('add') : $t('update')" :close-on-click-modal="false" :close-on-press-escape="false" append-to-body>
    <el-form :model="dataForm" :rules="dataRule" ref="dataForm" @keyup.enter.native="dataFormSubmitHandle()" label-width="120px">
<!--      <el-form-item prop="username" :label="$t('user.username')">
        <el-input v-model="dataForm.username" :placeholder="$t('user.username')"></el-input>
      </el-form-item>-->
      <el-form-item prop="employeeId" :label="$t('user.employeeId')">
        <el-input v-model="dataForm.employeeId" :placeholder="$t('user.employeeId')"></el-input>
      </el-form-item>
      <el-form-item prop="deptName" :label="$t('user.deptName')">
        <ren-dept-tree v-model="dataForm.deptId" :placeholder="$t('dept.title')" :dept-name.sync="dataForm.deptName"></ren-dept-tree>
      </el-form-item>
      <el-form-item prop="mobile" :label="$t('user.mobile')">
        <el-input v-model="dataForm.mobile" :placeholder="$t('user.mobile')"></el-input>
      </el-form-item>
<!--      <el-form-item prop="password" :label="$t('user.password')" :class="{ 'is-required': !dataForm.id }">-->
<!--        <el-input v-model="dataForm.password" type="password" :placeholder="$t('user.password')"></el-input>-->
<!--      </el-form-item>-->
<!--      <el-form-item prop="confirmPassword" :label="$t('user.confirmPassword')" :class="{ 'is-required': !dataForm.id }">-->
<!--        <el-input v-model="dataForm.confirmPassword" type="password" :placeholder="$t('user.confirmPassword')"></el-input>-->
<!--      </el-form-item>-->
      <el-form-item prop="surname" :label="$t('user.surname')">
        <el-input v-model="dataForm.surname" :placeholder="$t('user.surname')"></el-input>
      </el-form-item>
      <el-form-item prop="name" :label="$t('user.name')">
        <el-input v-model="dataForm.name" :placeholder="$t('user.name')"></el-input>
      </el-form-item>
<!--      <el-form-item prop="name" label="员工照片">
        <el-upload
            v-if="$hasPermission('sys:user:save')"
            class="upload-demo"
            :action="addImageUrl"
            :headers="headers"
            :on-preview="handlePreview"
            :on-remove="handleRemove"
            :on-success="handleSuccess"
            :on-change="handleChange"
            :on-exceed="handleExceed"
            :limit="3"
            multiple
            ref="upload"
            :auto-upload="false"
            :file-list="fileList">
          <el-button slot="trigger" size="small" type="primary">选取文件</el-button>
          <div slot="tip" class="el-upload__tip">只能上传jpg/png文件，且不超过500kb</div>
        </el-upload>
      </el-form-item>-->
      <el-form-item label="班别">
        <ren-radio-tick v-model="dataForm.documentPicture" dict-type="group_level"></ren-radio-tick>
      </el-form-item>
      <el-form-item prop="position" label="职位">
        <el-input v-model="dataForm.position" placeholder="请输入职位"></el-input>
      </el-form-item>
      <el-form-item prop="rank" label="职等">
        <el-input v-model="dataForm.rank" placeholder="职等"></el-input>
      </el-form-item>
      <el-form-item prop="gender" :label="$t('user.gender')">
        <ren-radio-group v-model="dataForm.gender" dict-type="gender"></ren-radio-group>
      </el-form-item>
      <el-form-item prop="email" :label="$t('user.email')">
        <el-input v-model="dataForm.email" :placeholder="$t('user.email')"></el-input>
      </el-form-item>
      <el-form-item prop="birthday" :label="$t('user.birthday')">
        <el-date-picker
            v-model="dataForm.birthday"
            type="datetime"
            value-format="yyyy-MM-dd HH:mm"
            placeholder="选择日期">
        </el-date-picker>
      </el-form-item>
      <el-form-item prop="hireDate" :label="$t('user.hireDate')">
        <el-date-picker
            v-model="dataForm.hireDate"
            type="date"
            value-format="yyyy-MM-dd"
            placeholder="选择日期">
        </el-date-picker>
      </el-form-item>
      <el-form-item label="员工状态" prop="employmentStatus">
        <ren-select v-model="dataForm.employmentStatus" placeholder="员工状态" dict-type="employment_status"></ren-select>
      </el-form-item>
      <el-form-item v-if="$hasPermission('sys:user:roleIdList')" prop="roleIdList" :label="$t('user.roleIdList')" class="role-list">
        <el-select v-model="dataForm.roleIdList" multiple :placeholder="$t('user.roleIdList')">
          <el-option v-for="role in roleList" :key="role.id" :label="role.name" :value="role.id"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item prop="status" :label="$t('user.status')" size="mini">
        <el-radio-group v-model="dataForm.status">
          <el-radio :label="0">{{ $t('user.status0') }}</el-radio>
          <el-radio :label="1">{{ $t('user.status1') }}</el-radio>
        </el-radio-group>
      </el-form-item>
    </el-form>
    <template slot="footer">
      <el-button @click="visible = false">{{ $t('cancel') }}</el-button>
      <el-button type="primary" @click="dataFormSubmitHandle()">{{ $t('confirm') }}</el-button>
    </template>
  </el-dialog>
</template>

<script>
import debounce from 'lodash/debounce'
import { isEmail, isMobile } from '@/utils/validate'
import Cookies from "js-cookie";
export default {
  data () {
    return {
      visible: false,
      selectedFile : '',
      roleList: [],
      roleIdListDefault: [],
      origin:{
        company:'',
        department:'',
        section:'',
        groupLevel:''
      },
      headers: {'token': Cookies.get('token')},
      addImageUrl: window.SITE_CONFIG['apiURL'] + '/sys/user/api/uploadFile',
      fileList:[],
      dataForm: {
        id: '',
        username: '',
        deptId: '',
        position: '',
        deptName: '',
        password: '',
        confirmPassword: '',
        surname: '',
        name: '',
        section:'',
        group:'',
        jobTitle: '',
        rank: '',
        file: '',
        documentId: '',
        documentPicture: '',
        documentType: '',
        employmentStatus:0,
        birthday: '',
        gender: 0,
        email: '',
        mobile: '',
        employeeId: '',
        hireDate: '',
        roleIdList: [],
        status: 1
      },
      employmentStatus:[{
        value: 0,
        label: '在职'
      }, {
        value: 1,
        label: '离职'
      },{
        value: 2,
        label: '自离'
      },{
        value: 3,
        label: '修养'
      },{
        value: 4,
        label: '退休'
      },{
        value: 5,
        label: '留职停薪'
      },{
        value: 6,
        label: '开除'
      }],
    }
  },
  computed: {
    dataRule () {
      var validatePassword = (rule, value, callback) => {
        if (!this.dataForm.id && !/\S/.test(value)) {
          return callback(new Error(this.$t('validate.required')))
        }
        callback()
      }
      var validateConfirmPassword = (rule, value, callback) => {
        if (!this.dataForm.id && !/\S/.test(value)) {
          return callback(new Error(this.$t('validate.required')))
        }
        if (this.dataForm.password !== value) {
          return callback(new Error(this.$t('user.validate.confirmPassword')))
        }
        callback()
      }
      var validateEmail = (rule, value, callback) => {
        if (value && !isEmail(value)) {
          return callback(new Error(this.$t('validate.format', { 'attr': this.$t('user.email') })))
        }
        callback()
      }
      var validateMobile = (rule, value, callback) => {
        if (value && !isMobile(value)) {
          return callback(new Error(this.$t('validate.format', { 'attr': this.$t('user.mobile') })))
        }
        callback()
      }
      return {
        employeeId: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        deptName: [
          { required: true, message: this.$t('validate.required'), trigger: 'change' }
        ],
        password: [
          { validator: validatePassword, trigger: 'blur' }
        ],
        confirmPassword: [
          { validator: validateConfirmPassword, trigger: 'blur' }
        ],
        realName: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        email: [
          { validator: validateEmail, trigger: 'blur' }
        ],
        mobile: [
          { required: true, validator: validateMobile, trigger: 'blur' }
        ]
      }
    }
  },
  watch:{
    "dataForm.mobile"(){
      if(this.dataForm.mobile.length > 6){
        this.dataForm.password = this.dataForm.mobile.substr(-6)
      }
    }
  },
  methods: {
    handleChange(file, fileList){
      this.fileList = fileList
      console.log(file,'添加文件')
      console.log(fileList,'文件列表')
    },
    handleExceed(files, fileList) {
      this.$message.warning(`当前限制选择 3 个文件，本次选择了 ${files.length} 个文件，共选择了 ${files.length + fileList.length} 个文件`);
    },
    /**
     *
     * @param response 服务器返回的数据
     * @param file 上传的文件
     * @param fileList 当前文件列表
     */
    handleSuccess(response, file, fileList) {
      debugger
      // 处理文件上传成功后的逻辑
      console.log(response)
      if (response.code != 0) {
        this.$message.error(response.msg)
      }
      console.log(this.dataForm,new Date(),'传照片')
    },
    handleRemove(file, fileList) {
      console.log(file, fileList);
    },
    handlePreview(file) {
      console.log(file);
    },
    // 上传照片
    submitUpload() {
      this.$refs.upload.submit();
    },
    init () {
      this.visible = true
      this.dataForm.deptId = ''
      this.$nextTick(() => {
        this.$refs['dataForm'].resetFields()
        this.roleIdListDefault = []
        this.fileList = []
        Promise.all([
          this.getRoleList()
        ]).then(() => {
          if (this.dataForm.id) {
            this.getInfo()
          }
        })
      })
    },
    // 获取角色列表
    getRoleList () {
      return this.$http.get('/sys/role/list').then(({ data: res }) => {
        if (res.code !== 0) {
          return this.$message.error(res.msg)
        }
        this.roleList = res.data
      }).catch(() => {})
    },
    // 获取信息
    getInfo () {
      this.$http.get(`/sys/user/${this.dataForm.id}`).then(({ data: res }) => {
        if (res.code !== 0) {
          return this.$message.error(res.msg)
        }
        this.dataForm = {
          ...this.dataForm,
          ...res.data,
          /*roleIdList: []*/
        }
        this.origin.company = this.dataForm.jobTitle.slice(0,2)
        this.origin.department = this.dataForm.jobTitle.slice(2,4)
        this.origin.section = this.dataForm.jobTitle.slice(4,6)
        this.origin.groupLevel = this.dataForm.jobTitle.slice(6,8)
        // 角色配置, 区分是否为默认角色
        for (var i = 0; i < res.data.roleIdList.length; i++) {
          if (this.roleList.filter(item => item.id === res.data.roleIdList[i])[0]) {
            this.dataForm.roleIdList.push(res.data.roleIdList[i])
            continue
          }
          this.roleIdListDefault.push(res.data.roleIdList[i])
        }
      }).catch(() => {})
    },
    // 表单提交
    dataFormSubmitHandle: debounce(function () {
      /*this.dataForm.jobTitle = this.origin.company+this.origin.department+this.origin.section+this.origin.groupLevel*/
      this.$refs['dataForm'].validate((valid) => {
        if (!valid) {
          return false
        }
        /*console.log(this.fileList,'文件')
        if (this.dataForm.documentId && this.fileList.length != 0){
          this.$http.delete('/fabricate/part/deleteImage?id=' + this.dataForm.documentId)
          this.dataForm.documentId = ''
          this.dataForm.documentPicture = ''
        }
        this.submitUpload()
        console.log(this.dataForm,new Date(),'提交')*/
        this.$http[!this.dataForm.id ? 'post' : 'put']('/sys/user', {
          ...this.dataForm,
          roleIdList: [
            ...this.dataForm.roleIdList,
            ...this.roleIdListDefault
          ]
        }).then(({ data: res }) => {
          if (res.code !== 0) {
            return this.$message.error(res.msg)
          }
          this.$message({
            message: this.$t('prompt.success'),
            type: 'success',
            duration: 500,
            onClose: () => {
              this.visible = false
              this.$emit('refreshDataList')
            }
          })
        }).catch(() => {})
      })
    }, 1000, { 'leading': true, 'trailing': false })
  }
}
</script>

<style lang="scss">
.mod-sys__user {
  .role-list {
    .el-select {
      width: 100%;
    }
  }
}
</style>
