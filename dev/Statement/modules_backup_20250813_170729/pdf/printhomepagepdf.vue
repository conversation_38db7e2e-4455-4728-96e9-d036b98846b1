<template>
  <el-card shadow="never" class="aui-card--fill">
    <div class="mod-pdf__printhomepagepdf">
      <h1 :style="{ textAlign: 'center' }">
        生产流程卡 打印
      </h1>
      <div class="container">
        <div class="make_one" @click="processCardPdf('制一')">
          制一流程卡
        </div>
        <div class="make_two" @click="processCardPdf('制二')">
          制二流程卡
        </div>
        <div class="reprint_one" @click="processCardPdf('补制一')">
          补印制一流程卡
        </div>
        <div class="reprint_two" @click="processCardPdf('补制二')">
          补印制二流程卡
        </div>
      </div>
    </div>
  </el-card>

</template>

<script>
import debounce from 'lodash/debounce'
import axios from "axios";
import mixinViewModule from '@/mixins/view-module'
import {addDynamicRoute} from '@/router'

export default {
  mixins: [mixinViewModule],
  data() {
    return {
      imageUrl: '',
      visible: false,
      //创建该页面时，不调用数据查询接口
      mixinViewModuleOptions: {
        createdIsNeed: false,
      },
      dataForm: {

      },
    }

  },
  computed: {
    dataRule() {
      return {

      }
    }
  },
  watch:{

  },
  methods: {
    processCardPdf(mark){
      let dept = ''
      if (mark.includes('制一')){
        dept = ['1641701467113046018']
      }else if (mark.includes('制二')){
        dept = ['1641701552458743810']
      }
      // 路由参数
      const routeParams = {
        routeName: `${this.$route.name}__processcardpdf`,
        title: `流程卡pdf生成`,
        path: 'pdf/processcardpdf',
        params: {
          deptId:dept
        }
      }
      // 动态路由
      addDynamicRoute(routeParams, this.$router)
    },
  }
}
</script>

<style scoped>
.container {
  display: flex;
  justify-content: center;
  align-items: center;
}

.box {
  width: 40%;
  height: 100px;
  line-height: 30%;
  border-radius: 20px;
}

.make_one, .make_two, .reprint_one, .reprint_two {
  text-align: center;
  line-height: 100px;
  border-radius: 20px;
  margin: 10px;
  width: 40%;
  height: 100px;
  font-size: 1.8vw;
  font-size: 1.8vh;
}

.make_one {
  background-color: #1764f6; /* 制一流程卡的背景色 */
}

.make_two {
  background-color: #1764f6; /* 制二流程卡的背景色 */
}

.reprint_one {
  background-color: #6f9df5; /* 补印制一流程卡的背景色 */
}

.reprint_two {
  background-color: #6f9df5; /* 补印制二流程卡的背景色 */
}

/* 媒体查询，根据页面宽度进行调整 */
@media screen and (max-width: 600px) {
  .make_one, .make_two, .reprint_one, .reprint_two {
    flex-basis: 100%; /* 当页面宽度小于 600px 时，每个板块占满一行 */
    height: 25%; /* 调整高度 */
  }
}
</style>

