<template>
  <el-card shadow="never" class="aui-card--fill">
    <div class="mod-pdf__processcardpdf">
      <el-form :model="dataForm" :rules="dataRule" ref="dataForm" :inline="true"
               @keyup.enter.native="dataFormSubmitHandle()"
               :label-width="$i18n.locale === 'en-US' ? '120px' : '80px'">
        <div>
          <div>
            <el-form-item label="参与部门:">
              <el-select multiple @change="getUserList" v-if="deptList" v-model="paramForm.deptIdList">
                <el-option v-for="(item,index) in deptList" :key="index" :label="item.name" :value="item.id"></el-option>
              </el-select>
              <el-select multiple @change="getUserList" v-else v-model="paramForm.deptIdList">
                <el-option label="制一部" :value="1641701467113046018"></el-option>
                <el-option label="制二部" :value="1641701552458743810"></el-option>
                <el-option label="制三部" :value="1641701649229725698"></el-option>
                <el-option label="制四部" :value="1641701777952915457"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="班别:">
              <ren-select v-model="paramForm.formingClass" placeholder="班别" dict-type="group_level"></ren-select>
            </el-form-item>
            <el-form-item label="打印次批:">
              <el-select @change="getFlowCardPdf" v-model="subBatchId">
                <el-option v-for="(item,index) in subBatchList" :key="index" :label="item.subBatchNumber" :value="item.id"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="打印机:">
              <el-select v-model="printerName">
                <el-option v-for="(item,index) in printers" :key="index" :label="item" :value="item"></el-option>
              </el-select>
            </el-form-item>
          </div>
          <div class="container">
            <el-button
                v-for="(item, index) in items"
                :key="index"
                :style="{ backgroundColor: item.color }"
                @click="changeColor(index)">
              {{ item.text }}
            </el-button>
          </div>
          <ren-radio-group v-model="dataForm.requisition" dict-type="requisition"></ren-radio-group>
          <div class="container">
            <el-form-item label="领料日期" prop="requisitionDate">
              <el-date-picker
                  class="datePicker"
                  v-model="dataForm.requisitionDate"
                  type="datetime"
                  value-format="yyyy-MM-dd HH:mm:ss"
                  placeholder="领料日期(含时分)">
              </el-date-picker>
            </el-form-item>

            <el-form-item label="生产日期" prop="manufacturingTime">
              <el-date-picker
                  class="datePicker"
                  v-model="dataForm.manufacturingTime"
                  type="datetime"
                  value-format="yyyy-MM-dd HH:mm:ss"
                  placeholder="生产日期(含时分)">
              </el-date-picker>
            </el-form-item>

            <el-form-item label="领料人" prop="picker">
              <employee-component
                  v-model="picker"
                  placeholder="领料人">
              </employee-component>
            </el-form-item>
          </div>
          <div class="container">
            <el-form-item label="制造指令" prop="manufacturingInstructions">
              <el-input v-model="dataForm.manufacturingInstructions" placeholder="制造指令"></el-input>
            </el-form-item>
            <el-form-item label="生产批号" prop="batchNumber">
              <el-input v-model="dataForm.batchNumber" placeholder="生产批号"></el-input>
            </el-form-item>
            <el-form-item label="配方编号" prop="formulationCode">
              <el-input v-model="dataForm.formulationCode"></el-input>
            </el-form-item>
          </div>
          <div class="container">
            <el-form-item label="品名代码" prop="partCode">
              <el-input v-model="dataForm.partCode" placeholder="品名代码"></el-input>
            </el-form-item>
            <el-form-item label="客户品名" prop="designation">
              <part-number-component v-model="dataForm.designation" placeholder="客户品名"></part-number-component>
            </el-form-item>
            <el-form-item label="客户代码" prop="customerCode">
              <customer-component v-model="dataForm.customerCode" placeholder="客户代码"></customer-component>
            </el-form-item>
          </div>
          <div class="container">
            <el-form-item label="出片尺寸" prop="location">
              <div class="grid_layout" style="grid-template-columns: 0.5fr 0.1fr 0.5fr 0.1fr;">
                <el-input v-model="dataForm.wideSize" placeholder="宽"></el-input>mm
                <el-input v-model="dataForm.dimensionsThick" placeholder="厚"></el-input>mm
              </div>
            </el-form-item>
            <el-form-item label="重量" prop="weight">
              <el-input v-model="dataForm.weight" placeholder="重量"></el-input>
            </el-form-item>
            <el-form-item label="开炼时间" prop="openingTime">
              <el-input v-model="dataForm.openingTime" placeholder="开炼时间"></el-input>
            </el-form-item>
            <el-form-item label="有效时间" prop="validityPeriod">
              <el-input v-model="dataForm.validityPeriod" placeholder="有效时间"></el-input>
            </el-form-item>
            <el-form-item label="切料" prop="cutter">
              <el-input v-model="dataForm.cutter" placeholder="切料"></el-input>
            </el-form-item>
            <el-form-item label="条重" prop="barWeightOne">
              <div class="grid_layout" style="grid-template-columns: 0.5fr 0.1fr 0.5fr 0.1fr 0.5fr 0.1fr;">
                <el-input v-model="dataForm.barWeightOne" placeholder="条重"></el-input>g~
                <el-input v-model="dataForm.barWeightTwo" placeholder="条重"></el-input>g
                <el-input v-model="dataForm.numberOfLines" placeholder="条数"></el-input>条
              </div>
            </el-form-item>
            <el-form-item label="补料" prop="nickname">
              <div class="grid_layout" style="grid-template-columns: 0.5fr 0.1fr 0.5fr 0.1fr 0.5fr 0.1fr;">
                <el-input v-model="dataForm.feedingOne" placeholder="补料"></el-input>g~
                <el-input v-model="dataForm.feedingTwo" placeholder="补料"></el-input>g
                <el-input v-model="dataForm.feedingQuantity" placeholder="条数"></el-input>条
              </div>
            </el-form-item>
            <el-form-item label="回头料重" prop="feedback">
              <el-input v-model="dataForm.feedback" placeholder="回头料重"></el-input>
            </el-form-item>
            <el-form-item label="加硫时间" prop="vulcanizationTime">
              <el-input v-model="dataForm.vulcanizationTime" placeholder="加硫时间"></el-input>
            </el-form-item>
            <el-form-item label="加硫温度" prop="sulfurizationTemperature">
              <el-input v-model="dataForm.sulfurizationTemperature" placeholder="加硫温度"></el-input>
            </el-form-item>
            <el-form-item label="机台压力" prop="machinePressure">
              <el-input v-model="dataForm.machinePressure" placeholder="机台压力"></el-input>
            </el-form-item>
            <el-form-item label="生产机台" prop="machineId">
              <el-input v-model="dataForm.machineId" placeholder="生产机台"></el-input>
            </el-form-item>
            <el-form-item label="模具编号" prop="moldNumber">
              <el-input v-model="dataForm.moldNumber" placeholder="模具编号"></el-input>
            </el-form-item>
            <el-form-item label="模具孔数" prop="numberOfHoles">
              <el-input v-model="dataForm.numberOfHoles" placeholder="模具孔数"></el-input>
            </el-form-item>
            <el-form-item label="镶件名称" prop="insertName">
              <el-input v-model="dataForm.insertName" placeholder="镶件名称"></el-input>
            </el-form-item>
            <el-form-item label="标准产量" prop="standardCapacity">
              <el-input v-model="dataForm.standardCapacity" placeholder="标准产量"></el-input>
            </el-form-item>
<!--            <el-form-item label="生产模数" prop="productionModulus">
              <el-input v-model="dataForm.productionModulus" placeholder="生产模数"></el-input>
            </el-form-item>
            <el-form-item label="实际生产数" prop="actualCapacity">
              <el-input v-model="dataForm.actualCapacity" placeholder="实际生产数"></el-input>
            </el-form-item>
            <el-form-item label="成形日期" prop="moldingDate">
              <el-date-picker
                  class="datePicker"
                  v-model="dataForm.moldingDate"
                  type="date"
                  value-format="yyyy-MM-dd"
                  placeholder="成形日期">
              </el-date-picker>
            </el-form-item>
            <el-form-item label="每包数量" prop="quantityPerPackage">
              <el-input v-model="dataForm.quantityPerPackage" placeholder="每包数量"></el-input>
            </el-form-item>-->
            <el-form-item label="产品单重" prop="singleWeight">
              <el-input v-model="dataForm.singleWeight" placeholder="产品单重"></el-input>
            </el-form-item>
<!--            <el-form-item label="每包重量" prop="weightPerPackage">
              <el-input v-model="dataForm.weightPerPackage" placeholder="每包重量"></el-input>
            </el-form-item>
            <el-form-item label="每箱数量" prop="amountPerBox">
              <el-input v-model="dataForm.amountPerBox" placeholder="每箱数量"></el-input>
            </el-form-item>-->
<!--            <el-form-item label="实际包装数量" prop="actualPackagingQuantity">
              <el-input v-model="dataForm.actualPackagingQuantity" placeholder="实际包装数量"></el-input>
            </el-form-item>
            <el-form-item label="包装日期" prop="packingDate">
              <el-date-picker
                  class="datePicker"
                  v-model="dataForm.packingDate"
                  type="date"
                  value-format="yyyy-MM-dd"
                  placeholder="包装日期">
              </el-date-picker>
            </el-form-item>
            <el-form-item label="余数" prop="remainder">
              <el-input v-model="dataForm.remainder" placeholder="余数"></el-input>
            </el-form-item>
            <el-form-item label="余数出货日期" prop="remainingShippingDate">
              <el-date-picker
                  class="datePicker"
                  v-model="dataForm.remainingShippingDate"
                  type="date"
                  value-format="yyyy-MM-dd"
                  placeholder="余数出货日期">
              </el-date-picker>
            </el-form-item>
            <el-form-item label="出货确认" prop="shippingConfirmation">
              <el-input v-model="dataForm.shippingConfirmation" placeholder="出货确认"></el-input>
            </el-form-item>-->
          </div>
        </div>
        <div class="grid_layout" style="grid-template-columns: 1fr 1fr;">
          <el-button type="danger" @click="init()" round>清除</el-button>
          <el-button type="primary" @click="dataFormSubmitHandle()" round>生成流程卡</el-button>
        </div>
      </el-form>
    </div>
  </el-card>

</template>

<script>
import debounce from 'lodash/debounce'
import axios from "axios";
import mixinViewModule from '@/mixins/view-module'
import PublicFunctions from '@/mixins/public-functions'
import {addDynamicRoute} from '@/router'
import qs from "qs";
import Cookies from "js-cookie";

export default {
  //导航守卫,当路由发生变化时调用函数
  beforeRouteEnter(to, from, next) {
    next(vm => {
      // 调用要运行的函数
      vm.init();
    });
  },
  mixins: [mixinViewModule,PublicFunctions],
  data() {
    return {
      imageUrl: '',
      visible: false,
      //创建该页面时，不调用数据查询接口
      mixinViewModuleOptions: {
        createdIsNeed: false,
      },
      paramForm:{
        formingClass: 0, // 班别
        deptIdList: this.$route.params.deptId,
      },
      deptList:[],
      subBatchList:[],
      printers:[],
      batchId:'',
      subBatchId:'',
      printerName:'',// 打印机名称
      picker:'',// 领料人id
      dataForm: {
        manufacturingInstructions: '',
        requisition: 0, // 领料
        requisitionDate: '', // 领料时间
        picker: '', // 领料人
        manufacturingTime: '', // 生产日期
        technician: '', // 成形操作员
        batchNumber: '',
        formulationCode: '',
        partCode: '',
        designation: '',
        customerCode: '',
        wideSize: '',
        dimensionsThick: '',
        weight: '',
        openingTime: '',
        validityPeriod: '',
        cutter: '',
        barWeightOne: '',
        barWeightTwo: '',
        numberOfLines: '',
        feedingOne: '',
        feedingTwo: '',
        feedingQuantity: '',
        feedback: '',
        vulcanizationTime: '',
        sulfurizationTemperature: '',
        machinePressure: '',
        machineId: '',
        moldNumber: '',
        numberOfHoles: '',
        insertName: '',
        standardCapacity: '',
        productionModulus: '',
        actualCapacity: '',
        moldingDate: '',
        quantityPerPackage: '',
        singleWeight: '',
        weightPerPackage: '',
        amountPerBox: '',
        actualPackagingQuantity: '',
        packingDate: '',
        remainder: '',
        remainingShippingDate: '',
        shippingConfirmation: '',

      },
      items: [],
    }

  },
  computed: {
    dataRule() {
      return {
        designation: [
          {required: true, message: this.$t('validate.required'), trigger: 'blur'}
        ],
        type: [
          {required: true, message: this.$t('validate.required'), trigger: 'blur'}
        ],
        productionCategory: [
          {required: true, message: this.$t('validate.required'), trigger: 'blur'}
        ],
        printerName: [
          {required: true, message: '请选择打印机', trigger: 'blur'}
        ],
      }
    }
  },
  watch:{
    picker(){
      //拿到下拉列表value所对应的整个对象的数据
      const selectedItem = window.SITE_CONFIG['employeesList'].find(item => item.value === value);
      this.dataForm.picker = selectedItem.label
    }
  },
  mounted() {
    this.getPrinters()
  },
  methods: {
    // 获取打印机列表
    getPrinters() {

    },
    searchByDept(){

    },
    changeColor(index) {
      if (this.items[index].color === 'red' || this.items[index].color === 'yellow') {
        this.items[index].color = '#f4dbf8'; // 恢复默认颜色
      } else if (this.items[index].color === '#f4dbf8') {
        const hasRed = this.items.some(item => item.color === 'red')
        const hasYellow = this.items.some(item => item.color === 'yellow')

        if (!hasRed) { // 如果没有红色的
          this.items[index].color = 'red';
          let extractedContent = this.afterBrackets(this.items[index].text);
          let commaIndex = this.dataForm.technician.indexOf('、');
          if (commaIndex !== -1) {
            // 从字符串开始截取到、后面的部分
            this.dataForm.technician = extractedContent + "、" + this.dataForm.technician.substring(commaIndex + 1);
          } else {
            this.dataForm.technician = extractedContent;
          }
        } else if (!hasYellow) { // 如果没有黄色的
          this.items[index].color = 'yellow';
          let extractedContent = this.afterBrackets(this.items[index].text);
          let commaIndex = this.dataForm.technician.indexOf('、');
          if (commaIndex === -1) {
            this.dataForm.technician = this.dataForm.technician + "、" + extractedContent;
          } else {
            let extractedContentBeforeComma = this.dataForm.technician.substring(0, commaIndex);
            this.dataForm.technician = extractedContentBeforeComma + "、" + extractedContent;
          }
        }
        console.log(this.dataForm.technician,'技术员')
      }
    },
    // 截取括号后面的内容
    afterBrackets(label) {
      // 查找括号的位置
      const index = label.indexOf(')');
      if (index !== -1) {
        // 使用substring截取括号后面的所有字符
        let extractedContent = label.substring(index+1 , label.length);
        return extractedContent;
      }
    },
    init() {
      this.visible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].resetFields()
        this.getUserList()
        this.getDeptList()
        this.getSubBatchList()
        this.getPrintServicesNameList()
      })
    },
    getUserList(){
      this.$http.post(`/sys/user/getUserListByDeptId`,this.paramForm.deptIdList).then(({data: res}) =>{
        if (res.code !== 0) {
          return this.$message.error(res.msg)
        }
        this.items = []
        res.data.forEach((item, index) => {
          this.items.push({text: `(${item.employeeId})${item.username}`, color: '#f4dbf8', originalColor: '#f4dbf8'})
        })
      })
    },
    getDeptList(){
      this.$http.get(`/sys/dept/processDepartment`).then(({data: res}) =>{
        if (res.code !== 0) {
          return this.$message.error(res.msg)
        }
        this.deptList = res.data
      })
    },
    getFlowCardPdf(){
      this.$http.get(`/batch/subbatch/getFlowCardPdf/${this.subBatchId}`).then(({data: res}) =>{
        if (res.code !== 0) {
          return this.$message.error(res.msg)
        }
        this.dataForm = {
          ...this.dataForm,
          ...res.data
        }
      })
    },
    /**
     *  获取打印机名称列表
     */
    getPrintServicesNameList(){
      this.$http.get(`/batch/subbatch/getPrintServicesNameList`).then(({data: res}) =>{
        if (res.code !== 0) {
          return this.$message.error(res.msg)
        }
        this.printers = res.data
      })
    },
    /**
     *   获取次批次列表
     */
    getSubBatchList(){
      this.$http.get(`/batch/subbatch/getIdBySubBatchNumberList`).then(({data: res}) =>{
        if (res.code !== 0) {
          return this.$message.error(res.msg)
        }
        this.subBatchList = res.data
      })
    },
    // 表单提交
    dataFormSubmitHandle: debounce(function () {
      if (!this.printerName){
        this.$message({
          message: '请先选择打印机',
          type: 'info',
          duration: 500,
        })
        return;
      }
      this.$refs['dataForm'].validate((valid) => {
        if (!valid) {
          return false
        }
        this.$http['post']('/batch/subbatch/printPdf', {dto:this.dataForm,printerName:this.printerName}).then(({data: res}) => {
          if (res.code !== 0) {
            return this.$message.error(res.msg)
          }
          this.$message({
            message: this.$t('prompt.success'),
            type: 'success',
            duration: 500,
          })
        }).catch(() => {})
      })
    }, 1000, {'leading': true, 'trailing': false})
  }
}
</script>

<style scoped>
.grid_layout{
  display: grid;
  grid-template-columns: 1fr 1fr;
  column-gap: 10px;
}
</style>
