
<template>
  <div>
    <div class="container">
      <div v-for="(menu,index) in menuLists" :index="index">
        <div class="div-button button-like-div">
          <span class="font_size">{{menu.name}}</span>
        </div>
        <div class="button-like-div" v-for="(item,index) in displayedMenuItems(menu.children)" :index="index">
          <div class="font_size">{{item.name}}</div>
          <img v-if="item.addPermissions" @click="loadComponent(item.url)" src="@/assets/img/add.png" class="picture-button"/>
          <img  @click="searchData(item.url)" src="@/assets/img/search.png" class="picture-button"/>
<!--          <use v-if="item.addPermissions" @click="loadComponent(item.url)" xlink:href="@/assets/img/add.svg"></use>
          <use @click="searchData(item.url)" xlink:href="@/assets/img/search.svg"></use>-->
        </div>
        <el-button type="text" v-if="menu.children.length > 5" @click="showAll = !showAll">
          {{ showAll ? '收起' : '展开更多' }}
        </el-button>
      </div>
    </div>
    <component :is="dynamicComponent" ref="dynamicAddition" v-show="false" @refreshDataList="getDataList"></component>
  </div>
</template>

<script>
import mixinViewModule from '@/mixins/view-module'

export default {
  mixins: [mixinViewModule],
  name: "home",
  data() {
    return {
      mixinViewModuleOptions: {
        createdIsNeed: false,
        activatedIsNeed: false,
        getDataListURL: '',
        getDataListIsPage: false,
        deleteURL: '',
        deleteIsBatch: false,
        deleteIsBatchKey: 'id',
        exportURL: '',
        exportTemplateURL: ''
      },
      dataListLoading: false,
      dataForm: {
        id: '',
      },
      menuLists: [],
      activeName: 'second',
      dynamicComponent: null,
      showAll: false
    }
  },
  components:{

  },
  created() {
    this.menuLists = window.SITE_CONFIG['menuList']
    this.$users.$on('getUser', this.getUser);
  },
  beforeDestroy() {
    this.$users.$off('getUser', this.getUser);
  },
  mounted(){
    this.$users.$on('getUser', this.getUser);
    this.getUser()
  },
  computed: {

  },
  methods: {
    displayedMenuItems(value) {
      let reduce = window.SITE_CONFIG['menuList']
      if (this.showAll || value.length <= 4) {
        return value;
      } else {
        return value.slice(0, 4);
      }
    },
    loadComponent(componentPath) {
      console.log(componentPath,'组件路径')
      import(`./${componentPath}.vue`)
          .then(component => {
            this.dynamicComponent = component.default;
            this.$nextTick(()=>{
              this.callDynamicFunction('addOrUpdateHandle')
            })
          })
          .catch(error => {
            console.error("无法加载组件:", error);
          })
    },
    //获取用户以及权限信息
    getUser(){
      let obtain = setInterval(()=>{
        let user = JSON.parse(localStorage.getItem("userData"));
        let rights = JSON.parse(localStorage.getItem("userRights"));
        this.getRights(user.id)
        this.getUsers(user.id)
        if(user){
          clearInterval(obtain)
        }
      },1000)
    },
    getUsers(userId){
      this.$http.get(`/sys/user/${userId}`).then(({ data: res }) => {
        if (res.code !== 0) {
          return this.$message.error(res.msg)
        }
        this.user = res.data;
        window.SITE_CONFIG['users'] = res.data
        sessionStorage.setItem("users", JSON.stringify(res.data))
      }).catch(() => {})
    },
    getRights(userId){
      this.$http['get'](`/sys/menu/getUserRights/`+userId ).then(({ data: res }) => {
        if (res.code !== 0) {
          return this.$message.error(res.msg)
        }
        console.log("count:",res.data)
        this.dataForm = res.data
      }).catch(() => {})
    },
    //动态调用函数
    callDynamicFunction(functionName) {
      if (typeof this[functionName] === "function") {
        this.$refs.dynamicAddition[functionName]()
      } else {
        console.error("函数不存在:", functionName);
      }
    },
    searchData(path) {
      const replacedStr = path.replace(/\//g, '-');
      this.$router.push(replacedStr)
    },
  }
}
</script>


<style scoped>

.picture-button{
  height: 40px;
  width: 40px;
  padding: 5px;
  margin: 10px;
}

.picture-button:active {
  transform: scale(1.5);
}

.picture-button:hover {
  transform: scale(1.5);
}

.div-button{
  background-color: gainsboro;
  width: 200px;
}

.button-like-div {
  padding: 10px 20px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  text-align: center;
  text-decoration: none;
  font-size: 16px;
  width: 200px;
}

.button-like-div:hover {
  background-color: #e7eef5;
}

.button-like-div:active {
  background-color: #004080;
}
/*flex布局*/
.container {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
}
/*两个子元素一行*/
.box {
  width: calc(50% - 10px);
  margin: 5px;
}
@media screen and (max-width: 767px) {
  .container {
    display: block;  /* Change container display to block */
  }
  .box {
    width: 100%;
  }
}

.item > * {
  flex: initial;
}

/*三个子元素一行*/
.item {
  width: calc(33.33% - 10px);
  margin: 5px;
}

@media screen and (max-width: 768px) {
  .container {
    display: block;  /* Change container display to block */
  }
  .item {
    width: 100%;  /* Change item width to 100% */
  }
}

@media (min-width: 768px) and (max-width: 1023px) {
  .container {
    display: block;  /* Change container display to block */
  }
  .item {
    width: calc(50% - 10px);
  }
}

@media (min-width: 1024px) {
  .item {
    width: calc(33.33% - 10px);
  }
}
/*四个子元素一行*/
.four{
  width: calc(25% - 10px);
  margin: 5px;
}
@media screen and (max-width: 768px) {
  .container {
    display: block;  /* Change container display to block */
  }
  .four {
    width: calc(50% - 10px);;  /* Change item width to 100% */
  }
}
/*五个子元素一行*/
.five {
  width: calc(20% - 10px);
  margin: 5px;
}

@media screen and (max-width: 768px) {
  .container {
    display: block;  /* Change container display to block */
  }
  .five {
    width: 100%;  /* Change item width to 100% */
  }
}
</style>
