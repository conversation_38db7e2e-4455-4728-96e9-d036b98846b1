<template>
  <el-dialog :visible.sync="visible" :title="!dataForm.id ? $t('add') : $t('update')" :close-on-click-modal="false" :close-on-press-escape="false" append-to-body>
    <el-form :model="dataForm" :rules="dataRule" ref="dataForm" @keyup.enter.native="dataFormSubmitHandle()" :label-width="$i18n.locale === 'en-US' ? '120px' : '80px'">
      <div class="container">
        <div class="box">
          <el-form-item label="名称" prop="name">
            <el-input v-model="dataForm.name" placeholder="名称"></el-input>
          </el-form-item>
        </div>

        <div class="box">
          <el-form-item label="编码" prop="code">
            <el-input v-model="dataForm.code" placeholder="编码"></el-input>
          </el-form-item>
        </div>
      </div>
      <div class="container">
        <el-form-item label="原编号" prop="primaryCode">
          <el-input v-model="dataForm.primaryCode" placeholder="原编号"></el-input>
        </el-form-item>

        <el-form-item label="类型" prop="type">
          <ren-select v-model="dataForm.type" placeholder="请选择机台类型" dict-type="machine_type"></ren-select>
        </el-form-item>
        <el-form-item label="型号" prop="model">
          <el-input v-model="dataForm.model" placeholder="型号"></el-input>
        </el-form-item>
        <el-form-item label="制造日期" prop="manufacturingDate">
          <el-date-picker
              v-model="dataForm.manufacturingDate"
              type="date"
              format="yyyy-MM-dd"
              value-format="yyyy-MM-dd"
              placeholder="制造日期">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="制造厂商" prop="manufacturer">
          <el-input v-model="dataForm.manufacturer" placeholder="制造厂商"></el-input>
        </el-form-item>
        <el-form-item label="进厂日期" prop="incomingDate">
          <el-date-picker
              v-model="dataForm.incomingDate"
              type="date"
              format="yyyy-MM-dd"
              value-format="yyyy-MM-dd"
              placeholder="进厂日期">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="吨数" prop="tonnage">
          <el-input v-model="dataForm.tonnage" placeholder="吨数"></el-input>
        </el-form-item>
        <el-form-item label="使用单位" prop="usingUnit">
          <el-input v-model="dataForm.usingUnit" placeholder="使用部门"></el-input>
        </el-form-item>
        <el-form-item label="状态" prop="status" v-if="dataForm.id">
          <ren-radio-group v-model="dataForm.status" dict-type="machine_status"></ren-radio-group>
        </el-form-item>
      </div>
      <el-form-item label="备注" prop="remark">
          <el-input v-model="dataForm.remark" placeholder="备注"></el-input>
      </el-form-item>
    </el-form>
    <template slot="footer">
      <el-button @click="visible = false">{{ $t('cancel') }}</el-button>
      <el-button type="primary" @click="dataFormSubmitHandle()">{{ $t('confirm') }}</el-button>
    </template>
  </el-dialog>
</template>

<script>
import debounce from 'lodash/debounce'
import mixinViewModule from '@/mixins/view-module'
export default {
  mixins: [mixinViewModule],
  data () {
    return {
      visible: false,
      dataForm: {
        id: '',
        name: '',
        code: '',
        primaryCode: '',
        model: '',
        type: '',
        manufacturingDate: '',
        manufacturer: '',
        incomingDate: '',
        usingUnit: '',
        tonnage: '',
        status: '',
        remark: '',
        disabled: '',
        creator: '',
        createDate: '',
        updater: '',
        updateDate: ''
      }
    }
  },
  computed: {
    dataRule () {
      return {
          code: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          type: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
      }
    }
  },
  methods: {
    init () {
      this.visible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].resetFields()
        if (this.dataForm.id) {
          this.getInfo()
        }
      })
    },
    // 获取信息
    getInfo () {
      this.$http.get(`/machine/machine/${this.dataForm.id}`).then(({ data: res }) => {
        if (res.code !== 0) {
          return this.$message.error(res.msg)
        }
        this.dataForm = {
          ...this.dataForm,
          ...res.data
        }
      }).catch(() => {})
    },
    // 表单提交
    dataFormSubmitHandle: debounce(function () {
      this.$refs['dataForm'].validate((valid) => {
        if (!valid) {
          return false
        }
        let message = '';
        if(this.dataForm.id){
          message = '此操作将修改该数据'
        }else {
          message = '此操作将新增一条数据'
        }
        this.$confirm(message+', 是否继续?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
          center: true
        }).then(() => {
          this.$http[!this.dataForm.id ? 'post' : 'put']('/machine/machine/', this.dataForm).then(({ data: res }) => {
            if (res.code !== 0) {
              return this.$message.error(res.msg)
            }
            this.$message({
              message: this.$t('prompt.success'),
              type: 'success',
              duration: 500,
              onClose: () => {
                this.visible = false
                this.$emit('refreshDataList')
              }
            })
          }).catch(() => {})
        }).catch(() => {
          this.$message({
            type: 'info',
            message: '已取消'
          });
        });
      })
    }, 1000, { 'leading': true, 'trailing': false })
  }
}
</script>
