<template>
  <el-dialog :visible.sync="visible" :title="!dataForm.id ? $t('add') : $t('update')" :close-on-click-modal="false" :close-on-press-escape="false" append-to-body>
    <el-form :model="dataForm" :rules="dataRule" ref="dataForm" @keyup.enter.native="dataFormSubmitHandle()" :label-width="$i18n.locale === 'en-US' ? '120px' : '80px'">
          <el-form-item label="批号id" prop="customerId">
          <el-input v-model="dataForm.customerId" placeholder="批号id"></el-input>
      </el-form-item>
          <el-form-item label="每包数量" prop="quantityPerPackage">
          <el-input v-model="dataForm.quantityPerPackage" placeholder="每包数量"></el-input>
      </el-form-item>
          <el-form-item label="每箱数量" prop="quantityPerBox">
          <el-input v-model="dataForm.quantityPerBox" placeholder="每箱数量"></el-input>
      </el-form-item>
          <el-form-item label="产品单重" prop="singleWeight">
          <el-input v-model="dataForm.singleWeight" placeholder="产品单重"></el-input>
      </el-form-item>
          <el-form-item label="内标签张数" prop="innerLabel">
          <el-input v-model="dataForm.innerLabel" placeholder="内标签张数"></el-input>
      </el-form-item>
          <el-form-item label="外标签张数" prop="outerLabelNumber">
          <el-input v-model="dataForm.outerLabelNumber" placeholder="外标签张数"></el-input>
      </el-form-item>
          <el-form-item label="总出货数量" prop="totalShipmentQuantity">
          <el-input v-model="dataForm.totalShipmentQuantity" placeholder="总出货数量"></el-input>
      </el-form-item>
          <el-form-item label="备注" prop="remark">
          <el-input v-model="dataForm.remark" placeholder="备注"></el-input>
      </el-form-item>
                </el-form>
    <template slot="footer">
      <el-button @click="visible = false">{{ $t('cancel') }}</el-button>
      <el-button type="primary" @click="dataFormSubmitHandle()">{{ $t('confirm') }}</el-button>
    </template>
  </el-dialog>
</template>

<script>
import debounce from 'lodash/debounce'
export default {
  data () {
    return {
      visible: false,
      dataForm: {
        id: '',
        customerId: '',
        quantityPerPackage: '',
        quantityPerBox: '',
        singleWeight: '',
        innerLabel: '',
        outerLabelNumber: '',
        totalShipmentQuantity: '',
        remark: '',
        disabled: '',
        creator: '',
        createDate: '',
        updater: '',
        updateDate: ''
      }
    }
  },
  computed: {
    dataRule () {
      return {
          id: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          customerId: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          quantityPerPackage: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          quantityPerBox: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          singleWeight: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          innerLabel: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          outerLabelNumber: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          totalShipmentQuantity: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          remark: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
                }
    }
  },
  methods: {
    init () {
      this.visible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].resetFields()
        if (this.dataForm.id) {
          this.getInfo()
        }
      })
    },
    // 获取信息
    getInfo () {
      this.$http.get(`/shipping/shippingdetails/${this.dataForm.id}`).then(({ data: res }) => {
        if (res.code !== 0) {
          return this.$message.error(res.msg)
        }
        this.dataForm = {
          ...this.dataForm,
          ...res.data
        }
      }).catch(() => {})
    },
    // 表单提交
    dataFormSubmitHandle: debounce(function () {
      this.$refs['dataForm'].validate((valid) => {
        if (!valid) {
          return false
        }
        this.$http[!this.dataForm.id ? 'post' : 'put']('/shipping/shippingdetails/', this.dataForm).then(({ data: res }) => {
          if (res.code !== 0) {
            return this.$message.error(res.msg)
          }
          this.$message({
            message: this.$t('prompt.success'),
            type: 'success',
            duration: 500,
            onClose: () => {
              this.visible = false
              this.$emit('refreshDataList')
            }
          })
        }).catch(() => {})
      })
    }, 1000, { 'leading': true, 'trailing': false })
  }
}
</script>
