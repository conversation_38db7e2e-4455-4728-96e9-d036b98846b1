<template>
  <el-dialog :visible.sync="visible" title="次批出货确认" :close-on-click-modal="false" :close-on-press-escape="false" append-to-body>

    <el-button type="primary" @click="shipConfirm">出货</el-button>

    <el-table :data="dataForm" border @selection-change="dataListSelectionChange" style="width: 100%;">
      <el-table-column type="selection" header-align="center" align="center" width="50"></el-table-column>
      <el-table-column prop="batchNumber" label="批号" header-align="center" align="center">
        <template slot-scope="scope">
          <div>
            {{$getDictLabel("size_category", scope.row.sizeCategory)+ scope.row.batchNumber}}
          </div>
        </template>
      </el-table-column>
      <el-table-column prop="subBatchNumber" label="次批号" header-align="center" align="center"></el-table-column>
      <el-table-column prop="toBeShipped" label="可出货数量" header-align="center" align="center"></el-table-column>
      <el-table-column prop="toBeShipped" label="尾数" header-align="center" align="center"></el-table-column>
      <el-table-column prop="toBeShipped" label="隔离数" header-align="center" align="center"></el-table-column>
    </el-table>
  </el-dialog>
</template>

<script>
import debounce from 'lodash/debounce'
export default {
  data () {
    return {
      visible: false,
      batchId: '',
      dataForm: [],
      shippingDataList: [],
    }
  },
  computed: {
    dataRule () {
      return {}
    }
  },
  methods: {
    shipConfirm(){
      this.$emit('callbackSubBatch',this.shippingDataList)
      this.visible = false
    },
    dataListSelectionChange(val){
      this.shippingDataList = val
    },
    init () {
      this.visible = true
      this.$nextTick(() => {
        if (this.batchId) {
          this.getInfo()
        }
      })
    },
    // 获取信息
    getInfo () {
      this.$http.get(`/batch/subbatch/queryShipmentQuantity?batchId=${this.batchId}`).then(({ data: res }) => {
        if (res.code !== 0) {
          return this.$message.error(res.msg)
        }
        this.dataForm = res.data
      }).catch(() => {})
    },
  }
}
</script>
