<template>
  <el-card shadow="never" class="aui-card--fill">
    <div class="mod-batch__shippingform}">
      <el-form :inline="true" :model="dataForm" @keyup.enter.native="getDataList()">
        <el-form-item>
          <el-input v-model="dataForm.id" placeholder="id" clearable></el-input>
        </el-form-item>
        <el-form-item>
          <el-button @click="getDataList()">{{ $t('query') }}</el-button>
        </el-form-item>
        <el-form-item>
          <el-button type="info" @click="exportHandle()">{{ $t('export') }}</el-button>
        </el-form-item>
        <el-form-item>
          <el-button v-if="$hasPermission('batch:shippingform:save')" type="primary" @click="conductShipment()">进行出货</el-button>
        </el-form-item>
        <el-form-item>
          <el-button v-if="$hasPermission('batch:shippingform:delete')" type="danger" @click="deleteHandle()">{{ $t('deleteBatch') }}</el-button>
        </el-form-item>
      </el-form>
      <el-table v-loading="dataListLoading" height="600px" :data="dataList" border @selection-change="dataListSelectionChangeHandle" style="width: 100%;">
        <el-table-column type="selection" header-align="center" align="center" width="50"></el-table-column>
        <el-table-column prop="customerCode" label="客户" header-align="center" align="center"></el-table-column>
        <el-table-column prop="shipmentStatus" label="出货状态" header-align="center" align="center">
          <template slot-scope="scope">
            <div>
              {{$getDictLabel("shipment_status", scope.row.shipmentStatus)}}
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="shippingMethod" label="运送方式" header-align="center" align="center"></el-table-column>
        <el-table-column prop="numberOfPallets" label="栈板个数" header-align="center" align="center"></el-table-column>
        <el-table-column prop="shippingDate" label="出货日期" header-align="center" align="center"></el-table-column>
        <el-table-column prop="remark" label="备注" header-align="center" align="center"></el-table-column>
        <el-table-column :label="$t('handle')" fixed="right" header-align="center" align="center" width="150">
          <template slot-scope="scope">
            <el-dropdown>
              <el-button type="primary">
                功能<i class="el-icon-arrow-down el-icon--right"></i>
              </el-button>
              <el-dropdown-menu slot="dropdown">
                <el-dropdown-item>
                  <el-button type="text" size="small" @click="getDetails(scope.row.id)">明细</el-button>
                </el-dropdown-item>
                <el-dropdown-item>
                  <el-button v-if="$hasPermission('batch:shippingform:update')" type="text" size="small" @click="addOrUpdateHandle(scope.row.id)">{{ $t('update') }}</el-button>
                </el-dropdown-item>
                <el-dropdown-item>
                  <el-button v-if="$hasPermission('batch:shippingform:delete')" type="text" size="small" @click="deleteHandle(scope.row.id)">{{ $t('delete') }}</el-button>
                </el-dropdown-item>
              </el-dropdown-menu>
            </el-dropdown>
          </template>
        </el-table-column>
      </el-table>
      <el-pagination
        :current-page="page"
        :page-sizes="[10, 20, 50, 100]"
        :page-size="limit"
        :total="total"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="pageSizeChangeHandle"
        @current-change="pageCurrentChangeHandle">
      </el-pagination>
      <!-- 弹窗, 新增 / 修改 -->
      <add-or-update v-if="addOrUpdateVisible" ref="addOrUpdate" @refreshDataList="getDataList"></add-or-update>
    </div>
  </el-card>
</template>

<script>
import mixinViewModule from '@/mixins/view-module'
import AddOrUpdate from './shippingform-add-or-update.vue'
export default {
  mixins: [mixinViewModule],
  data () {
    return {
      mixinViewModuleOptions: {
        getDataListURL: '/batch/shippingform/page',
        getDataListIsPage: true,
        exportURL: '/batch/shippingform/export',
        deleteURL: '/batch/shippingform',
        deleteIsBatch: true
      },
      dataForm: {
        id: ''
      }
    }
  },
  components: {
    AddOrUpdate
  },
  methods:{
    conductShipment(){
      this.$router.push("batch-conductshipment")
    },
    getDetails(id){
      this.$router.push({name: 'batch-shippingdetails',query:{shippingFormId:id}})
    }
  }
}
</script>
