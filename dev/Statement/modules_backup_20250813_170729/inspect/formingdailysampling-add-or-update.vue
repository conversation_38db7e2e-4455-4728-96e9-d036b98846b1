<template>
  <el-dialog :visible.sync="visible" :title="!dataForm.id ? $t('add') : $t('update')" :close-on-click-modal="false" :close-on-press-escape="false" append-to-body>
    <el-form :model="dataForm" :rules="dataRule" ref="dataForm" @keyup.enter.native="dataFormSubmitHandle()" :label-width="$i18n.locale === 'en-US' ? '120px' : '80px'">
      <div class="container">
        <el-form-item label="成形日期" prop="formingDate">
          <el-input v-model="dataForm.formingDate" placeholder="成形日期"></el-input>
        </el-form-item>
        <el-form-item label="配方id" prop="formulationId">
          <el-input v-model="dataForm.formulationId" placeholder="配方id"></el-input>
        </el-form-item>
        <el-form-item label="机台id" prop="machineId">
          <el-input v-model="dataForm.machineId" placeholder="机台id"></el-input>
        </el-form-item>
        <el-form-item label="部品id" prop="partId">
          <el-input v-model="dataForm.partId" placeholder="部品id"></el-input>
        </el-form-item>
        <el-form-item label="客户id" prop="customerId">
          <el-input v-model="dataForm.customerId" placeholder="客户id"></el-input>
        </el-form-item>
        <el-form-item label="模具孔数" prop="numberOfHoles">
          <el-input v-model="dataForm.numberOfHoles" placeholder="模具孔数"></el-input>
        </el-form-item>
        <el-form-item label="不良率" prop="defectiveRate">
          <el-input v-model="dataForm.defectiveRate" placeholder="不良率"></el-input>
        </el-form-item>
        <el-form-item label="班别" prop="classes">
          <el-input v-model="dataForm.classes" placeholder="班别"></el-input>
        </el-form-item>
        <el-form-item label="作业员" prop="operator">
          <el-input v-model="dataForm.operator" placeholder="作业员"></el-input>
        </el-form-item>
        <el-form-item label="检查员" prop="inspector">
          <el-input v-model="dataForm.inspector" placeholder="检查员"></el-input>
        </el-form-item>
        <el-form-item label="确认者" prop="confirmedBy">
          <el-input v-model="dataForm.confirmedBy" placeholder="确认者"></el-input>
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="dataForm.remark" placeholder="备注"></el-input>
        </el-form-item>
      </div>
    </el-form>
    <template slot="footer">
      <el-button @click="visible = false">{{ $t('cancel') }}</el-button>
      <el-button type="primary" @click="dataFormSubmitHandle()">{{ $t('confirm') }}</el-button>
    </template>
  </el-dialog>
</template>

<script>
import debounce from 'lodash/debounce'
import mixinViewModule from '@/mixins/view-module'
export default {
  mixins:[mixinViewModule],
  data () {
    return {
      visible: false,
      //创建该页面时，不调用数据查询接口
      mixinViewModuleOptions: {
        createdIsNeed: false,
      },
      dataForm: {
        id: '',
        formingDate: '',
        formulationId: '',
        machineId: '',
        partId: '',
        customerId: '',
        numberOfHoles: '',
        defectiveRate: '',
        classes: '',
        operator: '',
        inspector: '',
        confirmedBy: '',
        remark: '',
        disabled: '',
        creator: '',
        createDate: '',
        updater: '',
        updateDate: ''
      }
    }
  },
  computed: {
    dataRule () {
      return {
          id: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          formingDate: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          formulationId: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          machineId: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          partId: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          customerId: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          numberOfHoles: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          defectiveRate: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          classes: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          operator: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          inspector: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          confirmedBy: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          remark: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
                }
    }
  },
  methods: {
    init () {
      this.visible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].resetFields()
        if (this.dataForm.id) {
          this.getInfo()
        }
      })
    },
    // 获取信息
    getInfo () {
      this.$http.get(`/inspect/formingdailysampling/${this.dataForm.id}`).then(({ data: res }) => {
        if (res.code !== 0) {
          return this.$message.error(res.msg)
        }
        this.dataForm = {
          ...this.dataForm,
          ...res.data
        }
      }).catch(() => {})
    },
    // 表单提交
    dataFormSubmitHandle: debounce(function () {
      this.$refs['dataForm'].validate((valid) => {
        if (!valid) {
          return false
        }
        this.$http[!this.dataForm.id ? 'post' : 'put']('/inspect/formingdailysampling/', this.dataForm).then(({ data: res }) => {
          if (res.code !== 0) {
            return this.$message.error(res.msg)
          }
          this.$message({
            message: this.$t('prompt.success'),
            type: 'success',
            duration: 500,
            onClose: () => {
              this.visible = false
              this.$emit('refreshDataList')
            }
          })
        }).catch(() => {})
      })
    }, 1000, { 'leading': true, 'trailing': false })
  }
}
</script>
