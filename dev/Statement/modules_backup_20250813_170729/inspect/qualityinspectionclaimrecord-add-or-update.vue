<template>
  <el-dialog :visible.sync="visible" :title="!dataForm.id ? $t('add') : $t('update')" :close-on-click-modal="false" :close-on-press-escape="false" append-to-body>
    <el-form :model="dataForm" :rules="dataRule" ref="dataForm" @keyup.enter.native="dataFormSubmitHandle()" :label-width="$i18n.locale === 'en-US' ? '120px' : '80px'">
      <div class="container">
                                                <el-form-item label="领取品号id" prop="partId">
              <el-input v-model="dataForm.partId" placeholder="领取品号id"></el-input>
            </el-form-item>
                                        <el-form-item label="领取品号" prop="designation">
              <el-input v-model="dataForm.designation" placeholder="领取品号"></el-input>
            </el-form-item>
                                        <el-form-item label="领取次批id" prop="subBatchId">
              <el-input v-model="dataForm.subBatchId" placeholder="领取次批id"></el-input>
            </el-form-item>
                                        <el-form-item label="领取次批号" prop="subBatchNumber">
              <el-input v-model="dataForm.subBatchNumber" placeholder="领取次批号"></el-input>
            </el-form-item>
                                        <el-form-item label="领取人id" prop="receiverId">
              <el-input v-model="dataForm.receiverId" placeholder="领取人id"></el-input>
            </el-form-item>
                                        <el-form-item label="领取人姓名" prop="receiverName">
              <el-input v-model="dataForm.receiverName" placeholder="领取人姓名"></el-input>
            </el-form-item>
                                        <el-form-item label="领取数量" prop="receiveNumber">
              <el-input v-model="dataForm.receiveNumber" placeholder="领取数量"></el-input>
            </el-form-item>
                                        <el-form-item label="领取时间" prop="receiveTime">
              <el-input v-model="dataForm.receiveTime" placeholder="领取时间"></el-input>
            </el-form-item>
                                        <el-form-item label="当前状态0:作业中 1:作业完成" prop="currentState">
              <el-input v-model="dataForm.currentState" placeholder="当前状态0:作业中 1:作业完成"></el-input>
            </el-form-item>
                                                                                                                  </div>
    </el-form>
    <template slot="footer">
      <el-button @click="visible = false">{{ $t('cancel') }}</el-button>
      <el-button type="primary" @click="dataFormSubmitHandle()">{{ $t('confirm') }}</el-button>
    </template>
  </el-dialog>
</template>

<script>
import debounce from 'lodash/debounce'
import mixinViewModule from '@/mixins/view-module'
export default {
  mixins:[mixinViewModule],
  data () {
    return {
      visible: false,
      //创建该页面时，不调用数据查询接口
      mixinViewModuleOptions: {
        createdIsNeed: false,
      },
      dataForm: {
        id: '',
        partId: '',
        designation: '',
        subBatchId: '',
        subBatchNumber: '',
        receiverId: '',
        receiverName: '',
        receiveNumber: '',
        receiveTime: '',
        currentState: '',
        disabled: '',
        creator: '',
        createDate: '',
        updater: '',
        updateDate: ''
      }
    }
  },
  computed: {
    dataRule () {
      return {
          id: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          partId: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          designation: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          subBatchId: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          subBatchNumber: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          receiverId: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          receiverName: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          receiveNumber: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          receiveTime: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          currentState: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
                }
    }
  },
  methods: {
    init () {
      this.visible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].resetFields()
        if (this.dataForm.id) {
          this.getInfo()
        }
      })
    },
    // 获取信息
    getInfo () {
      this.$http.get(`/inspect/qualityinspectionclaimrecord/${this.dataForm.id}`).then(({ data: res }) => {
        if (res.code !== 0) {
          return this.$message.error(res.msg)
        }
        this.dataForm = {
          ...this.dataForm,
          ...res.data
        }
      }).catch(() => {})
    },
    // 表单提交
    dataFormSubmitHandle: debounce(function () {
      this.$refs['dataForm'].validate((valid) => {
        if (!valid) {
          return false
        }
        let message = '';
        if(this.dataForm.id){
          message = '此操作将修改该数据'
        }else {
          message = '此操作将新增一条数据'
        }
        this.$confirm(message+', 是否继续?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
          center: true
        }).then(() => {
          this.$http[!this.dataForm.id ? 'post' : 'put']('/inspect/qualityinspectionclaimrecord/', this.dataForm).then(({ data: res }) => {
            if (res.code !== 0) {
            return this.$message.error(res.msg)
            }
            this.$message({
          message: this.$t('prompt.success'),
            type: 'success',
                    duration: 500,
                    onClose: () => {
              this.visible = false
              this.$emit('refreshDataList')
            }
          })
          }).catch(() => {})
        }).catch(() => {
          this.$message({
            type: 'info',
            message: '已取消'
          });
        });
      })
    }, 1000, { 'leading': true, 'trailing': false })
  }
}
</script>
