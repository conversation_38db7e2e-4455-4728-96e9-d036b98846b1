<template>
    <div>
<!--      <el-button @click="addItem">添加</el-button>
      <el-button v-for="(item, index) in dataFormList"
                 :key="'button' + index"
                 :class="{ 'active': activeItem === index }"
                 @click="setActiveItem(index)">品检信息 {{ index + 1 }}</el-button>-->
      <el-form :rules="dataRule" ref="dataForm" @keyup.enter.native="dataFormSubmitHandle()" :label-width="$i18n.locale === 'en-US' ? '120px' : '80px'">
        <div>
          <div class="container">
            <div>
              <el-form-item label="品检日期" prop="pickingDate">
                <el-date-picker
                    class="datePicker"
                    v-model="dataForm.pickingDate"
                    type="date"
                    value-format="yyyy-MM-dd"
                    placeholder="品检日期">
                </el-date-picker>
              </el-form-item>
            </div>

            <div>
              <el-form-item label="品检员" prop="qualityInspector">
                <employee-component v-model="dataForm.qualityInspector" :departments="$inspectionDefault" placeholder="请选择"></employee-component>
              </el-form-item>
            </div>
            <div>
              <el-form-item label="外发员工姓名" prop="outgoingName">
                <el-input v-model="dataForm.outgoingName" placeholder="外发员工姓名"></el-input>
              </el-form-item>
            </div>
            <div>
              <el-form-item label="第几次检查" prop="inspectionType">
                <ren-radio-group v-model="dataForm.inspectionType" placeholder="请选择" dict-type="inspection_type"></ren-radio-group>
              </el-form-item>
            </div>
          </div>

          <div class="container">
            <!--        <div class="item">
                      <el-form-item label="品名编号" prop="itemNumber">
                        <el-input v-model="dataForm.itemNumber" placeholder="品名编号"></el-input>
                      </el-form-item>
                    </div>-->
            <!--        <div class="box">
                      <el-form-item label="部品品番" prop="designation">
                        <part-inspection-batch-component v-model="dataForm.subBatchId" placeholder="请选择品番"></part-inspection-batch-component>
                      </el-form-item>
                    </div>-->
            <div class="box">
              <el-form-item label="次批号" prop="subBatchNumber">
                <!--            <el-autocomplete
                                class="inline-input"
                                v-model="dataForm.subBatchNumber"
                                :fetch-suggestions="querySecondaryBatch"
                                placement="bottom"
                                placeholder="请输入客户代码"
                                :trigger-on-focus="false"
                                popper-class="my-popper"
                                clearable
                                @select="subBatchSelect">
                            </el-autocomplete>-->
                <span class="font_size">{{subBatchNumber}}</span>
              </el-form-item>
            </div>
          </div>

          <div class="container">
            <div>
              <el-form-item label="品检数量" prop="qcQuantity">
                <el-input v-model="dataForm.qcQuantity" placeholder="品检数量"></el-input>
              </el-form-item>
            </div>

            <div>
              <el-form-item label="实领数量" prop="actualReceivedAmount">
                <el-input v-model="actualReceivedAmount" placeholder="实际领取数"></el-input>
              </el-form-item>
            </div>

            <div>
              <el-form-item label="不良数" prop="adverseNumber">
                <el-input v-model="adverseNumber" placeholder="不良数"></el-input>
              </el-form-item>
            </div>
            <div>
              <el-form-item label="破裂" prop="rupture">
                <el-input v-model="dataForm.rupture" placeholder="破裂" class="inputSize"></el-input>
              </el-form-item>
            </div>

            <div>
              <el-form-item label="包风" prop="bagWind">
                <el-input v-model="dataForm.bagWind" placeholder="包风"></el-input>
              </el-form-item>
            </div>

            <div>
              <el-form-item label="压痕" prop="indentation">
                <el-input v-model="dataForm.indentation" placeholder="压痕"></el-input>
              </el-form-item>
            </div>

            <div>
              <el-form-item label="死料" prop="deathPenalty">
                <el-input v-model="dataForm.deathPenalty" placeholder="死料"></el-input>
              </el-form-item>
            </div>
            <div>
              <el-form-item label="缺料" prop="lackOfMaterial">
                <el-input v-model="dataForm.lackOfMaterial" placeholder="缺料"></el-input>
              </el-form-item>
            </div>

            <div>
              <el-form-item label="拉伤" prop="strain">
                <el-input v-model="dataForm.strain" placeholder="拉伤"></el-input>
              </el-form-item>
            </div>

            <div>
              <el-form-item label="压毛边" prop="burr">
                <el-input v-model="dataForm.burr" placeholder="压毛边"></el-input>
              </el-form-item>
            </div>

            <div>
              <el-form-item label="起泡" prop="bubbling">
                <el-input v-model="dataForm.bubbling" placeholder="起泡"></el-input>
              </el-form-item>
            </div>
            <div>
              <el-form-item label="杂质" prop="impurities">
                <el-input v-model="dataForm.impurities" placeholder="杂质"></el-input>
              </el-form-item>
            </div>

            <div>
              <el-form-item label="剪伤" prop="cut">
                <el-input v-model="dataForm.cut" placeholder="剪伤"></el-input>
              </el-form-item>
            </div>
          </div>

          <el-button type="text" @click="showAll = !showAll">
            {{ showAll ? '收起' : '展开更多' }}
          </el-button>
          <div v-show="showAll">
            <div class="container">
              <div>
                <el-form-item label="灌点凹" prop="concavePouringPoint">
                  <el-input v-model="dataForm.concavePouringPoint" placeholder="灌点凹"></el-input>
                </el-form-item>
              </div>
              <div>
                <el-form-item label="料污" prop="materialPollution">
                  <el-input v-model="dataForm.materialPollution" placeholder="料污"></el-input>
                </el-form-item>
              </div>
              <div>
                <el-form-item label="变形" prop="deformation">
                  <el-input v-model="dataForm.deformation" placeholder="变形"></el-input>
                </el-form-item>
              </div>
              <div>
                <el-form-item label="不粘胶" prop="notStickyGlue">
                  <el-input v-model="dataForm.notStickyGlue" placeholder="不粘胶"></el-input>
                </el-form-item>
              </div>
            </div>

            <div class="container">
              <div>
                <el-form-item label="露铁" prop="exposedIron">
                  <el-input v-model="dataForm.exposedIron" placeholder="露铁"></el-input>
                </el-form-item>
              </div>
              <div>
                <el-form-item label="毛边" prop="burrs">
                  <el-input v-model="dataForm.burrs" placeholder="毛边"></el-input>
                </el-form-item>
              </div>
              <div>
                <el-form-item label="颗粒" prop="particles">
                  <el-input v-model="dataForm.particles" placeholder="颗粒"></el-input>
                </el-form-item>
              </div>
              <div>
                <el-form-item label="断柱子" prop="brokenColumn">
                  <el-input v-model="dataForm.brokenColumn" placeholder="断柱子"></el-input>
                </el-form-item>
              </div>
            </div>

            <div class="container">
              <div>
                <el-form-item label="不熟" prop="undercooked">
                  <el-input v-model="dataForm.undercooked" placeholder="不熟"></el-input>
                </el-form-item>
              </div>
              <div>
                <el-form-item label="爆边" prop="burstEdge">
                  <el-input v-model="dataForm.burstEdge" placeholder="爆边"></el-input>
                </el-form-item>
              </div>
              <div>
                <el-form-item label="孔堵" prop="holePlug">
                  <el-input v-model="dataForm.holePlug" placeholder="孔堵"></el-input>
                </el-form-item>
              </div>
              <div>
                <el-form-item label="模伤" prop="moldDamage">
                  <el-input v-model="dataForm.moldDamage" placeholder="模伤"></el-input>
                </el-form-item>
              </div>
            </div>

            <div class="container">
              <div>
                <el-form-item label="灌点破" prop="brokenPouringPoint">
                  <el-input v-model="dataForm.brokenPouringPoint" placeholder="灌点破"></el-input>
                </el-form-item>
              </div>
              <div>
                <el-form-item label="五彩" prop="colorful">
                  <el-input v-model="dataForm.colorful" placeholder="五彩"></el-input>
                </el-form-item>
              </div>
              <div>
                <el-form-item label="针孔" prop="needleHole">
                  <el-input v-model="dataForm.needleHole" placeholder="针孔"></el-input>
                </el-form-item>
              </div>
              <div>
                <el-form-item label="脱胶" prop="degumming">
                  <el-input v-model="dataForm.degumming" placeholder="脱胶"></el-input>
                </el-form-item>
              </div>
            </div>

            <div class="container">
              <div>
                <el-form-item label="异品" prop="differentProducts">
                  <el-input v-model="dataForm.differentProducts" placeholder="异品"></el-input>
                </el-form-item>
              </div>
              <div>
                <el-form-item label="异物" prop="foreignMatter">
                  <el-input v-model="dataForm.foreignMatter" placeholder="异物"></el-input>
                </el-form-item>
              </div>
              <div>
                <el-form-item label="黑点" prop="blackSpot">
                  <el-input v-model="dataForm.blackSpot" placeholder="黑点"></el-input>
                </el-form-item>
              </div>
              <div>
                <el-form-item label="分模线粗" prop="coarsePartingLine">
                  <el-input v-model="dataForm.coarsePartingLine" placeholder="分模线粗"></el-input>
                </el-form-item>
              </div>
            </div>

            <div class="container">
              <div>
                <el-form-item label="发亮" prop="shiny">
                  <el-input v-model="dataForm.shiny" placeholder="发亮"></el-input>
                </el-form-item>
              </div>
              <div>
                <el-form-item label="铁件不良" prop="ironPartsDefects">
                  <el-input v-model="dataForm.ironPartsDefects" placeholder="铁件不良"></el-input>
                </el-form-item>
              </div>
              <div>
                <el-form-item label="胶歪" prop="glueCrooked">
                  <el-input v-model="dataForm.glueCrooked" placeholder="胶歪"></el-input>
                </el-form-item>
              </div>
              <div>
                <el-form-item label="灌点大" prop="largePouringPoint">
                  <el-input v-model="dataForm.largePouringPoint" placeholder="灌点大"></el-input>
                </el-form-item>
              </div>
            </div>
            <div class="container">
              <el-form-item label="折痕" prop="crease">
                <el-input-number :controls="false" v-model="dataForm.crease" placeholder="折痕"></el-input-number>
              </el-form-item>
              <el-form-item label="穿孔" prop="perforation">
                <el-input-number :controls="false" v-model="dataForm.perforation" placeholder="穿孔"></el-input-number>
              </el-form-item>
              <el-form-item label="偏模" prop="deviantMode">
                <el-input-number :controls="false" v-model="dataForm.deviantMode" placeholder="偏模"></el-input-number>
              </el-form-item>
              <el-form-item label="柱裂" prop="columnarFissure">
                <el-input-number :controls="false" v-model="dataForm.columnarFissure" placeholder="柱裂"></el-input-number>
              </el-form-item>
              <el-form-item label="柱歪" prop="columnCrooked">
                <el-input-number :controls="false" v-model="dataForm.columnCrooked" placeholder="柱歪"></el-input-number>
              </el-form-item>
              <el-form-item label="粘胶" prop="viscose">
                <el-input-number :controls="false" v-model="dataForm.viscose" placeholder="粘胶"></el-input-number>
              </el-form-item>
              <el-form-item label="灌点不良" prop="poorFillingPoint">
                <el-input-number :controls="false" v-model="dataForm.poorFillingPoint" placeholder="灌点不良"></el-input-number>
              </el-form-item>
              <el-form-item label="夹破" prop="pinching">
                <el-input-number :controls="false" v-model="dataForm.pinching" placeholder="夹破"></el-input-number>
              </el-form-item>
              <el-form-item label="孔破" prop="holeBroken">
                <el-input-number :controls="false" v-model="dataForm.holeBroken" placeholder="孔破"></el-input-number>
              </el-form-item>
              <el-form-item label="生胶" prop="rawGum">
                <el-input-number :controls="false" v-model="dataForm.rawGum" placeholder="生胶"></el-input-number>
              </el-form-item>
              <el-form-item label="粘毛边" prop="stickyEdge">
                <el-input-number :controls="false" v-model="dataForm.stickyEdge" placeholder="粘毛边"></el-input-number>
              </el-form-item>
              <el-form-item label="端子接合开裂" prop="terminalJointCracking">
                <el-input-number :controls="false" v-model="dataForm.terminalJointCracking" placeholder="端子接合开裂"></el-input-number>
              </el-form-item>
              <el-form-item label="剥线尺寸" prop="strippingSize">
                <el-input-number :controls="false" v-model="dataForm.strippingSize" placeholder="剥线尺寸"></el-input-number>
              </el-form-item>
              <el-form-item label="接合部位" prop="jointArea">
                <el-input-number :controls="false" v-model="dataForm.jointArea" placeholder="接合部位"></el-input-number>
              </el-form-item>
              <el-form-item label="端子破裂" prop="terminalBreakage">
                <el-input-number :controls="false" v-model="dataForm.terminalBreakage" placeholder="端子破裂"></el-input-number>
              </el-form-item>
              <el-form-item label="起皮" prop="peeling">
                <el-input-number :controls="false" v-model="dataForm.peeling" placeholder="起皮"></el-input-number>
              </el-form-item>
              <el-form-item label="脱破" prop="detachment">
                <el-input-number :controls="false" v-model="dataForm.detachment" placeholder="脱破"></el-input-number>
              </el-form-item>
              <el-form-item label="缺铁" prop="ironDeficiency">
                <el-input-number :controls="false" v-model="dataForm.ironDeficiency" placeholder="缺铁"></el-input-number>
              </el-form-item>
              <el-form-item label="夹伤" prop="pinchInjury">
                <el-input-number :controls="false" v-model="dataForm.pinchInjury" placeholder="夹伤"></el-input-number>
              </el-form-item>
            </div>
          </div>
          <el-form-item label="备注" prop="remark">
            <el-input v-model="dataForm.remark" placeholder="备注"></el-input>
          </el-form-item>
        </div>
      </el-form>
    </div>
</template>

<script>
import debounce from 'lodash/debounce'
import mixinViewModule from '@/mixins/view-module'
import PartNumberComponent from "../../../components/shared-search/part-number-component/src/part-number-component.vue";
import {getDictLabel} from '@/utils'
export default {
  props:{
    subBatchNumber: {
      type:String,
      default: ''
    },
  },
  components: {
    PartNumberComponent,
  },
  mixins: [mixinViewModule],
  data () {
    return {
      visible: false,
      //创建该页面时，不调用数据查询接口
      mixinViewModuleOptions: {
        createdIsNeed: false,
      },
      activeItem: 0,
      showAll: false,
      oldBatchAddVisible: false,
      dataFormList:[],
      dataForm: {
        id: '',
        pickingDate: this.formatTimes(new Date()),
        itemNumber: '',
        partId: '',
        designation: '',
        batchId: '',
        sizeCategory: '',
        batchNumber: '',
        subBatchId: '',
        subBatchNumber:this.subBatchNumber,
        qualityInspector: '',
        outgoingName: '',
        inspectionType: 1,
        qcQuantity: 0,
        actualReceivedAmount: 0,
        adverseNumber: 0,
        rupture: 0,
        bagWind: 0,
        indentation: 0,
        deathPenalty: 0,
        lackOfMaterial: 0,
        strain: 0,
        burr: 0,
        bubbling: 0,
        impurities: 0,
        cut: 0,
        concavePouringPoint: 0,
        materialPollution: 0,
        deformation: 0,
        exposedIron: 0,
        burrs: 0,
        particles: 0,
        brokenColumn: 0,
        undercooked: 0,
        burstEdge: 0,
        holePlug: 0,
        moldDamage:0 ,
        brokenPouringPoint:0 ,
        colorful: 0,
        needleHole: 0,
        degumming: 0,
        differentProducts: 0,
        foreignMatter: 0,
        blackSpot: 0,
        coarsePartingLine: 0,
        shiny: 0,
        ironPartsDefects: 0,
        glueCrooked: 0,
        largePouringPoint: 0,
        notStickyGlue: 0,
        crease: 0,
        perforation: 0,
        deviantMode: 0,
        columnarFissure: 0,
        columnCrooked: 0,
        viscose: 0,
        poorFillingPoint: 0,
        pinching: 0,
        holeBroken: 0,
        rawGum: 0,
        stickyEdge: 0,
        terminalJointCracking: 0,
        strippingSize: 0,
        jointArea: 0,
        terminalBreakage: 0,
        peeling: 0,
        detachment: 0,
        ironDeficiency: 0,
        pinchInjury: 0,
        defectiveItemOne: '',
        defectiveItemTwo: '',
        defectiveItemThree: '',
        buildTime: '',
        remark: '',
        disabled: '',
        creator: '',
        createDate: '',
        updater: '',
        updateDate: ''
      },
      options:[],
    }
  },
  computed: {
    dataRule () {
      return {

      }
    },
    actualReceivedAmount(){
      this.dataForm.actualReceivedAmount = this.dataForm.qcQuantity - this.dataForm.adverseNumber
      return this.dataForm.actualReceivedAmount
    }
  },
  /*mounted() {
    this.dataFormList.push(this.dataForm)
  },*/
  methods: {
    /*//添加按钮
    addItem() {
      this.dataFormList.push(this.dataForm); // 添加一个对象
      this.activeItem = this.dataFormList.length - 1; // 显示新添加的组件
      this.$refs['dataForm'].resetFields();
      console.log(this.activeItem,'下标')
      console.log(this.dataFormList,'数组')
    },
    //切换按钮
    setActiveItem(indexs) {
      console.log(this.dataFormList,'数组长度显示',indexs)
      let idx = this.dataFormList.findIndex((item, index) => index === indexs);
      if(idx === -1){
        this.dataFormList.push(this.dataForm);
      }else {
        this.dataFormList[this.activeItem] = {
          ...this.dataFormList[this.activeItem],
          ...this.dataForm
        }
        // 修改数组中的数据
        /!*this.$set(this.dataFormList,this.activeItem , this.dataForm);*!/
      }
      this.activeItem = indexs //显示为蓝色
      this.dataForm = this.dataFormList[indexs]
    },*/
    // 添加按钮
    addItem() {
      this.dataFormList.push({ ...this.dataForm }); // 添加一个对象的副本
      this.dataForm = {}; // 重置 dataForm
      this.activeItem = this.dataFormList.length - 1; // 显示新添加的组件
    },
    // 切换按钮
    setActiveItem(indexs) {
      // 如果当前操作的 dataForm 对象还没有保存到数组中，则先保存
      if (this.activeItem !== indexs && this.dataFormList[this.activeItem] !== this.dataForm) {
        this.$set(this.dataFormList, this.activeItem, { ...this.dataForm });
      }
      // 将 dataForm 对象的数据切换为数组下标对应的数据
      this.dataForm = { ...this.dataFormList[indexs] };
      this.activeItem = indexs; // 显示为蓝色
    },
    subBatchSelect(item){
      this.dataForm.subBatchId = item.subBatchId
    },
    querySecondaryBatch(batchNumber,cb){
      this.$http.get(`/batch/batch/querySubBatchNumberList/` + batchNumber).then(({data: res}) => {
        if (res.code !== 0) {
          return this.$message.error(res.msg)
        }
        const list = res.data.map((obj) => {
          return {
            value: getDictLabel("size_category", obj.sizeCategory) + obj.batchNumber + (obj.subBatchNumber != null ? ('-'+obj.subBatchNumber) : '未建立次批'),
            subBatchNumber:getDictLabel("size_category", obj.sizeCategory) + obj.batchNumber + (obj.subBatchNumber != null ? ('-'+obj.subBatchNumber) : ''),
            subBatchId: obj.subBatchId,
          }
        })
        this.timeout = setTimeout(() => {
          cb(list);
        }, 300 * Math.random());
      })
    },
    objectReturn(data){
      this.dataForm.subBatchId=data.subBatchId
      this.dataForm.subBatchNumber = data.temporaryBatchNumber
    },
    oldBatchAddOrUpdateHandle() {
      this.oldBatchAddVisible = true
      this.$nextTick(() => {
        this.$refs.oldBatchAddParameter.init()
      })
    },
    // 获取部品列表
    getPartList(queryString, cb) {
      this.$http.get(`/fabricate/part/fuzzyQueryPart?designation=` + queryString).then(({data: res}) => {
        if (res.code !== 0) {
          return this.$message.error(res.msg)
        }
        const list = res.data.map((obj) => {
          return {
            value:obj.designation+ '('+
                (obj.batchNumber != null ?
                    (obj.sizeCategory+obj.batchNumber)
                    +
                    (obj.subBatchNumber != null ? '-'+obj.subBatchNumber : '(无对应次批号)'):'无对应批号')
                +')',
            partId: obj.partId,
            designation: obj.designation,
            batchId: obj.batchId,
            batchNumber: obj.batchNumber,
            sizeCategory: obj.sizeCategory,
            subBatchNumber: obj.subBatchNumber,
            subBatchId: obj.subBatchId,
          }
        })
        this.timeout = setTimeout(() => {
          cb(list);
        }, 300 * Math.random());
      }).catch(() => {
      })
    },
    //  输出选择日志
    partSelect(item) {
      this.dataForm.subBatchId = item.subBatchId
      this.dataForm.designation = item.designation
      this.dataForm.customerCode = item.customerCode
      this.dataForm.partId = item.partId
      this.dataForm.batchId = item.batchId
      this.dataForm.batchNumber = item.batchNumber
      this.dataForm.subBatchNumber = item.subBatchNumber
      this.dataForm.sizeCategory = item.sizeCategory
    },
    init () {
      this.visible = true
      this.showAll = false
      this.$nextTick(() => {
        this.$refs['dataForm'].resetFields()
        if (this.dataForm.id) {
          this.getInfo()
        }
      })
    },
    // 获取信息
    getInfo () {
      this.$http.get(`/inspect/qualityinspectionnissanrecord/${this.dataForm.id}`).then(({ data: res }) => {
        if (res.code !== 0) {
          return this.$message.error(res.msg)
        }
        this.dataForm = {
          ...this.dataForm,
          ...res.data
        }
      }).catch(() => {})
    },
    // 表单提交
    dataFormSubmitHandle() {
      this.dataFormList.push(this.dataForm)
      this.$emit("qualityinspectionnissanrecord",this.dataFormList)
    }
  }
}
</script>

<style scoped>
.active {
  background-color: #1764f6;
}
</style>


