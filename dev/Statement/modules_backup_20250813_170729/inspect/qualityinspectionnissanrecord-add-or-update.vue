<template>
  <el-dialog :visible.sync="visible" :title="!dataForm.id ? '新增品检记录' : '修改品检记录'"
             :close-on-click-modal="false"
             :close-on-press-escape="false" append-to-body>
    <el-form :model="dataForm" :rules="dataRule" ref="dataForm" @keyup.enter.native="dataFormSubmitHandle()"
             :label-width="$i18n.locale === 'en-US' ? '120px' : '80px'">
      <div class="container">
        <div>
          <el-form-item label="品检日期" prop="pickingDate">
            <el-date-picker
                class="datePicker"
                v-model="dataForm.pickingDate"
                type="date"
                value-format="yyyy-MM-dd"
                placeholder="品检日期">
            </el-date-picker>
          </el-form-item>
        </div>
        <div>
          <el-form-item label="品检时间" prop="startTime">
            <div class="container" style="grid-template-columns: 1fr 1fr;">
              <el-time-picker
                  placeholder="开始时间"
                  v-model="dataForm.startTime"
                  value-format="HH:mm:ss"
                  format="HH:mm:ss">
              </el-time-picker>
              <el-time-picker
                  placeholder="结束时间"
                  v-model="dataForm.endTime"
                  value-format="HH:mm:ss"
                  format="HH:mm:ss">
              </el-time-picker>
            </div>
          </el-form-item>
        </div>
        <div>
          <el-form-item label="检查次数" prop="inspectionType">
            <ren-select v-model="dataForm.inspectionType" placeholder="请选择"
                        dict-type="inspection_type"></ren-select>
          </el-form-item>
        </div>
        <div>
          <el-form-item label="品检员" prop="qualityInspector">
            <employee-component v-model="dataForm.qualityInspector" placeholder="请选择"></employee-component>
          </el-form-item>
        </div>
        <!--        <div>-->
        <!--          <el-form-item label="品修员">-->
        <!--            <el-select v-model="dataForm.qualityRepairmanList" multiple clearable filterable>-->
        <!--              <el-option v-for="(item,index) in employeeList"-->
        <!--                :key="index"-->
        <!--                :label="item.username + '(' + item.userCode + ')'"-->
        <!--                :value="item.id"-->
        <!--              >-->
        <!--              </el-option>-->
        <!--            </el-select>-->
        <!--          </el-form-item>-->
        <!--        </div>-->
      </div>
      <!--      <div class="container">-->
      <!--        <el-form-item label="绩效计算" prop="performancePoints">-->
      <!--          <el-select v-model="dataForm.calculate">-->
      <!--            <el-option :value="0" label="时效"></el-option>-->
      <!--            <el-option :value="1" label="时数"></el-option>-->
      <!--          </el-select>-->
      <!--        </el-form-item>-->
      <!--        <el-form-item v-if="dataForm.calculate == 1"  label="时数" prop="hours">-->
      <!--          <el-input v-model="dataForm.hours" placeholder="时数"></el-input>-->
      <!--        </el-form-item>-->
      <!--      </div>-->
      <!--      <div class="container">-->
      <!--        <div>-->
      <!--          <el-form-item label="抽检日期">-->
      <!--            <el-date-picker-->
      <!--                v-model="dataForm.samplingDate"-->
      <!--                type="date"-->
      <!--                value-format="yyyy-MM-dd"-->
      <!--                placeholder="选择日期时间">-->
      <!--            </el-date-picker>-->
      <!--          </el-form-item>-->
      <!--        </div>-->
      <!--        <div>-->
      <!--          <el-form-item label="抽检员">-->
      <!--            <el-select v-model="dataForm.spotCheckers"  clearable filterable>-->
      <!--              <el-option v-for="(item,index) in employeeList"-->
      <!--                         :key="index"-->
      <!--                         :label="item.username + '(' + item.userCode + ')'"-->
      <!--                         :value="item.id"-->
      <!--              >-->
      <!--              </el-option>-->
      <!--            </el-select>-->
      <!--          </el-form-item>-->
      <!--        </div>-->
      <!--        <div>-->
      <!--          <el-form-item label="包装数量">-->
      <!--            <el-input v-model="dataForm.quantity" placeholder=""></el-input>-->
      <!--          </el-form-item>-->
      <!--        </div>-->
      <!--      </div>-->
      <div class="container">
        <!--        <el-form-item label="绩效点" prop="performancePoints">-->
        <!--          <span class="font_size">{{performancePoints}}</span>-->
        <!--        </el-form-item>-->
        <el-form-item v-if="examine" label="不合格原因" prop="reasonForFailure">
          <ren-select v-model="dataForm.reasonForFailure" placeholder="不合格原因"
                      dict-type="reason_for_failure"></ren-select>
        </el-form-item>
        <el-form-item v-if="examine" label="不合格现象" prop="unqualifiedPhenomenon">
          <el-input v-model="dataForm.unqualifiedPhenomenon" placeholder="不合格现象"></el-input>
        </el-form-item>
      </div>
      <div class="container">
        <div>
          <el-form-item label="次批号" prop="subBatchNumber">
            <el-select v-model="dataForm.subBatchId" @change="changeBySelectSubBatch" remote clearable filterable
                       :filter-method="customFilter">
              <el-option v-for="(item,index) in showOptions"
                         :key="index"
                         :label="item.label"
                         :value="item.value">
              </el-option>
            </el-select>
          </el-form-item>
        </div>
        <div>
          <el-form-item label="部品品番" prop="designation">
            <part-number-component :default-first-option="true" :allow-create="true" v-model="dataForm.partId"
                                   placeholder="请选择品番"></part-number-component>
          </el-form-item>
        </div>
        <!--        <div>-->
        <!--          <el-button type="primary" @click="oldBatchAddOrUpdateHandle">新增旧批号</el-button>-->
        <!--        </div>-->
      </div>

      <div class="container">
        <div>
          <el-form-item label="品检数量" prop="qcQuantity">
            <el-input v-model="dataForm.qcQuantity" placeholder="品检数量"></el-input>
          </el-form-item>
        </div>

        <div>
          <el-form-item label="实领数量" prop="actualReceivedAmount">
            <el-input v-model="dataForm.actualReceivedAmount" placeholder="实际领取数"></el-input>
          </el-form-item>
        </div>

        <div>
          <el-form-item label="不良数" prop="adverseNumber">
            <el-input v-model="adverseNumber" placeholder="不良数"></el-input>
          </el-form-item>
        </div>
      </div>

      <div class="container">
        <div>
          <el-form-item label="破裂" prop="rupture">
            <el-input v-model="dataForm.rupture" placeholder="破裂" class="inputSize"></el-input>
          </el-form-item>
        </div>

        <div>
          <el-form-item label="包风" prop="bagWind">
            <el-input v-model="dataForm.bagWind" placeholder="包风"></el-input>
          </el-form-item>
        </div>

        <div>
          <el-form-item label="压痕" prop="indentation">
            <el-input v-model="dataForm.indentation" placeholder="压痕"></el-input>
          </el-form-item>
        </div>

        <div>
          <el-form-item label="死料" prop="deathPenalty">
            <el-input v-model="dataForm.deathPenalty" placeholder="死料"></el-input>
          </el-form-item>
        </div>
        <div>
          <el-form-item label="缺料" prop="lackOfMaterial">
            <el-input v-model="dataForm.lackOfMaterial" placeholder="缺料"></el-input>
          </el-form-item>
        </div>

        <div>
          <el-form-item label="拉伤" prop="strain">
            <el-input v-model="dataForm.strain" placeholder="拉伤"></el-input>
          </el-form-item>
        </div>

        <div>
          <el-form-item label="压毛边" prop="burr">
            <el-input v-model="dataForm.burr" placeholder="压毛边"></el-input>
          </el-form-item>
        </div>

        <div>
          <el-form-item label="起泡" prop="bubbling">
            <el-input v-model="dataForm.bubbling" placeholder="起泡"></el-input>
          </el-form-item>
        </div>
        <div>
          <el-form-item label="杂质" prop="impurities">
            <el-input v-model="dataForm.impurities" placeholder="杂质"></el-input>
          </el-form-item>
        </div>

        <div>
          <el-form-item label="剪伤" prop="cut">
            <el-input v-model="dataForm.cut" placeholder="剪伤"></el-input>
          </el-form-item>
        </div>
        <div>
          <el-form-item label="其他" prop="other">
            <el-input v-model="dataForm.other" placeholder="其他"></el-input>
          </el-form-item>
        </div>
      </div>

      <el-button type="text" @click="showAll = !showAll">
        {{ showAll ? '收起' : '展开更多' }}
      </el-button>
      <div v-show="showAll">
        <div class="container">
          <div>
            <el-form-item label="灌点凹" prop="concavePouringPoint">
              <el-input v-model="dataForm.concavePouringPoint" placeholder="灌点凹"></el-input>
            </el-form-item>
          </div>
          <div>
            <el-form-item label="料污" prop="materialPollution">
              <el-input v-model="dataForm.materialPollution" placeholder="料污"></el-input>
            </el-form-item>
          </div>
          <div>
            <el-form-item label="变形" prop="deformation">
              <el-input v-model="dataForm.deformation" placeholder="变形"></el-input>
            </el-form-item>
          </div>
          <div>
            <el-form-item label="不粘胶" prop="notStickyGlue">
              <el-input v-model="dataForm.notStickyGlue" placeholder="不粘胶"></el-input>
            </el-form-item>
          </div>
          <div>
            <el-form-item label="露铁" prop="exposedIron">
              <el-input v-model="dataForm.exposedIron" placeholder="露铁"></el-input>
            </el-form-item>
          </div>
          <div>
            <el-form-item label="毛边" prop="burrs">
              <el-input v-model="dataForm.burrs" placeholder="毛边"></el-input>
            </el-form-item>
          </div>
          <div>
            <el-form-item label="颗粒" prop="particles">
              <el-input v-model="dataForm.particles" placeholder="颗粒"></el-input>
            </el-form-item>
          </div>
          <div>
            <el-form-item label="断柱子" prop="brokenColumn">
              <el-input v-model="dataForm.brokenColumn" placeholder="断柱子"></el-input>
            </el-form-item>
          </div>
          <div>
            <el-form-item label="不熟" prop="undercooked">
              <el-input v-model="dataForm.undercooked" placeholder="不熟"></el-input>
            </el-form-item>
          </div>
          <div>
            <el-form-item label="爆边" prop="burstEdge">
              <el-input v-model="dataForm.burstEdge" placeholder="爆边"></el-input>
            </el-form-item>
          </div>
          <div>
            <el-form-item label="孔堵" prop="holePlug">
              <el-input v-model="dataForm.holePlug" placeholder="孔堵"></el-input>
            </el-form-item>
          </div>
          <div>
            <el-form-item label="模伤" prop="moldDamage">
              <el-input v-model="dataForm.moldDamage" placeholder="模伤"></el-input>
            </el-form-item>
          </div>
          <div>
            <el-form-item label="灌点破" prop="brokenPouringPoint">
              <el-input v-model="dataForm.brokenPouringPoint" placeholder="灌点破"></el-input>
            </el-form-item>
          </div>
          <div>
            <el-form-item label="五彩" prop="colorful">
              <el-input v-model="dataForm.colorful" placeholder="五彩"></el-input>
            </el-form-item>
          </div>
          <div>
            <el-form-item label="针孔" prop="needleHole">
              <el-input v-model="dataForm.needleHole" placeholder="针孔"></el-input>
            </el-form-item>
          </div>
          <div>
            <el-form-item label="脱胶" prop="degumming">
              <el-input v-model="dataForm.degumming" placeholder="脱胶"></el-input>
            </el-form-item>
          </div>
          <div>
            <el-form-item label="异品" prop="differentProducts">
              <el-input v-model="dataForm.differentProducts" placeholder="异品"></el-input>
            </el-form-item>
          </div>
          <div>
            <el-form-item label="异物" prop="foreignMatter">
              <el-input v-model="dataForm.foreignMatter" placeholder="异物"></el-input>
            </el-form-item>
          </div>
          <div>
            <el-form-item label="黑点" prop="blackSpot">
              <el-input v-model="dataForm.blackSpot" placeholder="黑点"></el-input>
            </el-form-item>
          </div>
          <div>
            <el-form-item label="分模线粗" prop="coarsePartingLine">
              <el-input v-model="dataForm.coarsePartingLine" placeholder="分模线粗"></el-input>
            </el-form-item>
          </div>
          <div>
            <el-form-item label="发亮" prop="shiny">
              <el-input v-model="dataForm.shiny" placeholder="发亮"></el-input>
            </el-form-item>
          </div>
          <div>
            <el-form-item label="铁件不良" prop="ironPartsDefects">
              <el-input v-model="dataForm.ironPartsDefects" placeholder="铁件不良"></el-input>
            </el-form-item>
          </div>
          <div>
            <el-form-item label="胶歪" prop="glueCrooked">
              <el-input v-model="dataForm.glueCrooked" placeholder="胶歪"></el-input>
            </el-form-item>
          </div>
          <div>
            <el-form-item label="灌点大" prop="largePouringPoint">
              <el-input v-model="dataForm.largePouringPoint" placeholder="灌点大"></el-input>
            </el-form-item>
          </div>
        </div>
        <div class="container">
          <el-form-item label="折痕" prop="crease">
            <el-input-number :controls="false" v-model="dataForm.crease" placeholder="折痕"></el-input-number>
          </el-form-item>
          <el-form-item label="穿孔" prop="perforation">
            <el-input-number :controls="false" v-model="dataForm.perforation" placeholder="穿孔"></el-input-number>
          </el-form-item>
          <el-form-item label="偏模" prop="deviantMode">
            <el-input-number :controls="false" v-model="dataForm.deviantMode" placeholder="偏模"></el-input-number>
          </el-form-item>
          <el-form-item label="柱裂" prop="columnarFissure">
            <el-input-number :controls="false" v-model="dataForm.columnarFissure" placeholder="柱裂"></el-input-number>
          </el-form-item>
          <el-form-item label="柱歪" prop="columnCrooked">
            <el-input-number :controls="false" v-model="dataForm.columnCrooked" placeholder="柱歪"></el-input-number>
          </el-form-item>
          <el-form-item label="粘胶" prop="viscose">
            <el-input-number :controls="false" v-model="dataForm.viscose" placeholder="粘胶"></el-input-number>
          </el-form-item>
          <el-form-item label="灌点不良" prop="poorFillingPoint">
            <el-input-number :controls="false" v-model="dataForm.poorFillingPoint"
                             placeholder="灌点不良"></el-input-number>
          </el-form-item>
          <el-form-item label="夹破" prop="pinching">
            <el-input-number :controls="false" v-model="dataForm.pinching" placeholder="夹破"></el-input-number>
          </el-form-item>
          <el-form-item label="孔破" prop="holeBroken">
            <el-input-number :controls="false" v-model="dataForm.holeBroken" placeholder="孔破"></el-input-number>
          </el-form-item>
          <el-form-item label="生胶" prop="rawGum">
            <el-input-number :controls="false" v-model="dataForm.rawGum" placeholder="生胶"></el-input-number>
          </el-form-item>
          <el-form-item label="粘毛边" prop="stickyEdge">
            <el-input-number :controls="false" v-model="dataForm.stickyEdge" placeholder="粘毛边"></el-input-number>
          </el-form-item>
          <el-form-item label="端子接合开裂" prop="terminalJointCracking">
            <el-input-number :controls="false" v-model="dataForm.terminalJointCracking"
                             placeholder="端子接合开裂"></el-input-number>
          </el-form-item>
          <el-form-item label="剥线尺寸" prop="strippingSize">
            <el-input-number :controls="false" v-model="dataForm.strippingSize"
                             placeholder="剥线尺寸"></el-input-number>
          </el-form-item>
          <el-form-item label="接合部位" prop="jointArea">
            <el-input-number :controls="false" v-model="dataForm.jointArea" placeholder="接合部位"></el-input-number>
          </el-form-item>
          <el-form-item label="端子破裂" prop="terminalBreakage">
            <el-input-number :controls="false" v-model="dataForm.terminalBreakage"
                             placeholder="端子破裂"></el-input-number>
          </el-form-item>
          <el-form-item label="起皮" prop="peeling">
            <el-input-number :controls="false" v-model="dataForm.peeling" placeholder="起皮"></el-input-number>
          </el-form-item>
          <el-form-item label="脱破" prop="detachment">
            <el-input-number :controls="false" v-model="dataForm.detachment" placeholder="脱破"></el-input-number>
          </el-form-item>
          <el-form-item label="缺铁" prop="ironDeficiency">
            <el-input-number :controls="false" v-model="dataForm.ironDeficiency" placeholder="缺铁"></el-input-number>
          </el-form-item>
          <el-form-item label="夹伤" prop="pinchInjury">
            <el-input-number :controls="false" v-model="dataForm.pinchInjury" placeholder="夹伤"></el-input-number>
          </el-form-item>
        </div>
      </div>
      <el-form-item label="备注" prop="remark">
        <el-input v-model="dataForm.remark" placeholder="备注"></el-input>
      </el-form-item>
    </el-form>
    <template slot="footer">
      <el-button @click="visible = false">{{ $t('cancel') }}</el-button>
      <el-button type="primary" @click="dataFormSubmitHandle()">{{ $t('confirm') }}</el-button>
    </template>
    <div>
      <old-batch-add v-if="oldBatchAddVisible" ref="oldBatchAddParameter" @objectReturn="objectReturn"></old-batch-add>
    </div>
  </el-dialog>
</template>

<script>
import debounce from 'lodash/debounce'
import mixinViewModule from '@/mixins/view-module'
import PartNumberComponent from "../../../components/shared-search/part-number-component/src/part-number-component.vue";
import oldBatchAdd from "../batch/old-batch-add.vue";
import {getDictLabel, getInspectionAging} from '@/utils'
import {MessageBox} from "element-ui";

export default {
  components: {
    PartNumberComponent,
    oldBatchAdd
  },
  mixins: [mixinViewModule],
  data() {
    return {
      visible: false,
      //创建该页面时，不调用数据查询接口
      mixinViewModuleOptions: {
        createdIsNeed: false,
      },
      showAll: false,
      examine: false,
      oldBatchAddVisible: false,
      dataForm: {
        id: '',
        pickingDate: this.formatTimes(new Date()),
        itemNumber: '',
        partId: '',
        designation: '',
        qualityRepairmanList: [],
        samplingDate: '',
        spotCheckers: '',
        quantity: '',
        batchId: '',
        sizeCategory: '',
        batchNumber: '',
        subBatchId: '',
        subBatchNumber: '',
        qualityInspector: '',
        outgoingName: '',
        performancePoints: '',
        hours: '',
        calculate: 0,
        unqualifiedPhenomenon: '',
        reasonForFailure: '',
        startTime: '',
        endTime: '',
        inspectionType: 1,
        qcQuantity: 0,
        actualReceivedAmount: 0,
        adverseNumber: 0,
        rupture: 0,
        bagWind: 0,
        indentation: 0,
        deathPenalty: 0,
        lackOfMaterial: 0,
        strain: 0,
        burr: 0,
        bubbling: 0,
        impurities: 0,
        cut: 0,
        concavePouringPoint: 0,
        materialPollution: 0,
        deformation: 0,
        exposedIron: 0,
        burrs: 0,
        particles: 0,
        brokenColumn: 0,
        undercooked: 0,
        burstEdge: 0,
        holePlug: 0,
        moldDamage: 0,
        brokenPouringPoint: 0,
        colorful: 0,
        needleHole: 0,
        degumming: 0,
        differentProducts: 0,
        foreignMatter: 0,
        blackSpot: 0,
        coarsePartingLine: 0,
        shiny: 0,
        ironPartsDefects: 0,
        glueCrooked: 0,
        largePouringPoint: 0,
        notStickyGlue: 0,
        crease: 0,
        perforation: 0,
        deviantMode: 0,
        columnarFissure: 0,
        columnCrooked: 0,
        viscose: 0,
        poorFillingPoint: 0,
        pinching: 0,
        holeBroken: 0,
        rawGum: 0,
        stickyEdge: 0,
        terminalJointCracking: 0,
        strippingSize: 0,
        jointArea: 0,
        terminalBreakage: 0,
        peeling: 0,
        other: 0,
        detachment: 0,
        ironDeficiency: 0,
        pinchInjury: 0,
        defectiveItemOne: 0,
        defectiveItemTwo: 0,
        defectiveItemThree: 0,
        buildTime: 0,
        remark: '',
        disabled: '',
        creator: '',
        createDate: '',
        updater: '',
        updateDate: ''
      },
      options: [],
      showOptions: [],
      subBatchOption: [],
      // employeeList:[],
      // deptList:["1641701892159619073"],
    }
  },
  // created() {
  //   this.getRepairAndInspect();
  // },
  computed: {
    adverseNumber() {
      this.dataForm.adverseNumber = this.calculateTotal(this.dataForm)
      return this.dataForm.adverseNumber;
    },
    dataRule() {
      return {
        startTime: [
          {required: this.dataForm.calculate == 1, message: this.$t('validate.required'), trigger: 'blur'}
        ],
        endTime: [
          {required: this.dataForm.calculate == 1, message: this.$t('validate.required'), trigger: 'blur'}
        ],
        hours: [
          {required: this.dataForm.calculate == 1, message: this.$t('validate.required'), trigger: 'blur'}
        ],
      }
    },
    performancePoints() {
      console.log('调用计算属性')
      if (!this.dataForm.partId) {
        return "无时效";
      }
      if (this.dataForm.calculate === 0) {
        let num = Number(getInspectionAging(this.dataForm.partId, this.dataForm.inspectionType))
        if (num === 0) {
          return '无时效';
        }
        return (Number(this.dataForm.qcQuantity) / num).toFixed(2);
      }
      return this.dataForm.hours;
    }
  },
  watch: {
    "dataForm.inspectionType"(newValue) {
      this.returnForInspection()
    },
    'dataForm.partId': {
      immediate: true, // 立即执行一次
      handler(newVal, oldVal) {
        this.updatePerformancePoints();
        console.log(this.dataForm.performancePoints, '绩效')
      }
    },
    'dataForm.calculate': {
      immediate: true, // 立即执行一次
      handler(newVal, oldVal) {
        this.updatePerformancePoints();

      }
    }
  },
  activated() {

  },
  methods: {
    updatePerformancePoints() {
      if (!this.dataForm.partId || !this.dataForm.calculate) {
        this.dataForm.performancePoints = '';
        return;
      }

      if (this.dataForm.calculate === 0) {
        let num = Number(getInspectionAging(this.dataForm.partId, this.dataForm.inspectionType));
        if (num === 0) {
          this.dataForm.performancePoints = '';
          return;
        }
        this.dataForm.performancePoints = (Number(this.dataForm.qcQuantity) / num).toFixed(2);
      } else {
        this.dataForm.performancePoints = this.dataForm.hours;
      }
    },
    /**
     * 计算时数
     * @returns {number}
     */
    getHours() {
      if (this.dataForm.startTime && this.dataForm.endTime) {
        // 拆分开始时间字符串
        const startTimeArr = this.dataForm.startTime.split(':');
        const startHour = Number(startTimeArr[0]);
        const startMinute = Number(startTimeArr[1]);
        const startSecond = Number(startTimeArr[2]);

        // 拆分结束时间字符串
        const endTimeArr = this.dataForm.endTime.split(':');
        const endHour = Number(endTimeArr[0]);
        const endMinute = Number(endTimeArr[1]);
        const endSecond = Number(endTimeArr[2]);

        // 计算时间差
        const timeDiffHour = endHour - startHour;
        const timeDiffMinute = endMinute - startMinute;
        const timeDiffSecond = endSecond - startSecond;

        // 转换为小时数
        let hours = (timeDiffHour + timeDiffMinute / 60 + timeDiffSecond / 3600);
        if (!Number.isInteger(hours)) {
          hours = hours.toFixed(1);
        }
        this.dataForm.hours = hours;
        return hours;
      }
    },
    returnForInspection() {
      this.examine = !['1', '2', '3', '4'].includes(this.dataForm.inspectionType)
    },
    //自定义查询方法
    customFilter(val) {
      let filterList = this.subBatchOption.filter((item) => {
        return item.label.includes(val)
      });
      if (filterList.length > 200) {
        this.showOptions = filterList.slice(0, 200)
      } else {
        this.showOptions = filterList
      }
    },
    //查询次批
    getSubBatchNumber() {
      this.$http.get('/batch/batch/querySecondaryBatch').then(({data: res}) => {
        if (res.code !== 0) {
          return this.$message.error(res.msg)
        }
        this.subBatchOption = res.data.map((obj) => {
          return {
            label: getDictLabel("size_category", obj.sizeCategory) + obj.batchNumber +
                (obj.subBatchNumber != null ? ('-' + obj.subBatchNumber) : '(未建立次批)'),
            value: obj.subBatchId,
            data: obj
          }
        })
        this.showOptions = this.subBatchOption.slice(0, 200)
      })
    },
    changeBySelectSubBatch() {
      if (this.dataForm.subBatchId) {
        let filter = this.showOptions.filter(item => item.value === this.dataForm.subBatchId);
        this.dataForm.subBatchNumber = filter[0].label
        this.dataForm.partId = filter[0].data.partId
      } else {
        this.dataForm.subBatchNumber = ''
        this.dataForm.partId = ''
        this.showOptions = this.subBatchOption.slice(0, 200)
      }
    },
    oldBatchAddOrUpdateHandle() {
      this.oldBatchAddVisible = true
      this.$nextTick(() => {
        this.$refs.oldBatchAddParameter.init()
      })
    },
//  获取部品列表
    getPartList(queryString, cb) {
      this.$http.get(`/fabricate/part/fuzzyQueryPart?designation=` + queryString).then(({data: res}) => {
        if (res.code !== 0) {
          return this.$message.error(res.msg)
        }
        const list = res.data.map((obj) => {
          return {
            value: obj.designation + '(' +
                (obj.batchNumber != null ?
                    (obj.sizeCategory + obj.batchNumber)
                    +
                    (obj.subBatchNumber != null ? '-' + obj.subBatchNumber : '(无对应次批号)') : '无对应批号')
                + ')',
            partId: obj.partId,
            designation: obj.designation,
            batchId: obj.batchId,
            batchNumber: obj.batchNumber,
            sizeCategory: obj.sizeCategory,
            subBatchNumber: obj.subBatchNumber,
            subBatchId: obj.subBatchId,
          }
        })
        this.timeout = setTimeout(() => {
          cb(list);
        }, 300 * Math.random());
      }).catch(() => {
      })
    },
    //  输出选择日志
    partSelect(item) {
      this.dataForm.subBatchId = item.subBatchId
      this.dataForm.designation = item.designation
      this.dataForm.customerCode = item.customerCode
      this.dataForm.partId = item.partId
      this.dataForm.batchId = item.batchId
      this.dataForm.batchNumber = item.batchNumber
      this.dataForm.subBatchNumber = item.subBatchNumber
      this.dataForm.sizeCategory = item.sizeCategory
    },
    init() {
      this.visible = true
      this.showAll = false
      this.$nextTick(() => {
        this.getSubBatchNumber()
        this.dataForm.subBatchId = ''
        this.dataForm.partId = ''
        this.$refs['dataForm'].resetFields()
        if (this.dataForm.id) {
          this.getInfo()
        }
      })
    },
    // 获取信息
    getInfo() {
      this.$http.get(`/inspect/qualityinspectionnissanrecord/${this.dataForm.id}`).then(({data: res}) => {
        if (res.code !== 0) {
          return this.$message.error(res.msg)
        }
        this.dataForm = {
          ...this.dataForm,
          ...res.data
        }
      }).catch(() => {
      })
    },
    // 表单提交
    dataFormSubmitHandle: debounce(function () {
      this.$refs['dataForm'].validate((valid) => {
        if (!valid) {
          return false
        }
        let message = '';
        if (this.dataForm.id) {
          message = '此操作将修改该数据'
        } else {
          message = '此操作将新增一条数据'
        }
        this.$confirm(message + ', 是否继续?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
          center: true
        }).then(() => {
          this.$http[!this.dataForm.id ? 'post' : 'put']('/inspect/qualityinspectionnissanrecord/', this.dataForm).then(({data: res}) => {
            if (res.code !== 0) {
              return this.$message.error(res.msg)
            }
            this.$message({
              message: this.$t('prompt.success'),
              type: 'success',
              duration: 500,
              onClose: () => {
                this.visible = false
                this.$emit('refreshDataList')
              }
            })
          }).catch(() => {
          })
        }).catch(() => {
          this.$message({
            type: 'info',
            message: '已取消'
          });
        });
      })
    }, 1000, {'leading': true, 'trailing': false})
  }
}
</script>
