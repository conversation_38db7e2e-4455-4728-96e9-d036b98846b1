<template>
  <el-dialog :visible.sync="visible" title="新增外发收件记录" :close-on-click-modal="false"
             :close-on-press-escape="false" append-to-body>
    <el-form :model="dataForm" :rules="dataRule" ref="dataForm" @keyup.enter.native="dataFormSubmitHandle()"
             :label-width="$i18n.locale === 'en-US' ? '120px' : '120px'">
      <div style="display: flex">
        <el-form-item label="品号" prop="partId">
          <el-select v-model="dataForm.partId" @change="selectOutBatch()" filterable clearable style="width: 10vw">
            <el-option v-for="(item,index) in partOption"
                       :key="index"
                       :label="item.label"
                       :value="item.value">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="外发次批" >
          <el-select v-model="outIdList" @change="fillDataList" filterable multiple style="width: 10vw">
            <el-option v-for="(item,index) in outSubBatchOption"
                       :key="index"
                       :label="item.subBatchNumber"
                       :value="item.id">
            </el-option>
          </el-select>
        </el-form-item>
        <div v-if="outIdList.length !== 0" style="margin-left: 1vw">
          <el-button type="info" @click="visible1 = true">填写详情</el-button>
        </div>
      </div>
      <div style="display: flex">
        <!--                                        <el-form-item label="次批id" prop="subBatchId">-->
        <!--              <el-input v-model="dataForm.subBatchId" placeholder="次批id"></el-input>-->
        <!--            </el-form-item>-->
        <!--                                        <el-form-item label="次批号" prop="subBatchNumber">-->
        <!--              <el-input v-model="dataForm.subBatchNumber" placeholder="次批号"></el-input>-->
        <!--            </el-form-item>-->
        <!--                                        <el-form-item label="交货数量" prop="quantity">-->
        <!--              <el-input v-model="dataForm.quantity" placeholder="交货数量"></el-input>-->
        <!--            </el-form-item>-->
        <!--                                        <el-form-item label="检查人员编号" prop="inspectionNumber">-->
        <!--              <el-input v-model="dataForm.inspectionNumber" placeholder="检查人员编号"></el-input>-->
        <!--            </el-form-item>-->
        <el-form-item label="交货日期" prop="deliveryTime">
          <el-date-picker
              style="width: 13vw"
              v-model="dataForm.deliveryTime"
              type="datetime"
              placeholder="选择日期时间"
              default-time="8:00:00"
              value-format="yyyy-MM-dd HH:mm:ss"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item label="备注" >
          <el-input type="textarea" v-model="dataForm.remark" placeholder="备注" style="width: 15vw"></el-input>
        </el-form-item>
      </div>
    </el-form>
    <template slot="footer">
      <el-button @click="visible = false">{{ $t('cancel') }}</el-button>
      <el-button type="primary" @click="dataFormSubmitHandle()">{{ $t('confirm') }}</el-button>
    </template>
    <el-dialog width="50vw" :visible.sync="visible1" append-to-body title="收件详情" :close-on-click-modal="false" center>
      <div v-for="(item, index) in dataForm.dataList" :key="index">
        <div style="display: flex; margin-bottom: 1vh">
          <div style="flex-grow: 4; display: flex; justify-content: center; align-items: center;">
            <div style="font-size: larger;">
              次批号:<el-input disabled v-model="item.subBatchNumber" style="width: 170px; font-size: large"></el-input>
            </div>
          </div>
          <div style="flex-grow: 2; display: flex; justify-content: center; align-items: center;">
            <div style="font-size: large">
              检查人员:<el-input @change="judgmentCheckMode(index,item)" v-model="item.inspectionNumber" style="width: 100px; font-size: large"></el-input>
            </div>
          </div>
          <div style="flex-grow: 2; display: flex; justify-content: center; align-items: center;">
            <div style="font-size: large">
              交货数量:<el-input v-model="item.quantity" style="width: 100px; font-size: large"></el-input>
            </div>
          </div>
          <div style="flex-grow: 2; display: flex; justify-content: center; align-items: center;">
            <div style="font-size: large">
              不良数:<el-input v-model="item.defectQuantity" style="width: 100px; font-size: large"></el-input>
            </div>
          </div>
          <div style="flex-grow: 2; display: flex; justify-content: center; align-items: center;">

            <div style="font-size: large">
              检查方式:<el-select v-model="item.inspectionType" style="width: 100px;" class="large-font" clearable>
                <el-option v-for="(item,index) in inspectionTypeOption"
                           style="font-size: large"
                           :key="index"
                           :label="item.label"
                           :value="item.value">
                </el-option>
            </el-select>
            </div>
          </div>
        </div>
        <el-divider><i class="el-icon-edit"></i></el-divider>
        </div>
    </el-dialog>
  </el-dialog>
</template>

<script>
import debounce from 'lodash/debounce'
import mixinViewModule from '@/mixins/view-module'
import th from "element-ui/src/locale/lang/th";

export default {
  mixins: [mixinViewModule],
  data() {
    return {
      visible: false,
      visible1: false,
      //创建该页面时，不调用数据查询接口
      mixinViewModuleOptions: {
        createdIsNeed: false,
      },
      dataForm: {
        id: '',
        partId: '',
        dataList: [],
        // outId: '',
        // subBatchId: '',
        // subBatchNumber: '',
        // quantity: '',
        // inspectionNumber: '',
        deliveryTime: '',
        remark: '',
        // disabled: '',
        // creator: '',
        // createDate: '',
        // updater: '',
        // updateDate: ''
      },
      inspectionTypeOption:[
        {
          label:'抽检',
          value:0
        },
        {
          label:'全检',
          value:1
        },
      ],
      partOption: [],
      outSubBatchOption:[],
      outIdList: [],
    }
  },
  computed: {
    dataRule() {
      return {
        id: [
          {required: true, message: this.$t('validate.required'), trigger: 'blur'}
        ],
        outId: [
          {required: true, message: this.$t('validate.required'), trigger: 'blur'}
        ],
        partId: [
          {required: true, message: this.$t('validate.required'), trigger: 'blur'}
        ],
        subBatchId: [
          {required: true, message: this.$t('validate.required'), trigger: 'blur'}
        ],
        subBatchNumber: [
          {required: true, message: this.$t('validate.required'), trigger: 'blur'}
        ],
        quantity: [
          {required: true, message: this.$t('validate.required'), trigger: 'blur'}
        ],
        deliveryTime: [
          {required: true, message: this.$t('validate.required'), trigger: 'blur'}
        ],
        inspectionNumber: [
          {required: true, message: this.$t('validate.required'), trigger: 'blur'}
        ],
        remark: [
          {required: true, message: this.$t('validate.required'), trigger: 'blur'}
        ],
      }
    }
  },
  methods: {
    init() {
      this.visible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].resetFields()
        this.outIdList = []
        this.outSubBatchOption = []
        this.getPartList()
      })
    },
    // 获取信息
    getInfo() {
      this.$http.get(`/inspect/outcollection/${this.dataForm.id}`).then(({data: res}) => {
        if (res.code !== 0) {
          return this.$message.error(res.msg)
        }
        this.dataForm = {
          ...this.dataForm,
          ...res.data
        }
      }).catch(() => {
      })
    },
    //根据品号获取可收货次批
    selectOutBatch(){
      if(this.dataForm.partId){
          this.getOutManagementRecord(this.dataForm.partId)
      }else {
        this.outIdList = []
        this.outSubBatchOption = []
      }
    },
    //获取可收货次批列表
    getOutManagementRecord(partId){
      this.$http.get(`inspect/outmanagement/getOutManagementRecord?partId=${partId}`).then(({data: res})=> {
        if(res.code !== 0){
          return this.$message.error(res.msg)
        }
        this.outSubBatchOption = res.data
      })
    },
    //填写外发检查人员时判断检查方式
    judgmentCheckMode(index,item){
      if(item.inspectionNumber){
          this.$http.get('inspect/outpersionnelpartmiddle/judgmentCheckMode',{
            params:{
              partId:this.dataForm.partId,
              personnelCode:item.inspectionNumber
            }
          }).then(({data: res}) =>{
            if (res.code !== 0) {
              return this.$message.error(res.msg)
            }
            let data = res.data;
            console.log(data)
            if(data !== undefined && data !== null){
              if(data === 0){
                this.dataForm.dataList[index].inspectionType = 0
                console.log("抽检")
              }else {
                this.dataForm.dataList[index].inspectionType = 1
                console.log("全检")
              }
            }else {
              this.dataForm.dataList[index].inspectionType = 1
            }
          })
      }else {
        this.dataForm.dataList[index].inspectionType = ''
      }
    },
    //获取品号列表
    getPartList() {
      this.$http.get('fabricate/part/getPartListByDesignation').then(({data: res}) => {
        if (res.code !== 0) {
          return this.$message.error(res.msg)
        }
        this.partOption = res.data.map((obj) => {
          return {
            label: obj.designation,
            value: obj.id,
            data: obj
          }
        })
      })
    },
    //选取次批时做的动作
    fillDataList(){
     // 创建一个映射对象，以便快速查找subBatchOption中的项目
      const outSubBatchOptionMap = new Map(this.outSubBatchOption.map(item => [item.id, item]));
      // 使用subBatchIdList的顺序创建一个新的数组，只包含存在于subBatchOption中的项目
      this.dataForm.dataList = this.outIdList
          .filter(id => outSubBatchOptionMap.has(id)) // 确保subBatchOption中有对应的id
          .map(id => ({...outSubBatchOptionMap.get(id), 'inspectionNumber':'','inspectionType':'','defectQuantity':''}));
      // 如果需要保留原始的this.dataForm.subBatchList（即不覆盖），可以使用concat或者push等方法添加新元素
    },
    // 表单提交
    dataFormSubmitHandle: debounce(function () {
      this.$refs['dataForm'].validate((valid) => {
        if (!valid) {
          return false
        }
        if(this.outIdList.length === 0){
          return this.$message.error("请选取次批")
        }
        let b = this.dataForm.dataList.some(item =>
          item.inspectionNumber === null || item.inspectionNumber === undefined || item.inspectionNumber === ''
        );
        if (b) {
          return this.$message.error("请填写所有检查人员");
        }
        const hasEmptyQuantity = this.dataForm.dataList.some(item =>
            item.quantity === null || item.quantity === undefined || item.quantity === ''
        );
        if (hasEmptyQuantity) {
          return this.$message.error("请填写所有数量");
        }
        let d = this.dataForm.dataList.some(item =>
            item.defectQuantity === null || item.defectQuantity === undefined || item.defectQuantity === ''
        );
        if (d) {
          return this.$message.error("请填写所有不良数");
        }
        let message = '';
        if (this.dataForm.id) {
          message = '此操作将修改该数据'
        } else {
          message = '此操作将新增一条数据'
        }
        this.$confirm(message + ', 是否继续?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
          center: true
        }).then(() => {
          this.$http[!this.dataForm.id ? 'post' : 'put']('/inspect/outcollection/', this.dataForm).then(({data: res}) => {
            if (res.code !== 0) {
              return this.$message.error(res.msg)
            }
            this.$message({
              message: this.$t('prompt.success'),
              type: 'success',
              duration: 500,
              onClose: () => {
                this.visible = false
                this.$emit('refreshDataList')
              }
            })
          }).catch(() => {
          })
        }).catch(() => {
          this.$message({
            type: 'info',
            message: '已取消'
          });
        });
      })
    }, 1000, {'leading': true, 'trailing': false})
  }
}
</script>
<style scoped>
.large-font ::v-deep .el-input__inner {
  font-size: large;
}
</style>
