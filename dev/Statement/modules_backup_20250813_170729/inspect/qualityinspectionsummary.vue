<template>
  <el-card shadow="never" class="aui-card--fill">
    <div class="mod-inspect__qualityinspectionsummary}">
      <el-form :inline="true" :model="dataForm" @keyup.enter.native="getDataList()">
        <el-form-item>
          <el-tooltip content="请输入查询关键字" placement="top">
            <el-input v-model="dataForm.paramStr" placeholder="查询关键字" @input="throttleFunction" clearable>
              <el-button slot="append" icon="el-icon-search" @click="getDataList()"></el-button>
            </el-input>
          </el-tooltip>
        </el-form-item>
      </el-form>
      <el-table v-loading="dataListLoading" height="600px" :data="dataList" border @selection-change="dataListSelectionChangeHandle" style="width: 100%;">
        <el-table-column prop="qualityInspector" label="品检员" header-align="center" align="center">
          <template slot-scope="scope">
            <div>
              {{$getEmployeesList(scope.row.qualityInspector)}}
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="qcQuantity" label="品检数量" header-align="center" align="center"></el-table-column>
        <el-table-column prop="performancePoints" label="绩效" header-align="center" align="center"></el-table-column>
        <el-table-column prop="adverseNumber" label="不良数" header-align="center" align="center"></el-table-column>
        <el-table-column :label="$t('handle')" fixed="right" header-align="center" align="center" width="150px">
          <template slot-scope="scope">
            <el-dropdown>
              <el-button type="primary">
                功能<i class="el-icon-arrow-down el-icon--right"></i>
              </el-button>
              <el-dropdown-menu slot="dropdown">
                <el-dropdown-item>
                  <el-button v-if="$hasPermission('inspect:qualityinspectionnissanrecord:update')" type="text" size="small" @click="addOrUpdateHandle(scope.row.id)">{{ $t('update') }}</el-button>
                </el-dropdown-item>
                <el-dropdown-item>
                  <el-button v-if="$hasPermission('inspect:qualityinspectionnissanrecord:delete')" type="text" size="small" @click="deleteHandle(scope.row.id)">{{ $t('delete') }}</el-button>
                </el-dropdown-item>
              </el-dropdown-menu>
            </el-dropdown>
          </template>
        </el-table-column>
      </el-table>
      <el-pagination
        :current-page="page"
        :page-sizes="[10, 20, 50, 100]"
        :page-size="limit"
        :total="total"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="pageSizeChangeHandle"
        @current-change="pageCurrentChangeHandle">
      </el-pagination>
    </div>
  </el-card>
</template>

<script>
import mixinViewModule from '@/mixins/view-module'
import Cookies from "js-cookie";
export default {
  mixins: [mixinViewModule],
  data () {
    return {
      message:'',
      headers: {'token': Cookies.get('token')},
      fileList: [{name: '', url: ''}],
      unsuccessfulNameList: '',//  导入失败名称列表
      mixinViewModuleOptions: {
        getDataListURL: '/inspect/qualityinspectionnissanrecord/getQualityInspectionSummary',
        getDataListIsPage: true,
        exportURL: '/inspect/qualityinspectionnissanrecord/export',
        deleteURL: '/inspect/qualityinspectionnissanrecord',
        deleteStepStagingURL: '/notify/stepstaging',
        deleteIsBatch: true,
        deleteIsStepStagingBatchKey: 'snapshotId',   // 删除接口，批量状态下由那个key进行标记操作？比如：pid，uid...
        exportTemplateURL: 'inspect/qualityinspectionnissanrecord/export/template',
        addBatchUrl: window.SITE_CONFIG['apiURL'] + 'inspect/qualityinspectionnissanrecord/batchSave',
      },
      // 判断是否还在继续输入
      timer: null,
      dataForm: {
        id: '',
        paramStr: '',
      },
    }
  },
  methods:{
    //节流函数
    throttleFunction() {
      // 如果timer不为空就表明在持续输入,每次改变都清除定时，这样就可以实现只输入最后一次结果(注意每次间隔如果超过500毫秒就会输出)
      if (this.timer !== null) clearTimeout(this.timer)
      this.timer = setTimeout(() => {
        this.getDataList()
      }, 500)
    },
  }
}
</script>
