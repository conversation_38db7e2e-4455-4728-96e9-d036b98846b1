<template>
  <el-dialog :visible.sync="visible" :title="!dataForm.id ? '新增全检时效标准' : '修改全检时效标准'" :close-on-click-modal="false" :close-on-press-escape="false" append-to-body>
    <el-form :model="dataForm" :rules="dataRule" ref="dataForm" @keyup.enter.native="dataFormSubmitHandle()" :label-width="$i18n.locale === 'en-US' ? '120px' : '80px'">
      <div class="container">
        <el-form-item label="客户代码" prop="customerId">
          <customer-component v-model="dataForm.customerId" placeholder="客户代码"></customer-component>
        </el-form-item>
        <el-form-item label="客户品名" prop="partId">
          <part-number-component :customer-id="dataForm.customerId" v-model="dataForm.partId" placeholder="客户品名"></part-number-component>
        </el-form-item>
        <el-form-item label="一检" prop="firstInspection">
          <el-input-number :controls="false" v-model="dataForm.firstInspection" placeholder="一检"></el-input-number>
        </el-form-item>
        <el-form-item label="二检" prop="secondInspection">
          <el-input-number :controls="false" v-model="dataForm.secondInspection" placeholder="二检"></el-input-number>
        </el-form-item>
        <el-form-item label="三检" prop="threeInspections">
          <el-input-number :controls="false" v-model="dataForm.threeInspections" placeholder="三检"></el-input-number>
        </el-form-item>
        <el-form-item label="四检" prop="fourInspections">
          <el-input-number :controls="false" v-model="dataForm.fourInspections" placeholder="四检"></el-input-number>
        </el-form-item>
      </div>
      <el-form-item label="品检注记" prop="qualityInspectionNotes">
        <el-input type="textarea" :rows="2" v-model="dataForm.qualityInspectionNotes" placeholder="例如，一检：放大镜"></el-input>
      </el-form-item>
      <div class="container">
        <el-form-item label="返检一" prop="returnInspectionOne">
          <el-input-number :controls="false" v-model="dataForm.returnInspectionOne" placeholder="返检一"></el-input-number>
        </el-form-item>
        <el-form-item label="返检二" prop="returnInspectionTwo">
          <el-input-number :controls="false" v-model="dataForm.returnInspectionTwo" placeholder="返检二"></el-input-number>
        </el-form-item>
        <el-form-item label="返检三" prop="returnInspectionThree">
          <el-input-number :controls="false" v-model="dataForm.returnInspectionThree" placeholder="返检三"></el-input-number>
        </el-form-item>
        <el-form-item label="返检四" prop="returnInspectionFour">
          <el-input-number :controls="false" v-model="dataForm.returnInspectionFour" placeholder="返检四"></el-input-number>
        </el-form-item>
      </div>
      <el-form-item label="返检注记" prop="returnInspectionNotes">
        <el-input type="textarea" :rows="2" v-model="dataForm.returnInspectionNotes" placeholder="例如，返检一:"></el-input>
      </el-form-item>
      <el-form-item label="备注" prop="remark">
        <el-input v-model="dataForm.remark" placeholder="备注"></el-input>
      </el-form-item>
    </el-form>
    <template slot="footer">
      <el-button @click="visible = false">{{ $t('cancel') }}</el-button>
      <el-button type="primary" @click="dataFormSubmitHandle()">{{ $t('confirm') }}</el-button>
    </template>
  </el-dialog>
</template>

<script>
import debounce from 'lodash/debounce'
import mixinViewModule from '@/mixins/view-module'
export default {
  mixins:[mixinViewModule],
  data () {
    return {
      visible: false,
      //创建该页面时，不调用数据查询接口
      mixinViewModuleOptions: {
        createdIsNeed: false,
      },
      dataForm: {
        id: '',
        customerId: '',
        partId: '',
        firstInspection: '',
        secondInspection: '',
        threeInspections: '',
        fourInspections: '',
        returnInspectionOne: '',
        returnInspectionTwo: '',
        returnInspectionThree: '',
        returnInspectionFour: '',
        qualityInspectionNotes: '',
        returnInspectionNotes: '',
        remark: '',
        disabled: '',
        creator: '',
        createDate: '',
        updater: '',
        updateDate: ''
      }
    }
  },
  computed: {
    dataRule () {
      return {
          customerId: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          partId: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],}
    }
  },
  methods: {
    init () {
      this.visible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].resetFields()
        if (this.dataForm.id) {
          this.getInfo()
        }
      })
    },
    // 获取信息
    getInfo () {
      this.$http.get(`/inspect/qualityinspectiontimelimit/${this.dataForm.id}`).then(({ data: res }) => {
        if (res.code !== 0) {
          return this.$message.error(res.msg)
        }
        this.dataForm = {
          ...this.dataForm,
          ...res.data
        }
      }).catch(() => {})
    },
    // 表单提交
    dataFormSubmitHandle: debounce(function () {
      this.$refs['dataForm'].validate((valid) => {
        if (!valid) {
          return false
        }
        let message = '';
        if(this.dataForm.id){
          message = '此操作将修改该数据'
        }else {
          message = '此操作将新增一条数据'
        }
        this.$confirm(message+', 是否继续?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
          center: true
        }).then(() => {
          this.$http[!this.dataForm.id ? 'post' : 'put']('/inspect/qualityinspectiontimelimit/', this.dataForm).then(({ data: res }) => {
            if (res.code !== 0) {
            return this.$message.error(res.msg)
            }
            this.$message({
          message: this.$t('prompt.success'),
            type: 'success',
                    duration: 500,
                    onClose: () => {
              this.visible = false
              this.$emit('refreshDataList')
            }
          })
          }).catch(() => {})
        }).catch(() => {
          this.$message({
            type: 'info',
            message: '已取消'
          });
        });
      })
    }, 1000, { 'leading': true, 'trailing': false })
  }
}
</script>
