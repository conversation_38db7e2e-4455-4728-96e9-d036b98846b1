<template>
    <el-form :model="dataForm" :rules="dataRule" ref="dataForm" @keyup.enter.native="dataFormSubmitHandle()" :label-width="$i18n.locale === 'en-US' ? '120px' : '80px'">
      <div class="container">
        <el-form-item label="抽检日期" prop="samplingDate">
          <el-date-picker
              class="datePicker"
              v-model="dataForm.samplingDate"
              type="date"
              value-format="yyyy-MM-dd"
              placeholder="抽检日期">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="抽检员" prop="spotCheckers">
          <employee-component v-model="dataForm.spotCheckers" :departments="$inspectionDefault" placeholder="请选择"></employee-component>
        </el-form-item>
        <el-form-item label="外发员工姓名" prop="outgoingName">
          <el-input v-model="dataForm.outgoingName"  placeholder="外发员工姓名"></el-input>
        </el-form-item>
      </div>
      <el-form-item label="次批号" prop="subBatchId">
        <span class="font_size">{{subBatchNumber}}</span>
      </el-form-item>
      <div class="container">
        <el-form-item label="外观" prop="appearance">
          <el-radio v-model="dataForm.appearance" label="OK">OK</el-radio>
          <el-radio v-model="dataForm.appearance" label="NG">NG</el-radio>
        </el-form-item>
        <el-form-item label="处理结果" prop="processTheResults">
          <el-radio v-model="dataForm.processTheResults" label="OK">OK</el-radio>
          <el-radio v-model="dataForm.processTheResults" label="NG">NG</el-radio>
        </el-form-item>
      </div>
      <el-form-item label="备注" prop="remark">
        <el-input v-model="dataForm.remark" placeholder="备注"></el-input>
      </el-form-item>
    </el-form>
</template>

<script>
import debounce from 'lodash/debounce'
import mixinViewModule from '@/mixins/view-module'
export default {
  props:{
    subBatchNumber: {
      type:String,
      default: ''
    }
  },
  mixins:[mixinViewModule],
 data () {
    return {
      visible: false,
      //创建该页面时，不调用数据查询接口
      mixinViewModuleOptions: {
        createdIsNeed: false,
      },
      dataForm: {
        id: '',
        subBatchId: '',
        subBatchNumber: this.subBatchNumber,
        spotCheckers: '',
        appearance: '',
        samplingDate: this.formatTimes(new Date()),
        processTheResults: '',
        remark: '',
        disabled: '',
        creator: '',
        createDate: '',
        updater: '',
        updateDate: ''
      }
    }
  },
  computed: {
    dataRule () {
      return {}
    }
  },
  methods: {
    init () {
      this.visible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].resetFields()
        if (this.dataForm.id) {
          this.getInfo()
        }
      })
    },
    // 获取信息
    getInfo () {
      this.$http.get(`/inspect/qualityassurance/${this.dataForm.id}`).then(({ data: res }) => {
        if (res.code !== 0) {
          return this.$message.error(res.msg)
        }
        this.dataForm = {
          ...this.dataForm,
          ...res.data
        }
      }).catch(() => {})
    },
    // 表单提交
    dataFormSubmitHandle() {
      this.$emit('qualityassurance',this.dataForm)
    }
  }
}
</script>
