<template>
  <el-card shadow="never" class="aui-card--fill">
    <div class="mod-inspect__outinspectionrecords}">
      <el-form :inline="true" :model="dataForm" @keyup.enter.native="getDataList()">
        <el-form-item>
          <el-input v-model="dataForm.paramStr" @input="throttleFunction" clearable>
            <el-button slot="append" icon="el-icon-search" @click="getDataList()"></el-button>
          </el-input>
        </el-form-item>
        <el-form-item>
          <el-select v-model="dataForm.type" style="width: 100px">
            <el-option v-for="(item,index) in typeOption"
                       :key="index"
                       :label="item.label"
                       :value="item.value">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-select v-model="dataForm.judgementResult" style="width: 100px" clearable>
            <el-option v-for="(item,index) in judgementResultOption"
                       :key="index"
                       :label="item.label"
                       :value="item.value">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-date-picker
              style="width: 180px"
              v-model="dataForm.inspectionDate"
              type="date"
              value-format="yyyy-MM-dd"
              placeholder="选择日期">
          </el-date-picker>
        </el-form-item>
        <el-form-item>
          <el-button v-if="$hasPermission('inspect:outinspectionrecords:save')" type="primary"
                     @click="addOrUpdateHandle()">{{ $t('add') }}
          </el-button>
        </el-form-item>
        <el-form-item>
          <el-button type="info" @click="openInit">查询数据</el-button>
        </el-form-item>
        <el-dropdown style="float:right;">
          <el-button type="primary">
            更多功能<i class="el-icon-arrow-down el-icon--right"></i>
          </el-button>
          <el-dropdown-menu slot="dropdown">
            <el-dropdown-item>
              <el-form-item>
                <el-button type="info" @click="exportHandle()">{{ $t('export') }}</el-button>
              </el-form-item>
            </el-dropdown-item>
            <el-dropdown-item>
              <el-form-item>
                <el-button v-if="$hasPermission('inspect:outinspectionrecords:export')" type="info"
                           @click="exportTemplateHandle()">
                  {{ $t('exportTemplate') }}
                </el-button>
              </el-form-item>
            </el-dropdown-item>
            <el-dropdown-item>
              <el-form-item>
                <el-tooltip content="下载模板填入信息后导入" placement="top">
                  <el-upload
                      v-if="$hasPermission('inspect:outinspectionrecords:save')"
                      class="upload-demo"
                      :action="mixinViewModuleOptions.addBatchUrl"
                      :headers="headers"
                      :multiple="false"
                      :show-file-list="false"
                      :file-list="fileList"
                      :before-upload="beforeUpload"
                      :on-success="resultHandle"
                      :on-change="handleChange"
                      accept="'.xlsx','.xls'">
                    <el-button type="success">{{ $t('addBatch') }}</el-button>
                  </el-upload>
                </el-tooltip>
                <el-button type="text" v-show="unsuccessfulNameList" @click="dialogVisible = true">导入失败列表
                </el-button>
              </el-form-item>
            </el-dropdown-item>
            <el-dropdown-item>
              <el-form-item>
                <el-button v-if="$hasPermission('inspect:outinspectionrecords:delete')" type="danger"
                           @click="deleteHandle()">{{ $t('deleteBatch') }}
                </el-button>
              </el-form-item>
            </el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown>
      </el-form>
      <el-table v-loading="dataListLoading" ref="dataTable" height="500px" :data="dataList" border
                @selection-change="dataListSelectionChangeHandle" style="width: 100%;">
        <el-table-column type="selection" header-align="center" align="center" width="50"></el-table-column>
        <!--        <el-table-column prop="id" label="id" header-align="center" align="center"></el-table-column>-->
        <!--        <el-table-column prop="partId" label="品号id" header-align="center" align="center"></el-table-column>-->
        <el-table-column prop="designation" label="品号" header-align="center" align="center"></el-table-column>
        <!--        <el-table-column prop="subBatchId" label="次批id" header-align="center" align="center"></el-table-column>-->
        <el-table-column prop="subBatchNumber" label="次批号" header-align="center" align="center" width="140px">
          <template slot-scope="scope">
            <div @click="openTimeLine(scope.row)">{{ scope.row.subBatchNumber }}</div>
          </template>
        </el-table-column>
        <el-table-column prop="inspectionType" label="检查方式" header-align="center" align="center">
          <template slot-scope="scope">
            <el-tag
                effect="dark"
                :type="scope.row.inspectionType === 0 ? 'success' : 'info'">
              {{ scope.row.inspectionType === 0 ? '抽检' : '全检' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="inspectionTime" label="检查日期" header-align="center" align="center"
                         width="200"></el-table-column>
        <!--        <el-table-column prop="inspector" label="检查员" header-align="center" align="center"></el-table-column>-->
        <el-table-column prop="inspectorName" label="检查员姓名" header-align="center" align="center"
                         width="180"></el-table-column>
        <el-table-column prop="judgementResult" label="判定结果" header-align="center" align="center">
          <template slot-scope="scope">
            <el-tag
                effect="dark"
                :type="scope.row.judgementResult === 0 ? '' : 'danger'">
              {{ scope.row.judgementResult === 0 ? 'OK' : 'NG' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="adverseItem" label="不良项" header-align="center" align="center"></el-table-column>
        <el-table-column prop="processMode" label="处理方式" header-align="center" align="center"></el-table-column>
        <!--        <el-table-column prop="performance" label="时效" header-align="center" align="center"></el-table-column>-->
        <!--        <el-table-column prop="performancePoint" label="时效点" header-align="center" align="center"></el-table-column>-->
        <!--        <el-table-column prop="amount" label="金额" header-align="center" align="center"></el-table-column>-->
        <el-table-column prop="remark" label="备注" header-align="center" align="center"></el-table-column>
        <!--        <el-table-column prop="disabled" label="是否失效 0:有效; 1:失效 (逻辑删除)" header-align="center" align="center"></el-table-column>-->
        <!--        <el-table-column prop="creator" label="创建者" header-align="center" align="center"></el-table-column>-->
        <!--        <el-table-column prop="createDate" label="创建时间" header-align="center" align="center"></el-table-column>-->
        <!--        <el-table-column prop="updater" label="更新者" header-align="center" align="center"></el-table-column>-->
        <!--        <el-table-column prop="updateDate" label="更新时间" header-align="center" align="center"></el-table-column>-->
        <el-table-column :label="$t('handle')" fixed="right" header-align="center" align="center" width="150">
          <template slot-scope="scope">
            <el-dropdown>
              <el-button type="primary">
                功能<i class="el-icon-arrow-down el-icon--right"></i>
              </el-button>
              <el-dropdown-menu slot="dropdown">
                <el-dropdown-item>
                  <el-button v-if="$hasPermission('inspect:outinspectionrecords:update')" type="text" size="small"
                             @click="openUpdate(scope.row.id)">{{ $t('update') }}
                  </el-button>
                </el-dropdown-item>
                <el-dropdown-item>
                  <el-button v-if="$hasPermission('inspect:outinspectionrecords:delete')" type="text" size="small"
                             @click="deleteHandle(scope.row.id)">{{ $t('delete') }}
                  </el-button>
                </el-dropdown-item>
              </el-dropdown-menu>
            </el-dropdown>
          </template>
        </el-table-column>
      </el-table>
      <el-pagination
          :current-page="page"
          :page-sizes="[10, 20, 50, 100]"
          :page-size="limit"
          :total="total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="pageSizeChangeHandle"
          @current-change="pageCurrentChangeHandle">
      </el-pagination>
      <!-- 弹窗, 新增 / 修改 -->
      <out-inspection-records-add v-if="addOrUpdateVisible" ref="addOrUpdate"
                                  @refreshDataList="getDataList"></out-inspection-records-add>
      <out-inspection-records-update v-if="addOrUpdateVisible1" ref="outInspectionRecordsUpdate"
                                     @refreshDataList="getDataList"></out-inspection-records-update>
      <out-time-line-dialog v-if="addOrUpdateVisible2" ref="outTimeLine"></out-time-line-dialog>
    </div>
    <el-dialog width="75vw" :visible.sync="visible" title="数据查询">
      <div style="display: flex">
      <el-date-picker
          v-model="dateValue"
          type="daterange"
          @change="fillDate"
          value-format="yyyy-MM-dd"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期">
      </el-date-picker>
        <el-button type="primary" @click="queryStatistics">查询</el-button>
      </div>
      <el-table v-loading="loading1" :data="statisticsData" style="margin-top: 2vh">
        <el-table-column prop="designation" label="品号" header-align="center" align="center" width="140"></el-table-column>
        <el-table-column prop="amountShippedOut" label="发货数量" header-align="center" align="center"></el-table-column>
        <el-table-column prop="issueBatch" label="发货批次数" header-align="center" align="center" ></el-table-column>
        <el-table-column prop="quantityReturned" label="退货数量" header-align="center" align="center" ></el-table-column>
        <el-table-column prop="returnLot" label="退货批次数" header-align="center" align="center" ></el-table-column>
        <el-table-column prop="quantityReturnedAndReturned" label="交回数量" header-align="center" align="center"></el-table-column>
        <el-table-column prop="returnLotAndReturnedLot" label="交回批次数" header-align="center" align="center" ></el-table-column>
        <el-table-column prop="samplingBatch" label="抽检批次数" header-align="center" align="center"></el-table-column>
        <el-table-column prop="qualifiedForSampling" label="合格批次数" header-align="center" align="center"></el-table-column>
        <el-table-column prop="unqualifiedForSampling" label="不合格批次数" header-align="center" align="center">
          <template slot-scope="scope">
            <el-link :underline="false" @click="openGetBadParticularsInit(scope.row)">{{scope.row.unqualifiedForSampling}}</el-link>
          </template>
        </el-table-column>
        <el-table-column prop="unqualifiedRate" label="不合格率" header-align="center" align="center">
          <template slot-scope="scope">
            {{scope.row.unqualifiedRate + '%'}}
          </template>
        </el-table-column>
      </el-table>
      <el-dialog width="35vw" :visible.sync="visible1" title="不良详情" center append-to-body>
        <el-table :data="badParticularsData">
          <el-table-column prop="subBatchNumber" label="批号" width="250" header-align="center" align="center"></el-table-column>
          <el-table-column prop="inspectionType" label="检查方式" header-align="center" align="center">
            <template slot-scope="scope">
                {{ scope.row.inspectionType === 0 ? '抽检' : '全检' }}
            </template>
          </el-table-column>
          <el-table-column prop="adverseItem" label="不良项" width="200" header-align="center" align="center"></el-table-column>
        </el-table>
      </el-dialog>
    </el-dialog>
  </el-card>
</template>

<script>
import mixinViewModule from '@/mixins/view-module'
import OutInspectionRecordsAdd from './outinspectionrecords-add.vue'
import OutInspectionRecordsUpdate from "@/views/modules/inspect/outinspectionrecords-update.vue";
import OutTimeLineDialog from "@/views/modules/inspect/outTimeLineDialog.vue";
import Cookies from "js-cookie";
import th from "element-ui/src/locale/lang/th";

export default {
  mixins: [mixinViewModule],
  data() {
    return {
      message: '',
      headers: {'token': Cookies.get('token')},
      fileList: [{name: '', url: ''}],
      visible:false,
      visible1:false,
      unsuccessfulNameList: '',//  导入失败名称列表
      mixinViewModuleOptions: {
        getDataListURL: '/inspect/outinspectionrecords/page',
        getDataListIsPage: true,
        exportURL: '/inspect/outinspectionrecords/export',
        deleteURL: '/inspect/outinspectionrecords',
        deleteIsBatch: true,
        exportTemplateURL: '/inspect/outinspectionrecords/export/template',
        addBatchUrl: window.SITE_CONFIG['apiURL'] + '/inspect/outinspectionrecords/batchSave',
      },
      addOrUpdateVisible1: false,
      addOrUpdateVisible2: false,
      loading1:false,
      dataForm: {
        id: '',
        type:0,
        judgementResult:'',
        inspectionDate:'',
        paramStr: '',
      },
      statisticsData:[],
      badParticularsData:[],
      dateValue:'',
      startDate:'',
      endDate:'',
      // 判断是否还在继续输入
      timer: null,
      typeOption: [
        {
          label: '批号',
          value: 0
        },
        {
          label: '品号',
          value: 1
        },
      ],
      judgementResultOption:[
        {
          label:'OK',
          value:0
        },
        {
          label:'NG',
          value:1
        }
      ],
    }
  },
  components: {
    OutInspectionRecordsAdd,
    OutInspectionRecordsUpdate,
    OutTimeLineDialog
  },
  activated() {
    console.log("触发了")
    this.$nextTick(() => {
      if (this.$refs.dataTable) {
        this.$refs.dataTable.doLayout();
      }
    });
  },
  methods: {
    openUpdate(id) {
      this.addOrUpdateVisible1 = true
      this.$nextTick(() => {
        this.$refs.outInspectionRecordsUpdate.dataForm.id = id
        this.$refs.outInspectionRecordsUpdate.init()
      })
    },
    openTimeLine(row) {
      this.addOrUpdateVisible2 = true
      this.$nextTick(() => {
        this.$refs.outTimeLine.subBatchId = row.subBatchId;
        this.$refs.outTimeLine.subBatchNumber = row.subBatchNumber;
        this.$refs.outTimeLine.init()
      })
    },
    openInit(){
      this.startDate = ''
      this.endDate = ''
      this.dateValue = ''
      this.statisticsData = []
      this.visible = true
    },
    openGetBadParticularsInit(row){
      const queryData = {
        ...row,
        startTime:this.startDate,
        endTime:this.endDate
      }
      this.badParticularsData = []
      this.visible1 = true
      this.$http.post('inspect/outinspectionrecords/getBadParticulars',queryData).then(({data:res}) =>{
        if(res.code !== 0){
          return this.$message.error(res.msg)
        }
        this.badParticularsData = res.data
      })
    },
    fillDate() {
      if (this.dateValue) {
        // 复制开始和结束日期以避免直接修改原始值
        this.startDate = new Date(this.dateValue[0]);
        let endDate = new Date(this.dateValue[1]);
        // 结束日期加一天
        endDate.setDate(endDate.getDate() + 1);
        // 确保日期格式为 "yyyy-MM-dd"
        const formatDate = (date) => {
          const y = date.getFullYear();
          let m = date.getMonth() + 1;
          m = m < 10 ? '0' + m : m;
          let d = date.getDate();
          d = d < 10 ? '0' + d : d;
          return `${y}-${m}-${d}`;
        };
        this.startDate = formatDate(this.startDate);
        this.endDate = formatDate(endDate);
      } else {
        this.startDate = '';
        this.endDate = '';
      }
    },
    queryStatistics(){
      if(this.startDate === '' || this.endDate === ''){
        return this.$message.warning("请选择日期")
      }
      this.loading1 = true
      console.log('查询')
      this.$http.get(`inspect/outinspectionrecords/getOutgoingStatistics?startTime=${this.startDate}&endTime=${this.endDate}`).then(({data:res})=>{
        if (res.code !== 0) {
          return this.$message.error(res.msg)
        }
        console.log(res)
        this.statisticsData = res.data
        this.loading1 = false
      })
    },
    //节流函数
    throttleFunction() {
      // 如果timer不为空就表明在持续输入,每次改变都清除定时，这样就可以实现只输入最后一次结果(注意每次间隔如果超过500毫秒就会输出)
      if (this.timer !== null) clearTimeout(this.timer)
      this.timer = setTimeout(() => {
        this.getDataList()
      }, 500)
    },
    beforeUpload(file) {
      this.message = this.$message({
        message: '上传中，请勿操作',
        center: true,
        duration: 0,
      });
    },
    handleChange(file, fileList) {
      this.fileList = fileList.slice(-3);

    },
    resultHandle(res) {
      if (res.code !== 0) {
        return this.$message.error(res.msg)
      }
      this.$message({
        message: this.$t('prompt.success'),
        type: 'success',
        duration: 500,
        onClose: () => {
          if (res.data) {
            this.unsuccessfulNameList = res.data
          }
          this.visible = false
          this.query()
        }
      })
    },
  }
}
</script>
