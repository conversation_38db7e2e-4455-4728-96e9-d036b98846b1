<template>
  <el-dialog :visible.sync="visible" title="流转" :close-on-click-modal="false" :close-on-press-escape="false" append-to-body>
    <el-form v-if="dataForm[index]" :model="dataForm[index]" :rules="dataRule" ref="dataForm" @keyup.enter.native="dataFormSubmitHandle()" :label-width="$i18n.locale === 'en-US' ? '120px' : '80px'">
      <div class="container">
        <div class="box">
          <el-form-item label="部品品名" prop="partId">
            <part-inspection-batch-component  v-model="dataForm[index].partId" @partSubBatchData="partSubBatchData"  placeholder="请输入部品品名"></part-inspection-batch-component>
          </el-form-item>
        </div>
        <div class="box">
          <el-form-item label="批号查询" prop="subBatchId">
            <batch-under-component  v-model="dataForm[index].subBatchId" @batchSubBatchData="batchSubBatchData" placeholder="请输入批号"></batch-under-component>
          </el-form-item>
        </div>
      </div>
      <div class="container">
        <div class="box">
          <el-form-item label="批号" prop="temporaryBatchNumber">
            <span class="font_size">{{dataForm[index].temporaryBatchNumber}}</span>
          </el-form-item>
        </div>
        <div class="box">
          <el-form-item label="项目类别" prop="itemCategory">
            <ren-select-item-category v-model="dataForm[index].itemCategory" placeholder="请选择项目类别" dict-type="item_category"></ren-select-item-category>
          </el-form-item>
        </div>
      </div>
      <div class="container">
        <div class="item">
          <el-form-item label="转出单位" prop="productCategory">
            <ren-select-product-category @change="dropdownSelectionBox" v-model="dataForm[index].transferUnit" placeholder="转出单位" dict-type="product_category"></ren-select-product-category>
          </el-form-item>
        </div>
        <div class="item">
          <el-form-item label="转出者" prop="transferee">
            <employee-component v-model="dataForm[index].transferee" placeholder="工号"></employee-component>
          </el-form-item>
        </div>
      </div>
      <div class="container">
        <div class="box">
          <el-form-item label="转出数量" prop="transferOutQuantity">
            <el-input v-model.number="dataForm[index].transferOutQuantity" type="number" class="input-green" placeholder="转出数量" style="width: 30%;"></el-input>
          </el-form-item>
        </div>
        <div class="box" v-if="currentQuantity[index]">
          当前单位数量：<span class="font_size">{{currentQuantity[index]}}</span>
        </div>
      </div>

      <div class="container">
        <div class="box">
          <el-form-item label="损耗数量">
            <el-input v-model.number="dataForm[index].numberOfLosses" type="number" placeholder="损耗数" style="width: 200px;" class="birthday"></el-input>
            <p style="display: none">{{numberOfLosses}}</p>
          </el-form-item>
        </div>
      </div>

      <div class="container">
        <div class="box">
          <el-form-item label="接收数量" prop="receiveQuantity">
            <el-input v-model.number="dataForm[index].receiveQuantity" type="number" placeholder="接收数量" style="width: 30%;" class="birthday"></el-input>
          </el-form-item>
        </div>
      </div>
      <div class="container">
        <div class="item">
          <el-form-item label="接收单位" prop="receivingDepartment">
            <ren-select-product-category v-model="dataForm[index].receivingDepartment" placeholder="接收单位" dict-type="product_category"></ren-select-product-category>
          </el-form-item>
        </div>
        <div class="item">
          <el-form-item label="接收者" prop="recipient">
            <employee-component v-model="dataForm[index].recipient" placeholder="工号"></employee-component>
          </el-form-item>
        </div>
      </div>
      <div class="container">
        <div class="box">
          <el-form-item label="接收时间" prop="receivingTime">
            <el-date-picker
                style="width: 200px"
                v-model="dataForm[index].receivingTime"
                type="datetime"
                value-format="yyyy-MM-dd HH:mm:ss"
                placeholder="请选择接收时间(含时分)">
            </el-date-picker>
          </el-form-item>
        </div>
        <div class="box">
          <el-form-item label="存放位置" prop="storageLocation">
            <el-input v-model="dataForm[index].storageLocation" placeholder="存放位置"></el-input>
          </el-form-item>
        </div>
      </div>

      <el-form-item label="备注" prop="remark">
        <el-input v-model="dataForm[index].remark" placeholder="备注"></el-input>
      </el-form-item>
    </el-form>
    <template slot="footer">
      <el-button @click="handlePrev">上一条</el-button>
      <el-button @click="handleNext">下一条</el-button>
      <el-button @click="visible = false">{{ $t('cancel') }}</el-button>
      <el-button v-if="index == dataForm.length-1" type="primary" @click="circulationHandle()">{{ $t('confirm') }}</el-button>
    </template>
    <div>
      <queryproductprocessflow-total v-if="addOrUpdateVisibles" ref="confirmTotal" @refreshDataList="refreshDataList"></queryproductprocessflow-total>
    </div>
  </el-dialog>
</template>

<script>
import debounce from 'lodash/debounce'
import {getDictLabel} from "@/utils/index"
import mixinViewModule from '@/mixins/view-module'
import QueryproductprocessflowTotal from "./queryproductprocessflow-total.vue";
export default {
  components: {QueryproductprocessflowTotal},
  mixins:[mixinViewModule],
  props:{
    transferData:Array,
  },
  data () {
    return {
      currentQuantity:[],
      visible: false,
      addOrUpdateVisibles: false,
      index: 0,
      dataForm: [],
      subBatchIdWatch:'',
      partIdWatch:'',
    }
  },
  computed: {
    dataRule () {
      return {}
    },
    numberOfLosses:function(){
      let number = this.dataForm[this.index].transferOutQuantity-this.dataForm[this.index].numberOfLosses;
      console.log(this.dataForm[this.index].transferOutQuantity+'-'+this.dataForm[this.index].numberOfLosses+'='+number)
      this.dataForm[this.index].receiveQuantity = number
      return number
    },
  },
  mounted() {
    console.log(this.transferData,'流转数据')
  },
  watch:{
    "index"(value){
      console.log('听见')
      this.subBatchIdWatch = this.dataForm[value].subBatchId
      this.partIdWatch = this.dataForm[value].partIdWatch
    },
    subBatchIdWatch:function(newValue, oldValue){
      this.dropdownSelectionBox()
    },
    partIdWatch:function (newValue, oldValue) {
      this.dropdownSelectionBox()
    }
  },
  methods: {
    refreshDataList(){
      this.visible = false
      this.$emit('refreshDataList')
    },
    circulationHandle(){
      console.log('及那人')
        this.addOrUpdateVisibles = true
        this.$nextTick(()=>{
          this.$refs.confirmTotal.dataLists = this.dataForm
          this.$refs.confirmTotal.init()
        })
    },
    batchSubBatchData(data){
      if(!this.dataForm[this.index].subBatchId){
        this.dataForm[this.index].subBatchId = data.subBatchId
      }
      this.dataForm[this.index].temporaryBatchNumber = getDictLabel("size_category",data.sizeCategory)+data.batchNumber+'-'+data.subBatchNumber;
      this.dataForm[this.index].transferUnit = 0
      this.dataForm[this.index].receivingDepartment = 0

    },
    partSubBatchData(data){
      if(!this.dataForm[this.index].partId){
        this.dataForm[this.index].partId = data.partId
      }
      this.dataForm[this.index].subBatchId = data.subBatchId
      this.dataForm[this.index].temporaryBatchNumber = getDictLabel("size_category",data.sizeCategory)+data.batchNumber+'-'+data.subBatchNumber;
    },
    dropdownSelectionBox(value){
      let operation = 0
      if(value){
        operation = value
      }
      this.$http.get(`/batch/subbatch/queryTheCurrentUnitQuantity?subBatchId=`+this.dataForm[this.index].subBatchId+`&productCategoryTransferOut=
      `+operation).then(({data: res}) => {
        if (res.code !== 0) {
          return this.$message.error(res.msg)
        }
        this.currentQuantity[this.index] = res.data
        this.dataForm[this.index].transferOutQuantity = res.data
        this.dataForm[this.index].receiveQuantity = res.data
      }).catch(() => {
      })
    },
    handlePrev() {
      if (this.index > 0) {
        this.index -= 1;
      }else {
        this.$message({
          message: '已经是第一条',
          type: 'warning'
        });
      }
    },
    handleNext() {
      if (this.index < this.dataForm.length - 1) {
        this.index += 1;
      }else {
        this.$message({
          message: '已经是最后一条',
          type: 'warning'
        });
      }
    },
    init () {
      this.visible = true
      if(this.transferData){
        this.dataForm = this.transferData.map(item =>({
          ...item,
          temporaryBatchNumber:getDictLabel("size_category",`${item.sizeCategory}`)+`${item.batchNumber}-${item.subBatchNumber}`,
          transferUnit:`${item.deptName}`,
          receivingDepartment:5,
          itemCategory:0,
          transferee:'',
          transferOutQuantity:`${item.quantity}`,
          receiveQuantity:`${item.quantity}`,
          receivingTime:this.formatTimes(new Date()),
          remark:''
        }))
      }
    },
  }
}
</script>

<style lang="scss" scoped>
::v-deep .birthday .el-input__inner {
  background-color: #e8e889 !important;
  color: black !important;
}
::v-deep .input-green .el-input__inner{
  background-color: #88e388 !important; /* 设置输入框的背景色为绿色 */
  color: black !important;
}
</style>

