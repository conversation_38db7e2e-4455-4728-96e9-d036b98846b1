<template>
  <el-card shadow="never" class="aui-card--fill">
    <div class="mod-warehouse__queryproductprocessflow">
      <el-form :inline="true" :model="dataForm" @keyup.enter.native="getDataList()">
        <el-form-item label="部门查询">
          <ren-select v-model="dataForm.deptId" dict-type="product_category"></ren-select>
        </el-form-item>
        <el-form-item label="查询时间">
          <div class="block">
            <el-date-picker
                v-model="startToEnd"
                @change="handleDateChange"
                @clear="clearQuery"
                type="daterange"
                range-separator="至"
                start-placeholder="开始日期"
                value-format="yyyy-MM-dd"
                clearable
                end-placeholder="结束日期">
            </el-date-picker>
          </div>
        </el-form-item>
        <el-form-item label="批号查询">
          <batch-under-component v-model="dataForm.batchId" :display-or-not="false"></batch-under-component>
        </el-form-item>
        <el-form-item>
          <el-button v-if="$hasPermission('batch:productprocessflow:save')" type="primary" @click="circulationHandle()">流转</el-button>
        </el-form-item>
      </el-form>
      <el-table height="500px" ref="multipleTable" v-loading="dataListLoading" :data="dataList" border @selection-change="dataListSelectionChange" style="width: 100%;">
        <el-table-column type="selection" header-align="center" align="center" width="50"></el-table-column>
        <el-table-column label="批号" header-align="center" align="center">
          <template slot-scope="scope">
            <div>
              {{$getDictLabel("size_category",scope.row.sizeCategory) + scope.row.batchNumber+'-' + scope.row.subBatchNumber}}
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="" label="品名编号" header-align="center" align="center"></el-table-column>
        <el-table-column prop="designation" label="客户品名" header-align="center" align="center"></el-table-column>
        <el-table-column prop="" label="流转代码" header-align="center" align="center"></el-table-column>
        <el-table-column prop="productStatus" label="产品状态" header-align="center" align="center">
          <template slot-scope="scope">
            {{$getDictLabel("management_category",scope.row.productStatus)}}
          </template>
        </el-table-column>
        <el-table-column prop="deptName" label="目前部门" header-align="center" align="center">
          <template slot-scope="scope">
            {{$getDictLabel("product_category",scope.row.deptName)}}
          </template>
        </el-table-column>
        <el-table-column prop="quantity" label="数量" header-align="center" align="center"></el-table-column>
        <el-table-column prop="storageLocation" label="位置" header-align="center" align="center"></el-table-column>
      </el-table>
      <div>
        <el-form :inline="true">
          <el-form-item label="接收部门">
            <ren-select v-model="dataForm.receivingDeptId" dict-type="product_category"></ren-select>
          </el-form-item>

          <el-form-item label="工号">
            <employee-component v-model="dataForm.userId" @employeeData="employeeData"></employee-component>
          </el-form-item>

          <el-form-item label="姓名">
            <div class="font_size">
              {{dataForm.userName}}
            </div>
          </el-form-item>

          <el-form-item label="接收时间">
            <el-date-picker
                v-model="dataForm.acceptanceTime"
                type="date"
                value-format="yyyy-MM-dd"
                placeholder="选择日期">
            </el-date-picker>
          </el-form-item>
        </el-form>
      </div>
      <el-pagination
        :current-page="page"
        :page-sizes="[10, 20, 50, 100]"
        :page-size="limit"
        :total="total"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="pageSizeChangeHandle"
        @current-change="pageCurrentChangeHandle">
      </el-pagination>
      <!-- 弹窗, 新增 / 修改 -->
      <queryproductprocessflow-add-or-update v-if="addOrUpdateVisible" ref="addOrUpdate" :transfer-data="circulationList"  @refreshDataList="refreshDataList"></queryproductprocessflow-add-or-update>
    </div>
  </el-card>
</template>

<script>
import mixinViewModule from '@/mixins/view-module'
import queryproductprocessflowAddOrUpdate from "./queryproductprocessflow-add-or-update.vue";

export default {
  mixins: [mixinViewModule],
  data () {
    return {
      mixinViewModuleOptions: {
        getDataListURL: '/batch/subbatch/queryProductProcessFlowList',
        getDataListIsPage: true,
        deleteIsBatch: true
      },
      startToEnd: [],
      dataForm: {
        id: '',
        paramStr:'',
        startDate:'',
        endDate:'',
        acceptanceTime: this.formatDates(new Date()),
        deptId: 0,
        receivingDeptId: 5,
        batchId: '',
        storageLocation: '',
        userId: '',
        userName: '',
      },
      circulationList:[],
    }
  },
  components: {
    queryproductprocessflowAddOrUpdate
  },
  computed:{
    listenChange () {
      return {...this.dataForm}
    }
  },
  watch: {
    listenChange (val) {
      this.getDataList()
    },
    startToEnd(newVal) {
      if (!newVal) {
        this.dataForm.startDate = ''
        this.dataForm.endDate = ''
        this.getDataList();
      }
    }
  },
  methods:{
    clearQuery(){
      this.
      this.getDataList()
    },
    refreshDataList(){
      this.getDataList()
      this.$refs.multipleTable.clearSelection();
    },
    circulationHandle(){
      if(this.circulationList.length != 0){
        this.addOrUpdateVisible = true
        this.$nextTick(()=>{
          this.$refs.addOrUpdate.init()
        })
      }else {
        this.$message({
          message: '请先选择要流转的批次',
          type: 'warning'
        })
      }
    },
    dataListSelectionChange(selected){
      this.circulationList = selected;
      this.circulationList.forEach(function (item) {
        item.numberOfLosses = 0
      })
    },
    employeeData(data){
      const regex = /\(([^)]+)\)/; // 匹配括号中的内容
      const matches = data.label.match(regex); // 使用正则表达式匹配
      if (matches && matches.length > 1) {
        const content = matches[1]; // 获取匹配到的内容
        this.dataForm.userName = content
      }
    }
  }
}
</script>

<style scoped>

</style>
