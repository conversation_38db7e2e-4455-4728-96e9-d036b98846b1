<template>
  <el-dialog :visible.sync="visible" :title="!dataForm.id ? $t('add') : $t('update')" :close-on-click-modal="false" :close-on-press-escape="false" append-to-body>
    <el-form :model="dataForm" :rules="dataRule" ref="dataForm" @keyup.enter.native="dataFormSubmitHandle()" :label-width="$i18n.locale === 'en-US' ? '120px' : '80px'">
          <el-form-item label="客户id" prop="customerId">
          <el-input v-model="dataForm.customerId" placeholder="客户id"></el-input>
      </el-form-item>
          <el-form-item label="部门id" prop="deptId">
          <el-input v-model="dataForm.deptId" placeholder="部门id"></el-input>
      </el-form-item>
          <el-form-item label="姓名" prop="contactName">
          <el-input v-model="dataForm.contactName" placeholder="姓名"></el-input>
      </el-form-item>
          <el-form-item label="电话" prop="contactTelephone">
          <el-input v-model="dataForm.contactTelephone" placeholder="电话"></el-input>
      </el-form-item>
          <el-form-item label="电子邮箱" prop="email">
          <el-input v-model="dataForm.email" placeholder="电子邮箱"></el-input>
      </el-form-item>
          <el-form-item label="通讯软件" prop="contactCommunicationSoftware">
          <el-input v-model="dataForm.contactCommunicationSoftware" placeholder="通讯软件"></el-input>
      </el-form-item>
          <el-form-item label="地区" prop="area">
          <el-input v-model="dataForm.area" placeholder="地区"></el-input>
      </el-form-item>
          <el-form-item label="分类 1:技术;2:品保..." prop="sort">
          <el-input v-model="dataForm.sort" placeholder="分类 1:技术;2:品保..."></el-input>
      </el-form-item>
          <el-form-item label="应对事项" prop="countermeasures">
          <el-input v-model="dataForm.countermeasures" placeholder="应对事项"></el-input>
      </el-form-item>
          <el-form-item label="备注" prop="remark">
          <el-input v-model="dataForm.remark" placeholder="备注"></el-input>
      </el-form-item>
                </el-form>
    <template slot="footer">
      <el-button @click="visible = false">{{ $t('cancel') }}</el-button>
      <el-button type="primary" @click="dataFormSubmitHandle()">{{ $t('confirm') }}</el-button>
    </template>
  </el-dialog>
</template>

<script>
import debounce from 'lodash/debounce'
export default {
  data () {
    return {
      visible: false,
      dataForm: {
        id: '',
        customerId: '',
        deptId: '',
        contactName: '',
        contactTelephone: '',
        email: '',
        contactCommunicationSoftware: '',
        area: '',
        sort: '',
        countermeasures: '',
        remark: '',
        disabled: '',
        creator: '',
        createDate: '',
        updater: '',
        updateDate: ''
      }
    }
  },
  computed: {
    dataRule () {
      return {
          id: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          customerId: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          deptId: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          contactName: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          contactTelephone: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          email: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          contactCommunicationSoftware: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          area: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          sort: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          countermeasures: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          remark: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
                }
    }
  },
  methods: {
    init () {
      this.visible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].resetFields()
        if (this.dataForm.id) {
          this.getInfo()
        }
      })
    },
    // 获取信息
    getInfo () {
      this.$http.get(`/customer/customercontact/${this.dataForm.id}`).then(({ data: res }) => {
        if (res.code !== 0) {
          return this.$message.error(res.msg)
        }
        this.dataForm = {
          ...this.dataForm,
          ...res.data
        }
      }).catch(() => {})
    },
    // 表单提交
    dataFormSubmitHandle: debounce(function () {
      this.$refs['dataForm'].validate((valid) => {
        if (!valid) {
          return false
        }
        this.$http[!this.dataForm.id ? 'post' : 'put']('/customer/customercontact/', this.dataForm).then(({ data: res }) => {
          if (res.code !== 0) {
            return this.$message.error(res.msg)
          }
          this.$message({
            message: this.$t('prompt.success'),
            type: 'success',
            duration: 500,
            onClose: () => {
              this.visible = false
              this.$emit('refreshDataList')
            }
          })
        }).catch(() => {})
      })
    }, 1000, { 'leading': true, 'trailing': false })
  }
}
</script>
