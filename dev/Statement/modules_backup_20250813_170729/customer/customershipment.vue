<template>
  <el-card shadow="never" class="aui-card--fill">
    <div class="mod-customer__customershipment}">
      <el-form :inline="true" :model="dataForm" @keyup.enter.native="getDataList()">
        <el-form-item>
          <el-input v-model="dataForm.id" placeholder="id" clearable>
            <el-button slot="append" icon="el-icon-search" @click="getDataList()"></el-button>
          </el-input>
        </el-form-item>
<!--        <el-form-item>
          <el-button @click="getDataList()">{{ $t('query') }}</el-button>
        </el-form-item>-->
        <el-form-item>
          <el-button type="info" @click="exportHandle()">{{ $t('export') }}</el-button>
        </el-form-item>
        <el-form-item>
          <el-button v-if="$hasPermission('customer:customershipment:save')" type="primary" @click="addOrUpdateHandle()">{{ $t('add') }}</el-button>
        </el-form-item>
        <el-form-item>
          <el-button v-if="$hasPermission('customer:customershipment:delete')" type="danger" @click="deleteHandle()">{{ $t('deleteBatch') }}</el-button>
        </el-form-item>
      </el-form>
      <el-table v-loading="dataListLoading" :data="dataList" border @selection-change="dataListSelectionChangeHandle" style="width: 100%;">
        <el-table-column type="selection" header-align="center" align="center" width="50"></el-table-column>
        <el-table-column prop="id" label="id" header-align="center" align="center"></el-table-column>
        <el-table-column prop="orderType" label="订单类型" header-align="center" align="center"><template slot-scope="scope">{{scope.row.orderType===0?'生产订单':'预示订单'}}</template></el-table-column>
<!--        <el-table-column prop="customerId" label="客户id" header-align="center" align="center"></el-table-column>-->
        <el-table-column prop="orderNumber" label="订单号" header-align="center" align="center"></el-table-column>
        <el-table-column prop="trackingNumber" label="制交单号" header-align="center" align="center"></el-table-column>
        <el-table-column prop="partCode" label="品号" header-align="center" align="center"></el-table-column>
        <el-table-column prop="amountReceivable" label="应收金额" header-align="center" align="center"></el-table-column>
        <el-table-column prop="currency" label="币别" header-align="center" align="center">
          <template slot-scope="scope">
            {{scope.row.currency===0? '人民币' :
              scope.row.currency===1?'欧元':
                  scope.row.currency===2?'英镑':
                      scope.row.currency===3?'日元':
                          scope.row.currency===4?'澳元':
                              scope.row.currency===5?'加元':
                                  scope.row.currency===6?'瑞士法郎':
                                      scope.row.currency===7?'美元':
                                          scope.row.currency===8?'印度卢比':
                                              scope.row.currency===9?'新加坡元':
                          scope.row.currency===10?'港元':'未知'
            }}
          </template>
        </el-table-column>
<!--        <el-table-column prop="deliveryMethod" label="交货方式(指定或者未指定)" header-align="center" align="center"></el-table-column>-->
        <el-table-column prop="shipmentQuantity" label="出货数量" header-align="center" align="center"></el-table-column>
        <el-table-column prop="placeOfReceipt" label="收货地点(国内或者国外)" header-align="center" align="center"></el-table-column>
        <el-table-column prop="receiver" label="收货人" header-align="center" align="center"></el-table-column>
        <el-table-column prop="shipmentTelephone" label="电话" header-align="center" align="center"></el-table-column>
        <el-table-column prop="email" label="电子邮箱" header-align="center" align="center"></el-table-column>
        <el-table-column prop="shipmentCommunicationSoftware" label="通讯软件" header-align="center" align="center"></el-table-column>
        <el-table-column prop="packing" label="包装方式" header-align="center" align="center"></el-table-column>
        <el-table-column prop="remark" label="备注" header-align="center" align="center"></el-table-column>
<!--        <el-table-column prop="disabled" label="是否失效 0:有效; 1:失效 (逻辑删除)" header-align="center" align="center"></el-table-column>
        <el-table-column prop="creator" label="创建者" header-align="center" align="center"></el-table-column>
        <el-table-column prop="createDate" label="创建时间" header-align="center" align="center"></el-table-column>
        <el-table-column prop="updater" label="更新者" header-align="center" align="center"></el-table-column>
        <el-table-column prop="updateDate" label="更新时间" header-align="center" align="center"></el-table-column>-->
        <el-table-column :label="$t('handle')" fixed="right" header-align="center" align="center" width="150">
          <template slot-scope="scope">
            <el-button v-if="$hasPermission('customer:customershipment:update')" type="text" size="small" @click="addOrUpdateHandle(scope.row.id)">{{ $t('update') }}</el-button>
            <el-button v-if="$hasPermission('customer:customershipment:delete')" type="text" size="small" @click="deleteHandle(scope.row.id)">{{ $t('delete') }}</el-button>
          </template>
        </el-table-column>
      </el-table>
      <el-pagination
        :current-page="page"
        :page-sizes="[10, 20, 50, 100]"
        :page-size="limit"
        :total="total"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="pageSizeChangeHandle"
        @current-change="pageCurrentChangeHandle">
      </el-pagination>
      <!-- 弹窗, 新增 / 修改 -->
      <add-or-update v-if="addOrUpdateVisible" ref="addOrUpdate" @refreshDataList="getDataList"></add-or-update>
    </div>
  </el-card>
</template>

<script>
import mixinViewModule from '@/mixins/view-module'
import AddOrUpdate from './customershipment-add-or-update'
export default {
  mixins: [mixinViewModule],
  data () {
    return {
      mixinViewModuleOptions: {
        getDataListURL: '/customer/customershipment/page',
        getDataListIsPage: true,
        exportURL: '/customer/customershipment/export',
        deleteURL: '/customer/customershipment',
        deleteIsBatch: true
      },
      dataForm: {
        id: '',
        orderNumber:'',
        orderType:null,
      }
    }
  },
  components: {
    AddOrUpdate
  }
}
</script>
