<template>
  <el-dialog :visible.sync="visible" :title="!dataForm.id ? '新增客户信息' : '修改客户信息'" :close-on-click-modal="false"
             :close-on-press-escape="false" append-to-body>
    <el-form :model="dataForm" :rules="dataRule" ref="dataForm" @keyup.enter.native="dataFormSubmitHandle()"
             :label-width="$i18n.locale === 'en-US' ? '120px' : '80px'">
      <el-form-item label="代号" prop="code">
        <el-input v-model="dataForm.code" placeholder="代号"></el-input>
      </el-form-item>
      <el-form-item label="中文名称" prop="chineseName">
        <el-input v-model="dataForm.chineseName" placeholder="中文名称"></el-input>
      </el-form-item>
      <el-form-item label="英文名称" prop="englishName">
        <el-input v-model="dataForm.englishName" placeholder="英文名称"></el-input>
      </el-form-item>
      <el-form-item label="社会码" prop="socialCode">
        <el-input v-model="dataForm.socialCode" placeholder="社会码"></el-input>
      </el-form-item>
      <el-form-item label="营业项目" prop="businessItems">
        <el-input v-model="dataForm.businessItems" placeholder="营业项目"></el-input>
      </el-form-item>
      <el-form-item label="客户等级" prop="level">
        <el-select v-model="dataForm.level" placeholder="生产类型">
          <el-option
              v-for="item in level"
              :key="item.value"
              :label="item.label"
              :value="item.value">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="采购周期" prop="leadTime">
        <el-input v-model="dataForm.leadTime" placeholder="采购周期"></el-input>
      </el-form-item>
      <el-form-item label="对账周期" prop="reconciliationCycle">
        <el-input v-model="dataForm.reconciliationCycle" placeholder="对账周期"></el-input>
      </el-form-item>
      <el-form-item label="付款周期" prop="paymentCycle">
        <el-input v-model="dataForm.paymentCycle" placeholder="付款周期"></el-input>
      </el-form-item>
      <el-form-item label="昵称" prop="nickname">
        <el-input v-model="dataForm.nickname" placeholder="昵称"></el-input>
      </el-form-item>
      <el-form-item label="备注" prop="remark">
        <el-input v-model="dataForm.remark" placeholder="备注"></el-input>
      </el-form-item>
    </el-form>
    <template slot="footer">
      <el-button @click="visible = false">{{ $t('cancel') }}</el-button>
      <el-button type="primary" @click="dataFormSubmitHandle()">{{ $t('confirm') }}</el-button>
    </template>
  </el-dialog>
</template>

<script>
import debounce from 'lodash/debounce'

export default {
  data() {
    return {
      visible: false,
      dataForm: {
        id: '',
        code: '',
        chineseName: '',
        englishName: '',
        socialCode: '',
        businessItems: '',
        level: '',
        leadTime: '',
        reconciliationCycle: '',
        paymentCycle: '',
        nickname: '',
        remark: '',
      },
      level: [{
        value: 1,
        label: '一般'
      }, {
        value: 2,
        label: '重要'
      }, {
        value: 3,
        label: 'VIP'
      }],
    }
  },
  computed: {
    dataRule() {
      return {
        code: [
          {required: true, message: this.$t('validate.required'), trigger: 'blur'}
        ],
        chineseName: [
          {required: true, message: this.$t('validate.required'), trigger: 'blur'}
        ],
        englishName: [
          {required: true, message: this.$t('validate.required'), trigger: 'blur'}
        ],
      }
    }
  },
  methods: {
    init() {
      this.visible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].resetFields()
        if (this.dataForm.id) {
          this.getInfo()
        }
      })
    },
    // 获取信息
    getInfo() {
      this.$http.get(`/customer/customer/${this.dataForm.id}`).then(({data: res}) => {
        if (res.code !== 0) {
          return this.$message.error(res.msg)
        }
        this.dataForm = {
          ...this.dataForm,
          ...res.data
        }
      }).catch(() => {
      })
    },
    // 表单提交
    dataFormSubmitHandle: debounce(function () {
      this.$refs['dataForm'].validate((valid) => {
        if (!valid) {
          return false
        }
        this.$http[!this.dataForm.id ? 'post' : 'put']('/customer/customer/', this.dataForm).then(({data: res}) => {
          if (res.code !== 0) {
            return this.$message.error(res.msg)
          }
          this.$message({
            message: this.$t('prompt.success'),
            type: 'success',
            duration: 500,
            onClose: () => {
              this.visible = false
              this.$emit('refreshDataList')
            }
          })
        }).catch(() => {
        })
      })
    }, 1000, {'leading': true, 'trailing': false})
  }
}
</script>
