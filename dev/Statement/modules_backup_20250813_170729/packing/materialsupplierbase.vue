<template>
  <el-card shadow="never" class="aui-card--fill">
    <div class="mod-packing__materialsupplierbase}">
      <el-form :inline="true" :model="dataForm" @keyup.enter.native="getDataList()">
        <el-form-item>
          <el-tooltip content="请输入查询关键字" placement="top">
            <el-input v-model="dataForm.paramStr" placeholder="查询关键字" @input="throttleFunction" clearable>
              <el-button slot="append" icon="el-icon-search" @click="getDataList()"></el-button>
            </el-input>
          </el-tooltip>
        </el-form-item>
        <el-form-item>
          <el-button v-if="$hasPermission('packing:materialsupplierbase:save')" type="primary" @click="addOrUpdateHandle()">{{ $t('add') }}</el-button>
        </el-form-item>
        <el-dropdown  style="float:right;">
          <el-button type="primary">
            更多功能<i class="el-icon-arrow-down el-icon--right"></i>
          </el-button>
          <el-dropdown-menu slot="dropdown">
            <el-dropdown-item>
              <el-form-item>
                <el-button type="info" @click="exportHandle()">{{ $t('export') }}</el-button>
              </el-form-item>
            </el-dropdown-item>
            <el-dropdown-item>
              <el-form-item>
                <el-button v-if="$hasPermission('packing:materialsupplierbase:export')" type="info" @click="exportTemplateHandle()">
                  {{ $t('exportTemplate') }}
                </el-button>
              </el-form-item>
            </el-dropdown-item>
            <el-dropdown-item>
              <el-form-item>
                <el-tooltip content="下载模板填入信息后导入" placement="top">
                  <el-upload
                          v-if="$hasPermission('packing:materialsupplierbase:save')"
                          class="upload-demo"
                          :action="mixinViewModuleOptions.addBatchUrl"
                          :headers="headers"
                          :multiple="false"
                          :show-file-list="false"
                          :file-list="fileList"
                          :before-upload="beforeUpload"
                          :on-success="resultHandle"
                          :on-change="handleChange"
                          accept="'.xlsx','.xls'">
                    <el-button type="success">{{ $t('addBatch') }}</el-button>
                  </el-upload>
                </el-tooltip>
                <el-button type="text" v-show="unsuccessfulNameList" @click="dialogVisible = true">导入失败列表</el-button>
              </el-form-item>
            </el-dropdown-item>
            <el-dropdown-item>
              <el-form-item>
                <el-button v-if="$hasPermission('packing:materialsupplierbase:delete')" type="danger" @click="deleteHandle()">{{ $t('deleteBatch') }}</el-button>
              </el-form-item>
            </el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown>
      </el-form>
      <el-table v-loading="dataListLoading" height="550px" :data="dataList" border @selection-change="dataListSelectionChangeHandle" style="width: 100%;">
        <el-table-column type="selection" header-align="center" align="center" width="50"></el-table-column>
<!--        <el-table-column prop="id" label="id" header-align="center" align="center" width="100"></el-table-column>-->
        <el-table-column prop="supplierCode" label="供应商代码" header-align="center" align="center" width="100"></el-table-column>
        <el-table-column prop="supplierName" label="供应商名称" header-align="center" align="center" width="280"></el-table-column>
        <el-table-column prop="chineseName" label="中文名称" header-align="center" align="center" width="280"></el-table-column>
        <el-table-column prop="englishName" label="英文名称" header-align="center" align="center"></el-table-column>
        <el-table-column prop="supplierAddress" label="供应商地址" header-align="center" align="center" width="280"></el-table-column>
        <el-table-column prop="mapCoordinates" label="地图坐标" header-align="center" align="center"></el-table-column>
<!--        <el-table-column prop="distance" label="与公司的距离" header-align="center" align="center" width="120"></el-table-column>-->
<!--        <el-table-column prop="travelTime" label="车程时间" header-align="center" align="center"></el-table-column>-->
        <el-table-column prop="phone" label="电话" header-align="center" align="center" width="120"></el-table-column>
        <el-table-column prop="website" label="公司网址" header-align="center" align="center" width="120"></el-table-column>
        <el-table-column prop="companyLicense" label="营业执照" header-align="center" align="center" width="100"></el-table-column>
        <el-table-column prop="socialCode" label="社会码" header-align="center" align="center"></el-table-column>
        <el-table-column prop="companyBusinessProjects" label="公司营业项目" header-align="center" align="center" width="120"></el-table-column>
        <el-table-column prop="companyTransactions" label="往来项目" header-align="center" align="center"></el-table-column>
<!--        <el-table-column prop="futureDevelopmentEstimate" label="未来发展预估" header-align="center" align="center"></el-table-column>-->
<!--        <el-table-column prop="mainTradingCompany" label="主要往来公司名称" header-align="center" align="center"></el-table-column>-->
<!--        <el-table-column prop="supplierLevel" label="供应商等级" header-align="center" align="center"></el-table-column>-->
<!--        <el-table-column prop="ratingTime" label="评定时间" header-align="center" align="center"></el-table-column>-->
<!--        <el-table-column prop="procurementRequirements" label="采购需求及注意事项" header-align="center" align="center"></el-table-column>-->
        <el-table-column prop="procurementCycle" label="采购周期" header-align="center" align="center"></el-table-column>
        <el-table-column prop="reconciliationCycle" label="对账周期" header-align="center" align="center"></el-table-column>
        <el-table-column prop="paymentCycle" label="付款周期" header-align="center" align="center"></el-table-column>
        <el-table-column prop="mainCurrency" label="主要货币" header-align="center" align="center"></el-table-column>
<!--        <el-table-column prop="secondaryCurrency" label="次要货币" header-align="center" align="center"></el-table-column>-->
<!--        <el-table-column prop="logonTime" label="登入时间" header-align="center" align="center"></el-table-column>-->
<!--        <el-table-column prop="modificationTime" label="修改时间" header-align="center" align="center"></el-table-column>-->
<!--        <el-table-column prop="sucess" label="作成" header-align="center" align="center"></el-table-column>-->
<!--        <el-table-column prop="confirm" label="确认" header-align="center" align="center"></el-table-column>-->
        <el-table-column prop="notes" label="备注" header-align="center" align="center"></el-table-column>
<!--        <el-table-column prop="disabled" label="是否失效 0:有效; 1:失效 (逻辑删除)" header-align="center" align="center"></el-table-column>
        <el-table-column prop="creator" label="创建者" header-align="center" align="center"></el-table-column>
        <el-table-column prop="createDate" label="创建时间" header-align="center" align="center"></el-table-column>
        <el-table-column prop="updater" label="更新者" header-align="center" align="center"></el-table-column>
        <el-table-column prop="updateDate" label="更新时间" header-align="center" align="center"></el-table-column>-->
        <el-table-column :label="$t('handle')" fixed="right" header-align="center" align="center" width="150">
          <template slot-scope="scope">
            <el-dropdown>
              <el-button type="primary">
                功能<i class="el-icon-arrow-down el-icon--right"></i>
              </el-button>
              <el-dropdown-menu slot="dropdown">
                <el-dropdown-item>
                  <el-button v-if="$hasPermission('packing:materialsupplierbase:update')" type="text" size="small" @click="addOrUpdateHandle(scope.row.id)">{{ $t('update') }}</el-button>
                </el-dropdown-item>
                <el-dropdown-item>
                  <el-button v-if="$hasPermission('packing:materialsupplierbase:delete')" type="text" size="small" @click="deleteHandle(scope.row.id)">{{ $t('delete') }}</el-button>
                </el-dropdown-item>
              </el-dropdown-menu>
            </el-dropdown>
          </template>
        </el-table-column>
      </el-table>
      <el-pagination
        :current-page="page"
        :page-sizes="[10, 20, 50, 100]"
        :page-size="limit"
        :total="total"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="pageSizeChangeHandle"
        @current-change="pageCurrentChangeHandle">
      </el-pagination>
      <!-- 弹窗, 新增 / 修改 -->
      <add-or-update v-if="addOrUpdateVisible" ref="addOrUpdate" @refreshDataList="getDataList"></add-or-update>
    </div>
  </el-card>
</template>

<script>
import mixinViewModule from '@/mixins/view-module'
import AddOrUpdate from './materialsupplierbase-add-or-update'
import Cookies from "js-cookie";
export default {
  mixins: [mixinViewModule],
  data () {
    return {
      message:'',
      headers: {'token': Cookies.get('token')},
      fileList: [{name: '', url: ''}],
      unsuccessfulNameList: '',//  导入失败名称列表
      mixinViewModuleOptions: {
        getDataListURL: '/packing/materialsupplierbase/page',
        getDataListIsPage: true,
        exportURL: '/packing/materialsupplierbase/export',
        deleteURL: '/packing/materialsupplierbase',
        deleteIsBatch: true,
        exportTemplateURL: '/packing/materialsupplierbase/export/template',
        addBatchUrl: window.SITE_CONFIG['apiURL'] + '/packing/materialsupplierbase/batchSave',
      },
      dataForm: {
        id: '',
        paramStr: '',
      },
      // 判断是否还在继续输入
      timer: null,
    }
  },
  components: {
    AddOrUpdate
  },
  methods:{
    //节流函数
    throttleFunction() {
      // 如果timer不为空就表明在持续输入,每次改变都清除定时，这样就可以实现只输入最后一次结果(注意每次间隔如果超过500毫秒就会输出)
      if (this.timer !== null) clearTimeout(this.timer)
      this.timer = setTimeout(() => {
        this.getDataList()
      }, 500)
    },
    beforeUpload(file) {
      this.message = this.$message({
        message: '上传中，请勿操作',
        center: true,
        duration: 0,
      });
    },
    handleChange(file, fileList) {
      this.fileList = fileList.slice(-3);
    },
    resultHandle(res) {
      if (res.code !== 0) {
      return this.$message.error(res.msg)
      }

      if(this.message){
        this.message.close()
      }

      this.$message({
        message: this.$t('prompt.success'),
        type: 'success',
        duration: 500,
        onClose: () => {
          if (res.data) {
            this.unsuccessfulNameList = res.data
          }
          this.visible = false
          this.query()
        }
      })
    },
  }
}
</script>
