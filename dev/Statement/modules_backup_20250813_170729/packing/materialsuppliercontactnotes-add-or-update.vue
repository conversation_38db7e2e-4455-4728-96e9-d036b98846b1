<template>
  <el-dialog :visible.sync="visible" :title="!dataForm.id ? $t('add') : $t('update')" :close-on-click-modal="false" :close-on-press-escape="false" append-to-body>
    <el-form :model="dataForm" :rules="dataRule" ref="dataForm" @keyup.enter.native="dataFormSubmitHandle()" :label-width="$i18n.locale === 'en-US' ? '120px' : '80px'">
      <div class="container">
                                                <el-form-item label="客户代码" prop="customerCode">
              <el-input v-model="dataForm.customerCode" placeholder="客户代码"></el-input>
            </el-form-item>
                                        <el-form-item label="联络人姓名" prop="contactPersonName">
              <el-input v-model="dataForm.contactPersonName" placeholder="联络人姓名"></el-input>
            </el-form-item>
                                        <el-form-item label="主题归类(一般，质量，技术，财务，交货，其他)" prop="subjectClassification">
              <el-input v-model="dataForm.subjectClassification" placeholder="主题归类(一般，质量，技术，财务，交货，其他)"></el-input>
            </el-form-item>
                                        <el-form-item label="主要事项内容" prop="subjectMatterContent">
              <el-input v-model="dataForm.subjectMatterContent" placeholder="主要事项内容"></el-input>
            </el-form-item>
                                        <el-form-item label="结论" prop="conclusion">
              <el-input v-model="dataForm.conclusion" placeholder="结论"></el-input>
            </el-form-item>
                                        <el-form-item label="资料上传" prop="dataUpload">
              <el-input v-model="dataForm.dataUpload" placeholder="资料上传"></el-input>
            </el-form-item>
                                        <el-form-item label="作成" prop="sucess">
              <el-input v-model="dataForm.sucess" placeholder="作成"></el-input>
            </el-form-item>
                                        <el-form-item label="确认" prop="confirm">
              <el-input v-model="dataForm.confirm" placeholder="确认"></el-input>
            </el-form-item>
                                        <el-form-item label="会签(或通知)" prop="countersign">
              <el-input v-model="dataForm.countersign" placeholder="会签(或通知)"></el-input>
            </el-form-item>
                                        <el-form-item label="备注" prop="remark">
              <el-input v-model="dataForm.remark" placeholder="备注"></el-input>
            </el-form-item>
                        </div>
    </el-form>
    <template slot="footer">
      <el-button @click="visible = false">{{ $t('cancel') }}</el-button>
      <el-button type="primary" @click="dataFormSubmitHandle()">{{ $t('confirm') }}</el-button>
    </template>
  </el-dialog>
</template>

<script>
import debounce from 'lodash/debounce'
import mixinViewModule from '@/mixins/view-module'
export default {
  mixins:[mixinViewModule],
  data () {
    return {
      visible: false,
      //创建该页面时，不调用数据查询接口
      mixinViewModuleOptions: {
        createdIsNeed: false,
      },
      dataForm: {
        id: '',
        customerCode: '',
        contactPersonName: '',
        subjectClassification: '',
        subjectMatterContent: '',
        conclusion: '',
        dataUpload: '',
        sucess: '',
        confirm: '',
        countersign: '',
        remark: ''
      }
    }
  },
  computed: {
    dataRule () {
      return {
          id: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          customerCode: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ]/*,
          contactPersonName: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          subjectClassification: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          subjectMatterContent: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          conclusion: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          dataUpload: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          sucess: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          confirm: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          countersign: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          remark: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ]*/
      }
    }
  },
  methods: {
    init () {
      this.visible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].resetFields()
        if (this.dataForm.id) {
          this.getInfo()
        }
      })
    },
    // 获取信息
    getInfo () {
      this.$http.get(`/packing/materialsuppliercontactnotes/${this.dataForm.id}`).then(({ data: res }) => {
        if (res.code !== 0) {
          return this.$message.error(res.msg)
        }
        this.dataForm = {
          ...this.dataForm,
          ...res.data
        }
      }).catch(() => {})
    },
    // 表单提交
    dataFormSubmitHandle: debounce(function () {
      this.$refs['dataForm'].validate((valid) => {
        if (!valid) {
          return false
        }
        let message = '';
        if(this.dataForm.id){
          message = '此操作将修改该数据'
        }else {
          message = '此操作将新增一条数据'
        }
        this.$confirm(message+', 是否继续?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
          center: true
        }).then(() => {
          this.$http[!this.dataForm.id ? 'post' : 'put']('/packing/materialsuppliercontactnotes/', this.dataForm).then(({ data: res }) => {
            if (res.code !== 0) {
            return this.$message.error(res.msg)
            }
            this.$message({
          message: this.$t('prompt.success'),
            type: 'success',
                    duration: 500,
                    onClose: () => {
              this.visible = false
              this.$emit('refreshDataList')
            }
          })
          }).catch(() => {})
        }).catch(() => {
          this.$message({
            type: 'info',
            message: '已取消'
          });
        });
      })
    }, 1000, { 'leading': true, 'trailing': false })
  }
}
</script>
