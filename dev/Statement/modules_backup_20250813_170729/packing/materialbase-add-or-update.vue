<template>
  <el-dialog :visible.sync="visible" :title="!dataForm.id ? '新增资材原物料信息' : '修改资材原物料信息'" :close-on-click-modal="false" :close-on-press-escape="false" append-to-body>
    <el-form :model="dataForm" :rules="dataRule" ref="dataForm" @keyup.enter.native="dataFormSubmitHandle()" :label-width="$i18n.locale === 'en-US' ? '120px' : '80px'">
      <div class="container">
                                        <el-form-item label="物料代码" prop="materialsCode">
              <el-input v-model="dataForm.materialsCode" placeholder="物料代码"></el-input>
            </el-form-item>
                                        <el-form-item label="类别" prop="category">
              <el-input v-model="dataForm.category" placeholder="类别"></el-input>
            </el-form-item>
                                        <el-form-item label="物料名称" prop="materialsName">
              <el-input v-model="dataForm.materialsName" placeholder="物料名称"></el-input>
            </el-form-item>
                                        <el-form-item label="物料规格" prop="materialsSpecification">
              <el-input v-model="dataForm.materialsSpecification" placeholder="物料规格"></el-input>
            </el-form-item>
                                        <el-form-item label="检验标准" prop="inspectionStandards">
              <el-input v-model="dataForm.inspectionStandards" placeholder="检验标准"></el-input>
            </el-form-item>
                                        <el-form-item label="包装规格" prop="packageSpecification">
              <el-input v-model="dataForm.packageSpecification" placeholder="包装规格"></el-input>
            </el-form-item>
                                        <el-form-item label="重量" prop="weight">
              <el-input v-model="dataForm.weight" placeholder="重量"></el-input>
            </el-form-item>
                                        <el-form-item label="单价" prop="unitPrice">
              <el-input v-model="dataForm.unitPrice" placeholder="单价"></el-input>
            </el-form-item>
                                        <el-form-item label="产地" prop="producingArea">
              <el-input v-model="dataForm.producingArea" placeholder="产地"></el-input>
            </el-form-item>
                                        <el-form-item label="牌号" prop="trademark">
              <el-input v-model="dataForm.trademark" placeholder="牌号"></el-input>
            </el-form-item>
                                        <el-form-item label="有效期限" prop="validPeriod">
              <el-input v-model="dataForm.validPeriod" placeholder="有效期限"></el-input>
            </el-form-item>
                                        <el-form-item label="保存期限" prop="retentionPeriod">
              <el-input v-model="dataForm.retentionPeriod" placeholder="保存期限"></el-input>
            </el-form-item>
                                        <el-form-item label="交货天数" prop="deliveryDays">
              <el-input v-model="dataForm.deliveryDays" placeholder="交货天数"></el-input>
            </el-form-item>
                                        <el-form-item label="采购周期" prop="procurementCycle">
              <el-input v-model="dataForm.procurementCycle" placeholder="采购周期"></el-input>
            </el-form-item>
                                        <el-form-item label="最少采购量" prop="minimumPurchaseQuantity">
              <el-input v-model="dataForm.minimumPurchaseQuantity" placeholder="最少采购量"></el-input>
            </el-form-item>
                                        <el-form-item label="币别" prop="currency">
              <el-input v-model="dataForm.currency" placeholder="币别"></el-input>
            </el-form-item>
                                        <el-form-item label="汇率" prop="exchangeRate">
              <el-input v-model="dataForm.exchangeRate" placeholder="汇率"></el-input>
            </el-form-item>
                                        <el-form-item label="确认" prop="confirm">
              <el-input v-model="dataForm.confirm" placeholder="确认"></el-input>
            </el-form-item>
                                        <el-form-item label="作成" prop="success">
              <el-input v-model="dataForm.success" placeholder="作成"></el-input>
            </el-form-item>
                                        <el-form-item label="备注" prop="remark">
              <el-input v-model="dataForm.remark" placeholder="备注"></el-input>
            </el-form-item>
                                                                                                                  </div>
    </el-form>
    <template slot="footer">
      <el-button @click="visible = false">{{ $t('cancel') }}</el-button>
      <el-button type="primary" @click="dataFormSubmitHandle()">{{ $t('confirm') }}</el-button>
    </template>
  </el-dialog>
</template>

<script>
import debounce from 'lodash/debounce'
import mixinViewModule from '@/mixins/view-module'
export default {
  mixins:[mixinViewModule],
  data () {
    return {
      visible: false,
      //创建该页面时，不调用数据查询接口
      mixinViewModuleOptions: {
        createdIsNeed: false,
      },
      dataForm: {
        id: '',
        supplierCode: '',
        materialsCode: '',
        category: '',
        materialsName: '',
        materialsSpecification: '',
        inspectionStandards: '',
        packageSpecification: '',
        weight: '',
        unitPrice: '',
        producingArea: '',
        trademark: '',
        validPeriod: '',
        appearancePhotos: '',
        contentPhotos: '',
        retentionPeriod: '',
        deliveryDays: '',
        procurementCycle: '',
        minimumPurchaseQuantity: '',
        currency: '',
        exchangeRate: '',
        confirm: '',
        success: '',
        remark: '',
        disabled: '',
        creator: '',
        createDate: '',
        updater: '',
        updateDate: ''
      }
    }
  },
  computed: {
    dataRule () {
      return {
          id: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          supplierCode: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          materialsCode: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          /*category: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          materialsName: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          materialsSpecification: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          inspectionStandards: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          packageSpecification: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          weight: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          unitPrice: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          producingArea: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          trademark: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          validPeriod: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          appearancePhotos: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          contentPhotos: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          retentionPeriod: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          deliveryDays: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          procurementCycle: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          minimumPurchaseQuantity: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          currency: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          exchangeRate: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          confirm: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          success: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          remark: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],*/
                }
    }
  },
  methods: {
    init () {
      this.visible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].resetFields()
        if (this.dataForm.id) {
          this.getInfo()
        }
      })
    },
    // 获取信息
    getInfo () {
      this.$http.get(`/packing/materialbase/${this.dataForm.id}`).then(({ data: res }) => {
        if (res.code !== 0) {
          return this.$message.error(res.msg)
        }
        this.dataForm = {
          ...this.dataForm,
          ...res.data
        }
      }).catch(() => {})
    },
    // 表单提交
    dataFormSubmitHandle: debounce(function () {
      this.$refs['dataForm'].validate((valid) => {
        if (!valid) {
          return false
        }
        let message = '';
        if(this.dataForm.id){
          message = '此操作将修改该数据'
        }else {
          message = '此操作将新增一条数据'
        }
        this.$confirm(message+', 是否继续?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
          center: true
        }).then(() => {
          this.$http[!this.dataForm.id ? 'post' : 'put']('/packing/materialbase/', this.dataForm).then(({ data: res }) => {
            if (res.code !== 0) {
            return this.$message.error(res.msg)
            }
            this.$message({
          message: this.$t('prompt.success'),
            type: 'success',
                    duration: 500,
                    onClose: () => {
              this.visible = false
              this.$emit('refreshDataList')
            }
          })
          }).catch(() => {})
        }).catch(() => {
          this.$message({
            type: 'info',
            message: '已取消'
          });
        });
      })
    }, 1000, { 'leading': true, 'trailing': false })
  }
}
</script>
