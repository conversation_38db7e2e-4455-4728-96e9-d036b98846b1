<template>
  <el-dialog :visible.sync="visible" :title="!dataForm.id ? $t('add') : $t('update')" :close-on-click-modal="false" :close-on-press-escape="false" append-to-body>
    <el-form :model="dataForm" :rules="dataRule" ref="dataForm" @keyup.enter.native="dataFormSubmitHandle()" :label-width="$i18n.locale === 'en-US' ? '120px' : '80px'">
          <el-form-item label="包装箱号" prop="packageNo">
          <el-input v-model="dataForm.packageNo" placeholder="包装箱号"></el-input>
      </el-form-item>
          <el-form-item label="包装编号" prop="packagingNumber">
          <el-input v-model="dataForm.packagingNumber" placeholder="包装编号"></el-input>
      </el-form-item>
          <el-form-item label="卡板号" prop="cardNumber">
          <el-input v-model="dataForm.cardNumber" placeholder="卡板号"></el-input>
      </el-form-item>
          <el-form-item label="每包数量" prop="quantityPerPackage">
          <el-input v-model="dataForm.quantityPerPackage" placeholder="每包数量"></el-input>
      </el-form-item>
          <el-form-item label="每箱数量" prop="quantityPerBox">
          <el-input v-model="dataForm.quantityPerBox" placeholder="每箱数量"></el-input>
      </el-form-item>
          <el-form-item label="实际出货总量" prop="actualShipmentVolume">
          <el-input v-model="dataForm.actualShipmentVolume" placeholder="实际出货总量"></el-input>
      </el-form-item>
          <el-form-item label="净重" prop="netWeight">
          <el-input v-model="dataForm.netWeight" placeholder="净重"></el-input>
      </el-form-item>
          <el-form-item label="毛重" prop="grossWeight">
          <el-input v-model="dataForm.grossWeight" placeholder="毛重"></el-input>
      </el-form-item>
          <el-form-item label="备注" prop="remark">
          <el-input v-model="dataForm.remark" placeholder="备注"></el-input>
      </el-form-item>
                </el-form>
    <template>
      <el-button @click="visible = false">{{ $t('cancel') }}</el-button>
      <el-button type="primary" @click="dataFormSubmitHandle()">{{ $t('confirm') }}</el-button>
    </template>
  </el-dialog>
</template>

<script>
import debounce from 'lodash/debounce'
export default {
  data () {
    return {
      visible: false,
      dataForm: {
        id: '',
        packageNo: '',
        packagingNumber: '',
        cardNumber: '',
        quantityPerPackage: '',
        quantityPerBox: '',
        actualShipmentVolume: '',
        netWeight: '',
        grossWeight: '',
        remark: '',
        disabled: '',
        creator: '',
        createDate: '',
        updater: '',
        updateDate: ''
      }
    }
  },
  computed: {
    dataRule () {
      return {}
    }
  },
  methods: {
    init () {
      this.visible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].resetFields()
        if (this.dataForm.id) {
          this.getInfo()
        }
      })
    },
    // 获取信息
    getInfo () {
      this.$http.get(`/packing/wrapdetails/${this.dataForm.id}`).then(({ data: res }) => {
        if (res.code !== 0) {
          return this.$message.error(res.msg)
        }
        this.dataForm = {
          ...this.dataForm,
          ...res.data
        }
      }).catch(() => {})
    },
    // 表单提交
    dataFormSubmitHandle: debounce(function () {
      this.$refs['dataForm'].validate((valid) => {
        if (!valid) {
          return false
        }
        this.$http[!this.dataForm.id ? 'post' : 'put']('/packing/wrapdetails/', this.dataForm).then(({ data: res }) => {
          if (res.code !== 0) {
            return this.$message.error(res.msg)
          }
          this.$message({
            message: this.$t('prompt.success'),
            type: 'success',
            duration: 500,
            onClose: () => {
              this.visible = false
              this.$emit('refreshDataList')
            }
          })
        }).catch(() => {})
      })
    }, 1000, { 'leading': true, 'trailing': false })
  }
}
</script>
