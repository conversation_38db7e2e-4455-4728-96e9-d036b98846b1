<template>
  <el-card shadow="never" class="aui-card--fill">
    <div class="mod-packing__materialbase}">
      <el-form :inline="true" :model="dataForm" @keyup.enter.native="getDataList()">
        <el-form-item>
          <el-tooltip content="请输入查询关键字" placement="top">
            <el-input v-model="dataForm.paramStr" placeholder="查询关键字" @input="throttleFunction" clearable>
              <el-button slot="append" icon="el-icon-search" @click="getDataList()"></el-button>
            </el-input>
          </el-tooltip>
        </el-form-item>
        <el-form-item>
          <el-button v-if="$hasPermission('packing:materialbase:save')" type="primary" @click="addOrUpdateHandle()">{{ $t('add') }}</el-button>
        </el-form-item>
        <el-dropdown  style="float:right;">
          <el-button type="primary">
            更多功能<i class="el-icon-arrow-down el-icon--right"></i>
          </el-button>
          <el-dropdown-menu slot="dropdown">
            <el-dropdown-item>
              <el-form-item>
                <el-button type="info" @click="exportHandle()">{{ $t('export') }}</el-button>
              </el-form-item>
            </el-dropdown-item>
            <el-dropdown-item>
              <el-form-item>
                <el-button v-if="$hasPermission('packing:materialbase:export')" type="info" @click="exportTemplateHandle()">
                  {{ $t('exportTemplate') }}
                </el-button>
              </el-form-item>
            </el-dropdown-item>
            <el-dropdown-item>
              <el-form-item>
                <el-tooltip content="下载模板填入信息后导入" placement="top">
                  <el-upload
                          v-if="$hasPermission('packing:materialbase:save')"
                          class="upload-demo"
                          :action="mixinViewModuleOptions.addBatchUrl"
                          :headers="headers"
                          :multiple="false"
                          :show-file-list="false"
                          :file-list="fileList"
                          :before-upload="beforeUpload"
                          :on-success="resultHandle"
                          :on-change="handleChange"
                          accept="'.xlsx','.xls'">
                    <el-button type="success">{{ $t('addBatch') }}</el-button>
                  </el-upload>
                </el-tooltip>
                <el-button type="text" v-show="unsuccessfulNameList" @click="dialogVisible = true">导入失败列表</el-button>
              </el-form-item>
            </el-dropdown-item>
            <el-dropdown-item>
              <el-form-item>
                <el-button v-if="$hasPermission('packing:materialbase:delete')" type="danger" @click="deleteHandle()">{{ $t('deleteBatch') }}</el-button>
              </el-form-item>
            </el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown>
      </el-form>
      <el-table v-loading="dataListLoading" height="550px" :data="dataList" border @selection-change="dataListSelectionChangeHandle" style="width: 100%;">
        <el-table-column type="selection" header-align="center" align="center" width="50"></el-table-column>
<!--        <el-table-column prop="id" label="id" header-align="center" align="center"></el-table-column>-->
        <el-table-column prop="materialsCode" label="物料代码" header-align="center" align="center"></el-table-column>
        <el-table-column prop="category" label="类别" header-align="center" align="center"></el-table-column>
        <el-table-column prop="materialsName" label="物料名称" header-align="center" align="center" width="120"></el-table-column>
        <el-table-column prop="materialsSpecification" label="物料规格" header-align="center" align="center"></el-table-column>
        <el-table-column prop="inspectionStandards" label="检验标准" header-align="center" align="center" width="215"></el-table-column>
        <el-table-column prop="packageSpecification" label="包装规格" header-align="center" align="center"></el-table-column>
        <el-table-column prop="weight" label="重量" header-align="center" align="center"></el-table-column>
        <el-table-column prop="unitPrice" label="单价" header-align="center" align="center"></el-table-column>
        <el-table-column prop="producingArea" label="产地" header-align="center" align="center" width="120"></el-table-column>
        <el-table-column prop="trademark" label="牌号" header-align="center" align="center" width="120"></el-table-column>
        <el-table-column prop="validPeriod" label="有效期限" header-align="center" align="center"></el-table-column>
        <el-table-column prop="appearancePhotos" label="外观相片" header-align="center" align="center">
          <template slot-scope="scope">
            <div class="block" @click="openDialog(scope.row.appearancePhotos,scope.row.appearancePhotosType)" v-if="scope.row.appearancePhotos != null">
              <el-avatar shape="square" v-if="scope.row.appearancePhotos" :size="40">
                <el-image :src="$filePath+scope.row.appearancePhotos+scope.row.appearancePhotosType"
                          style="width: 100%; height: 100%; object-fit: contain; max-width: 100%; max-height: 100%;"></el-image>
              </el-avatar>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="contentPhotos" label="内容物相片" header-align="center" align="center" width="110">
          <template slot-scope="scope">
            <div class="block" @click="openDialog(scope.row.contentPhotos,scope.row.contentPhotosType)" v-if="scope.row.contentPhotos != null">
          <el-avatar shape="square" v-if="scope.row.contentPhotos" :size="40">
            <img :src="$filePath+scope.row.contentPhotos+scope.row.contentPhotosType"
                 style="width: 100%; height: 100%; object-fit: contain; max-width: 100%; max-height: 100%;" />
          </el-avatar>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="retentionPeriod" label="保存期限" header-align="center" align="center"></el-table-column>
        <el-table-column prop="deliveryDays" label="交货天数" header-align="center" align="center"></el-table-column>
        <el-table-column prop="procurementCycle" label="采购周期" header-align="center" align="center"></el-table-column>
        <el-table-column prop="minimumPurchaseQuantity" label="最少采购量" header-align="center" align="center" width="110"></el-table-column>
        <el-table-column prop="currency" label="币别" header-align="center" align="center"></el-table-column>
        <el-table-column prop="exchangeRate" label="汇率" header-align="center" align="center"></el-table-column>
        <el-table-column prop="confirm" label="确认" header-align="center" align="center"></el-table-column>
        <el-table-column prop="success" label="作成" header-align="center" align="center"></el-table-column>
        <el-table-column prop="remark" label="备注" header-align="center" align="center"></el-table-column>
<!--        <el-table-column prop="disabled" label="是否失效 0:有效; 1:失效 (逻辑删除)" header-align="center" align="center"></el-table-column>
        <el-table-column prop="creator" label="创建者" header-align="center" align="center"></el-table-column>
        <el-table-column prop="createDate" label="创建时间" header-align="center" align="center"></el-table-column>
        <el-table-column prop="updater" label="更新者" header-align="center" align="center"></el-table-column>
        <el-table-column prop="updateDate" label="更新时间" header-align="center" align="center"></el-table-column>-->
        <el-table-column :label="$t('handle')" fixed="right" header-align="center" align="center" width="150">
          <template slot-scope="scope">
            <el-dropdown>
              <el-button type="primary">
                功能<i class="el-icon-arrow-down el-icon--right"></i>
              </el-button>
              <el-dropdown-menu slot="dropdown">
                <el-dropdown-item>
                  <el-button v-if="$hasPermission('packing:materialbase:update')" type="text" size="small" @click="addOrUpdateHandle(scope.row.id)">{{ $t('update') }}</el-button>
                </el-dropdown-item>
                <el-dropdown-item>
                  <el-upload
                      v-if="$hasPermission('packing:materialbase:save')"
                      :action="mixinViewModuleOptions.addAppearanceImageUrl+scope.row.id"
                      class="upload-demo"
                      :show-file-list="false"
                      :headers="headers"
                      :multiple="false"
                      :on-success="handleUploadSuccess"
                      :before-upload="beforeUploadFile"
                      :accept="'image/*'">
                    <el-button
                        v-if="$hasPermission('packing:materialbase:save')"
                        type="text"
                        size="small"
                        @click="chooseFile(scope.row.id,scope.row.appearancePhotos)"
                    >上传外观照片</el-button>
                  </el-upload>
                </el-dropdown-item>
                <el-dropdown-item >
                  <el-upload
                      v-if="$hasPermission('packing:materialbase:save')"
                      :action="mixinViewModuleOptions.addContentImageUrl+scope.row.id"
                      class="upload-demo"
                      :show-file-list="false"
                      :headers="headers"
                      :multiple="false"
                      :on-success="handleUploadSuccess"
                      :before-upload="beforeUploadFile"
                      :accept="'image/*'">
                    <el-button
                        v-if="$hasPermission('packing:materialbase:save')"
                        type="text"
                        size="small"
                        @click="chooseFile(scope.row.id,scope.row.contentPhotos)"
                    >上传内容照片</el-button>
                  </el-upload>
                </el-dropdown-item>
                <el-dropdown-item>
                  <el-button v-if="$hasPermission('packing:materialbase:delete')" type="text" size="small" @click="deleteHandle(scope.row.id)">{{ $t('delete') }}</el-button>
                </el-dropdown-item>
              </el-dropdown-menu>
            </el-dropdown>
          </template>
        </el-table-column>
      </el-table>
      <el-pagination
        :current-page="page"
        :page-sizes="[10, 20, 50, 100]"
        :page-size="limit"
        :total="total"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="pageSizeChangeHandle"
        @current-change="pageCurrentChangeHandle">
      </el-pagination>
      <!-- 弹窗, 新增 / 修改 -->
      <add-or-update v-if="addOrUpdateVisible" ref="addOrUpdate" @refreshDataList="getDataList"></add-or-update>
      <!-- 弹出框 -->
      <el-dialog title=图片显示 :visible.sync="dialogVisible" @closed="closeDialog">
        <el-image :src="$filePath+imagePath+documentType" alt="未上传图片" fit="contain" style="width: 50vw; height: 70vh"></el-image>
      </el-dialog>
    </div>
  </el-card>
</template>

<script>
import mixinViewModule from '@/mixins/view-module'
import AddOrUpdate from './materialbase-add-or-update'
import Cookies from "js-cookie";
export default {
  mixins: [mixinViewModule],
  data () {
    return {
      dialogVisible: false,
      imagePath: '',
      documentType: '',
      currentRowId:'',
      message:'',
      headers: {'token': Cookies.get('token')},
      fileList: [{name: '', url: ''}],
      unsuccessfulNameList: '',//  导入失败名称列表
      mixinViewModuleOptions: {
        getDataListURL: '/packing/materialbase/page',
        getDataListIsPage: true,
        exportURL: '/packing/materialbase/export',
        deleteURL: '/packing/materialbase',
        deleteIsBatch: true,
        exportTemplateURL: '/packing/materialbase/export/template',
        addBatchUrl: window.SITE_CONFIG['apiURL'] + '/packing/materialbase/batchSave',
        addAppearanceImageUrl: window.SITE_CONFIG['apiURL'] + '/packing/materialbase/api/appearanceUpload?id=',
        addContentImageUrl: window.SITE_CONFIG['apiURL'] + '/packing/materialbase/api/contentUpload?id=',
      },
      dataForm: {
        id: '',
        paramStr: '',
      },
      // 判断是否还在继续输入
      timer: null,
    }
  },
  components: {
    AddOrUpdate
  },
  methods:{
    //节流函数
    throttleFunction() {
      // 如果timer不为空就表明在持续输入,每次改变都清除定时，这样就可以实现只输入最后一次结果(注意每次间隔如果超过500毫秒就会输出)
      if (this.timer !== null) clearTimeout(this.timer)
      this.timer = setTimeout(() => {
        this.getDataList()
      }, 500)
    },
    closeDialog(){
      this.dialogVisible = false
    },
    chooseFile(id, documentId) {
      this.currentRowId = documentId
    },
    openDialog(documentId,documentType) { // 打开对话框
      this.imagePath = documentId
      this.documentType = documentType
      this.dialogVisible = true;
    },
    beforeUploadFile(file) {
      if (!file.type.startsWith('image/')) {
        this.$message.error('只能上传图片文件')
        return false
      }
    },
    handleUploadSuccess(response) {
      console.log(response)
      if (response.code === 200) {
        if(this.currentRowId){
          this.$http.delete('/packing/materialbase/deleteImage?id=' + this.currentRowId)
        }
        this.$message.success('上传成功')
      } else {
        this.$message.error('上传失败')
      }
      this.query()
    },
    beforeUpload(file) {
      this.message = this.$message({
        message: '上传中，请勿操作',
        center: true,
        duration: 0,
      });
    },
    handleChange(file, fileList) {
      this.fileList = fileList.slice(-3);
    },
    resultHandle(res) {
      if (res.code !== 0) {
      return this.$message.error(res.msg)
      }

      if(this.message){
        this.message.close()
      }

      this.$message({
        message: this.$t('prompt.success'),
        type: 'success',
        duration: 500,
        onClose: () => {
          if (res.data) {
            this.unsuccessfulNameList = res.data
          }
          this.visible = false
          this.query()
        }
      })
    },
  }
}
</script>
