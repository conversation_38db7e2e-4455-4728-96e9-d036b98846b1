<template>
    <div class="mod-packing__wrapdetails}">
      <el-form :inline="true" :model="dataForm" @keyup.enter.native="getDataList()">
        <el-form-item v-if="labelDisplay">
          <el-input v-model="dataForm.paramStr" placeholder="关键字查询" clearable @clear="getDataList()">
            <el-button slot="append" icon="el-icon-search" @click="getDataList()"></el-button>
          </el-input>
        </el-form-item>
        <el-button v-if="$hasPermission('packing:wrapdetails:save')" type="primary" icon="el-icon-plus" @click="addOrUpdateHandle()" circle></el-button>
        <el-dropdown v-if="labelDisplay"  style="float:right;">
          <el-button type="primary">
            更多功能<i class="el-icon-arrow-down el-icon--right"></i>
          </el-button>
          <el-dropdown-menu slot="dropdown">
            <el-dropdown-item>
              <el-form-item>
                <el-button type="info" @click="exportHandle()">{{ $t('export') }}</el-button>
              </el-form-item>
            </el-dropdown-item>
            <el-dropdown-item>
              <el-form-item>
                <el-button v-if="$hasPermission('packing:wrapdetails:export')" type="info" @click="exportTemplateHandle()">
                  {{ $t('exportTemplate') }}
                </el-button>
              </el-form-item>
            </el-dropdown-item>
            <el-dropdown-item>
              <el-form-item>
                <el-tooltip content="下载模板填入信息后导入" placement="top">
                  <el-upload
                      v-if="$hasPermission('packing:wrapdetails:save')"
                      class="upload-demo"
                      :action="mixinViewModuleOptions.addBatchUrl"
                      :headers="headers"
                      :multiple="false"
                      :show-file-list="false"
                      :file-list="fileList"
                      :before-upload="beforeUpload"
                      :on-success="resultHandle"
                      :on-change="handleChange"
                      accept="'.xlsx','.xls'">
                    <el-button type="success">{{ $t('addBatch') }}</el-button>
                  </el-upload>
                </el-tooltip>
                <el-button type="text" v-show="unsuccessfulNameList" @click="dialogVisible = true">导入失败列表</el-button>
              </el-form-item>
            </el-dropdown-item>
            <el-dropdown-item>
              <el-form-item>
                <el-button v-if="$hasPermission('packing:wrapdetails:delete')" type="danger" @click="deleteHandle()">{{ $t('deleteBatch') }}</el-button>
              </el-form-item>
            </el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown>
      </el-form>
      <el-table v-loading="dataListLoading" ref="tables" :data="dataList" border @selection-change="handleSelectionChange" style="width: 100%;">
        <el-table-column type="selection" header-align="center" align="center" width="50"></el-table-column>
        <el-table-column type="expand">
          <template slot-scope="props">
            <div>
              <packable-query-list></packable-query-list>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="packageNo" label="包装箱号" header-align="center" align="center"></el-table-column>
        <el-table-column prop="packagingNumber" label="包装编号" header-align="center" align="center"></el-table-column>
        <el-table-column prop="cardNumber" label="卡板号" header-align="center" align="center"></el-table-column>
        <el-table-column prop="quantityPerPackage" label="每包数量" header-align="center" align="center"></el-table-column>
        <el-table-column prop="quantityPerBox" label="每箱数量" header-align="center" align="center"></el-table-column>
        <el-table-column prop="actualShipmentVolume" label="实际出货总量" header-align="center" align="center"></el-table-column>
        <el-table-column prop="netWeight" label="净重" header-align="center" align="center"></el-table-column>
        <el-table-column prop="grossWeight" label="毛重" header-align="center" align="center"></el-table-column>
        <el-table-column prop="remark" label="备注" header-align="center" align="center"></el-table-column>
        <el-table-column :label="$t('handle')" fixed="right" header-align="center" align="center" width="150">
          <template slot-scope="scope">
            <el-dropdown>
              <el-button type="primary">
                功能<i class="el-icon-arrow-down el-icon--right"></i>
              </el-button>
              <el-dropdown-menu slot="dropdown">
                <el-dropdown-item>
                  <el-button v-if="$hasPermission('packing:wrapdetails:update')" type="text" size="small" @click="addOrUpdateHandle(scope.row.id)">{{ $t('update') }}</el-button>
                </el-dropdown-item>
                <el-dropdown-item>
                  <el-button v-if="$hasPermission('packing:wrapdetails:delete')" type="text" size="small" @click="deleteHandle(scope.row.id)">{{ $t('delete') }}</el-button>
                </el-dropdown-item>
              </el-dropdown-menu>
            </el-dropdown>
          </template>
        </el-table-column>
      </el-table>
      <el-pagination
          v-if="labelDisplay"
        :current-page="page"
        :page-sizes="[10, 20, 50, 100]"
        :page-size="limit"
        :total="total"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="pageSizeChangeHandle"
        @current-change="pageCurrentChangeHandle">
      </el-pagination>
      <!-- 弹窗, 新增 / 修改 -->
      <add-or-update v-if="addOrUpdateVisible" ref="addOrUpdate" @refreshDataList="getDataList"></add-or-update>
    </div>
</template>

<script>
import mixinViewModule from '@/mixins/view-module'
import AddOrUpdate from './wrapdetails-add-or-update'
import PackableQueryList from "./packable-query-list.vue";
import Cookies from "js-cookie";
export default {
  mixins: [mixinViewModule],
  props:{
    labelDisplay:{
      type: Boolean,
      required: true,
    },
  },
  data () {
    return {
      message:'',
      headers: {'token': Cookies.get('token')},
      fileList: [{name: '', url: ''}],
      unsuccessfulNameList: '',//  导入失败名称列表
      mixinViewModuleOptions: {
        getDataListURL: '/packing/wrapdetails/page',
        getDataListIsPage: true,
        exportURL: '/packing/wrapdetails/export',
        deleteURL: '/packing/wrapdetails',
        deleteIsBatch: true,
        exportTemplateURL: 'packing/wrapdetails/export/template',
        addBatchUrl: window.SITE_CONFIG['apiURL'] + 'packing/wrapdetails/batchSave',
      },
      dataForm: {
        id: '',
        paramStr: '',
      },
      selectedRows: []
    }
  },
  components: {
    AddOrUpdate,
    PackableQueryList
  },
  methods:{
    handleSelectionChange(selection) {
      console.log(selection,"键入")
      this.dataList.forEach(row => {
        this.$refs.tables.toggleRowExpansion(row, selection.includes(row));
      });
    },
    beforeUpload(file) {
      this.message = this.$message({
        message: '上传中，请勿操作',
        center: true,
        duration: 0,
      });
    },
    handleChange(file, fileList) {
      this.fileList = fileList.slice(-3);

    },
    resultHandle(res) {
      if (res.code !== 0) {
      return this.$message.error(res.msg)
      }
      this.$message({
        message: this.$t('prompt.success'),
        type: 'success',
        duration: 500,
        onClose: () => {
          if (res.data) {
            this.unsuccessfulNameList = res.data
          }
          this.visible = false
          this.query()
        }
      })
    },
  }
}
</script>
