<template>
  <div>
    <div>
  <el-card shadow="never" class="aui-card--fill">
    <div class="mod-material__materialinandoutmanagement}">
      <el-form :inline="true" :model="dataForm" @keyup.enter.native="getDataList()">
        <el-form-item>
          <el-tooltip content="请输入查询关键字" placement="top">
            <el-input v-model="dataForm.paramStr" placeholder="查询关键字" @input="throttleFunction" clearable>
              <el-button slot="append" icon="el-icon-search" @click="getDataList()"></el-button>
            </el-input>
          </el-tooltip>
        </el-form-item>
        <el-form-item>
          <el-button v-if="$hasPermission('material:materialinandoutmanagement:save')" type="primary" @click="addOrUpdateHandle()">新增进料记录</el-button>
          <el-button v-if="$hasPermission('material:materialinandoutmanagement:save')" type="info" @click="dialogVisible1 = true">新增领料记录</el-button>
        </el-form-item>
        <el-dropdown  style="float:right;">
          <el-button type="primary">
            更多功能<i class="el-icon-arrow-down el-icon--right"></i>
          </el-button>
          <el-dropdown-menu slot="dropdown">
            <el-dropdown-item>
              <el-form-item>
                <el-button type="info" @click="exportHandle()">{{ $t('export') }}</el-button>
              </el-form-item>
            </el-dropdown-item>
            <el-dropdown-item>
              <el-form-item>
                <el-button v-if="$hasPermission('material:materialinandoutmanagement:export')" type="info" @click="exportTemplateHandle()">
                  {{ $t('exportTemplate') }}
                </el-button>
              </el-form-item>
            </el-dropdown-item>
            <el-dropdown-item>
              <el-form-item>
                <el-tooltip content="下载模板填入信息后导入" placement="top">
                  <el-upload
                          v-if="$hasPermission('material:materialinandoutmanagement:save')"
                          class="upload-demo"
                          :action="mixinViewModuleOptions.addBatchUrl"
                          :headers="headers"
                          :multiple="false"
                          :show-file-list="false"
                          :file-list="fileList"
                          :before-upload="beforeUpload"
                          :on-success="resultHandle"
                          :on-change="handleChange"
                          accept="'.xlsx','.xls'">
                    <el-button type="success">{{ $t('addBatch') }}</el-button>
                  </el-upload>
                </el-tooltip>
                <el-button type="text" v-show="unsuccessfulNameList" @click="dialogVisible = true">导入失败列表</el-button>
              </el-form-item>
            </el-dropdown-item>
            <el-dropdown-item>
              <el-form-item>
                <el-button v-if="$hasPermission('material:materialinandoutmanagement:delete')" type="danger" @click="deleteHandle()">{{ $t('deleteBatch') }}</el-button>
              </el-form-item>
            </el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown>
      </el-form>
      <el-table v-loading="dataListLoading" height="750px" :data="dataList" border @selection-change="dataListSelectionChangeHandle" style="width: 100%;">
        <el-table-column type="selection" header-align="center" align="center" width="50"></el-table-column>
<!--        <el-table-column prop="id" label="id" header-align="center" align="center"></el-table-column>-->
        <el-table-column prop="materialCode" label="原物料代码" header-align="center" align="center"></el-table-column>
        <el-table-column prop="category" label="类别" header-align="center" align="center"></el-table-column>
        <el-table-column prop="materialName" label="材料名称" header-align="center" align="center"></el-table-column>
        <el-table-column prop="inAndOutOfMaterials" label="流水类型" header-align="center" align="center">
          <template slot-scope="scope">
            <el-tag
                :type="scope.row.inAndOutOfMaterials === 0 ? 'primary' : 'info'"
                size="medium"
                effect="dark"
                disable-transitions>{{scope.row.inAndOutOfMaterials === 0 ? '进料' : '领料'}}</el-tag>
          </template>
        </el-table-column>
<!--        <el-table-column prop="inAndOutOfMaterialsNumber" label="资材进料批号" header-align="center" align="center"></el-table-column>-->
<!--        <el-table-column prop="inAndOutOfMaterialsBatch" label="进/出料批次" header-align="center" align="center"></el-table-column>-->
<!--        <el-table-column prop="inAndOutOfMaterialsDate" label="进/出厂日期" header-align="center" align="center"></el-table-column>-->
<!--        <el-table-column prop="inAndOutOfMaterialsWarehouseDate" label="进/出仓库日期" header-align="center" align="center"></el-table-column>-->
<!--        <el-table-column prop="produceDate" label="本批生产日期" header-align="center" align="center"></el-table-column>-->
<!--        <el-table-column prop="quantity" label="单位数量" header-align="center" align="center"></el-table-column>-->
<!--        <el-table-column prop="unitOne" label="单位1" header-align="center" align="center"></el-table-column>-->
<!--        <el-table-column prop="unitTwo" label="单位2" header-align="center" align="center"></el-table-column>-->
        <el-table-column prop="materialSum" label="总数量" header-align="center" align="center">
          <template slot-scope="scope">
            {{scope.row.materialSum}}{{scope.row.unitOneString}}
          </template>
        </el-table-column>
<!--        <el-table-column prop="roughWeight" label="毛重" header-align="center" align="center"></el-table-column>-->
<!--        <el-table-column prop="suttle" label="净重" header-align="center" align="center"></el-table-column>-->
        <el-table-column prop="materialMantissa" label="尾数" header-align="center" align="center"></el-table-column>
        <el-table-column prop="accumulatedSurplusQuantity" label="累计结余数量" header-align="center" align="center">
          <template slot-scope="scope">
          {{scope.row.accumulatedSurplusQuantity}}{{scope.row.unitOneString}}
          </template>
        </el-table-column>
<!--        <el-table-column prop="materialExpirationDate" label="材料到期日" header-align="center" align="center"></el-table-column>-->
<!--        <el-table-column prop="purchaseOrderNumber" label="采购单号/生产工单号" header-align="center" align="center"></el-table-column>-->
<!--        <el-table-column prop="supplierCode" label="供应商代码" header-align="center" align="center"></el-table-column>-->
<!--        <el-table-column prop="qrCode" label="二维码" header-align="center" align="center"></el-table-column>-->
<!--        <el-table-column prop="barCode" label="条码" header-align="center" align="center"></el-table-column>-->
<!--        <el-table-column prop="success" label="成作" header-align="center" align="center"></el-table-column>-->
<!--        <el-table-column prop="remark" label="备注" header-align="center" align="center"></el-table-column>-->
<!--        <el-table-column prop="disabled" label="是否失效 0:有效; 1:失效 (逻辑删除)" header-align="center" align="center"></el-table-column>-->
        <el-table-column prop="creator" label="创建者" header-align="center" align="center">
          <template slot-scope="scope">
            {{ $getEmployeesList(scope.row.creator)}}
          </template>
        </el-table-column>
        <el-table-column prop="createDate" label="创建时间" header-align="center" align="center"></el-table-column>
<!--        <el-table-column prop="updater" label="更新者" header-align="center" align="center"></el-table-column>-->
<!--        <el-table-column prop="updateDate" label="更新时间" header-align="center" align="center"></el-table-column>-->
        <el-table-column :label="$t('handle')" fixed="right" header-align="center" align="center" width="150">
          <template slot-scope="scope">
            <el-dropdown>
              <el-button type="primary">
                功能<i class="el-icon-arrow-down el-icon--right"></i>
              </el-button>
              <el-dropdown-menu slot="dropdown">
                <el-dropdown-item>
                  <el-button v-if="$hasPermission('material:materialinandoutmanagement:update')" type="text" size="small" @click="addOrUpdateHandle(scope.row.id)">{{ $t('update') }}</el-button>
                </el-dropdown-item>
                <el-dropdown-item>
                  <el-button v-if="$hasPermission('material:materialinandoutmanagement:delete')" type="text" size="small" @click="deleteHandle(scope.row.id)">{{ $t('delete') }}</el-button>
                </el-dropdown-item>
              </el-dropdown-menu>
            </el-dropdown>
          </template>
        </el-table-column>
      </el-table>
      <el-pagination
        :current-page="page"
        :page-sizes="[10, 20, 50, 100]"
        :page-size="limit"
        :total="total"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="pageSizeChangeHandle"
        @current-change="pageCurrentChangeHandle">
      </el-pagination>
      <!-- 弹窗, 新增 / 修改 -->
      <add-or-update v-if="addOrUpdateVisible" ref="addOrUpdate" @refreshDataList="getDataList"></add-or-update>
    </div>
  </el-card>
    </div>
  <div>
    <el-dialog width="60vw" :visible.sync="dialogVisible1" title="新增领料记录">
      <el-form :model="reduceForm" :rules="reduceRule" ref="reduceForm"  label-width="7vw">
        <div style="display: flex">
          <el-form-item label="物料代码" prop="materialCode">
            <el-select v-model="reduceForm.materialCode"
                       placeholder="请选择"
                       filterable
                       @change="changeMaterialInfo"
            >
              <el-option
                  v-for="item in materialList"
                  :key="item.id"
                  :label="item.materialName + '(' + item.rawMaterialCode + ')'"
                  :value="item.rawMaterialCode">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="类别" prop="category" >
            <el-input v-model="reduceForm.category" placeholder="类别" disabled></el-input>
          </el-form-item>
          <el-form-item label="材料名称" prop="materialName">
            <el-input v-model="reduceForm.materialName" placeholder="材料名称" disabled></el-input>
          </el-form-item>
        </div>
        <div style="display: flex">
          <el-form-item label="供应商代码" prop="supplierCode">
            <el-select v-model="reduceForm.supplierCode"
                       placeholder="请选择"
                       filterable
            >
              <el-option
                  v-for="item in supplierList"
                  :key="item.id"
                  :label="item.supplierName"
                  :value="item.supplierCode">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="领料数量" prop="materialSum">
            <el-input style="width: 8vw" v-model="reduceForm.materialSum" placeholder=""></el-input>
            <span style="width: 6vw;margin-left: 1vw;font-size: large">{{reduceUnit}}</span>
          </el-form-item>
          <el-form-item label="生产工单号" prop="success">
            <el-input style="width: 10vw" v-model="reduceForm.purchaseOrderNumber" placeholder=""></el-input>
          </el-form-item>
        </div>
        <el-form-item label="备注" >
          <el-input v-model="reduceForm.remark" type="textarea" :rows="3" placeholder=""></el-input>
        </el-form-item>
      </el-form>
      <template slot="footer">
        <el-button @click="dialogVisible1 = false">{{ $t('cancel') }}</el-button>
        <el-button type="primary" @click="reduceFormSubmitHandle()">{{ $t('confirm') }}</el-button>
      </template>
    </el-dialog>
  </div>
  </div>
</template>

<script>
import mixinViewModule from '@/mixins/view-module'
import AddOrUpdate from './materialinandoutmanagement-add-or-update'
import Cookies from "js-cookie";
import debounce from "lodash/debounce";
export default {
  mixins: [mixinViewModule],
  data () {
    return {
      message:'',
      headers: {'token': Cookies.get('token')},
      fileList: [{name: '', url: ''}],
      unsuccessfulNameList: '',//  导入失败名称列表
      mixinViewModuleOptions: {
        getDataListURL: '/material/materialinandoutmanagement/page',
        getDataListIsPage: true,
        exportURL: '/material/materialinandoutmanagement/export',
        deleteURL: '/material/materialinandoutmanagement',
        deleteIsBatch: true,
        exportTemplateURL: '/material/materialinandoutmanagement/export/template',
        addBatchUrl: window.SITE_CONFIG['apiURL'] + '/material/materialinandoutmanagement/batchSave',
      },
      dataForm: {
        id: '',
        paramStr: '',
      },
      reduceUnit:'',
      materialList:[],
      supplierList:[],
      reduceForm:{
          materialCode:'',
          category:'',
          inAndOutOfMaterials:1,
          materialName:'',
          supplierCode:'',
          materialSum:'',
          purchaseOrderNumber:'',
          unitOne:'',
          remark:''
      },
      dialogVisible1:false,
      // 判断是否还在继续输入
      timer: null,
    }
  },
  computed:{
    reduceRule(){
      return {
        materialCode: [
          { required: true, message: '请输入物料编码', trigger: 'blur' },
        ]
        }
    }
  },
  components: {
    AddOrUpdate
  },
  created() {
    this.getMaterialList()
    this.getSupplierList()
  },
  methods:{
    //节流函数
    throttleFunction() {
      // 如果timer不为空就表明在持续输入,每次改变都清除定时，这样就可以实现只输入最后一次结果(注意每次间隔如果超过500毫秒就会输出)
      if (this.timer !== null) clearTimeout(this.timer)
      this.timer = setTimeout(() => {
        this.getDataList()
      }, 500)
    },
    changeMaterialInfo(){
      let material = this.materialList.filter(item => item.rawMaterialCode === this.reduceForm.materialCode);
      this.reduceForm.category = material[0].category;
      this.reduceForm.materialName = material[0].materialName;
      this.$http.get(`/material/measuringunit/getInventoryUnit?materialCode=${this.reduceForm.materialCode}`).then(({data : res}) => {
        if (res.code !== 0) {
          return this.$message.error(res.msg)
        }
        this.reduceForm.unitOne = res.data.id;
        this.reduceUnit = res.data.unit;
      })
    },
    //获取库存物料代码列表
    getMaterialList(){
      console.log('查询物料库存信息')
      this.$http.get(`/packing/materialbase/getMaterialInventoryList`).then(({ data: res }) => {
        if (res.code !== 0) {
          return this.$message.error(res.msg)
        }
        this.materialList = res.data
      }).catch(() => {

      })
    },
    //获取供应商代码列表
    getSupplierList(){
      this.$http.get(`/packing/materialsupplierbase/getInfoList`).then(({ data: res }) => {
        if (res.code !== 0) {
          return this.$message.error(res.msg)
        }
        this.supplierList = res.data
      }).catch(() => {})
    },
    // 表单提交
    reduceFormSubmitHandle: debounce(function () {
      this.$refs['reduceForm'].validate((valid) => {
        if (!valid) {
          return false
        }
        this.$confirm('此操作将新增一条数据, 是否继续?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
          center: true
        }).then(() => {
          this.$http.post('/material/materialinandoutmanagement/', this.reduceForm).then(({ data: res }) => {
            if (res.code !== 0) {
              return this.$message.error(res.msg)
            }
            this.$message({
              message: this.$t('prompt.success'),
              type: 'success',
              duration: 500,
              onClose: () => {
                this.dialogVisible1 = false
                this.$emit('refreshDataList')
              }
            })
          }).catch(() => {})
        }).catch(() => {
          this.$message({
            type: 'info',
            message: '已取消'
          });
        });
      })
    }, 1000, { 'leading': true, 'trailing': false }),

    beforeUpload(file) {
      this.message = this.$message({
        message: '上传中，请勿操作',
        center: true,
        duration: 0,
      });
    },
    handleChange(file, fileList) {
      this.fileList = fileList.slice(-3);

    },
    resultHandle(res) {
      if (res.code !== 0) {
      return this.$message.error(res.msg)
      }
      this.$message({
        message: this.$t('prompt.success'),
        type: 'success',
        duration: 500,
        onClose: () => {
          if (res.data) {
            this.unsuccessfulNameList = res.data
          }
          this.visible = false
          this.query()
        }
      })
    },
  }
}
</script>
