<template>
  <el-table v-loading="dataListLoading" :data="dataList" border :show-header="false" @selection-change="dataListSelectionChangeHandle" style="width: 100%;">
    <el-table-column type="selection" header-align="center" align="center" width="50"></el-table-column>
    <el-table-column prop="subBatchId" label="次批号id" header-align="center" align="center"></el-table-column>
    <el-table-column prop="quantityPerBag" label="每袋数量" header-align="center" align="center"></el-table-column>
    <el-table-column prop="numberOfBags" label="总袋数" header-align="center" align="center"></el-table-column>
    <el-table-column prop="totalQuantity" label="总数量" header-align="center" align="center"></el-table-column>
    <el-table-column prop="practicalNumber" label="实际数" header-align="center" align="center"></el-table-column>
    <el-table-column prop="residual number" label="结余数" header-align="center" align="center"></el-table-column>
  </el-table>
</template>

<script>
import mixinViewModule from '@/mixins/view-module'
import AddOrUpdate from './detailsperbox-add-or-update'
import Cookies from "js-cookie";
export default {
  mixins: [mixinViewModule],
  data () {
    return {
      mixinViewModuleOptions: {
        getDataListURL: '/packing/detailsperbox/page',
      },
      dataForm: {
        id: '',
        paramStr: '',
      }
    }
  },
  components: {
    AddOrUpdate
  },
  methods:{
  }
}
</script>