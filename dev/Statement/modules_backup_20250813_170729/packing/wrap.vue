<template>
  <el-card shadow="never" class="aui-card--fill">
    <div class="mod-packing__wrap}">
      <el-form :inline="true" :model="dataForm" @keyup.enter.native="getDataList()">
        <el-tooltip content="请输入查询内容" placement="top">
          <el-form-item>
            <el-input v-model="dataForm.paramStr" placeholder="关键字查询" @input="throttleFunction" clearable>
              <el-button slot="append" icon="el-icon-search" @click="getDataList()"></el-button>
            </el-input>
          </el-form-item>
        </el-tooltip>
        <el-form-item>
          <el-button type="info" @click="exportHandle()">{{ $t('export') }}</el-button>
        </el-form-item>
        <el-form-item>
          <el-button v-if="$hasPermission('packing:wrap:export')" type="info" @click="exportTemplateHandle()">
            {{ $t('exportTemplate') }}
          </el-button>
        </el-form-item>
        <el-form-item>
          <el-tooltip content="下载模板填入信息后导入" placement="top">
            <el-upload
                    v-if="$hasPermission('packing:wrap:save')"
                    class="upload-demo"
                    :action="mixinViewModuleOptions.addBatchUrl"
                    :headers="headers"
                    :multiple="false"
                    :show-file-list="false"
                    :file-list="fileList"
                    :before-upload="beforeUpload"
                    :on-success="resultHandle"
                    :on-change="handleChange"
                    accept="'.xlsx','.xls'">
              <el-button type="success">{{ $t('addBatch') }}</el-button>
            </el-upload>
          </el-tooltip>
          <el-button type="text" v-show="unsuccessfulNameList" @click="dialogVisible = true">导入失败列表</el-button>
        </el-form-item>
        <el-form-item>
          <el-button v-if="$hasPermission('packing:wrap:save')" type="primary" @click="addOrUpdateHandle()">{{ $t('add') }}</el-button>
        </el-form-item>
        <el-form-item>
          <el-button v-if="$hasPermission('packing:wrap:delete')" type="danger" @click="deleteHandle()">{{ $t('deleteBatch') }}</el-button>
        </el-form-item>
      </el-form>
      <el-table v-loading="dataListLoading" :data="dataList" height="700px" border @selection-change="dataListSelectionChangeHandle" style="width: 100%;">
        <el-table-column type="selection" header-align="center" align="center" width="50"></el-table-column>
        <el-table-column prop="subBatchNumber" label="次批号" header-align="center" align="center"></el-table-column>
        <el-table-column prop="weighers" label="称量员" header-align="center" align="center">
          <template slot-scope="scope">
            {{$getEmployeesList(scope.row.weighers)}}
          </template>
        </el-table-column>
<!--        <el-table-column prop="outgoingName" label="外发员工姓名" header-align="center" align="center"></el-table-column>-->
        <el-table-column prop="numberOfSamples" label="取样数量" header-align="center" align="center"></el-table-column>
        <el-table-column prop="singleWeight" label="单重" header-align="center" align="center"></el-table-column>
        <el-table-column prop="packers" label="包装员" header-align="center" align="center">
          <template slot-scope="scope">
            {{$getEmployeesList(scope.row.packers)}}
          </template>
        </el-table-column>
        <el-table-column prop="quantity" label="数量" header-align="center" align="center"></el-table-column>
        <el-table-column prop="remainder" label="余数" header-align="center" align="center"></el-table-column>
        <el-table-column prop="intoTheQuantity" label="进仓数量" header-align="center" align="center"></el-table-column>
        <el-table-column prop="cartonAppearance" label="纸箱外观" header-align="center" align="center"></el-table-column>
        <el-table-column prop="packingDate" label="包装日期" header-align="center" align="center"></el-table-column>
        <el-table-column prop="remark" label="备注" header-align="center" align="center"></el-table-column>
        <el-table-column :label="$t('handle')" fixed="right" header-align="center" align="center" width="150">
          <template slot-scope="scope">
            <el-dropdown>
              <el-button type="primary">
                功能<i class="el-icon-arrow-down el-icon--right"></i>
              </el-button>
              <el-dropdown-menu slot="dropdown">
                <el-dropdown-item>
                  <el-button v-if="$hasPermission('packing:wrap:update')" type="text" size="small" @click="addOrUpdateHandle(scope.row.id)">{{ $t('update') }}</el-button>
                </el-dropdown-item>
                <el-dropdown-item>
                  <el-button v-if="$hasPermission('packing:wrap:delete')" type="text" size="small" @click="deleteHandle(scope.row.id)">{{ $t('delete') }}</el-button>
                </el-dropdown-item>
              </el-dropdown-menu>
            </el-dropdown>
          </template>
        </el-table-column>
      </el-table>
      <el-pagination
        :current-page="page"
        :page-sizes="[10, 20, 50, 100]"
        :page-size="limit"
        :total="total"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="pageSizeChangeHandle"
        @current-change="pageCurrentChangeHandle">
      </el-pagination>
      <!-- 弹窗, 新增 / 修改 -->
      <add-or-update v-if="addOrUpdateVisible" ref="addOrUpdate" @refreshDataList="getDataList"></add-or-update>
    </div>
  </el-card>
</template>

<script>
import mixinViewModule from '@/mixins/view-module'
import AddOrUpdate from './wrap-add-or-update'
import Cookies from "js-cookie";
export default {
  mixins: [mixinViewModule],
  data () {
    return {
      message:'',
      headers: {'token': Cookies.get('token')},
      fileList: [{name: '', url: ''}],
      unsuccessfulNameList: '',//  导入失败名称列表
      mixinViewModuleOptions: {
        getDataListURL: '/packing/wrap/page',
        getDataListIsPage: true,
        exportURL: '/packing/wrap/export',
        deleteURL: '/packing/wrap',
        deleteIsBatch: true,
        exportTemplateURL: 'packing/wrap/export/template',
        addBatchUrl: window.SITE_CONFIG['apiURL'] + 'packing/wrap/batchSave',
      },
      dataForm: {
        id: '',
        paramStr: '',
      },
      // 判断是否还在继续输入
      timer: null,
    }
  },
  components: {
    AddOrUpdate
  },
  methods:{
    //节流函数
    throttleFunction() {
      // 如果timer不为空就表明在持续输入,每次改变都清除定时，这样就可以实现只输入最后一次结果(注意每次间隔如果超过500毫秒就会输出)
      if (this.timer !== null) clearTimeout(this.timer)
      this.timer = setTimeout(() => {
        this.getDataList()
      }, 500)
    },
    /*addOrUpdateHandle(id){
      this.$router.push({name: 'packing-wrap-add-or-update',query:{id:id}})
    },*/
    beforeUpload(file) {
      this.message = this.$message({
        message: '上传中，请勿操作',
        center: true,
        duration: 0,
      });
    },
    handleChange(file, fileList) {
      this.fileList = fileList.slice(-3);

    },
    resultHandle(res) {
      if (res.code !== 0) {
      return this.$message.error(res.msg)
      }
      this.$message({
        message: this.$t('prompt.success'),
        type: 'success',
        duration: 500,
        onClose: () => {
          if (res.data) {
            this.unsuccessfulNameList = res.data
          }
          this.visible = false
          this.query()
        }
      })
    },
  }
}
</script>
