<template>
  <el-card shadow="never" class="aui-card--fill">
    <div class="mod-packing__hrvacation}">
      <el-form :inline="true" :model="dataForm" @keyup.enter.native="getDataList()">
        <el-form-item>
          <el-tooltip content="请输入查询关键字" placement="top">
            <el-input v-model="dataForm.id" placeholder="查询关键字" @input="throttleFunction" clearable>
              <el-button slot="append" icon="el-icon-search" @click="getDataList()"></el-button>
            </el-input>
          </el-tooltip>
        </el-form-item>
        <el-form-item>
          <el-button v-if="$hasPermission('packing:hrvacation:save')" type="primary" @click="addOrUpdateHandle()">{{ $t('add') }}</el-button>
        </el-form-item>
        <el-dropdown  style="float:right;">
          <el-button type="primary">
            更多功能<i class="el-icon-arrow-down el-icon--right"></i>
          </el-button>
          <el-dropdown-menu slot="dropdown">
            <el-dropdown-item>
              <el-form-item>
                <el-button type="info" @click="exportHandle()">{{ $t('export') }}</el-button>
              </el-form-item>
            </el-dropdown-item>
            <el-dropdown-item>
              <el-form-item>
                <el-button v-if="$hasPermission('packing:hrvacation:export')" type="info" @click="exportTemplateHandle()">
                  {{ $t('exportTemplate') }}
                </el-button>
              </el-form-item>
            </el-dropdown-item>
            <el-dropdown-item>
              <el-form-item>
                <el-tooltip content="下载模板填入信息后导入" placement="top">
                  <el-upload
                          v-if="$hasPermission('packing:hrvacation:save')"
                          class="upload-demo"
                          :action="mixinViewModuleOptions.addBatchUrl"
                          :headers="headers"
                          :multiple="false"
                          :show-file-list="false"
                          :file-list="fileList"
                          :before-upload="beforeUpload"
                          :on-success="resultHandle"
                          :on-change="handleChange"
                          accept="'.xlsx','.xls'">
                    <el-button type="success">{{ $t('addBatch') }}</el-button>
                  </el-upload>
                </el-tooltip>
                <el-button type="text" v-show="unsuccessfulNameList" @click="dialogVisible = true">导入失败列表</el-button>
              </el-form-item>
            </el-dropdown-item>
            <el-dropdown-item>
              <el-form-item>
                <el-button v-if="$hasPermission('packing:hrvacation:delete')" type="danger" @click="deleteHandle()">{{ $t('deleteBatch') }}</el-button>
              </el-form-item>
            </el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown>
      </el-form>
      <el-table v-loading="dataListLoading" height="500px" :data="dataList" border @selection-change="dataListSelectionChangeHandle" style="width: 100%;">
        <el-table-column type="selection" header-align="center" align="center" width="50"></el-table-column>
        <el-table-column prop="id" label="id" header-align="center" align="center"></el-table-column>
        <el-table-column prop="vacationType" label="假期类别" header-align="center" align="center">
          <template slot-scope="scope">
            {{scope.row.vacationType===0? '事假' :
              scope.row.vacationType===1?'病假':
                  scope.row.vacationType===2?'迟到/早退':
                      scope.row.vacationType===3?'未打卡':
                          scope.row.vacationType===4?'特休':
                              scope.row.vacationType===5?'婚假':
                                  scope.row.vacationType===6?'产假':
                                      scope.row.vacationType===7?'丧假':
                                          scope.row.vacationType===8?'年假':
                                              scope.row.vacationType===9?'公假':
                                                  scope.row.vacationType===10?'会议':
                                                  scope.row.vacationType===11?'培训':
                                                      scope.row.vacationType===12?'正常请假':
                                                          scope.row.vacationType===13?'紧急请假':'未知'
            }}
          </template>
        </el-table-column>

        <el-table-column prop="department" label="部门" header-align="center" align="center"></el-table-column>
        <el-table-column prop="title" label="职称" header-align="center" align="center"></el-table-column>
        <el-table-column prop="workerNumber" label="工号" header-align="center" align="center"></el-table-column>
        <el-table-column prop="surname" label="姓" header-align="center" align="center"></el-table-column>
        <el-table-column prop="name" label="名" header-align="center" align="center"></el-table-column>
        <el-table-column prop="leaveDate" label="请假日期" header-align="center" align="center"><template slot-scope="scope">
          {{scope.row.leaveDate+scope.row.leaveTime}}
        </template></el-table-column>
        <el-table-column prop="startDate" label="请假开始日期" header-align="center" align="center">
          <template slot-scope="scope">
            {{scope.row.startDate+scope.row.startLeaveTime}}
          </template>
        </el-table-column>
        <el-table-column prop="endDate" label="请假结束日期" header-align="center" align="center">
          <template slot-scope="scope">
            {{scope.row.endDate+scope.row.endLeaveTime}}
          </template>
        </el-table-column>
        <el-table-column prop="totalDays" label="总天数" header-align="center" align="center"></el-table-column>
        <el-table-column prop="totalHours" label="总小时数" header-align="center" align="center"></el-table-column>
        <el-table-column prop="agentWorkerNumber" label="代理人" header-align="center" align="center"></el-table-column>
        <el-table-column prop="remark" label="备注" header-align="center" align="center"></el-table-column>
<!--        <el-table-column prop="creator" label="创建者" header-align="center" align="center"></el-table-column>
        <el-table-column prop="createDate" label="创建时间" header-align="center" align="center"></el-table-column>
        <el-table-column prop="updater" label="更新者" header-align="center" align="center"></el-table-column>
        <el-table-column prop="updateDate" label="更新时间" header-align="center" align="center"></el-table-column>
        <el-table-column prop="disabled" label="是否失效 0:有效; 1:失效 (逻辑删除)" header-align="center" align="center"></el-table-column>
        <el-table-column prop="deptId" label="部门ID" header-align="center" align="center"></el-table-column>
        <el-table-column prop="employeeId" label="员工ID" header-align="center" align="center"></el-table-column>
        <el-table-column prop="userId" label="用户ID" header-align="center" align="center"></el-table-column>-->
        <el-table-column :label="$t('handle')" fixed="right" header-align="center" align="center" width="150">
          <template slot-scope="scope">
            <el-dropdown>
              <el-button type="primary">
                功能<i class="el-icon-arrow-down el-icon--right"></i>
              </el-button>
              <el-dropdown-menu slot="dropdown">
                <el-dropdown-item>
                  <el-button v-if="$hasPermission('packing:hrvacation:update')" type="text" size="small" @click="addOrUpdateHandle(scope.row.id)">{{ $t('update') }}</el-button>
                </el-dropdown-item>
                <el-dropdown-item>
                  <el-button v-if="$hasPermission('packing:hrvacation:delete')" type="text" size="small" @click="deleteHandle(scope.row.id)">{{ $t('delete') }}</el-button>
                </el-dropdown-item>
              </el-dropdown-menu>
            </el-dropdown>
          </template>
        </el-table-column>
      </el-table>
      <el-pagination
        :current-page="page"
        :page-sizes="[10, 20, 50, 100]"
        :page-size="limit"
        :total="total"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="pageSizeChangeHandle"
        @current-change="pageCurrentChangeHandle">
      </el-pagination>
      <!-- 弹窗, 新增 / 修改 -->
      <add-or-update v-if="addOrUpdateVisible" ref="addOrUpdate" @refreshDataList="getDataList"></add-or-update>
    </div>
  </el-card>
</template>

<script>
import mixinViewModule from '@/mixins/view-module'
import AddOrUpdate from './hrvacation-add-or-update'
import Cookies from "js-cookie";
export default {
  mixins: [mixinViewModule],
  data () {
    return {
      message:'',
      headers: {'token': Cookies.get('token')},
      fileList: [{name: '', url: ''}],
      unsuccessfulNameList: '',//  导入失败名称列表
      mixinViewModuleOptions: {
        getDataListURL: '/packing/hrvacation/page',
        getDataListIsPage: true,
        exportURL: '/packing/hrvacation/export',
        deleteURL: '/packing/hrvacation',
        deleteIsBatch: true,
        exportTemplateURL: '/packing/hrvacation/export/template',
        addBatchUrl: window.SITE_CONFIG['apiURL'] + '/packing/hrvacation/batchSave',
      },
      dataForm: {
        id: '',
        paramStr: '',
      },
      // 判断是否还在继续输入
      timer: null,
    }
  },
  components: {
    AddOrUpdate
  },
  methods:{
    //节流函数
    throttleFunction() {
      // 如果timer不为空就表明在持续输入,每次改变都清除定时，这样就可以实现只输入最后一次结果(注意每次间隔如果超过500毫秒就会输出)
      if (this.timer !== null) clearTimeout(this.timer)
      this.timer = setTimeout(() => {
        this.getDataList()
      }, 500)
    },
    beforeUpload(file) {
      this.message = this.$message({
        message: '上传中，请勿操作',
        center: true,
        duration: 0,
      });
    },
    handleChange(file, fileList) {
      this.fileList = fileList.slice(-3);

    },
    resultHandle(res) {
      if (res.code !== 0) {
      return this.$message.error(res.msg)
      }
      this.$message({
        message: this.$t('prompt.success'),
        type: 'success',
        duration: 500,
        onClose: () => {
          if (res.data) {
            this.unsuccessfulNameList = res.data
          }
          this.visible = false
          this.query()
        }
      })
    },
  }
}
</script>
