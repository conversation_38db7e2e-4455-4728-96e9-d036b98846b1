<template>
  <el-dialog width="65vw" :visible.sync="visible" :title="!dataForm.id ? $t('add') : $t('update')" :close-on-click-modal="false"
             :close-on-press-escape="false" append-to-body>
    <el-form :model="dataForm" :rules="dataRule" ref="dataForm" @keyup.enter.native="dataFormSubmitHandle()"
             :label-width="$i18n.locale === 'en-US' ? '120px' : '120px'">
      <div class="container">
        <el-form-item label="包装箱号" >
          <el-input v-model="dataForm.boxCode" placeholder="请输入箱号"></el-input>
        </el-form-item>
        <el-form-item label="品号" prop="partId">
          <el-select @change="autoFillBySelectPartId" v-model="dataForm.partId" filterable clearable>
            <el-option v-for="(item,index) in partOption"
                       :key="index"
                       :label="item.label"
                       :value="item.value">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="包装批号">
          <el-cascader
              v-model="dataForm.subBatchList"
              :options="subBatchOption"
              :props="props"
              filterable
              clearable></el-cascader>
        </el-form-item>
        <div v-if="dataForm.subBatchList.length !== 0">
          <el-button @click="visible1 = true" type="primary" style="margin-left: 2vw">包装数量详情</el-button>
        </div>
<!--        <el-form-item label="品号" prop="partId">-->
<!--                                                <el-form-item label="品号" prop="designation">-->
<!--                      <el-input v-model="dataForm.designation" placeholder="品号"></el-input>-->
<!--                    </el-form-item>-->
        <el-form-item label="客户代码" prop="customerCode">
          <el-input v-model="dataForm.customerCode" placeholder="客户代码"></el-input>
        </el-form-item>
        <el-form-item label="PE袋规格" prop="peSpecification">
          <el-autocomplete v-model="dataForm.peSpecification"
                           :fetch-suggestions="queryPeBySearch"
                           placeholder="请输入PE袋规格">
          </el-autocomplete>
        </el-form-item>
        <el-form-item label="纸箱规格" prop="boxSpecification">
          <el-autocomplete v-model="dataForm.boxSpecification"
                           :fetch-suggestions="queryBoxBySearch"
                           placeholder="请输入纸箱规格">
          </el-autocomplete>
        </el-form-item>
        <el-form-item label="每袋数量" prop="quantityPerBag">
          <el-autocomplete v-model="dataForm.quantityPerBag"
                           :fetch-suggestions="queryBagNumberBySearch"
          >
          </el-autocomplete>
        </el-form-item>
        <el-form-item label="每箱数量" prop="quantityOfEachCarton">
          <el-autocomplete v-model="dataForm.quantityOfEachCarton"
                           :fetch-suggestions="queryBoxNumberBySearch"
          >
          </el-autocomplete>
        </el-form-item>
        <el-form-item label="单重" >
          <el-input v-model="dataForm.pieceWeight" placeholder="单重"></el-input>
        </el-form-item>
        <el-form-item label="净重" >
          <el-input v-model="dataForm.suttle" placeholder="净重"></el-input>
        </el-form-item>
        <el-form-item label="毛重" >
          <el-input v-model="dataForm.grossWeight" placeholder="毛重"></el-input>
        </el-form-item>
<!--        <el-form-item label="数量" prop="quantity">-->
<!--          <el-input v-model="dataForm.quantity" placeholder="数量"></el-input>-->
<!--        </el-form-item>-->
        <!--                                        <el-form-item label="当前状态：0-待包装 1-已包装 2-已出货" prop="shippingStatus">-->
        <!--              <el-input v-model="dataForm.shippingStatus" placeholder="当前状态：0-待包装 1-已包装 2-已出货"></el-input>-->
        <!--            </el-form-item>-->
<!--        <el-form-item label="包装者" prop="packerId">-->
<!--          <el-input v-model="dataForm.packerId" placeholder="包装者id"></el-input>-->
<!--        </el-form-item>-->
        <!--                                        <el-form-item label="包装者" prop="packer">-->
        <!--              <el-input v-model="dataForm.packer" placeholder="包装者"></el-input>-->
        <!--            </el-form-item>-->
<!--        <el-form-item label="包装时间" prop="packingTime">-->
<!--          <el-input v-model="dataForm.packingTime" placeholder="包装时间"></el-input>-->
<!--        </el-form-item>-->
        <el-form-item label="库位">
          <el-input v-model="dataForm.storageLocation" placeholder="库位"></el-input>
        </el-form-item>
        <el-form-item label="备注" >
          <el-input v-model="dataForm.remark" placeholder="备注"></el-input>
        </el-form-item>
      </div>
    </el-form>
    <template slot="footer">
      <el-button @click="visible = false">{{ $t('cancel') }}</el-button>
      <el-button type="primary" @click="dataFormSubmitHandle()">{{ $t('confirm') }}</el-button>
    </template>
    <el-dialog width="40vw" :visible.sync="visible1" title="包装数量详情" :close-on-click-modal="false" :close-on-press-escape="false" append-to-body>
      <div style="display: flex; margin-bottom: 1vh" v-for="(item,index) in dataForm.subBatchList">
        <div>
          次批号：<el-input style="width: 160px" v-model="item[1].batchNumber + '-' + item[1].subBatchNumber"></el-input>
          <span style="margin-left: 2vw">包装数量：<el-input type="number" style="width: 120px;" v-model="item[1].toBePacked"></el-input></span>
        </div>
      </div>
    </el-dialog>
  </el-dialog>
</template>

<script>
import debounce from 'lodash/debounce'
import mixinViewModule from '@/mixins/view-module'
import th from "element-ui/src/locale/lang/th";

export default {
  mixins: [mixinViewModule],
  data() {
    return {
      visible: false,
      visible1: false,
      //创建该页面时，不调用数据查询接口
      mixinViewModuleOptions: {
        createdIsNeed: false,
      },
      props: {multiple: true},
      dataForm: {
        id: '',
        boxCode: '',
        partId: '',
        designation: '',
        customerCode: '',
        peSpecification: '',
        boxSpecification: '',
        quantityPerBag: '',
        quantityOfEachCarton: '',
        pieceWeight: '',
        suttle: '',
        grossWeight: '',
        quantity: '',
        shippingStatus: '',
        packerId: '',
        packer: '',
        packingTime: '',
        storageLocation: '',
        remark: '',
        subBatchList: []
      },
      partOption: [],
      subBatchOption:[],
      packStandards:[],
    }
  },
  computed: {
    dataRule() {
      return {
        id: [
          {required: true, message: this.$t('validate.required'), trigger: 'blur'}
        ],
        // boxCode: [
        //   {required: true, message: this.$t('validate.required'), trigger: 'blur'}
        // ],
        partId: [
          {required: true, message: this.$t('validate.required'), trigger: 'blur'}
        ],
        designation: [
          {required: true, message: this.$t('validate.required'), trigger: 'blur'}
        ],
        customerCode: [
          {required: true, message: this.$t('validate.required'), trigger: 'blur'}
        ],
        peSpecification: [
          {required: true, message: this.$t('validate.required'), trigger: 'blur'}
        ],
        boxSpecification: [
          {required: true, message: this.$t('validate.required'), trigger: 'blur'}
        ],
        quantityPerBag: [
          {required: true, message: this.$t('validate.required'), trigger: 'blur'}
        ],
        quantityOfEachCarton: [
          {required: true, message: this.$t('validate.required'), trigger: 'blur'}
        ],
        // pieceWeight: [
        //   {required: true, message: this.$t('validate.required'), trigger: 'blur'}
        // ],
        // suttle: [
        //   {required: true, message: this.$t('validate.required'), trigger: 'blur'}
        // ],
        // grossWeight: [
        //   {required: true, message: this.$t('validate.required'), trigger: 'blur'}
        // ],
        quantity: [
          {required: true, message: this.$t('validate.required'), trigger: 'blur'}
        ],
        // shippingStatus: [
        //   {required: true, message: this.$t('validate.required'), trigger: 'blur'}
        // ],
        packerId: [
          {required: true, message: this.$t('validate.required'), trigger: 'blur'}
        ],
        packer: [
          {required: true, message: this.$t('validate.required'), trigger: 'blur'}
        ],
        packingTime: [
          {required: true, message: this.$t('validate.required'), trigger: 'blur'}
        ],
        storageLocation: [
          {required: true, message: this.$t('validate.required'), trigger: 'blur'}
        ],
        remark: [
          {required: true, message: this.$t('validate.required'), trigger: 'blur'}
        ],
      }
    }
  },
  methods: {
    init() {
      this.visible = true
      this.$nextTick(() => {
        this.dataForm = {
          id: '',
          boxCode: '',
          partId: '',
          designation: '',
          customerCode: '',
          peSpecification: '',
          boxSpecification: '',
          quantityPerBag: '',
          quantityOfEachCarton: '',
          pieceWeight: '',
          suttle: '',
          grossWeight: '',
          quantity: '',
          shippingStatus: '',
          packerId: '',
          packer: '',
          packingTime: '',
          storageLocation: '',
          remark: '',
          subBatchList: []
        }
        this.getPartOption()
        this.subBatchOption = []
        if (this.dataForm.id) {
          this.getInfo()
        }
      })
    },
    // 获取信息
    getInfo() {
      this.$http.get(`/packing/packagingbox/${this.dataForm.id}`).then(({data: res}) => {
        if (res.code !== 0) {
          return this.$message.error(res.msg)
        }
        this.dataForm = {
          ...this.dataForm,
          ...res.data
        }
      }).catch(() => {
      })
    },
    //选取品号后的动作
    autoFillBySelectPartId(){
        if(this.dataForm.partId){
          let filter = this.partOption.filter(item => item.value === this.dataForm.partId);
          this.dataForm.designation = filter[0].data.designation
          this.getSubBatchOption(this.dataForm.partId);
          this.getCustomerCode(this.dataForm.partId)
          this.getPackStandards(this.dataForm.partId)
          this.getPackCode(this.dataForm.partId)
        }else {
          this.dataForm.subBatchList = []
          this.dataForm.customerCode = ''
          this.dataForm.designation = ''
          this.dataForm.boxCode = ''
        }
    },
    queryPeBySearch(queryString, cb){
      let filter = this.packStandards.filter(item => item.peBagSize.includes(queryString));
      let arr = []
      filter.forEach(item => arr.push({value:item.peBagSize}))
      cb(arr)
    },
    queryBoxBySearch(queryString, cb){
      let filter = this.packStandards.filter(item => item.cartonSize.includes(queryString));
      let arr = []
      filter.forEach(item => arr.push({value:item.cartonSize}))
      cb(arr)
    },
    queryBagNumberBySearch(queryString, cb){
      let filter = this.packStandards.filter(item => item.quantityPerBag.includes(queryString));
      let arr = []
      filter.forEach(item => arr.push({value:item.quantityPerBag}))
      cb(arr)
    },
    queryBoxNumberBySearch(queryString, cb){
      let filter = this.packStandards.filter(item => item.amountPerBox.includes(queryString));
      let arr = []
      filter.forEach(item => arr.push({value:item.amountPerBox}))
      cb(arr)
    },
    //获取包装箱号
    getPackCode(partId){
      this.$http.get(`packing/packagingbox/getPackCode?partId=${partId}`).then(({data: res}) => {
        if (res.code !== 0) {
          return this.$message.error(res.msg)
        }
        let number = Number(res.data) + 1;
        this.dataForm.boxCode = this.dataForm.designation + '-' + number
      })
    },
    //获取包装规格
    getPackStandards(partId){
      this.$http.get(`packing/packagingstandards/getPackStandardsByPartId?partId=${partId}`).then(({data : res}) =>{
        if (res.code !== 0) {
          return this.$message.error(res.msg)
        }
        if(res.data.length > 1){
          this.packStandards =  res.data
        }else if(res.data.length === 1){
          this.packStandards =  res.data
          this.dataForm.peSpecification = res.data[0].peBagSize
          this.dataForm.boxSpecification = res.data[0].cartonSize
          this.dataForm.quantityPerBag = res.data[0].quantityPerBag
          this.dataForm.quantityOfEachCarton = res.data[0].amountPerBox
        }else {
          this.packStandards =  []
          this.dataForm.peSpecification = ''
          this.dataForm.boxSpecification = ''
          this.dataForm.quantityPerBag = ''
          this.dataForm.quantityOfEachCarton = ''
        }
      })
    },
    //获取客户代码
    getCustomerCode(partId){
      this.$http.get(`fabricate/partcustomermiddle/getCustomerCodeByPartId?partId=${partId}`).then(({data : res}) =>{
        if (res.code !== 0) {
          return this.$message.error(res.msg)
        }
        this.dataForm.customerCode = res.data
      })
    },
    //获取次批列表
    getSubBatchOption(partId){
      this.$http.get(`batch/subbatch/getPackingSubBatchListByPartId?partId=${partId}`).then(({data: res}) => {
        if (res.code !== 0) {
          return this.$message.error(res.msg)
        }
        this.subBatchOption = res.data
      })
    },
    //获取品号列表
    getPartOption(){
      this.$http.get(`/fabricate/part/getPartListByDesignation`).then(({data: res}) => {
        if (res.code !== 0) {
          return this.$message.error(res.msg)
        }
        this.partOption = res.data.map((obj) => {
          return {
            label: obj.designation,
            value: obj.id,
            data: obj
          }
        })
      })
    },
    // 表单提交
    dataFormSubmitHandle: debounce(function () {
      this.$refs['dataForm'].validate((valid) => {
        if (!valid) {
          return false
        }
        let message = '';
        if (this.dataForm.id) {
          message = '此操作将修改该数据'
        } else {
          message = '此操作将新增一条数据'
        }
        this.$confirm(message + ', 是否继续?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
          center: true
        }).then(() => {
          let arr = []
          this.dataForm.subBatchList.forEach(item => arr.push(item[1]))
          this.dataForm.subBatchList = arr
          this.$http[!this.dataForm.id ? 'post' : 'put']('/packing/packagingbox/', this.dataForm).then(({data: res}) => {
            if (res.code !== 0) {
              return this.$message.error(res.msg)
            }
            this.dataForm =  {
              id: '',
                  boxCode: '',
                  partId: '',
                  designation: '',
                  customerCode: '',
                  peSpecification: '',
                  boxSpecification: '',
                  quantityPerBag: '',
                  quantityOfEachCarton: '',
                  pieceWeight: '',
                  suttle: '',
                  grossWeight: '',
                  quantity: '',
                  shippingStatus: '',
                  packerId: '',
                  packer: '',
                  packingTime: '',
                  storageLocation: '',
                  remark: '',
                  subBatchList: []
            }
            this.$message({
              message: this.$t('prompt.success'),
              type: 'success',
              duration: 500,
              onClose: () => {
                this.visible = false
                this.$emit('refreshDataList')
              }
            })
          }).catch(() => {
          })
        }).catch(() => {
          this.$message({
            type: 'info',
            message: '已取消'
          });
        });
      })
    }, 1000, {'leading': true, 'trailing': false})
  }
}
</script>

<style scoped>
.row {
  display: flex;
  justify-content: space-between;
  margin-bottom: 10px;
}

.col {
  width: 50%;
  box-sizing: border-box;
  padding: 0 10px;
}
</style>
