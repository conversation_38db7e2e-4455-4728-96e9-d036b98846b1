<template>
    <el-form :model="dataForm" :rules="dataRule" ref="dataForm" @keyup.enter.native="dataFormSubmitHandle()" :label-width="$i18n.locale === 'en-US' ? '120px' : '80px'">
      <div class="container">
        <div>
          <el-form-item label="包装日期" prop="packingDate">
            <el-date-picker
                v-model="dataForm.packingDate"
                type="date"
                value-format="yyyy-MM-dd"
                placeholder="包装日期">
            </el-date-picker>
          </el-form-item>
        </div>
        <div>
          <el-form-item label="包装员" prop="packers">
            <employee-component v-model="dataForm.packers" :departments="$productRepairDefault" placeholder="包装员"></employee-component>
          </el-form-item>
        </div>
        <div>
          <el-form-item label="外发员工姓名" prop="outgoingName">
            <el-input v-model="dataForm.outgoingName"  placeholder="外发员工姓名"></el-input>
          </el-form-item>
        </div>
      </div>
      <div class="container">
        <div class="box">
          <el-form-item label="次批号" prop="subBatchNumber">
            <span class="font_size">{{subBatchNumber}}</span>
          </el-form-item>
        </div>
      </div>
      <div class="container">
        <div class="item">
          <el-form-item label="称量员" prop="weighers">
            <employee-component v-model="dataForm.weighers" :departments="$productRepairDefault" placeholder="称量员"></employee-component>
          </el-form-item>
        </div>
        <div class="item">
          <el-form-item label="取样数量" prop="numberOfSamples">
            <el-input-number :controls="false" v-model="dataForm.numberOfSamples" placeholder="取样数量"></el-input-number>
          </el-form-item>
        </div>
        <div class="item">
          <el-form-item label="单重" prop="singleWeight">
            <el-input v-model="dataForm.singleWeight" placeholder="单重"></el-input>
          </el-form-item>
        </div>
      </div>
      <div class="container">
        <div>
          <el-form-item label="数量" prop="quantity">
            <el-input-number :controls="false" v-model="dataForm.quantity" placeholder="数量"></el-input-number>
          </el-form-item>
        </div>
        <div>
          <el-form-item label="余数" prop="remainder">
            <el-input-number :controls="false" v-model="dataForm.remainder" placeholder="余数"></el-input-number>
          </el-form-item>
        </div>
      </div>
      <div class="container">
        <div>
          <el-form-item label="进仓数量" prop="intoTheQuantity">
            <el-input v-model="dataForm.intoTheQuantity" placeholder="进仓数量"></el-input>
          </el-form-item>
        </div>
        <div>
          <el-form-item label="纸箱外观" prop="cartonAppearance">
            <ren-radio-group  v-model="dataForm.cartonAppearance" dict-type="ok_ng"></ren-radio-group>
          </el-form-item>
        </div>
        <div>
          <el-form-item label="确认人" prop="confirmPerson">
            <employee-component v-model="dataForm.confirmPerson" :departments="$productRepairDefault" placeholder="确认人"></employee-component>
          </el-form-item>
        </div>
      </div>
      <el-form-item label="备注" prop="remark">
        <el-input v-model="dataForm.remark" placeholder="备注"></el-input>
      </el-form-item>
    </el-form>
</template>

<script>
import debounce from 'lodash/debounce'
import mixinViewModule from '@/mixins/view-module'
export default {
  props:{
    subBatchNumber:{
      type:String,
      default:''
    }
  },
  mixins:[mixinViewModule],
 data () {
    return {
      visible: false,
      //创建该页面时，不调用数据查询接口
      mixinViewModuleOptions: {
        createdIsNeed: false,
      },
      dataForm: {
        id: this.$route.query.id,
        customerId: '',
        partId: '',
        customerOrderId: '',
        orderDate: '',
        subBatchId: '',
        subBatchNumber: this.subBatchNumber,
        deliveryDate: '',
        weighers: '',
        orderQuantity: 0,
        numberOfBoxes: 0,
        cartonId: '',
        numberOfSamples: 0,
        singleWeight: '',
        packers: '',
        outgoingName: '',
        quantity: 0,
        remainder: 0,
        intoTheQuantity: 0,
        cartonAppearance: 0,
        confirmPerson: '',
        packingDate: this.formatTimes(new Date()),
        remark: '',
        disabled: '',
        creator: '',
        createDate: '',
        updater: '',
        updateDate: ''
      }
    }
  },
  computed: {
    dataRule () {
      return {}
    }
  },
  methods: {
    init () {
      this.visible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].resetFields()
        if (this.dataForm.id) {
          this.getInfo()
        }
      })
    },
    // 获取信息
    getInfo () {
      this.$http.get(`/packing/wrap/${this.dataForm.id}`).then(({ data: res }) => {
        if (res.code !== 0) {
          return this.$message.error(res.msg)
        }
        this.dataForm = {
          ...this.dataForm,
          ...res.data
        }
      }).catch(() => {})
    },
    // 表单提交
    dataFormSubmitHandle() {
      this.$emit('wrap',this.dataForm)
    }
  }
}
</script>
