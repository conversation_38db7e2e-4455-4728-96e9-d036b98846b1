<template>
  <el-dialog :visible.sync="visible" title="调节库存" :close-on-click-modal="false" :close-on-press-escape="false" append-to-body>
    <el-form :model="dataForm" :rules="dataRule" ref="dataForm" @keyup.enter.native="dataFormSubmitHandle()" :label-width="$i18n.locale === 'en-US' ? '120px' : '130px'">
      <div style="display:flex">
        <el-form-item label="物料代码" prop="materialCode">
          <el-select v-model="dataForm.materialCode"
                     placeholder="请选择"
                     @change="changeInfo()"
                     filterable>
            <el-option
                v-for="item in materialList"
                :key="item.id"
                :label="item.category + '(' + item.rawMaterialCode + ')'"
                :value="item.rawMaterialCode">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="库存数量" >
          <el-input v-model="dataForm.inventoryQuantity" placeholder="" disabled>
            <template slot="append">{{this.dataForm.unit}}</template>
          </el-input>
        </el-form-item>
            <el-form-item label="调节数量" prop="regulatedQuantity">
              <el-input v-model="dataForm.regulatedQuantity" placeholder="调节数量">
                <template slot="append">{{this.dataForm.unit}}</template>
              </el-input>
            </el-form-item>
      </div>
      <div>
                                        <el-form-item label="调节原因" prop="regulatoryCause">
              <el-input type="textarea" :span:="3" v-model="dataForm.regulatoryCause" placeholder="调节原因"></el-input>
            </el-form-item>
      </div>
    </el-form>
    <template slot="footer">
      <el-button @click="closeDialog">{{ $t('cancel') }}</el-button>
      <el-button type="primary" @click="dataFormSubmitHandle()">{{ $t('confirm') }}</el-button>
    </template>
  </el-dialog>
</template>

<script>
import debounce from 'lodash/debounce'
import mixinViewModule from '@/mixins/view-module'
export default {
  mixins:[mixinViewModule],
  data () {
    return {
      visible: false,
      //创建该页面时，不调用数据查询接口
      mixinViewModuleOptions: {
        createdIsNeed: false,
      },
      dataForm: {
        id: '',
        materialCode: '',
        inventoryQuantity:'',
        unit:'',
        regulatedQuantity: '',
        regulatoryCause: '',
      },
      materialList:[],

    }
  },
  created() {
    this.getMaterialList()
  },
  computed: {
    dataRule () {
      return {
          id: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          materialCode: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          regulatedQuantity: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          regulatoryCause: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
                }
    }
  },
  methods: {
    init () {
      this.visible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].resetFields()
        if (this.dataForm.id) {
          this.getInfo()
        }
      })
    },
    closeDialog(){
      this.visible = false
      this.dataForm ={
            id: '',
            materialCode: '',
            inventoryQuantity:'',
            unit:'',
            regulatedQuantity: '',
            regulatoryCause: ''
      }
    },
    changeInfo(val){
        console.log('选中的值:' + val)
        let material = this.materialList.filter(item => item.rawMaterialCode === this.dataForm.materialCode);
        this.dataForm.inventoryQuantity = material[0].inventoryQuantity;
        this.dataForm.unit = material[0].unit;
    },
    //获取库存物料代码列表
    getMaterialList(){
      console.log('查询物料存库信息')
      this.$http.get(`/packing/materialbase/getMaterialInventoryList`).then(({ data: res }) => {
        if (res.code !== 0) {
          return this.$message.error(res.msg)
        }
        this.materialList = res.data
      }).catch(() => {

      })
    },
    // 获取信息
    getInfo () {
      this.$http.get(`/material/adjustmentofmaterialinventory/${this.dataForm.id}`).then(({ data: res }) => {
        if (res.code !== 0) {
          return this.$message.error(res.msg)
        }
        this.dataForm = {
          ...this.dataForm,
          ...res.data
        }
      }).catch(() => {})
    },
    // 表单提交
    dataFormSubmitHandle: debounce(function () {
      this.$refs['dataForm'].validate((valid) => {
        if (!valid) {
          return false
        }
        let message = '';
        if(this.dataForm.id){
          message = '此操作将修改该数据'
        }else {
          message = '此操作将新增一条数据'
        }
        this.$confirm(message+', 是否继续?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
          center: true
        }).then(() => {
          this.$http[!this.dataForm.id ? 'post' : 'put']('/material/adjustmentofmaterialinventory/', this.dataForm).then(({ data: res }) => {
            if (res.code !== 0) {
            return this.$message.error(res.msg)
            }
            this.$message({
          message: this.$t('prompt.success'),
            type: 'success',
                    duration: 500,
                    onClose: () => {
              this.visible = false
              this.$emit('refreshDataList')
            }
          })
              this.getMaterialList();
              this.dataForm = {
                id: '',
                materialCode: '',
                inventoryQuantity:'',
                unit:'',
                regulatedQuantity: '',
                regulatoryCause: ''
              }
          }).catch(() => {})
        }).catch(() => {
          this.$message({
            type: 'info',
            message: '已取消'
          });
        });
      })
    }, 1000, { 'leading': true, 'trailing': false })
  }
}
</script>
