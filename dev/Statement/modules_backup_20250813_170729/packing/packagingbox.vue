<template>
  <el-card shadow="never" class="aui-card--fill">
    <div class="mod-packing__packagingbox}">
      <el-form :inline="true" :model="dataForm" @keyup.enter.native="getDataList()">
        <el-form-item>
          <el-tooltip content="请输入查询关键字" placement="top">
            <el-input v-model="dataForm.id" placeholder="查询关键字" @input="throttleFunction" clearable>
              <el-button slot="append" icon="el-icon-search" @click="getDataList()"></el-button>
            </el-input>
          </el-tooltip>
        </el-form-item>
<!--        <el-form-item>-->
<!--          <el-button v-if="$hasPermission('packing:packagingbox:save')" type="primary" @click="addOrUpdateHandle()">{{ $t('add') }}</el-button>-->
<!--        </el-form-item>-->
        <el-dropdown  style="float:right;">
          <el-button type="primary">
            更多功能<i class="el-icon-arrow-down el-icon--right"></i>
          </el-button>
          <el-dropdown-menu slot="dropdown">
            <el-dropdown-item>
              <el-form-item>
                <el-button type="info" @click="exportHandle()">{{ $t('export') }}</el-button>
              </el-form-item>
            </el-dropdown-item>
            <el-dropdown-item>
              <el-form-item>
                <el-button v-if="$hasPermission('packing:packagingbox:export')" type="info" @click="exportTemplateHandle()">
                  {{ $t('exportTemplate') }}
                </el-button>
              </el-form-item>
            </el-dropdown-item>
            <el-dropdown-item>
              <el-form-item>
                <el-tooltip content="下载模板填入信息后导入" placement="top">
                  <el-upload
                          v-if="$hasPermission('packing:packagingbox:save')"
                          class="upload-demo"
                          :action="mixinViewModuleOptions.addBatchUrl"
                          :headers="headers"
                          :multiple="false"
                          :show-file-list="false"
                          :file-list="fileList"
                          :before-upload="beforeUpload"
                          :on-success="resultHandle"
                          :on-change="handleChange"
                          accept="'.xlsx','.xls'">
                    <el-button type="success">{{ $t('addBatch') }}</el-button>
                  </el-upload>
                </el-tooltip>
                <el-button type="text" v-show="unsuccessfulNameList" @click="dialogVisible = true">导入失败列表</el-button>
              </el-form-item>
            </el-dropdown-item>
            <el-dropdown-item>
              <el-form-item>
                <el-button v-if="$hasPermission('packing:packagingbox:delete')" type="danger" @click="deleteHandle()">{{ $t('deleteBatch') }}</el-button>
              </el-form-item>
            </el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown>
      </el-form>
      <el-table v-loading="dataListLoading" height="500px" :data="dataList" border @selection-change="dataListSelectionChangeHandle" style="width: 100%;">
        <el-table-column type="selection" header-align="center" align="center" width="50"></el-table-column>
<!--        <el-table-column prop="id" label="主键id" header-align="center" align="center"></el-table-column>-->
        <el-table-column prop="boxCode" label="箱号" header-align="center" align="center">
            <template slot-scope="scope">
              <el-link @click="showPackDetail(scope.row.id)" type="primary">{{scope.row.boxCode}}</el-link>
            </template>
        </el-table-column>
<!--        <el-table-column prop="partId" label="品号id" header-align="center" align="center"></el-table-column>-->
        <el-table-column prop="designation" label="品号" header-align="center" align="center"></el-table-column>
        <el-table-column prop="customerCode" label="客户代码" header-align="center" align="center"></el-table-column>
<!--        <el-table-column prop="peSpecification" label="PE袋规格" header-align="center" align="center"></el-table-column>-->
<!--        <el-table-column prop="boxSpecification" label="纸箱规格" header-align="center" align="center"></el-table-column>-->
<!--        <el-table-column prop="quantityPerBag" label="每袋数量" header-align="center" align="center"></el-table-column>-->
<!--        <el-table-column prop="quantityOfEachCarton" label="每箱数量" header-align="center" align="center"></el-table-column>-->
        <el-table-column prop="pieceWeight" label="单重" header-align="center" align="center">
          <template slot-scope="scope">
            {{scope.row.pieceWeight}}g
          </template>
        </el-table-column>
        <el-table-column prop="suttle" label="净重" header-align="center" align="center">
          <template slot-scope="scope">
            {{scope.row.suttle}}kg
          </template>
        </el-table-column>
        <el-table-column prop="grossWeight" label="毛重" header-align="center" align="center">
          <template slot-scope="scope">
            {{scope.row.grossWeight}}kg
          </template>
        </el-table-column>
        <el-table-column prop="quantity" label="数量" header-align="center" align="center">

        </el-table-column>
        <el-table-column prop="shippingStatus" label="当前状态" header-align="center" align="center">
          <template slot-scope="scope">
            <el-tag
                :type="scope.row.shippingStatus === 0 ? '' : scope.row.shippingStatus === 1 ? 'success' : 'info'"
            >{{scope.row.shippingStatus === 0 ? '待包装' : scope.row.shippingStatus === 1 ? '已包装' : '已出货'}}
            </el-tag>
          </template>
        </el-table-column>
<!--        <el-table-column prop="packerId" label="包装者id" header-align="center" align="center"></el-table-column>-->
        <el-table-column prop="packer" label="包装者" header-align="center" align="center"></el-table-column>
        <el-table-column prop="packingTime" label="包装时间" header-align="center" align="center"></el-table-column>
        <el-table-column prop="storageLocation" label="库位" header-align="center" align="center"></el-table-column>
<!--        <el-table-column prop="remark" label="备注" header-align="center" align="center"></el-table-column>-->
<!--        <el-table-column prop="disabled" label="是否失效 0:有效; 1:失效 (逻辑删除)" header-align="center" align="center"></el-table-column>-->
<!--        <el-table-column prop="creator" label="创建者" header-align="center" align="center"></el-table-column>-->
<!--        <el-table-column prop="createDate" label="创建时间" header-align="center" align="center"></el-table-column>-->
<!--        <el-table-column prop="updater" label="更新者" header-align="center" align="center"></el-table-column>-->
<!--        <el-table-column prop="updateDate" label="更新时间" header-align="center" align="center"></el-table-column>-->
        <el-table-column :label="$t('handle')" fixed="right" header-align="center" align="center" width="150">
          <template slot-scope="scope">
            <el-dropdown>
              <el-button type="primary">
                功能<i class="el-icon-arrow-down el-icon--right"></i>
              </el-button>
              <el-dropdown-menu slot="dropdown">
<!--                <el-dropdown-item>-->
<!--                  <el-button v-if="$hasPermission('packing:packagingbox:update')" type="text" size="small" @click="addOrUpdateHandle(scope.row.id)">{{ $t('update') }}</el-button>-->
<!--                </el-dropdown-item>-->
                <el-dropdown-item>
                  <el-button v-if="$hasPermission('packing:packagingbox:delete')" type="text" size="small" @click="deleteRow(scope.row.id)">{{ $t('delete') }}</el-button>
                </el-dropdown-item>
              </el-dropdown-menu>
            </el-dropdown>
          </template>
        </el-table-column>
      </el-table>
      <el-pagination
        :current-page="page"
        :page-sizes="[10, 20, 50, 100]"
        :page-size="limit"
        :total="total"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="pageSizeChangeHandle"
        @current-change="pageCurrentChangeHandle">
      </el-pagination>
      <!-- 弹窗, 新增 / 修改 -->
      <add-or-update v-if="addOrUpdateVisible" ref="addOrUpdate" @refreshDataList="getDataList"></add-or-update>
    </div>
    <el-dialog :visible.sync="visible" title="包装详情" width="550px" center>
  <div style="display: flex; justify-content: center;">
    <el-table :data="detailData" style="width: 480px;">
      <el-table-column prop="subBatchNumber" label="批次号" width="240"></el-table-column>
      <el-table-column prop="quantity" label="数量" width="230"></el-table-column>
    </el-table>
  </div>
</el-dialog>
  </el-card>
</template>

<script>
import mixinViewModule from '@/mixins/view-module'
import AddOrUpdate from './packagingbox-add-or-update'
import Cookies from "js-cookie";
export default {
  mixins: [mixinViewModule],
  data () {
    return {
      visible: false,
      message:'',
      headers: {'token': Cookies.get('token')},
      fileList: [{name: '', url: ''}],
      unsuccessfulNameList: '',//  导入失败名称列表
      mixinViewModuleOptions: {
        getDataListURL: '/packing/packagingbox/page',
        getDataListIsPage: true,
        exportURL: '/packing/packagingbox/export',
        deleteURL: '/packing/packagingbox',
        deleteIsBatch: true,
        exportTemplateURL: '/packing/packagingbox/export/template',
        addBatchUrl: window.SITE_CONFIG['apiURL'] + '/packing/packagingbox/batchSave',
      },
      dataForm: {
        id: '',
        paramStr: '',
      },
      detailData:[],
      // 判断是否还在继续输入
      timer: null,
    }
  },
  components: {
    AddOrUpdate
  },
  methods:{
    //查看包装详情
    showPackDetail(boxId){
        //获取该包装详情
        this.visible = true
        this.searchPackDetail(boxId)
    },
    //查找包装详情
    searchPackDetail(boxId){
        this.$http.get(`packing/packagingboxdetail/getBoxDetailData?boxId=${boxId}`).then(({data: res}) => {
          if (res.code !== 0) {
            return this.$message.error(res.msg)
          }
          this.detailData = res.data
        })
    },
    deleteRow(id){
      this.$confirm('<h2>点击确认删除</h2>', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
        center: true,
        customClass:'set_custom_class',
        dangerouslyUseHTMLString: true
      }).then(() => {
        this.$http.delete(`packing/packagingbox/deleteBox?id=${id}`).then(({data: res}) => {
          if (res.code !== 0){
            return this.$message.error(res.msg)
          }
          console.log('已删除')
          this.query()
        })
      })
    },
    //节流函数
    throttleFunction() {
      // 如果timer不为空就表明在持续输入,每次改变都清除定时，这样就可以实现只输入最后一次结果(注意每次间隔如果超过500毫秒就会输出)
      if (this.timer !== null) clearTimeout(this.timer)
      this.timer = setTimeout(() => {
        this.getDataList()
      }, 500)
    },
    beforeUpload(file) {
      this.message = this.$message({
        message: '上传中，请勿操作',
        center: true,
        duration: 0,
      });
    },
    handleChange(file, fileList) {
      this.fileList = fileList.slice(-3);

    },
    resultHandle(res) {
      if (res.code !== 0) {
      return this.$message.error(res.msg)
      }
      this.$message({
        message: this.$t('prompt.success'),
        type: 'success',
        duration: 500,
        onClose: () => {
          if (res.data) {
            this.unsuccessfulNameList = res.data
          }
          this.visible = false
          this.query()
        }
      })
    },
  }
}
</script>
