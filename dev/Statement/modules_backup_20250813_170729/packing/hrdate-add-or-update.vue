<template>
  <el-dialog :visible.sync="visible" :title="!dataForm.id ? $t('add') : $t('update')" :close-on-click-modal="false" :close-on-press-escape="false" append-to-body>
    <el-form :model="dataForm" :rules="dataRule" ref="dataForm" @keyup.enter.native="dataFormSubmitHandle()" :label-width="$i18n.locale === 'en-US' ? '120px' : '80px'">
      <div class="container">
                                                <el-form-item label="日历数" prop="dateInt">
              <el-input v-model="dataForm.dateInt" placeholder="日历数"></el-input>
            </el-form-item>
                                        <el-form-item label="日期" prop="dateNew">
              <el-input v-model="dataForm.dateNew" placeholder="日期"></el-input>
            </el-form-item>
                                        <el-form-item label="农历" prop="dateTraditional">
              <el-input v-model="dataForm.dateTraditional" placeholder="农历"></el-input>
            </el-form-item>
                                        <el-form-item label="年周别" prop="weekYearNumber">
              <el-input v-model="dataForm.weekYearNumber" placeholder="年周别"></el-input>
            </el-form-item>
                                        <el-form-item label="星期数" prop="weekNumber">
              <el-input v-model="dataForm.weekNumber" placeholder="星期数"></el-input>
            </el-form-item>
                                        <el-form-item label="班别" prop="workType">
              <el-input v-model="dataForm.workType" placeholder="班别"></el-input>
            </el-form-item>
                                        <el-form-item label="薪点数" prop="salaryPoints">
              <el-input v-model="dataForm.salaryPoints" placeholder="薪点数"></el-input>
            </el-form-item>
                                        <el-form-item label="政府上班日" prop="workDay">
              <el-input v-model="dataForm.workDay" placeholder="政府上班日"></el-input>
            </el-form-item>
                                        <el-form-item label="备注" prop="remark">
              <el-input v-model="dataForm.remark" placeholder="备注"></el-input>
            </el-form-item>
                                                                                                                                  <el-form-item label="节气" prop="solarTerms">
              <el-input v-model="dataForm.solarTerms" placeholder="节气"></el-input>
            </el-form-item>
                        </div>
    </el-form>
    <template slot="footer">
      <el-button @click="visible = false">{{ $t('cancel') }}</el-button>
      <el-button type="primary" @click="dataFormSubmitHandle()">{{ $t('confirm') }}</el-button>
    </template>
  </el-dialog>
</template>

<script>
import debounce from 'lodash/debounce'
import mixinViewModule from '@/mixins/view-module'
export default {
  mixins:[mixinViewModule],
  data () {
    return {
      visible: false,
      //创建该页面时，不调用数据查询接口
      mixinViewModuleOptions: {
        createdIsNeed: false,
      },
      dataForm: {
        id: '',
        dateInt: '',
        dateNew: '',
        dateTraditional: '',
        weekYearNumber: '',
        weekNumber: '',
        workType: '',
        salaryPoints: '',
        workDay: '',
        remark: '',
        disabled: '',
        creator: '',
        createDate: '',
        updater: '',
        updateDate: '',
        solarTerms: ''
      }
    }
  },
  computed: {
    dataRule () {
      return {
          id: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          dateInt: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          dateNew: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          dateTraditional: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          weekYearNumber: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          weekNumber: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          workType: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          salaryPoints: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          workDay: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          remark: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
                    solarTerms: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ]
      }
    }
  },
  methods: {
    init () {
      this.visible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].resetFields()
        if (this.dataForm.id) {
          this.getInfo()
        }
      })
    },
    // 获取信息
    getInfo () {
      this.$http.get(`/packing/hrdate/${this.dataForm.id}`).then(({ data: res }) => {
        if (res.code !== 0) {
          return this.$message.error(res.msg)
        }
        this.dataForm = {
          ...this.dataForm,
          ...res.data
        }
      }).catch(() => {})
    },
    // 表单提交
    dataFormSubmitHandle: debounce(function () {
      this.$refs['dataForm'].validate((valid) => {
        if (!valid) {
          return false
        }
        let message = '';
        if(this.dataForm.id){
          message = '此操作将修改该数据'
        }else {
          message = '此操作将新增一条数据'
        }
        this.$confirm(message+', 是否继续?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
          center: true
        }).then(() => {
          this.$http[!this.dataForm.id ? 'post' : 'put']('/packing/hrdate/', this.dataForm).then(({ data: res }) => {
            if (res.code !== 0) {
            return this.$message.error(res.msg)
            }
            this.$message({
          message: this.$t('prompt.success'),
            type: 'success',
                    duration: 500,
                    onClose: () => {
              this.visible = false
              this.$emit('refreshDataList')
            }
          })
          }).catch(() => {})
        }).catch(() => {
          this.$message({
            type: 'info',
            message: '已取消'
          });
        });
      })
    }, 1000, { 'leading': true, 'trailing': false })
  }
}
</script>
