<template>
  <el-dialog :visible.sync="visible" :title="!dataForm.id ? $t('add') : $t('update')" :close-on-click-modal="false" :close-on-press-escape="false" append-to-body>
    <el-form :model="dataForm" :rules="dataRule" ref="dataForm" @keyup.enter.native="dataFormSubmitHandle()" :label-width="$i18n.locale === 'en-US' ? '120px' : '80px'">
      <div class="container">
                                                <el-form-item label="客户代码" prop="customerCode">
              <el-input v-model="dataForm.customerCode" placeholder="客户代码"></el-input>
            </el-form-item>
                                        <el-form-item label="部门别" prop="department">
              <el-input v-model="dataForm.department" placeholder="部门别"></el-input>
            </el-form-item>
                                        <el-form-item label="姓名" prop="name">
              <el-input v-model="dataForm.name" placeholder="姓名"></el-input>
            </el-form-item>
                                        <el-form-item label="电话" prop="phone">
              <el-input v-model="dataForm.phone" placeholder="电话"></el-input>
            </el-form-item>
                                        <el-form-item label="传真电话" prop="faxPhone">
              <el-input v-model="dataForm.faxPhone" placeholder="传真电话"></el-input>
            </el-form-item>
                                        <el-form-item label="电子邮件" prop="email">
              <el-input v-model="dataForm.email" placeholder="电子邮件"></el-input>
            </el-form-item>
                                        <el-form-item label="通讯软件" prop="communicationSoftware">
              <el-input v-model="dataForm.communicationSoftware" placeholder="通讯软件"></el-input>
            </el-form-item>
                                        <el-form-item label="所在地区" prop="area">
              <el-input v-model="dataForm.area" placeholder="所在地区"></el-input>
            </el-form-item>
                                        <el-form-item label="分类(业务，财务，技术，品保)" prop="category">
              <el-input v-model="dataForm.category" placeholder="分类(业务，财务，技术，品保)"></el-input>
            </el-form-item>
                                        <el-form-item label="对应事项" prop="correspondingMatters">
              <el-input v-model="dataForm.correspondingMatters" placeholder="对应事项"></el-input>
            </el-form-item>
                                        <el-form-item label="备注" prop="remark">
              <el-input v-model="dataForm.remark" placeholder="备注"></el-input>
            </el-form-item>
                        </div>
    </el-form>
    <template slot="footer">
      <el-button @click="visible = false">{{ $t('cancel') }}</el-button>
      <el-button type="primary" @click="dataFormSubmitHandle()">{{ $t('confirm') }}</el-button>
    </template>
  </el-dialog>
</template>

<script>
import debounce from 'lodash/debounce'
import mixinViewModule from '@/mixins/view-module'
export default {
  mixins:[mixinViewModule],
  data () {
    return {
      visible: false,
      //创建该页面时，不调用数据查询接口
      mixinViewModuleOptions: {
        createdIsNeed: false,
      },
      dataForm: {
        id: '',
        customerCode: '',
        department: '',
        name: '',
        phone: '',
        faxPhone: '',
        email: '',
        communicationSoftware: '',
        area: '',
        category: '',
        correspondingMatters: '',
        remark: ''
      }
    }
  },
  computed: {
    dataRule () {
      return {
          id: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          customerCode: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ]/*,
          department: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          name: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          phone: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          faxPhone: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          email: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          communicationSoftware: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          area: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          category: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          correspondingMatters: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          remark: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ]*/
      }
    }
  },
  methods: {
    init () {
      this.visible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].resetFields()
        if (this.dataForm.id) {
          this.getInfo()
        }
      })
    },
    // 获取信息
    getInfo () {
      this.$http.get(`/packing/materialsuppliercontactpersoninformation/${this.dataForm.id}`).then(({ data: res }) => {
        if (res.code !== 0) {
          return this.$message.error(res.msg)
        }
        this.dataForm = {
          ...this.dataForm,
          ...res.data
        }
      }).catch(() => {})
    },
    // 表单提交
    dataFormSubmitHandle: debounce(function () {
      this.$refs['dataForm'].validate((valid) => {
        if (!valid) {
          return false
        }
        let message = '';
        if(this.dataForm.id){
          message = '此操作将修改该数据'
        }else {
          message = '此操作将新增一条数据'
        }
        this.$confirm(message+', 是否继续?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
          center: true
        }).then(() => {
          this.$http[!this.dataForm.id ? 'post' : 'put']('/packing/materialsuppliercontactpersoninformation/', this.dataForm).then(({ data: res }) => {
            if (res.code !== 0) {
            return this.$message.error(res.msg)
            }
            this.$message({
          message: this.$t('prompt.success'),
            type: 'success',
                    duration: 500,
                    onClose: () => {
              this.visible = false
              this.$emit('refreshDataList')
            }
          })
          }).catch(() => {})
        }).catch(() => {
          this.$message({
            type: 'info',
            message: '已取消'
          });
        });
      })
    }, 1000, { 'leading': true, 'trailing': false })
  }
}
</script>
