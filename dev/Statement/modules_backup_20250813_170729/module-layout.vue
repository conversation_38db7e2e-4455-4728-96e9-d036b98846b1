<template>
  <div>
    <div>
      <p v-if="greeting">
        {{greeting}}，
        <b v-if="user.surname">
          <span v-if="user.surname">{{user.surname}}</span>
          <span v-if="user.name">{{user.name}}</span>
        </b>
        <b v-else>{{user.username}}</b>
        <span>现在时间是{{nowTime}}</span>
      </p>
    </div>
    <div>
      <el-divider content-position="center">
        <span style="font-size: 20px; font-weight: bold;">您的权限</span>
      </el-divider>
      <div>

        <div class="borderStyle" v-for="item in dataForm"><div><span style="font-size: 15px; font-weight: bold;">{{item.parentName}}</span></div>


            <div class="container">
        <div  v-for="item in item.menuList">
          <div @click="jumpToYourComponent(item.url)" class="div-display">
            <div class="bottom">
              <svg class="icon-svg aui-sidebar__menu-icon" aria-hidden="true"><use :xlink:href="`#${item.icon}`"></use></svg>
            </div>
            <p>{{item.name}}</p>
          </div>
        </div>
            </div>

        </div>
    </div>

    </div>
  </div>
</template>

<script>
import el from "element-ui/src/locale/lang/el";

export default {
  name: "module-layout",
  data(){
    return{
      nowTime:'',
      greeting:'',
      user:'',
      rights:'',
      dataForm: {

      }
    }
  },
  created() {
    this.$users.$on('getUser', this.getUser);
  },
  beforeDestroy() {
    this.$users.$off('getUser', this.getUser);
  },
  mounted(){
    this.$users.$on('getUser', this.getUser);
    let time  = setInterval(()=>{
      this.getNowDate()
    },1000)
    this.getUser()
  },
  methods:{

    jumpToYourComponent(url) {
      let newUrl = url.replace(/\//, '-')
      this.$router.push(newUrl)
    },
    //获取用户以及权限信息
    getUser(){
      let obtain = setInterval(()=>{
        let user = JSON.parse(localStorage.getItem("userData"));
        let rights = JSON.parse(localStorage.getItem("userRights"));
        console.log("获取的用户数据：",user)
        console.log("获取的用户数据this.user：",this.user)
        console.log("获取的用户权限：",rights)
        this.getRights(user.id)
        this.getUsers(user.id)
        if(user){
          clearInterval(obtain)
        }
      },1000)
    },
    getUsers(userId){
      this.$http.get(`/sys/user/${userId}`).then(({ data: res }) => {
        if (res.code !== 0) {
          return this.$message.error(res.msg)
        }
        this.user = res.data;
        console.log("用户数据为：",res.data)
        sessionStorage.setItem("users", JSON.stringify(res.data))
      }).catch(() => {})
    },
    getRights(userId){
      this.$http['get'](`/sys/menu/getUserRights/`+userId ).then(({ data: res }) => {
        if (res.code !== 0) {
          return this.$message.error(res.msg)
        }
        console.log("count:",res.data)
        this.dataForm = res.data
      }).catch(() => {})
    },
    //获取当前时间
    getNowDate() {
      var date = new Date();
      var sign2 = ":";
      var year = date.getFullYear() // 年
      var month = date.getMonth() + 1; // 月
      var day = date.getDate(); // 日
      var hour = date.getHours(); // 时
      var minutes = date.getMinutes(); // 分
      var seconds = date.getSeconds() //秒
      var weekArr = ['星期天','星期一', '星期二', '星期三', '星期四', '星期五', '星期六'];
      var week = weekArr[date.getDay()];
      let greet = ''

      if(hour < 8 && hour > 6){
        greet = '早上好'
      }else if (hour < 12){
        greet = '上午好'
      }else if (hour < 13){
        greet = '中午好'
      }else if (hour < 18){
        greet = '下午好'
      }else{
        greet = '晚上好'
      }
      this.greeting = greet

      // 给一位数的数据前面加 “0”
      if (month >= 1 && month <= 9) {
        month = "0" + month;
      }
      if (day >= 0 && day <= 9) {
        day = "0" + day;
      }
      if (hour >= 0 && hour <= 9) {
        hour = "0" + hour;
      }
      if (minutes >= 0 && minutes <= 9) {
        minutes = "0" + minutes;
      }
      if (seconds >= 0 && seconds <= 9) {
        seconds = "0" + seconds;
      }

      this.nowTime =  year + "年" + month + "月" + day + "号 " + week + ' ' + hour + sign2 + minutes + sign2 + seconds;
    },
  }
}
</script>

<style scoped>
.container {
  display: flex;
  flex-wrap: wrap;
  align-items: center; /* 实现垂直居中 */
  justify-content: center; /* 实现水平居中 */
  text-align: center; /* 实现水平居中 */
}
.item {
  width: calc(20%);
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  text-align: center;
  /* 可以根据实际情况设置子 div 的大小 */
}
.bottom{
  width: 65px;
  height: 65px;
  line-height: 50px;
  text-align: center;
  background-color: cornflowerblue;
  border-radius: 20px;
  cursor: pointer;
  margin-top: 2px;
  /*color: white;
  text-align: center; !* 让文本居中 *!
  display: flex; !* 将元素转换为弹性盒子 *!
  justify-content: center; !* 水平居中 *!
  align-items: center; !* 垂直居中 *!
  transition: all 0.2s ease-in-out; !* 添加过渡效果 *!
  font-weight: bold; !* 加粗 *!
  font-size: 15px; !* 加大 *!*/
}

.div-display{
  width: 100px;
  height: 100px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  text-align: center;
}
.div-display:hover{
  transform: scale(1.1); /* 放大 10% */
  background-color: gray; /* 变亮一点 */
}
.borderStyle{
  border: solid 1px black;
  padding: 10px;
}
</style>
