<template>
  <el-dialog :visible.sync="visible" title="通知信息" :close-on-click-modal="false" :close-on-press-escape="false" append-to-body>
    <el-form :model="dataForm" :rules="dataRule" ref="dataForm" @keyup.enter.native="dataFormSubmitHandle()" :label-width="$i18n.locale === 'en-US' ? '120px' : '80px'">
      <div class="container">
        <el-form-item label="通知类型" prop="notifyType">
          <ren-select v-model="dataForm.notifyType" dict-type="notify_type" placeholder="通知类型 0:业务流程通知;1:直接通知;2:异常通知"></ren-select>
        </el-form-item>
        <el-form-item label="发送者" prop="sender">
          <employee-component v-model="dataForm.sender" placeholder="发送者"></employee-component>
        </el-form-item>
        <el-form-item v-if="dataForm.id" label="发送时间" prop="sendTime">
          <el-input v-model="dataForm.sendTime" placeholder="发送时间"></el-input>
        </el-form-item>
      </div>
      <el-form-item label="通知内容" prop="notifyContent">
        <el-input type="textarea" :autosize="{ minRows: 2, maxRows: 4}" v-model="dataForm.notifyContent" placeholder="通知内容"></el-input>
      </el-form-item>
    </el-form>
    <template slot="footer">
      <el-button type="primary" @click="visible = false">{{ $t('confirm') }}</el-button>
    </template>
  </el-dialog>
</template>

<script>
import debounce from 'lodash/debounce'
import mixinViewModule from '@/mixins/view-module'
export default {
  mixins:[mixinViewModule],
  data () {
    return {
      visible: false,
      //创建该页面时，不调用数据查询接口
      mixinViewModuleOptions: {
        createdIsNeed: false,
      },
      dataForm: {
        id: '',
        notifyType: 0,
        sender: '',
        receiver: '',
        notifyContent: '',
        notifyStatus: 0,
        sendTime: '',
        readingTime: '',
        remark: '',
        disabled: '',
        creator: '',
        createDate: '',
        updater: '',
        updateDate: ''
      }
    }
  },
  computed: {
    dataRule () {
      return {
        notifyType: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        sender: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        receiver: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        notifyContent: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
      }
    }
  },
  methods: {
    init () {
      this.visible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].resetFields()
        if (this.dataForm.id) {
          this.getInfo()
        }
      })
    },
    // 获取信息
    getInfo () {
      this.$http.get(`/notify/notify/${this.dataForm.id}`).then(({ data: res }) => {
        if (res.code !== 0) {
          return this.$message.error(res.msg)
        }
        this.dataForm = {
          ...this.dataForm,
          ...res.data
        }
      }).catch(() => {})
    },
  }
}
</script>
