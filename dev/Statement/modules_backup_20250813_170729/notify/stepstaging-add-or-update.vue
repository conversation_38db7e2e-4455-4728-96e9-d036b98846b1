<template>
  <el-dialog :visible.sync="visible" :title="!dataForm.id ? $t('add') : $t('update')" :close-on-click-modal="false" :close-on-press-escape="false" append-to-body>
    <el-form :model="dataForm" :rules="dataRule" ref="dataForm" @keyup.enter.native="dataFormSubmitHandle()" :label-width="$i18n.locale === 'en-US' ? '120px' : '80px'">
      <div class="container">
                                                <el-form-item label="关联表名(和哪个表进行关联)" prop="relatedTableName">
              <el-input v-model="dataForm.relatedTableName" placeholder="关联表名(和哪个表进行关联)"></el-input>
            </el-form-item>
                                        <el-form-item label="关联字段(和哪个字段进行关联)" prop="relatedFields">
              <el-input v-model="dataForm.relatedFields" placeholder="关联字段(和哪个字段进行关联)"></el-input>
            </el-form-item>
                                        <el-form-item label="关联数据(用什么和它关联)" prop="linkedData">
              <el-input v-model="dataForm.linkedData" placeholder="关联数据(用什么和它关联)"></el-input>
            </el-form-item>
                                        <el-form-item label="表名" prop="tableName">
              <el-input v-model="dataForm.tableName" placeholder="表名"></el-input>
            </el-form-item>
                                        <el-form-item label="暂存数据" prop="jsonData">
              <el-input v-model="dataForm.jsonData" placeholder="暂存数据"></el-input>
            </el-form-item>
                                        <el-form-item label="是否完成 0:暂存; 1:完成" prop="finish">
              <el-input v-model="dataForm.finish" placeholder="是否完成 0:暂存; 1:完成"></el-input>
            </el-form-item>
                                        <el-form-item label="备注" prop="remark">
              <el-input v-model="dataForm.remark" placeholder="备注"></el-input>
            </el-form-item>
                                                                                                                  </div>
    </el-form>
    <template slot="footer">
      <el-button @click="visible = false">{{ $t('cancel') }}</el-button>
      <el-button type="primary" @click="dataFormSubmitHandle()">{{ $t('confirm') }}</el-button>
    </template>
  </el-dialog>
</template>

<script>
import debounce from 'lodash/debounce'
import mixinViewModule from '@/mixins/view-module'
export default {
  mixins:[mixinViewModule],
  data () {
    return {
      visible: false,
      //创建该页面时，不调用数据查询接口
      mixinViewModuleOptions: {
        createdIsNeed: false,
      },
      dataForm: {
        id: '',
        relatedTableName: '',
        relatedFields: '',
        linkedData: '',
        tableName: '',
        jsonData: '',
        finish: '',
        remark: '',
        disabled: '',
        creator: '',
        createDate: '',
        updater: '',
        updateDate: ''
      }
    }
  },
  computed: {
    dataRule () {
      return {
          id: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          relatedTableName: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          relatedFields: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          linkedData: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          tableName: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          jsonData: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          finish: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          remark: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
                }
    }
  },
  methods: {
    init () {
      this.visible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].resetFields()
        if (this.dataForm.id) {
          this.getInfo()
        }
      })
    },
    // 获取信息
    getInfo () {
      this.$http.get(`/notify/stepstaging/${this.dataForm.id}`).then(({ data: res }) => {
        if (res.code !== 0) {
          return this.$message.error(res.msg)
        }
        this.dataForm = {
          ...this.dataForm,
          ...res.data
        }
      }).catch(() => {})
    },
    // 表单提交
    dataFormSubmitHandle: debounce(function () {
      this.$refs['dataForm'].validate((valid) => {
        if (!valid) {
          return false
        }
        let message = '';
        if(this.dataForm.id){
          message = '此操作将修改该数据'
        }else {
          message = '此操作将新增一条数据'
        }
        this.$confirm(message+', 是否继续?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
          center: true
        }).then(() => {
          this.$http[!this.dataForm.id ? 'post' : 'put']('/notify/stepstaging/', this.dataForm).then(({ data: res }) => {
            if (res.code !== 0) {
            return this.$message.error(res.msg)
            }
            this.$message({
          message: this.$t('prompt.success'),
            type: 'success',
                    duration: 500,
                    onClose: () => {
              this.visible = false
              this.$emit('refreshDataList')
            }
          })
          }).catch(() => {})
        }).catch(() => {
          this.$message({
            type: 'info',
            message: '已取消'
          });
        });
      })
    }, 1000, { 'leading': true, 'trailing': false })
  }
}
</script>
