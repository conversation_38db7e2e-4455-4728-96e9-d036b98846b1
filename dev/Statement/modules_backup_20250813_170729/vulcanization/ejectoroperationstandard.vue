<template>
  <el-card shadow="never" class="aui-card--fill">
    <div class="mod-vulcanization__ejectoroperationstandard}">
      <el-form :inline="true" :model="dataForm" @keyup.enter.native="getDataList()">
        <el-form-item>
          <el-tooltip content="请输入查询关键字" placement="top">
            <el-input v-model="dataForm.id" placeholder="查询关键字" @input="throttleFunction" clearable>
              <el-button slot="append" icon="el-icon-search" @click="getDataList()"></el-button>
            </el-input>
          </el-tooltip>
        </el-form-item>
        <el-form-item>
          <el-button v-if="$hasPermission('vulcanization:ejectoroperationstandard:save')" type="primary" @click="addOrUpdateHandle()">{{ $t('add') }}</el-button>
        </el-form-item>
        <el-dropdown  style="float:right;">
          <el-button type="primary">
            更多功能<i class="el-icon-arrow-down el-icon--right"></i>
          </el-button>
          <el-dropdown-menu slot="dropdown">
            <el-dropdown-item>
              <el-form-item>
                <el-button type="info" @click="exportHandle()">{{ $t('export') }}</el-button>
              </el-form-item>
            </el-dropdown-item>
            <el-dropdown-item>
              <el-form-item>
                <el-button v-if="$hasPermission('vulcanization:ejectoroperationstandard:export')" type="info" @click="exportTemplateHandle()">
                  {{ $t('exportTemplate') }}
                </el-button>
              </el-form-item>
            </el-dropdown-item>
            <el-dropdown-item>
              <el-form-item>
                <el-tooltip content="下载模板填入信息后导入" placement="top">
                  <el-upload
                          v-if="$hasPermission('vulcanization:ejectoroperationstandard:save')"
                          class="upload-demo"
                          :action="mixinViewModuleOptions.addBatchUrl"
                          :headers="headers"
                          :multiple="false"
                          :show-file-list="false"
                          :file-list="fileList"
                          :before-upload="beforeUpload"
                          :on-success="resultHandle"
                          :on-change="handleChange"
                          accept="'.xlsx','.xls'">
                    <el-button type="success">{{ $t('addBatch') }}</el-button>
                  </el-upload>
                </el-tooltip>
                <el-button type="text" v-show="unsuccessfulNameList" @click="dialogVisible = true">导入失败列表</el-button>
              </el-form-item>
            </el-dropdown-item>
            <el-dropdown-item>
              <el-form-item>
                <el-button v-if="$hasPermission('vulcanization:ejectoroperationstandard:delete')" type="danger" @click="deleteHandle()">{{ $t('deleteBatch') }}</el-button>
              </el-form-item>
            </el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown>
      </el-form>
      <el-table v-loading="dataListLoading" height="500px" :data="dataList" border @selection-change="dataListSelectionChangeHandle" style="width: 100%;">
        <el-table-column type="selection" header-align="center" align="center" width="50"></el-table-column>
        <el-table-column prop="id" label="id" header-align="center" align="center"></el-table-column>
        <el-table-column prop="partName" label="部品名称" header-align="center" align="center"></el-table-column>
        <el-table-column prop="partId" label="部品id" header-align="center" align="center"></el-table-column>
        <el-table-column prop="version" label="版本" header-align="center" align="center"></el-table-column>
        <el-table-column prop="material" label="材质" header-align="center" align="center"></el-table-column>
        <el-table-column prop="formulationId" label="配方编号" header-align="center" align="center"></el-table-column>
        <el-table-column prop="fileNo" label="文件编号" header-align="center" align="center"></el-table-column>
        <el-table-column prop="machineId" label="机台编号" header-align="center" align="center"></el-table-column>
        <el-table-column prop="dieSizeOne" label="模具尺寸1" header-align="center" align="center"></el-table-column>
        <el-table-column prop="dieSizeTwo" label="模具尺寸2" header-align="center" align="center"></el-table-column>
        <el-table-column prop="dieSizeThree" label="模具尺寸3" header-align="center" align="center"></el-table-column>
        <el-table-column prop="formingMachine" label="成形机" header-align="center" align="center"></el-table-column>
        <el-table-column prop="moldNumber" label="模具孔数" header-align="center" align="center"></el-table-column>
        <el-table-column prop="moldFace" label="模具面" header-align="center" align="center"></el-table-column>
        <el-table-column prop="materialSizeWidth" label="材料尺寸宽" header-align="center" align="center"></el-table-column>
        <el-table-column prop="materialSizeWidthSuitable" label="材料尺寸宽±" header-align="center" align="center"></el-table-column>
        <el-table-column prop="materialSizeThick" label="材料尺寸厚" header-align="center" align="center"></el-table-column>
        <el-table-column prop="materialSizeThickSuitable" label="材料尺寸厚±" header-align="center" align="center"></el-table-column>
        <el-table-column prop="shelfLife" label="保存期限" header-align="center" align="center"></el-table-column>
        <el-table-column prop="assignmentStandardBookId" label="作业标准书id" header-align="center" align="center"></el-table-column>
        <el-table-column prop="shapingSettingsLocation" label="成形设定_位置" header-align="center" align="center"></el-table-column>
        <el-table-column prop="shapingSettingsLocationSuitable" label="成形设定_位置±" header-align="center" align="center"></el-table-column>
        <el-table-column prop="shapingSettingsPressure" label="成形设定_压力" header-align="center" align="center"></el-table-column>
        <el-table-column prop="shapingSettingsPressureSuitable" label="成形设定_压力±" header-align="center" align="center"></el-table-column>
        <el-table-column prop="shapingSettingsSpeed" label="成形设定_速度" header-align="center" align="center"></el-table-column>
        <el-table-column prop="shapingSettingsSpeedSuitable" label="成形设定_速度±" header-align="center" align="center"></el-table-column>
        <el-table-column prop="lockingSettingsLocation" label="锁模设定_位置" header-align="center" align="center"></el-table-column>
        <el-table-column prop="lockingSettingsLocationSuitable" label="锁模设定_位置±" header-align="center" align="center"></el-table-column>
        <el-table-column prop="lockingSettingsPressure" label="锁模设定_压力" header-align="center" align="center"></el-table-column>
        <el-table-column prop="lockingSettingsPressureSuitable" label="锁模设定_压力±" header-align="center" align="center"></el-table-column>
        <el-table-column prop="lockingSettingsSpeed" label="锁模设定_速度" header-align="center" align="center"></el-table-column>
        <el-table-column prop="lockingSettingsSpeedSuitable" label="锁模设定_速度±" header-align="center" align="center"></el-table-column>
        <el-table-column prop="forwardShotLocation" label="前射退_位置" header-align="center" align="center"></el-table-column>
        <el-table-column prop="forwardShotLocationSuitable" label="前射退_位置±" header-align="center" align="center"></el-table-column>
        <el-table-column prop="forwardShotPressure" label="前射退_压力" header-align="center" align="center"></el-table-column>
        <el-table-column prop="forwardShotPressureSuitable" label="前射退_压力±" header-align="center" align="center"></el-table-column>
        <el-table-column prop="forwardShotSpeed" label="前射退_速度" header-align="center" align="center"></el-table-column>
        <el-table-column prop="forwardShotSpeedSuitable" label="前射退_速度±" header-align="center" align="center"></el-table-column>
        <el-table-column prop="feedingSectionLocation" label="入料段_位置" header-align="center" align="center"></el-table-column>
        <el-table-column prop="feedingSectionLocationSuitable" label="前射退_位置±" header-align="center" align="center"></el-table-column>
        <el-table-column prop="feedingSectionPressure" label="入料段_压力" header-align="center" align="center"></el-table-column>
        <el-table-column prop="feedingSectionPressureSuitable" label="入料段_压力±" header-align="center" align="center"></el-table-column>
        <el-table-column prop="feedingSectionSpeed" label="入料段_速度" header-align="center" align="center"></el-table-column>
        <el-table-column prop="feedingSectionSpeedSuitable" label="入料段_速度±" header-align="center" align="center"></el-table-column>
        <el-table-column prop="backShotLocation" label="后射退_位置" header-align="center" align="center"></el-table-column>
        <el-table-column prop="backShotLocationSuitable" label="后射退_位置±" header-align="center" align="center"></el-table-column>
        <el-table-column prop="backShotPressure" label="后射退_压力" header-align="center" align="center"></el-table-column>
        <el-table-column prop="backShotPressureSuitable" label="后射退_压力±" header-align="center" align="center"></el-table-column>
        <el-table-column prop="backShotSpeed" label="后射退_速度" header-align="center" align="center"></el-table-column>
        <el-table-column prop="backShotSpeedSuitable" label="后射退_速度±" header-align="center" align="center"></el-table-column>
        <el-table-column prop="packingSectionLocation" label="保压段_位置" header-align="center" align="center"></el-table-column>
        <el-table-column prop="packingSectionLocationSuitable" label="保压段_位置±" header-align="center" align="center"></el-table-column>
        <el-table-column prop="packingSectionPressure" label="保压段_压力" header-align="center" align="center"></el-table-column>
        <el-table-column prop="packingSectionPressureSuitable" label="保压段_压力±" header-align="center" align="center"></el-table-column>
        <el-table-column prop="packingSectionSpeed" label="保压段_速度" header-align="center" align="center"></el-table-column>
        <el-table-column prop="packingSectionSpeedSuitable" label="保压段_速度±" header-align="center" align="center"></el-table-column>
        <el-table-column prop="endOfEjectionLocation" label="射出终段_位置" header-align="center" align="center"></el-table-column>
        <el-table-column prop="endOfEjectionLocationSuitable" label="射出终段_位置±" header-align="center" align="center"></el-table-column>
        <el-table-column prop="endOfEjectionPressure" label="射出终段_压力" header-align="center" align="center"></el-table-column>
        <el-table-column prop="endOfEjectionPressureSuitable" label="射出终段_压力±" header-align="center" align="center"></el-table-column>
        <el-table-column prop="endOfEjectionSpeed" label="射出终段_速度" header-align="center" align="center"></el-table-column>
        <el-table-column prop="endOfEjectionSpeedSuitable" label="射出终段_速度±" header-align="center" align="center"></el-table-column>
        <el-table-column prop="injectionSectionThreeLocation" label="射出3段_位置" header-align="center" align="center"></el-table-column>
        <el-table-column prop="injectionSectionThreeLocationSuitable" label="射出3段_位置±" header-align="center" align="center"></el-table-column>
        <el-table-column prop="injectionSectionThreePressure" label="射出3段_压力" header-align="center" align="center"></el-table-column>
        <el-table-column prop="injectionSectionThreePressureSuitable" label="射出3段_压力±" header-align="center" align="center"></el-table-column>
        <el-table-column prop="injectionSectionThreeSpeed" label="射出3段_速度" header-align="center" align="center"></el-table-column>
        <el-table-column prop="injectionSectionThreeSpeedSuitable" label="射出3段_速度±" header-align="center" align="center"></el-table-column>
        <el-table-column prop="injectionSectionTwoLocation" label="射出2段_位置" header-align="center" align="center"></el-table-column>
        <el-table-column prop="injectionSectionTwoLocationSuitable" label="射出2段_位置±" header-align="center" align="center"></el-table-column>
        <el-table-column prop="injectionSectionTwoPressure" label="射出2段_压力" header-align="center" align="center"></el-table-column>
        <el-table-column prop="injectionSectionTwoPressureSuitable" label="射出2段_压力±" header-align="center" align="center"></el-table-column>
        <el-table-column prop="injectionSectionTwoSpeed" label="射出2段_速度" header-align="center" align="center"></el-table-column>
        <el-table-column prop="injectionSectionTwoSpeedSuitable" label="射出2段_速度±" header-align="center" align="center"></el-table-column>
        <el-table-column prop="injectionSectionOneLocation" label="射出1段_位置" header-align="center" align="center"></el-table-column>
        <el-table-column prop="injectionSectionOneLocationSuitable" label="射出1段_位置±" header-align="center" align="center"></el-table-column>
        <el-table-column prop="injectionSectionOnePressure" label="射出1段_压力" header-align="center" align="center"></el-table-column>
        <el-table-column prop="injectionSectionOnePressureSuitable" label="射出1段_压力±" header-align="center" align="center"></el-table-column>
        <el-table-column prop="injectionSectionOneSpeed" label="射出1段_速度" header-align="center" align="center"></el-table-column>
        <el-table-column prop="injectionSectionOneSpeedSuitable" label="射出1段_速度±" header-align="center" align="center"></el-table-column>
        <el-table-column prop="modulusSettingValue" label="模重_设定值" header-align="center" align="center"></el-table-column>
        <el-table-column prop="modulusSettingValueSuitable" label="模重_设定值±" header-align="center" align="center"></el-table-column>
        <el-table-column prop="modulusActualValue" label="模重_实际值" header-align="center" align="center"></el-table-column>
        <el-table-column prop="modulusActualValueSuitable" label="模重_实际值±" header-align="center" align="center"></el-table-column>
        <el-table-column prop="upperMoldHeatingBothSidesValue" label="上模两侧电热_设定值" header-align="center" align="center"></el-table-column>
        <el-table-column prop="upperMoldHeatingBothSidesValueSuitable" label="上模两侧电热_设定值±" header-align="center" align="center"></el-table-column>
        <el-table-column prop="upperMoldHeatingBothSidesActualValue" label="上模中间电热_实际值" header-align="center" align="center"></el-table-column>
        <el-table-column prop="upperMoldHeatingBothSidesActualValueSuitable" label="上模中间电热_实际值±" header-align="center" align="center"></el-table-column>
        <el-table-column prop="lowerMiddleHeatingSetValue" label="下模中间电热_设定值" header-align="center" align="center"></el-table-column>
        <el-table-column prop="lowerMiddleHeatingSetValueSuitable" label="下模中间电热_设定值±" header-align="center" align="center"></el-table-column>
        <el-table-column prop="lowerMiddleHeatingActualValue" label="下模中间电热_实际值" header-align="center" align="center"></el-table-column>
        <el-table-column prop="lowerMiddleHeatingActualValueSuitable" label="下模中间电热_设定值±" header-align="center" align="center"></el-table-column>
        <el-table-column prop="moldTemperatureSetValue" label="模具上模温度_设定值" header-align="center" align="center"></el-table-column>
        <el-table-column prop="moldTemperatureSetValueSuitable" label="模具上模温度_设定值±" header-align="center" align="center"></el-table-column>
        <el-table-column prop="moldTemperatureActualValue" label="模具上模温度_实际值" header-align="center" align="center"></el-table-column>
        <el-table-column prop="moldTemperatureActualValueSuitable" label="模具上模温度_实际值±" header-align="center" align="center"></el-table-column>
        <el-table-column prop="feedTubeSetValue" label="入料料管_设定值" header-align="center" align="center"></el-table-column>
        <el-table-column prop="feedTubeSetValueSuitable" label="入料料管_设定值±" header-align="center" align="center"></el-table-column>
        <el-table-column prop="feedTubeActualValue" label="入料料管_实际值" header-align="center" align="center"></el-table-column>
        <el-table-column prop="feedTubeActualValueSuitable" label="入料料管_实际值±" header-align="center" align="center"></el-table-column>
        <el-table-column prop="injectionTubeSetValue" label="射料料管_设定值" header-align="center" align="center"></el-table-column>
        <el-table-column prop="injectionTubeSetValueSuitable" label="射料料管_设定值±" header-align="center" align="center"></el-table-column>
        <el-table-column prop="injectionTubeActualValue" label="射料料管_实际值" header-align="center" align="center"></el-table-column>
        <el-table-column prop="injectionTubeActualValueSuitable" label="射料料管_实际值±" header-align="center" align="center"></el-table-column>
        <el-table-column prop="upAndDownSetValue" label="上下切换点_设定值" header-align="center" align="center"></el-table-column>
        <el-table-column prop="upAndDownSetValueSuitable" label="上下切换点_设定值±" header-align="center" align="center"></el-table-column>
        <el-table-column prop="upAndDownActualValue" label="上下切换点_实际值" header-align="center" align="center"></el-table-column>
        <el-table-column prop="upAndDownActualValueSuitable" label="上下切换点_实际值±" header-align="center" align="center"></el-table-column>
        <el-table-column prop="dropPositioningSetValue" label="下降定位位置_设定值" header-align="center" align="center"></el-table-column>
        <el-table-column prop="dropPositioningSetValueSuitable" label="下降定位位置_设定值±" header-align="center" align="center"></el-table-column>
        <el-table-column prop="dropPositioningActualValue" label="下降定位位置_实际值" header-align="center" align="center"></el-table-column>
        <el-table-column prop="dropPositioningActualValueSuitable" label="下降定位位置_实际值±" header-align="center" align="center"></el-table-column>
        <el-table-column prop="vulcanizationTime" label="加硫时间_时间" header-align="center" align="center"></el-table-column>
        <el-table-column prop="vulcanizationTimeSuitable" label="加硫时间_时间±" header-align="center" align="center"></el-table-column>
        <el-table-column prop="injectionDelayTime" label="射料延迟_时间" header-align="center" align="center"></el-table-column>
        <el-table-column prop="injectionDelayTimeSuitable" label="射料延迟_时间±" header-align="center" align="center"></el-table-column>
        <el-table-column prop="compressTime" label="保压时间_时间" header-align="center" align="center"></el-table-column>
        <el-table-column prop="compressTimeSuitable" label="保压时间_时间±" header-align="center" align="center"></el-table-column>
        <el-table-column prop="feedDelayTime" label="入料延迟_时间" header-align="center" align="center"></el-table-column>
        <el-table-column prop="feedDelayTimeSuitable" label="入料延迟_时间±" header-align="center" align="center"></el-table-column>
        <el-table-column prop="returnTime" label="退料时间_时间" header-align="center" align="center"></el-table-column>
        <el-table-column prop="returnTimeSuitable" label="退料时间_时间±" header-align="center" align="center"></el-table-column>
        <el-table-column prop="exhaustTimes" label="排气次数_时间" header-align="center" align="center"></el-table-column>
        <el-table-column prop="exhaustTimesSuitable" label="排气次数_时间±" header-align="center" align="center"></el-table-column>
        <el-table-column prop="firstStopOnExhaust" label="第一次排气上停_时间" header-align="center" align="center"></el-table-column>
        <el-table-column prop="firstStopOnExhaustSuitable" label="第一次排气上停_时间±" header-align="center" align="center"></el-table-column>
        <el-table-column prop="stopAtTheFirstExhaust" label="第一次排气下停_时间" header-align="center" align="center"></el-table-column>
        <el-table-column prop="stopAtTheFirstExhaustSuitable" label="第一次排气下停_时间±" header-align="center" align="center"></el-table-column>
        <el-table-column prop="firstExhaustDistance" label="第一次排气距离_时间" header-align="center" align="center"></el-table-column>
        <el-table-column prop="firstExhaustDistanceSuitable" label="第一次排气距离_时间±" header-align="center" align="center"></el-table-column>
        <el-table-column prop="firstExhaustTimes" label="第一次排气次数_时间" header-align="center" align="center"></el-table-column>
        <el-table-column prop="firstExhaustTimesSuitable" label="第一次排气次数_时间±" header-align="center" align="center"></el-table-column>
        <el-table-column prop="secondExhaustStop" label="第二次排气上停_时间" header-align="center" align="center"></el-table-column>
        <el-table-column prop="secondExhaustStopSuitable" label="第二次排气上停_时间±" header-align="center" align="center"></el-table-column>
        <el-table-column prop="secondStop" label="第二次排气下停_时间" header-align="center" align="center"></el-table-column>
        <el-table-column prop="secondStopSuitable" label="第二次排气下停_时间±" header-align="center" align="center"></el-table-column>
        <el-table-column prop="secondExhaustDistance" label="第二次排气距离_时间" header-align="center" align="center"></el-table-column>
        <el-table-column prop="secondExhaustDistanceSuitable" label="第二次排气距离_时间±" header-align="center" align="center"></el-table-column>
        <el-table-column prop="theSecondExhaustTimes" label="第二次排气次数_时间" header-align="center" align="center"></el-table-column>
        <el-table-column prop="theSecondExhaustTimesSuitable" label="第二次排气次数_时间±" header-align="center" align="center"></el-table-column>
        <el-table-column prop="exhaustWaitingTime" label="排气等待时间_时间" header-align="center" align="center"></el-table-column>
        <el-table-column prop="exhaustWaitingTimeSuitable" label="排气等待时间_时间±" header-align="center" align="center"></el-table-column>
        <el-table-column prop="exhaustRisePressure" label="排气上升压力_时间" header-align="center" align="center"></el-table-column>
        <el-table-column prop="exhaustRisePressureSuitable" label="排气上升压力_时间±" header-align="center" align="center"></el-table-column>
        <el-table-column prop="exhaustRisingSpeed" label="排气上升速度_时间" header-align="center" align="center"></el-table-column>
        <el-table-column prop="exhaustRisingSpeedSuitable" label="排气上升速度_时间±" header-align="center" align="center"></el-table-column>
        <el-table-column prop="exhaustDropPressure" label="排气下降压力_时间" header-align="center" align="center"></el-table-column>
        <el-table-column prop="exhaustDropPressureSuitable" label="排气下降压力_时间±" header-align="center" align="center"></el-table-column>
        <el-table-column prop="exhaustDescendingSpeed" label="排气下降速度_时间" header-align="center" align="center"></el-table-column>
        <el-table-column prop="exhaustDescendingSpeedSuitable" label="排气下降速度_时间±" header-align="center" align="center"></el-table-column>
        <el-table-column prop="moldCleaning" label="模具清洗" header-align="center" align="center"></el-table-column>
        <el-table-column prop="cleaningFrequency" label="清洗频率_时间" header-align="center" align="center"></el-table-column>
        <el-table-column prop="cleaningFrequencySuitable" label="清洗频率_时间±" header-align="center" align="center"></el-table-column>
        <el-table-column prop="precautions" label="注意事项" header-align="center" align="center"></el-table-column>
        <el-table-column prop="picture" label="图片" header-align="center" align="center"></el-table-column>
        <el-table-column prop="holeNumberDistribution" label="孔号分布" header-align="center" align="center"></el-table-column>
        <el-table-column prop="defectiveRateTarget" label="不良率目标" header-align="center" align="center"></el-table-column>
        <el-table-column prop="productivityTarget" label="产效目标" header-align="center" align="center"></el-table-column>
        <el-table-column prop="semiFinishedProductNumber" label="半成品装箱数量" header-align="center" align="center"></el-table-column>
        <el-table-column prop="approve" label="批准" header-align="center" align="center"></el-table-column>
        <el-table-column prop="confirm" label="确认" header-align="center" align="center"></el-table-column>
        <el-table-column prop="create" label="作成" header-align="center" align="center"></el-table-column>
        <el-table-column prop="remark" label="备注" header-align="center" align="center"></el-table-column>
        <el-table-column prop="disabled" label="是否失效 0:有效; 1:失效 (逻辑删除)" header-align="center" align="center"></el-table-column>
        <el-table-column prop="creator" label="创建者" header-align="center" align="center"></el-table-column>
        <el-table-column prop="createDate" label="创建时间" header-align="center" align="center"></el-table-column>
        <el-table-column prop="updater" label="更新者" header-align="center" align="center"></el-table-column>
        <el-table-column prop="updateDate" label="更新时间" header-align="center" align="center"></el-table-column>
        <el-table-column :label="$t('handle')" fixed="right" header-align="center" align="center" width="150">
          <template slot-scope="scope">
            <el-dropdown>
              <el-button type="primary">
                功能<i class="el-icon-arrow-down el-icon--right"></i>
              </el-button>
              <el-dropdown-menu slot="dropdown">
                <el-dropdown-item>
                  <el-button v-if="$hasPermission('vulcanization:ejectoroperationstandard:update')" type="text" size="small" @click="addOrUpdateHandle(scope.row.id)">{{ $t('update') }}</el-button>
                </el-dropdown-item>
                <el-dropdown-item>
                  <el-button v-if="$hasPermission('vulcanization:ejectoroperationstandard:delete')" type="text" size="small" @click="deleteHandle(scope.row.id)">{{ $t('delete') }}</el-button>
                </el-dropdown-item>
              </el-dropdown-menu>
            </el-dropdown>
          </template>
        </el-table-column>
      </el-table>
      <el-pagination
        :current-page="page"
        :page-sizes="[10, 20, 50, 100]"
        :page-size="limit"
        :total="total"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="pageSizeChangeHandle"
        @current-change="pageCurrentChangeHandle">
      </el-pagination>
      <!-- 弹窗, 新增 / 修改 -->
      <add-or-update v-if="addOrUpdateVisible" ref="addOrUpdate" @refreshDataList="getDataList"></add-or-update>
    </div>
  </el-card>
</template>

<script>
import mixinViewModule from '@/mixins/view-module'
import AddOrUpdate from './ejectoroperationstandard-add-or-update'
import Cookies from "js-cookie";
export default {
  mixins: [mixinViewModule],
  data () {
    return {
      message:'',
      headers: {'token': Cookies.get('token')},
      fileList: [{name: '', url: ''}],
      unsuccessfulNameList: '',//  导入失败名称列表
      mixinViewModuleOptions: {
        getDataListURL: '/vulcanization/ejectoroperationstandard/page',
        getDataListIsPage: true,
        exportURL: '/vulcanization/ejectoroperationstandard/export',
        deleteURL: '/vulcanization/ejectoroperationstandard',
        deleteIsBatch: true,
        exportTemplateURL: '/vulcanization/ejectoroperationstandard/export/template',
        addBatchUrl: window.SITE_CONFIG['apiURL'] + '/vulcanization/ejectoroperationstandard/batchSave',
      },
      dataForm: {
        id: '',
        paramStr: '',
      },
      // 判断是否还在继续输入
      timer: null,
    }
  },
  components: {
    AddOrUpdate
  },
  methods:{
    //节流函数
    throttleFunction() {
      // 如果timer不为空就表明在持续输入,每次改变都清除定时，这样就可以实现只输入最后一次结果(注意每次间隔如果超过500毫秒就会输出)
      if (this.timer !== null) clearTimeout(this.timer)
      this.timer = setTimeout(() => {
        this.getDataList()
      }, 500)
    },
    beforeUpload(file) {
      this.message = this.$message({
        message: '上传中，请勿操作',
        center: true,
        duration: 0,
      });
    },
    handleChange(file, fileList) {
      this.fileList = fileList.slice(-3);

    },
    resultHandle(res) {
      if (res.code !== 0) {
      return this.$message.error(res.msg)
      }
      this.$message({
        message: this.$t('prompt.success'),
        type: 'success',
        duration: 500,
        onClose: () => {
          if (res.data) {
            this.unsuccessfulNameList = res.data
          }
          this.visible = false
          this.query()
        }
      })
    },
  }
}
</script>
