<template>
  <el-dialog :visible.sync="visible" :title="!dataForm.id ? $t('add') : $t('update')" :close-on-click-modal="false" :close-on-press-escape="false" append-to-body>
    <el-form :model="dataForm" :rules="dataRule" ref="dataForm" @keyup.enter.native="dataFormSubmitHandle()" :label-width="$i18n.locale === 'en-US' ? '120px' : '80px'">
          <el-form-item label="生产指令" prop="manufacturingInstructions">
              <el-autocomplete
                  class="inline-input"
                  v-model="dataForm.manufacturingInstructions"
                  :fetch-suggestions="getLikemanufacturing"
                  placement="bottom"
                  placeholder="请输入生产指令"
                  :trigger-on-focus="false"
                  @select="handleSelectManufacturing">
              </el-autocomplete>
      </el-form-item>
          <el-form-item label="总生产量" prop="totalProduction">
          <el-input v-model="dataForm.totalProduction" placeholder="总生产量"></el-input>
      </el-form-item>
          <el-form-item label="剩余量" prop="remainingAmount">
          <el-input v-model="dataForm.remainingAmount" placeholder="剩余量"></el-input>
      </el-form-item>
          <el-form-item label="备注" prop="remark">
          <el-input v-model="dataForm.remark" placeholder="备注"></el-input>
      </el-form-item>
      </el-form>
    <template slot="footer">
      <el-button @click="visible = false">{{ $t('cancel') }}</el-button>
      <el-button type="primary" @click="dataFormSubmitHandle()">{{ $t('confirm') }}</el-button>
    </template>
  </el-dialog>
</template>

<script>
import debounce from 'lodash/debounce'
export default {
  data () {
    return {
      visible: false,
      dataForm: {
        id: '',
        manufacturingId: '',
        manufacturingInstructions:'',
        totalProduction: '',
        remainingAmount: '',
        remark: '',
        disabled: '',
        creator: '',
        createDate: '',
        updater: '',
        updateDate: ''
      }
    }
  },
  computed: {
    dataRule () {
      return {
        manufacturingInstructions: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        totalProduction: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        remainingAmount: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ]
      }
    }
  },
  methods: {
    getLikemanufacturing(manufacturingInstructions,cb){
      this.$http.get(`fabricate/manufacturingOrder/selectLike?manufacturingInstructions=`+manufacturingInstructions).then(({data: res}) => {
        console.log("res",res)
        if (res.code !== 0) {
          return this.$message.error(res.msg)
        }
        const list = res.data.map((obj) => {
          console.log("obj",obj)
          return {
            manufacturingInstructions: obj.manufacturingInstructions,
            value: obj.manufacturingInstructions + '(' + obj.manufacturingInstructions + ')',
            manufacturingId : obj.id,
          }
        })
        this.timeout = setTimeout(() => {
          cb(list);
        }, 3000 * Math.random());
      }).catch(() => {
      })
    },
    //  输出选择日志
    handleSelectManufacturing(item) {
      console.log("item",item)
      this.dataForm.manufacturingInstructions = item.manufacturingInstructions
      this.dataForm.manufacturingId = item.manufacturingId
    },
    init () {
      this.visible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].resetFields()
        if (this.dataForm.id) {
          this.getInfo()
        }
      })
    },
    // 获取信息
    getInfo () {
      this.$http.get(`/produce/productioncontrol/${this.dataForm.id}`).then(({ data: res }) => {
        if (res.code !== 0) {
          return this.$message.error(res.msg)
        }
        this.dataForm = {
          ...this.dataForm,
          ...res.data
        }
      }).catch(() => {})
    },
    // 表单提交
    dataFormSubmitHandle: debounce(function () {
      console.log(this.dataForm)
      this.$refs['dataForm'].validate((valid) => {
        if (!valid) {
          return false
        }
        this.$http[!this.dataForm.id ? 'post' : 'put']('/produce/productioncontrol/', this.dataForm).then(({ data: res }) => {
          if (res.code !== 0) {
            return this.$message.error(res.msg)
          }
          this.$message({
            message: this.$t('prompt.success'),
            type: 'success',
            duration: 500,
            onClose: () => {
              this.visible = false
              this.$emit('refreshDataList')
            }
          })
        }).catch(() => {})
      })
    }, 1000, { 'leading': true, 'trailing': false })
  }
}
</script>
