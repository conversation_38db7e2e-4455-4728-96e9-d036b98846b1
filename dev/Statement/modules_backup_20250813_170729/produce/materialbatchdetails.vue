<template>
  <el-card shadow="never" class="aui-card--fill">
    <div class="mod-produce__materialbatchdetails}">
      <el-form :inline="true" :model="dataForm" @keyup.enter.native="getDataList()">
        <el-form-item>
          <el-input v-model="dataForm.batchNumber" placeholder="批号" clearable @clear="getDataList">
            <el-button slot="append" icon="el-icon-search" @click="getDataList()"></el-button>
          </el-input>
        </el-form-item>
        <el-form-item>
          <el-input v-model="dataForm.designation" placeholder="部品番号" clearable></el-input>
        </el-form-item>
        <el-form-item>
          <el-input v-model="dataForm.manufacturingInstructions" placeholder="生产指令" clearable></el-input>
        </el-form-item>
        <el-form-item>
          <el-button @click="getDataList()">{{ $t('query') }}</el-button>
        </el-form-item>
        <el-form-item>
          <el-button type="info" @click="exportHandle()">{{ $t('export') }}</el-button>
        </el-form-item>
        <el-form-item>
          <el-button v-if="$hasPermission('batch:batchbasic:export')" type="info" @click="exportTemplateHandle()">
            {{ $t('exportTemplate') }}
          </el-button>
        </el-form-item>
        <el-form-item>
          <el-tooltip content="下载模板填入信息后导入" placement="top">
            <el-upload
                v-if="$hasPermission('produce:materialbatchdetails:save')"
                class="upload-demo"
                :action="mixinViewModuleOptions.addBatchUrl"
                :headers="headers"
                :multiple="false"
                :show-file-list="false"
                :file-list="fileList"
                :on-success="resultHandle"
                :on-change="handleChange"
                accept="'.xlsx','.xls'">
              <el-button type="success">{{ $t('addBatch') }}</el-button>
            </el-upload>
          </el-tooltip>
          <el-button type="text" v-show="unsuccessfulNameList" @click="dialogVisible = true">导入失败列表</el-button>
        </el-form-item>
        <el-form-item>
          <el-button v-if="$hasPermission('produce:materialbatchdetails:save')" type="primary" @click="addOrUpdateHandle()">{{ $t('add') }}</el-button>
        </el-form-item>
        <el-form-item>
          <el-button v-if="$hasPermission('produce:materialbatchdetails:delete')" type="danger" @click="deleteHandle()">{{ $t('deleteBatch') }}</el-button>
        </el-form-item>
      </el-form>
      <el-table v-loading="dataListLoading" :data="dataList" border @selection-change="dataListSelectionChangeHandle" style="width: 100%;">
        <el-table-column type="selection" header-align="center" align="center" width="50"></el-table-column>
        <el-table-column prop="batchNumber" label="批号" header-align="center" align="center"></el-table-column>
        <el-table-column prop="designation" label="部品番号" header-align="center" align="center"></el-table-column>
        <el-table-column prop="manufacturingInstructions" label="生产指令" header-align="center" align="center"></el-table-column>
        <el-table-column prop="instructionDate" label="指令日期(含时分)" header-align="center" align="center"></el-table-column>
       <!-- <el-table-column prop="materialConfirmation" label="材料确认者" header-align="center" align="center"></el-table-column>
        <el-table-column prop="banburyingOperator" label="密炼员" header-align="center" align="center"></el-table-column>
        <el-table-column prop="mixingConditionTime" label="密炼条件时间(含时分)" header-align="center" align="center"></el-table-column>
        <el-table-column prop="mixingTemperature" label="密炼条件温度" header-align="center" align="center"></el-table-column>
        <el-table-column prop="openingOperator" label="开炼员" header-align="center" align="center"></el-table-column>
        <el-table-column prop="openingTime" label="开炼条件时间(分)" header-align="center" align="center"></el-table-column>
        <el-table-column prop="openingTemperature" label="开炼条件温度" header-align="center" align="center"></el-table-column>
        <el-table-column prop="wideSize" label="出片尺寸宽(mm)" header-align="center" align="center"></el-table-column>
        <el-table-column prop="dimensionsThick" label="出片尺寸厚(mm)" header-align="center" align="center"></el-table-column>
        <el-table-column prop="productionWeight" label="生产重量(kg)" header-align="center" align="center"></el-table-column>
        <el-table-column prop="dateOfManufacture" label="制造日期(含时分)" header-align="center" align="center"></el-table-column>
        <el-table-column prop="validityPeriod" label="有效期限(天)" header-align="center" align="center"></el-table-column>
        <el-table-column prop="weighing" label="称料(Y/N)" header-align="center" align="center"></el-table-column>
        <el-table-column prop="cutter" label="切料员" header-align="center" align="center"></el-table-column>
        <el-table-column prop="barWeightOne" label="条重1(g)" header-align="center" align="center"></el-table-column>
        <el-table-column prop="barWeightTwo" label="条重2(g)" header-align="center" align="center"></el-table-column>
        <el-table-column prop="numberOfLines" label="条数" header-align="center" align="center"></el-table-column>
        <el-table-column prop="letGo" label="是否放行(T/F)" header-align="center" align="center"></el-table-column>
        <el-table-column prop="hardnessStandard" label="硬度标准(XX~XX)" header-align="center" align="center"></el-table-column>
        <el-table-column prop="measuredHardness" label="实测硬度" header-align="center" align="center"></el-table-column>
        <el-table-column prop="judgementResult" label="判定结果" header-align="center" align="center"></el-table-column>-->
        <el-table-column prop="exceptionDescription" label="异常说明" header-align="center" align="center"></el-table-column>
        <el-table-column prop="inspectors" label="检验员" header-align="center" align="center"></el-table-column>
        <el-table-column prop="remark" label="备注" header-align="center" align="center"></el-table-column>
        <el-table-column :label="$t('handle')" fixed="right" header-align="center" align="center" width="150">
          <template slot-scope="scope">
            <el-dropdown>
              <el-button type="primary">
                功能<i class="el-icon-arrow-down el-icon--right"></i>
              </el-button>
              <el-dropdown-menu slot="dropdown">
                <el-dropdown-item>
                  <el-button v-if="$hasPermission('produce:materialbatchdetails:info')" type="text" size="small" @click="selectHandle(scope.row.id)">{{ $t('detail') }}</el-button>
                </el-dropdown-item>
                <el-dropdown-item>
                  <el-button v-if="$hasPermission('produce:materialbatchdetails:update')" type="text" size="small" @click="addOrUpdateHandle(scope.row.id)">{{ $t('update') }}</el-button>
                </el-dropdown-item>
                <el-dropdown-item>
                  <el-button v-if="$hasPermission('produce:materialbatchdetails:delete')" type="text" size="small" @click="deleteHandle(scope.row.id)">{{ $t('delete') }}</el-button>
                </el-dropdown-item>
              </el-dropdown-menu>
            </el-dropdown>

          </template>
        </el-table-column>
      </el-table>
      <el-pagination
        :current-page="page"
        :page-sizes="[10, 20, 50, 100]"
        :page-size="limit"
        :total="total"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="pageSizeChangeHandle"
        @current-change="pageCurrentChangeHandle">
      </el-pagination>
      <!-- 弹窗, 新增 / 修改 -->
      <add-or-update v-if="addOrUpdateVisible" ref="addOrUpdate" @refreshDataList="getDataList"></add-or-update>

      <!--弹窗形式显示-->
      <el-dialog title="数据表格" :visible.sync="dialogVisible" @closed="closeDialog">
        <el-form :model="tableData">
          <el-form-item label="材料确认" prop="materialConfirmation">
            <el-input v-model="tableData.materialConfirmation" placeholder="材料确认者" disabled></el-input>
          </el-form-item>
          <el-form-item label="密炼员" prop="banburyingOperator">
            <el-input v-model="tableData.banburyingOperator" placeholder="密炼员" disabled></el-input>
          </el-form-item>
          <el-form-item label="密炼条件时间(含时分)" prop="mixingConditionTime">
            <el-input v-model="tableData.mixingConditionTime" placeholder="密炼条件时间(含时分)" disabled></el-input>
          </el-form-item>
          <el-form-item label="密炼条件温度" prop="mixingTemperature">
            <el-input v-model="tableData.mixingTemperature" placeholder="密炼条件温度" disabled></el-input>
          </el-form-item>
          <el-form-item label="开炼员" prop="openingOperator">
            <el-input v-model="tableData.openingOperator" placeholder="开炼员" disabled></el-input>
          </el-form-item>
          <el-form-item label="开炼条件时间(分)" prop="openingTime">
            <el-input v-model="tableData.openingTime" placeholder="开炼条件时间(分)" disabled></el-input>
          </el-form-item>
          <el-form-item label="开炼条件温度" prop="openingTemperature">
            <el-input v-model="tableData.openingTemperature" placeholder="开炼条件温度" disabled></el-input>
          </el-form-item>
          <el-form-item label="出片尺寸宽(mm)" prop="出片尺寸宽(mm)">
            <el-input v-model="tableData.wideSize" placeholder="出片尺寸宽(mm)" disabled></el-input>
          </el-form-item>
          <el-form-item label="出片尺寸厚(mm)" prop="dimensionsThick">
            <el-input v-model="tableData.dimensionsThick" placeholder="出片尺寸厚(mm)" disabled></el-input>
          </el-form-item>
          <el-form-item label="生产重量(kg)" prop="productionWeight">
            <el-input v-model="tableData.productionWeight" placeholder="生产重量(kg)" disabled></el-input>
          </el-form-item>
          <el-form-item label="制造日期(含时分)" prop="dateOfManufacture">
            <el-input v-model="tableData.dateOfManufacture" placeholder="制造日期(含时分)" disabled></el-input>
          </el-form-item>
          <el-form-item label="有效期限(天)" prop="validityPeriod">
            <el-input v-model="tableData.validityPeriod" placeholder="有效期限(天)" disabled></el-input>
          </el-form-item>
          <el-form-item label="称料(Y/N)" prop="weighing">
            <el-input v-model="tableData.weighing" placeholder="称料(Y/N)" disabled></el-input>
          </el-form-item>
          <el-form-item label="切料员" prop="cutter">
            <el-input v-model="tableData.cutter" placeholder="切料员" disabled></el-input>
          </el-form-item>
          <el-form-item label="条重1(g)" prop="barWeightOne">
            <el-input v-model="tableData.barWeightOne" placeholder="条重1(g)" disabled></el-input>
          </el-form-item>
          <el-form-item label="条重2(g)" prop="barWeightTwo">
            <el-input v-model="tableData.barWeightTwo" placeholder="条重2(g)" disabled></el-input>
          </el-form-item>
          <el-form-item label="条数" prop="numberOfLines">
            <el-input v-model="tableData.numberOfLines" placeholder="条数" disabled></el-input>
          </el-form-item>
          <el-form-item label="是否放行(T/F)" prop="letGo">
            <el-input v-model="tableData.letGo" placeholder="是否放行(T/F)" disabled></el-input>
          </el-form-item>
          <el-form-item label="硬度标准(XX~XX)" prop="hardnessStandard">
            <el-input v-model="tableData.hardnessStandard" placeholder="硬度标准(XX~XX)" disabled></el-input>
          </el-form-item>
          <el-form-item label="实测硬度" prop="measuredHardness">
            <el-input v-model="tableData.measuredHardness" placeholder="实测硬度" disabled></el-input>
          </el-form-item>
          <el-form-item label="判定结果" prop="judgementResult">
            <el-input v-model="tableData.judgementResult" placeholder="判定结果" disabled></el-input>
          </el-form-item>
          <el-form-item label="异常说明" prop="exceptionDescription">
            <el-input v-model="tableData.exceptionDescription" placeholder="异常说明" disabled></el-input>
          </el-form-item>
          <el-form-item label="检验员" prop="inspectors">
            <el-input v-model="tableData.inspectors" placeholder="检验员" disabled></el-input>
          </el-form-item>
          <el-form-item label="备注" prop="remark">
            <el-input v-model="tableData.remark" placeholder="备注" disabled></el-input>
          </el-form-item>
        </el-form>
      </el-dialog>
    </div>
  </el-card>
</template>

<script>
import mixinViewModule from '@/mixins/view-module'
import AddOrUpdate from './materialbatchdetails-add-or-update'
import Cookies from "js-cookie";
export default {
  mixins: [mixinViewModule],
  data () {
    return {
      message:'',
      dialogVisible: false, // 对话框是否显示
      tableData:'',
      headers: {'token': Cookies.get('token')},
      fileList: [{name: '', url: ''}],
      unsuccessfulNameList: '',//  导入失败名称列表
      mixinViewModuleOptions: {
        getDataListURL: '/produce/materialbatchdetails/page',
        getDataListIsPage: true,
        exportURL: '/produce/materialbatchdetails/export',
        deleteURL: '/produce/materialbatchdetails',
        deleteIsBatch: true,
        exportTemplateURL: '/produce/materialbatchdetails/export/template',
        addBatchUrl: window.SITE_CONFIG['apiURL'] + '/produce/materialbatchdetails/batchSave',
      },
      dataForm: {
        id: ''
      }
    }
  },
  components: {
    AddOrUpdate
  },
  methods:{
    openDialog() { // 打开对话框
      this.dialogVisible = true;
      this.selectHandle()
    },
    closeDialog() { // 关闭对话框
      this.dialogVisible = false;
    },
    selectHandle(id){
      this.dialogVisible = true;
      this.dataDatchListLoading=true
      this.$http.get(`/produce/materialbatchdetails/${id}`).then(({data: res}) => {
        console.log("res:",res)
        if (res.code !== 0) {
          this.tableData = ''
          return this.$message.error(res.msg)
        }
        this.tableData = res.data
        this.tableData.isWeighing = this.sfktHouse(this.tableData)
        this.dataForm = {
          ...this.dataForm,
        }
      }).catch(() => {
        this.dataListLoading = false
      })
    },
    handleChange(file, fileList) {
      this.fileList = fileList.slice(-3);

    },
    resultHandle(res) {
      if (res.code !== 0) {
        return this.$message.error(res.msg)
      }
      this.$message({
        message: this.$t('prompt.success'),
        type: 'success',
        duration: 500,
        onClose: () => {
          if (res.data) {
            this.unsuccessfulNameList = res.data
          }
          this.message.close()
          this.visible = false
          this.query()
        }
      })
    },
  }
}
</script>
