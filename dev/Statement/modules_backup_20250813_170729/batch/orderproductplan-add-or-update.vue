<template>
  <el-dialog :visible.sync="visible" :title="!dataForm.id ? $t('add') : $t('update')" :close-on-click-modal="false" :close-on-press-escape="false" append-to-body>
    <el-form :model="dataForm" :rules="dataRule" ref="dataForm" @keyup.enter.native="dataFormSubmitHandle()" :label-width="$i18n.locale === 'en-US' ? '120px' : '80px'">
      <div class="container">
                                                <el-form-item label="订单号" prop="orderNumber">
              <el-input v-model="dataForm.orderNumber" placeholder="订单号"></el-input>
            </el-form-item>
                                        <el-form-item label="品名" prop="designation">
              <el-input v-model="dataForm.designation" placeholder="品名"></el-input>
            </el-form-item>
                                        <el-form-item label="模具id" prop="moldId">
              <el-input v-model="dataForm.moldId" placeholder="模具id"></el-input>
            </el-form-item>
                                        <el-form-item label="模具名" prop="moldName">
              <el-input v-model="dataForm.moldName" placeholder="模具名"></el-input>
            </el-form-item>
                                        <el-form-item label="配方代码" prop="formulationCode">
              <el-input v-model="dataForm.formulationCode" placeholder="配方代码"></el-input>
            </el-form-item>
                                        <el-form-item label="订单总数" prop="orderQuantity">
              <el-input v-model="dataForm.orderQuantity" placeholder="订单总数"></el-input>
            </el-form-item>
                                        <el-form-item label="订单交期" prop="deliveryTime">
              <el-input v-model="dataForm.deliveryTime" placeholder="订单交期"></el-input>
            </el-form-item>
                                        <el-form-item label="状态" prop="planStatus">
              <el-input v-model="dataForm.planStatus" placeholder="状态"></el-input>
            </el-form-item>
                                        <el-form-item label="计划排产数" prop="planScheduleQuantity">
              <el-input v-model="dataForm.planScheduleQuantity" placeholder="计划排产数"></el-input>
            </el-form-item>
                                        <el-form-item label="实际生产数" prop="productQuantity">
              <el-input v-model="dataForm.productQuantity" placeholder="实际生产数"></el-input>
            </el-form-item>
                                        <el-form-item label="生产订单欠数" prop="productLackQuantity">
              <el-input v-model="dataForm.productLackQuantity" placeholder="生产订单欠数"></el-input>
            </el-form-item>
                                        <el-form-item label="达成率" prop="finishingRate">
              <el-input v-model="dataForm.finishingRate" placeholder="达成率"></el-input>
            </el-form-item>
                                                                                                                  </div>
    </el-form>
    <template slot="footer">
      <el-button @click="visible = false">{{ $t('cancel') }}</el-button>
      <el-button type="primary" @click="dataFormSubmitHandle()">{{ $t('confirm') }}</el-button>
    </template>
  </el-dialog>
</template>

<script>
import debounce from 'lodash/debounce'
import mixinViewModule from '@/mixins/view-module'
export default {
  mixins:[mixinViewModule],
  data () {
    return {
      visible: false,
      //创建该页面时，不调用数据查询接口
      mixinViewModuleOptions: {
        createdIsNeed: false,
      },
      dataForm: {
        id: '',
        orderNumber: '',
        designation: '',
        moldId: '',
        moldName: '',
        formulationCode: '',
        orderQuantity: '',
        deliveryTime: '',
        planStatus: '',
        planScheduleQuantity: '',
        productQuantity: '',
        productLackQuantity: '',
        finishingRate: '',
        disabled: '',
        creator: '',
        createDate: '',
        updater: '',
        updateDate: ''
      }
    }
  },
  computed: {
    dataRule () {
      return {
          id: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          orderNumber: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          designation: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          moldId: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          moldName: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          formulationCode: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          orderQuantity: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          deliveryTime: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          planStatus: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          planScheduleQuantity: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          productQuantity: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          productLackQuantity: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          finishingRate: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
                }
    }
  },
  methods: {
    init () {
      this.visible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].resetFields()
        if (this.dataForm.id) {
          this.getInfo()
        }
      })
    },
    // 获取信息
    getInfo () {
      this.$http.get(`/batch/orderproductplan/${this.dataForm.id}`).then(({ data: res }) => {
        if (res.code !== 0) {
          return this.$message.error(res.msg)
        }
        this.dataForm = {
          ...this.dataForm,
          ...res.data
        }
      }).catch(() => {})
    },
    // 表单提交
    dataFormSubmitHandle: debounce(function () {
      this.$refs['dataForm'].validate((valid) => {
        if (!valid) {
          return false
        }
        let message = '';
        if(this.dataForm.id){
          message = '此操作将修改该数据'
        }else {
          message = '此操作将新增一条数据'
        }
        this.$confirm(message+', 是否继续?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
          center: true
        }).then(() => {
          this.$http[!this.dataForm.id ? 'post' : 'put']('/batch/orderproductplan/', this.dataForm).then(({ data: res }) => {
            if (res.code !== 0) {
            return this.$message.error(res.msg)
            }
            this.$message({
          message: this.$t('prompt.success'),
            type: 'success',
                    duration: 500,
                    onClose: () => {
              this.visible = false
              this.$emit('refreshDataList')
            }
          })
          }).catch(() => {})
        }).catch(() => {
          this.$message({
            type: 'info',
            message: '已取消'
          });
        });
      })
    }, 1000, { 'leading': true, 'trailing': false })
  }
}
</script>
