<template>
  <el-dialog  width="60vw" :visible.sync="visible" :title="!dataForm.id ? '批号生产' : '修改批号'" :close-on-click-modal="false" :close-on-press-escape="false" append-to-body>
    <el-form :style="{backgroundColor:backgroundColor}" :model="dataForm" :rules="dataRule" ref="dataForm" @keyup.enter.native="dataFormSubmitHandle()" :label-width="$i18n.locale === 'en-US' ? '120px' : '80px'">
      <el-divider content-position="center">
        <span style="font-size: 20px; font-weight: bold;">B炼胶成形批号建立</span>
      </el-divider>
      <div class="container">
        <div class="item">
          <el-form-item label="生产指令" prop="manufacturingInstructions">
            <el-autocomplete
                class="inline-input"
                v-model="dataForm.manufacturingInstructions"
                :fetch-suggestions="getLikemanufacturing"
                placement="bottom"
                placeholder="请输入生产指令"
                :trigger-on-focus="false"
                popper-class="my-popper"
                @select="handleSelectManufacturing">
            </el-autocomplete>
          </el-form-item>
        </div>
        <div class="item">
          <el-form-item label="品号" prop="designation">
            <el-autocomplete
                class="inline-input"
                v-model="dataForm.designation"
                :fetch-suggestions="getPartList"
                placement="bottom"
                placeholder="请输入品号"
                :trigger-on-focus="false"
                popper-class="my-popper"
                @select="partSelect">
            </el-autocomplete>
          </el-form-item>
        </div>
        <div class="item">
          <el-form-item label="客户代码" prop="customerCode">
            <el-autocomplete
                class="inline-input"
                v-model="dataForm.customerCode"
                :fetch-suggestions="getCustomerCode"
                placement="bottom"
                placeholder="请输入客户代码"
                :trigger-on-focus="false"
                popper-class="my-popper"
                @select="customerSelect">
            </el-autocomplete>
          </el-form-item>
        </div>
      </div>
      <div class="container">
        <div class="item">
          <el-form-item label="配方代码" prop="formulationCode" v-if="oldLotNumber">
            <el-autocomplete
                class="inline-input"
                v-model="dataForm.formulationCode"
                :fetch-suggestions="getFormulationCode"
                placement="bottom"
                placeholder="请输入配方代码"
                :trigger-on-focus="false"
                @select="formulationSelect"
            ></el-autocomplete>
          </el-form-item>
        </div>
        <div class="item">
          <el-form-item label="生产日期" prop="productionDate" v-if="oldLotNumber">
            <el-date-picker
                class="datePicker"
                v-model="dataForm.productionDate"
                type="date"
                value-format="yyyy-MM-dd"
                placeholder="选择日期">
            </el-date-picker>
          </el-form-item>
        </div>
        <div class="item">
          <el-form-item label="生产件别" prop="productionPart">
            <ren-select v-model="dataForm.productionPart" placeholder="生产件别" dict-type="production_part"></ren-select>
          </el-form-item>
        </div>
      </div>
      <div class="container">
        <div class="item">
          <el-form-item label="生产地点" prop="produceArea">
            <ren-select v-model="dataForm.produceArea" placeholder="生产地点" dict-type="produce_area"></ren-select>
          </el-form-item>
        </div>
      </div>
      <el-button v-if="!dataForm.id" type="success" @click="openMultipleBatch">多批号填写</el-button>
      <div class="container" v-if="multipleBatch">
        <div>
          <el-form-item label="批号类型" prop="category">
            <ren-select v-model="dataForm.category" dict-type="batch_category"></ren-select>
          </el-form-item>
        </div>
        <div>
          <el-form-item label="胶料类别" prop="sizeCategory">
            <div>
              <ren-radio-tick v-model="dataForm.sizeCategory" dict-type="size_category"></ren-radio-tick>
            </div>
          </el-form-item>
        </div>
        <div>
          <el-form-item label="批号" prop="batchNumber">
            <el-input v-model="dataForm.batchNumber" placeholder="批号"></el-input>
          </el-form-item>
        </div>
      </div>
      <multiple-batch-numbers-component ref="multipleBatchComponent" @return="multipleBatchNumbers" v-if="!multipleBatch"></multiple-batch-numbers-component>
      <div class="container">
<!--        <div class="item">
          <el-form-item label="交期时间" prop="deliveryTime">
            <el-date-picker
                class="datePicker"
                v-model="dataForm.deliveryTime"
                type="datetime"
                value-format="yyyy-MM-dd HH:mm:ss"
                placeholder="交期时间">
            </el-date-picker>
          </el-form-item>
        </div>-->
        <div class="item">
          <el-form-item label="材料确认" prop="materialConfirmation">
            <employee-component v-model="dataForm.materialConfirmation" placeholder="工号"></employee-component>
          </el-form-item>
        </div>
        <!--        <div class="item">
                  <el-form-item label="品检判定" prop="qualityInspection">
                    <el-select v-model="dataForm.qualityInspection" placeholder="品检判定">
                      <el-option
                          v-for="item in qualityInspection"
                          :key="item.value"
                          :label="item.label"
                          :value="item.value">
                      </el-option>
                    </el-select>
                  </el-form-item>
                </div>-->
<!--        <div class="item">
          <el-form-item label="出货日期" prop="shippingDate">
            <el-date-picker
                v-model="dataForm.shippingDate"
                type="datetime"
                value-format="yyyy-MM-dd HH:mm:ss"
                placeholder="出货日期">
            </el-date-picker>
          </el-form-item>
        </div>
        <div class="item">
          <el-form-item label="周期结束" prop="endOfCycle">
            <ren-select v-model="dataForm.endOfCycle" dict-type="end_of_cycle"></ren-select>
          </el-form-item>
        </div>-->
        <div class="item">
          <el-form-item label="重量" prop="weight">
            <el-input v-model="dataForm.weight" placeholder="重量"></el-input>
          </el-form-item>
        </div>
        <div class="item">
          <el-form-item label="建立人" prop="founder">
            <employee-component v-model="dataForm.founder" placeholder="工号"></employee-component>
          </el-form-item>
        </div>
        <div class="item">
          <el-form-item label="打回次数" prop="numberOfCallbacks">
            <el-input v-model="dataForm.numberOfCallbacks" placeholder="打回次数"></el-input>
          </el-form-item>
        </div>
        <div class="item">
          <el-form-item label="处理状态" prop="processingStatus">
            <el-input v-model="dataForm.processingStatus" placeholder="处理状态"></el-input>
          </el-form-item>
        </div>
      </div>
      <el-divider content-position="center">
        <span style="font-size: 20px; font-weight: bold;">批号材料生产详细数据</span>
      </el-divider>
      <div class="container">
        <div class="item">
          <el-form-item label="密炼员" prop="banburyingOperator">
            <employee-component v-model="dataForm.banburyingOperator" placeholder="工号"></employee-component>
          </el-form-item>
        </div>
        <div class="item">
          <el-form-item prop="mixingConditionTime" label="密炼时间">
            <el-input v-model="dataForm.mixingConditionTime" placeholder="密炼时间"></el-input>
          </el-form-item>
        </div>
        <div class="item">
          <el-form-item label="密炼温度" prop="mixingTemperature">
            <el-input v-model="dataForm.mixingTemperature" placeholder="密炼温度"></el-input>
          </el-form-item>
        </div>
        <div class="item">
          <el-form-item label="开炼员" prop="openingOperator">
            <employee-component v-model="dataForm.openingOperator" placeholder="工号"></employee-component>
          </el-form-item>
        </div>
        <div class="item">
          <el-form-item prop="openingTime" label="开炼时间">
            <el-input v-model="dataForm.openingTime" placeholder="开炼时间"></el-input>
          </el-form-item>
        </div>
        <div class="item">
          <el-form-item label="开炼温度" prop="openingTemperature">
            <el-input v-model="dataForm.openingTemperature" placeholder="开炼温度"></el-input>
          </el-form-item>
        </div>
        <div class="item">
          <el-form-item label="出片尺寸宽(mm)" prop="wideSize">
            <el-input v-model="dataForm.wideSize" placeholder="出片尺寸宽(mm)"></el-input>
          </el-form-item>
        </div>
        <div class="item">
          <el-form-item label="出片尺寸厚(mm)" prop="dimensionsThick">
            <el-input v-model="dataForm.dimensionsThick" placeholder="出片尺寸厚(mm)"></el-input>
          </el-form-item>
        </div>
<!--        <div class="item">
          <el-form-item label="生产重量(kg)" prop="productionWeight">
            <el-input v-model="dataForm.productionWeight" placeholder="生产重量(kg)"></el-input>
          </el-form-item>
        </div>-->
<!--        <div class="item">
          <el-form-item prop="dateOfManufacture" label="制造日期(含时分)">
            <el-date-picker
                v-model="dataForm.dateOfManufacture"
                type="datetime"
                value-format="yyyy-MM-dd HH:mm:ss"
                placeholder="选择日期">
            </el-date-picker>
          </el-form-item>
        </div>-->
        <div class="item">
          <el-form-item label="有效期限(天)" prop="validityPeriod">
            <el-input v-model="dataForm.validityPeriod" placeholder="有效期限(天)"></el-input>
          </el-form-item>
        </div>
        <div class="item">
          <el-form-item label="称料" prop="weighing">
            <ren-radio-group v-model="dataForm.weighing" dict-type="weighing"></ren-radio-group>
          </el-form-item>
        </div>
        <div class="item">
          <el-form-item label="切料员" prop="cutter">
            <employee-component v-model="dataForm.cutter" placeholder="工号"></employee-component>
          </el-form-item>
        </div>
        <div class="box">
          <el-form-item label="条重" prop="barWeightOne">
            <div style="display: flex;">
              <el-input v-model="dataForm.barWeightOne"  style="width: 100px;"></el-input>g
              <el-input v-model="dataForm.barWeightTwo"  style="width: 100px;"></el-input>g
              <el-input v-model="dataForm.numberOfLines"  style="width: 100px;"></el-input>条
            </div>
          </el-form-item>
        </div>

        <div class="box">
          <el-form-item label="补料" prop="feedingOne">
            <div style="display: flex;">
              <el-input v-model="dataForm.feedingOne"  style="width: 100px;"></el-input>g
              <el-input v-model="dataForm.feedingTwo"  style="width: 100px;"></el-input>g
              <el-input v-model="dataForm.feedingQuantity"  style="width: 100px;"></el-input>条
            </div>
          </el-form-item>
        </div>
        <div class="item">
          <el-form-item label="回头料重" prop="feedback">
            <el-input v-model="dataForm.feedback" placeholder="回头料重"></el-input>
          </el-form-item>
        </div>
<!--        <div class="item">
          <el-form-item label="是否放行(T/F)" prop="letGo">
            <el-select v-model="dataForm.letGo" placeholder="是否放行">
              <el-option
                  v-for="item in letGo"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value">
              </el-option>
            </el-select>
          </el-form-item>
        </div>
        <div class="item">
          <el-form-item label="硬度标准(XX~XX)" prop="hardnessStandard">
            <el-input v-model="dataForm.hardnessStandard" placeholder="硬度标准(XX~XX)"></el-input>
          </el-form-item>
        </div>
        <div class="item">
          <el-form-item label="实测硬度" prop="measuredHardness">
            <el-input v-model="dataForm.measuredHardness" placeholder="实测硬度"></el-input>
          </el-form-item>
        </div>
        <div class="item">
          <el-form-item label="判定结果" prop="judgementResult">
            <el-input v-model="dataForm.judgementResult" placeholder="判定结果"></el-input>
          </el-form-item>
        </div>
        <div class="item">
          <el-form-item label="异常说明" prop="exceptionDescription">
            <el-input v-model="dataForm.exceptionDescription" placeholder="异常说明"></el-input>
          </el-form-item>
        </div>
        <div class="item">
          <el-form-item label="检验员" prop="inspectors">
            <el-input v-model="dataForm.inspectors" placeholder="检验员"></el-input>
          </el-form-item>
        </div>-->
        <div class="item">
          <el-form-item label="备注" prop="remark">
            <el-input v-model="dataForm.remark" placeholder="备注"></el-input>
          </el-form-item>
        </div>
      </div>
      </el-form>
    <template slot="footer" v-if="displayButton">
      <el-button @click="visible = false">{{ $t('cancel') }}</el-button>
      <el-button type="primary" @click="confirmToAdd()">{{ $t('confirm') }}</el-button>
      <el-button v-if="!dataForm.id" type="primary" @click="continueBatchNumber()">再新增</el-button>
    </template>
  </el-dialog>
</template>

<script>
import debounce from 'lodash/debounce'
import mixinViewModule from '@/mixins/view-module'
import MultipleBatchNumbersComponent from "./multiple-batch-numbers-component.vue";

// 节流函数
const delay = (function() {
  let timer = 0;
  return function(callback, ms) {
    clearTimeout(timer);
    timer = setTimeout(callback, ms);
  };
})();
export default {
  mixins:[mixinViewModule],
  data () {
    return {
      control:true,
      oldLotNumber:true,
      visible: false,
      multipleBatch: true,
      copyBatch:'',
      backgroundColor:'',
      dataForm: {
        id: '',
        multipleBatchNumbers:[{}],
        manufacturingOrderId: '',
        manufacturingInstructions: '',
        partId: '',
        designation:'',
        customerId: '',
        customerCode:'',
        formulaId: '',
        formulationCode:'',
        batchNumber: '',
        temporaryState: '',
        processingStatus: '',
        lockLotNumber: '',
        numberOfCallbacks: '',
        feeding: '',
        endOfCycle: 0,
        shippingDate: '',
        qualityInspection: '',
        founder: '',
        deliveryTime: '',
        productionDate:this.formatDates(new Date()),
        productionPart: 2,
        produceArea: 0,
        manufacturingOrderDate: '',
        subBatchCount: 0,
        sizeCategory: '',
        category: 0,

        weight: '',

        batchId: '',
        productionBatch:'',
        porductMappingId: '',
        manufacturingId: '',
        qualityInspectionId: '',
        instructionDate: '',
        materialConfirmation: '',
        banburyingOperator: '',
        mixingConditionTime: '',
        mixingTemperature: '',
        openingOperator: '',
        openingTime: '',
        openingTemperature: '',
        wideSize: '',
        dimensionsThick: '',
        productionWeight: '',
        dateOfManufacture: this.formatTimes(new Date()),
        validityPeriod: '',
        weighing: 0,
        cutter: '',
        barWeightOne: '',
        barWeightTwo: '',
        numberOfLines: '',
        feedingOne: '',
        feedingTwo: '',
        feedingQuantity: '',
        feedback: '',
        letGo: '',
        hardnessStandard: '',
        measuredHardness: '',
        judgementResult: '',
        exceptionDescription: '',
        inspectors: '',

        remark: '',
        disabled: '',
        creator: '',
        createDate: '',
        updater: '',
        updateDate: ''
      },
      displayButton:true,
      /*produceArea: [{
        value: 0,
        label: '清远厂'
      },
        {
          value: 1,
          label: '湖北厂'
        }],*/
      productionPart:[{
        value: 1,
        label: '库存生产'
      }, {
        value: 2,
        label: '订单生产'
      }, {
        value: 3,
        label: '急件生产'
      }, {
        value: 4,
        label: '打切'
      }, {
        value: 5,
        label: '补料生产'
      }, {
        value: 6,
        label: '暂停生产'
      }],
      endOfCycle:[{
        value: 0,
        label: 'T'
      }, {
        value: 1,
        label: 'F'
      }],
      lockLotNumber:[{
        value: 0,
        label: '锁定'
      }, {
        value: 1,
        label: '不锁定'
      }],
      letGo:[{
        value: 0,
        label: 'T'
      }, {
        value: 1,
        label: 'F'
      }],
      qualityInspection:[{
        value: 1,
        label: 'OK'
      }, {
        value: 2,
        label: 'NG'
      }, {
        value: 3,
        label: '特采'
      }],
      sizeCategory: [{
        value: 1,
        label: 'C'
      }, {
        value: 2,
        label: 'F'
      }, {
        value: 3,
        label: 'T'
      }, {
        value: 4,
        label: 'R'
      }],
    }
  },
  computed: {
    dataRule () {
      return {
        manufacturingInstructions: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        formulationCode: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        batchNumber: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        sizeCategory: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        category: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        produceArea: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
      }
    },
  },
  components:{
    MultipleBatchNumbersComponent
  },
  watch:{
    "dataForm.category"(){
      if (this.dataForm.category == 1){
        this.backgroundColor = '#dbf6b3';
      }else {
        this.backgroundColor = '';
      }
    }
  },
  methods: {
    multipleBatchNumbers(value){
      this.dataForm.multipleBatchNumbers = value
    },
    openMultipleBatch(){
      this.multipleBatch = false
      this.$nextTick(()=>{
        if(this.$refs.multipleBatchComponent.multipleBatchNumbers.length == 0){
          this.$refs.multipleBatchComponent.multipleBatchNumbers.push({sizeCategory:'',batchNumber:this.dataForm.batchNumber})
        }
        this.$refs.multipleBatchComponent.init();
      })
    },
    //确认新增
    confirmToAdd(){
      this.control = true
      this.dataFormSubmitHandle()
    },
    //再新增
    continueBatchNumber(){
      this.copyBatch = ''
      this.control = false
      this.dataFormSubmitHandle()
    },
    // 获取生产指令
    getLikemanufacturing(manufacturingInstructions,cb){
      this.$http.get(`fabricate/manufacturingOrder/selectLike?manufacturingInstructions=`+manufacturingInstructions).then(({data: res}) => {
        console.log("res",res)
        if (res.code !== 0) {
          return this.$message.error(res.msg)
        }
        const list = res.data.map((obj) => {
          console.log("obj",obj)
          return {
            manufacturingInstructions: obj.manufacturingInstructions,
            value: obj.manufacturingInstructions + '(' + obj.orderNumber + ')',
            manufacturingOrderId : obj.id,
            designation:obj.partDesignation,
            customerCode: obj.customerCode,
            partId: obj.partId,
          }
        })
        this.timeout = setTimeout(() => {
          cb(list);
        }, 300 * Math.random());
      }).catch(() => {
      })
    },
    //  输出选择日志
    handleSelectManufacturing(item) {
      console.log("item",item)
      this.dataForm.manufacturingInstructions = item.manufacturingInstructions
      this.dataForm.manufacturingOrderId = item.manufacturingOrderId
      this.dataForm.customerCode = item.customerCode
      this.dataForm.partId = item.partId
      this.dataForm.designation = item.designation
    },
    // 获取配方代码
    getFormulationCode(formulationCode,cb){
      this.$http.get(`produce/formulation/selectFormulationCodeInfo?formulationCode=`+formulationCode).then(({data: res}) => {
        if (res.code !== 0) {
          return this.$message.error(res.msg)
        }
        const list = res.data.map((obj) => {
          return {
            formulationCode: obj.formulationCode,
            value: obj.formulationCode + '(' + obj.formulationCode + ')',
            formulaId : obj.id,
          }
        })
        this.timeout = setTimeout(() => {
          cb(list);
        }, 300 * Math.random());
      }).catch(() => {
      })
    },
    //  输出选择日志
    formulationSelect(item) {
      this.dataForm.formulationCode = item.formulationCode
      this.dataForm.formulaId = item.formulaId
    },
    // 获取客户代码
    getCustomerCode(customerCode,cb){
      this.$http.get(`customer/customer/selectCode?code=`+customerCode).then(({data: res}) => {
        if (res.code !== 0) {
          return this.$message.error(res.msg)
        }
        const list = res.data.map((obj) => {
          return {
            customerCode: obj.code,
            value: obj.code + '(' + obj.code + ')',
            customerId : obj.id,
          }
        })
        this.timeout = setTimeout(() => {
          cb(list);
        }, 300 * Math.random());
      }).catch(() => {
      })
    },
    //  输出选择日志
    customerSelect(item) {
      this.dataForm.customerCode = item.customerCode
      this.dataForm.customerId = item.customerId
    },
    //  获取量产类型部品列表
    getPartList(queryString, cb) {
      this.$http.get(`/fabricate/part/getPartDesignation/` + queryString).then(({data: res}) => {
        if (res.code !== 0) {
          return this.$message.error(res.msg)
        }
        const list = res.data.map((obj) => {
          return {
            customerCode: obj.customerCode,
            value: obj.designation + '(' + obj.customerCode + ')',
            partId: obj.id,
            designation: obj.designation
          }
        })
        this.timeout = setTimeout(() => {
          cb(list);
        }, 300 * Math.random());
      }).catch(() => {
      })
    },
    //  输出选择日志
    partSelect(item) {
      this.dataForm.designation = item.designation
      this.dataForm.customerCode = item.customerCode
      this.dataForm.partId = item.partId
    },
    init () {
      this.visible = true
      this.multipleBatch = true
      this.$nextTick(() => {
        this.$refs['dataForm'].resetFields()
        if (this.dataForm.id) {
          this.getInfo()
        }
      })
    },
    // 获取信息
    getInfo () {
      this.$http.get(`/batch/batch/${this.dataForm.id}`).then(({ data: res }) => {
        if (res.code !== 0) {
          return this.$message.error(res.msg)
        }
        this.dataForm = {
          ...this.dataForm,
          ...res.data
        }
      }).catch(() => {})
    },
    // 表单提交
    dataFormSubmitHandle: debounce(function () {
      this.$refs['dataForm'].validate((valid) => {
        if (!valid) {
          return false
        }
        let message = '';
        if(this.dataForm.id){
          message = '此操作将修改该数据'
        }else {
          message = '此操作将新增一条数据'
        }
        this.$confirm(message+', 是否继续?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
          center: true
        }).then(() => {
          this.$http[!this.dataForm.id ? 'post' : 'put']('/batch/batch/', this.dataForm).then(({ data: res }) => {
            if (res.code !== 0) {
              return this.$message.error(res.msg)
            }
            if(!this.dataForm.id){
              this.$emit("callbackFlow",res.data)
            }
            this.$message({
              message: this.$t('prompt.success'),
              type: 'success',
              duration: 500,
              onClose: () => {
                if(this.control){
                  this.visible = false
                }else {
                  this.dataForm = {
                    ...this.dataForm,
                    ...res.data
                  }
                  this.dataForm.id = ''
                }
                console.log(this.control,'是否关闭页面')
                this.$emit('refreshDataList')
              }
            })
          }).catch(() => {})
        }).catch(() => {
          this.$message({
            type: 'info',
            message: '已取消'
          });
        });
      })
    }, 1000, { 'leading': true, 'trailing': false })
  }
}
</script>
<style scoped>
.container {
  display: flex;
  flex-wrap: wrap;
}
.item {
  width: calc(33.33% - 10px);
  margin: 5px;
}

@media screen and (max-width: 768px) {
  .container {
    display: block;  /* Change container display to block */
  }
  .item {
    width: 100%;  /* Change item width to 100% */
  }
}

@media (min-width: 768px) and (max-width: 1023px) {
  .container {
    display: block;  /* Change container display to block */
  }
  .item {
    width: calc(50% - 10px);
  }
}

@media (min-width: 1024px) {
  .item {
    width: calc(33.33% - 10px);
  }
}
.el-input, .el-select, .el-autocomplete{
  width: 100%;
}
</style>
