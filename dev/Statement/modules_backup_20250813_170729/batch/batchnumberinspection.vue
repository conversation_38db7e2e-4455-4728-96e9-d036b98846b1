<template>
  <el-card shadow="never" class="aui-card--fill">
    <div class="mod-batch__batchnumberinspection}">
      <el-form :inline="true" :model="dataForm" @keyup.enter.native="getDataList()">
        <el-form-item>
          <el-input v-model="dataForm.id" placeholder="id" clearable>
            <el-button slot="append" icon="el-icon-search" @click="getDataList()"></el-button>
          </el-input>
        </el-form-item>
<!--        <el-form-item>
          <el-button @click="getDataList()">{{ $t('query') }}</el-button>
        </el-form-item>-->
        <el-form-item>
          <el-button type="info" @click="exportHandle()">{{ $t('export') }}</el-button>
        </el-form-item>
        <el-form-item>
          <el-button v-if="$hasPermission('batch:batchnumberinspection:save')" type="primary" @click="addOrUpdateHandle()">{{ $t('add') }}</el-button>
        </el-form-item>
        <el-form-item>
          <el-button v-if="$hasPermission('batch:batchnumberinspection:delete')" type="danger" @click="deleteHandle()">{{ $t('deleteBatch') }}</el-button>
        </el-form-item>
      </el-form>
      <el-table v-loading="dataListLoading" :data="dataList" border @selection-change="dataListSelectionChangeHandle" style="width: 100%;">
        <el-table-column type="selection" header-align="center" align="center" width="50"></el-table-column>
        <el-table-column prop="partId" label="部品番号" header-align="center" align="center"></el-table-column>
        <el-table-column prop="manufacturingOrderId" label="制造指令" header-align="center" align="center"></el-table-column>
        <el-table-column prop="manufacturingOrderDate" label="制造指令日期" header-align="center" align="center"></el-table-column>
        <el-table-column prop="qualityInspectionId" label="品检id" header-align="center" align="center"></el-table-column>
        <el-table-column prop="letGo" label="是否放行 0:T 1:F" header-align="center" align="center"></el-table-column>
        <el-table-column prop="hardnessStandard" label="硬度标准" header-align="center" align="center"></el-table-column>
        <el-table-column prop="measuredHardness" label="实测硬度" header-align="center" align="center"></el-table-column>
        <el-table-column prop="inspectors" label="检验员" header-align="center" align="center"></el-table-column>
        <el-table-column prop="qualityInspection" label="品检判定 1:OK 2:NG 3:特采" header-align="center" align="center"></el-table-column>
        <el-table-column prop="remark" label="备注" header-align="center" align="center"></el-table-column>
        <el-table-column :label="$t('handle')" fixed="right" header-align="center" align="center" width="150">
          <template slot-scope="scope">
            <el-button v-if="$hasPermission('batch:batchnumberinspection:update')" type="text" size="small" @click="addOrUpdateHandle(scope.row.id)">{{ $t('update') }}</el-button>
            <el-button v-if="$hasPermission('batch:batchnumberinspection:delete')" type="text" size="small" @click="deleteHandle(scope.row.id)">{{ $t('delete') }}</el-button>
          </template>
        </el-table-column>
      </el-table>
      <el-pagination
        :current-page="page"
        :page-sizes="[10, 20, 50, 100]"
        :page-size="limit"
        :total="total"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="pageSizeChangeHandle"
        @current-change="pageCurrentChangeHandle">
      </el-pagination>
      <!-- 弹窗, 新增 / 修改 -->
      <add-or-update v-if="addOrUpdateVisible" ref="addOrUpdate" @refreshDataList="getDataList"></add-or-update>
    </div>
  </el-card>
</template>

<script>
import mixinViewModule from '@/mixins/view-module'
import AddOrUpdate from './batchnumberinspection-add-or-update'
export default {
  mixins: [mixinViewModule],
  data () {
    return {
      mixinViewModuleOptions: {
        getDataListURL: '/batch/batchnumberinspection/page',
        getDataListIsPage: true,
        exportURL: '/batch/batchnumberinspection/export',
        deleteURL: '/batch/batchnumberinspection',
        deleteIsBatch: true
      },
      dataForm: {
        id: ''
      }
    }
  },
  components: {
    AddOrUpdate
  }
}
</script>
