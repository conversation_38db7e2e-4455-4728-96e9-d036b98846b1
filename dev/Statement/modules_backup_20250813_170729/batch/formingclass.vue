<template>
  <div style="display: flex; width: 100%;">
    <el-card  style="flex: 1;" shadow="never">
      <div  style="text-align: center;">
      <h1>A班</h1>
      </div>
      <el-button  v-if="$hasPermission('batch:formingclass:save')" type="primary" @click="addOrUpdateHandle()">新增</el-button>
      <div style="margin-top: 2vh">
        <el-tag
          v-for="(item,index) in dataList.classA"
          :key="index"
          closable
          @close="closeHandle(item)"
          style="margin-left: 8px; margin-bottom: 8px; font-size: large"
        >
          {{item.employeeName}}
        </el-tag>
      </div>
    </el-card>
    <el-card  style="flex: 1;" shadow="never">
      <div style="text-align: center">
        <h1>B班</h1>
      </div>
      <el-button  v-if="$hasPermission('batch:formingclass:save')" type="primary" @click="addOrUpdateHandle()">修改</el-button>
      <div style="margin-top: 2vh">
        <el-tag
            v-for="(item,index) in dataList.classB"
            :key="index"
            type="info"
            closable
            @close="closeHandle(item)"
            style="margin-left: 8px; margin-bottom: 8px; font-size: large"
        >
          {{item.employeeName}}
        </el-tag>
      </div>
    </el-card>
    <add-or-update v-if="addOrUpdateVisible" ref="addOrUpdate" @refreshDataList="getDataList"></add-or-update>
  </div>
</template>

<script>
import mixinViewModule from '@/mixins/view-module'
import AddOrUpdate from './formingclass-add-or-update'
import Cookies from "js-cookie";
export default {
  mixins: [mixinViewModule],
  data () {
    return {
      message:'',
      headers: {'token': Cookies.get('token')},
      fileList: [{name: '', url: ''}],
      unsuccessfulNameList: '',//  导入失败名称列表
      mixinViewModuleOptions: {
        getDataListURL: '/batch/formingclass/page',
        getDataListIsPage: false,
        exportURL: '/batch/formingclass/export',
        deleteURL: '/batch/formingclass',
        deleteIsBatch: true,
        exportTemplateURL: '/batch/formingclass/export/template',
        addBatchUrl: window.SITE_CONFIG['apiURL'] + '/batch/formingclass/batchSave',
      },
      dataForm: {
        id: '',
        paramStr: '',
      },
      // 判断是否还在继续输入
      timer: null,
    }
  },
  components: {
    AddOrUpdate
  },
  methods:{
    //节流函数
    throttleFunction() {
      // 如果timer不为空就表明在持续输入,每次改变都清除定时，这样就可以实现只输入最后一次结果(注意每次间隔如果超过500毫秒就会输出)
      if (this.timer !== null) clearTimeout(this.timer)
      this.timer = setTimeout(() => {
        this.getDataList()
      }, 500)
    },
    closeHandle(data){
      this.$confirm('将要删除' + data.employeeName +  ', 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
        center: true
      }).then(() => {
        this.$http.delete(`batch/formingclass/delete?id=${data.id}`).then(({data: res}) =>{
          if (res.code !== 0) {
            return this.$message.error(res.msg)
          }
          this.$message({
            message: '删除成功',
            type: 'success',
            duration: 500,
            onClose: () => {
              this.visible = false
              this.getDataList()
            }
          })
        })
      })
    },
    beforeUpload(file) {
      this.message = this.$message({
        message: '上传中，请勿操作',
        center: true,
        duration: 0,
      });
    },
    handleChange(file, fileList) {
      this.fileList = fileList.slice(-3);

    },
    resultHandle(res) {
      if (res.code !== 0) {
      return this.$message.error(res.msg)
      }
      this.$message({
        message: this.$t('prompt.success'),
        type: 'success',
        duration: 500,
        onClose: () => {
          if (res.data) {
            this.unsuccessfulNameList = res.data
          }
          this.visible = false
          this.query()
        }
      })
    },
  }
}
</script>

<style>
.aui-wrapper .el-card + .el-card {
  margin-top: 0px;
}
</style>