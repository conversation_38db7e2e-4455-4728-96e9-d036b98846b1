<template>
  <el-card shadow="never" class="aui-card--fill">
    <div class="mod-batch__formingprocesssampling}">
      <el-form :inline="true" :model="dataForm" @keyup.enter.native="getDataList()">
        <el-tooltip content="请输入客户代码或者部品番号" placement="top">
          <el-form-item>
            <el-input v-model="dataForm.paramStr" placeholder="关键字查询" @input="throttleFunction" clearable>
              <el-button slot="append" icon="el-icon-search" @click="getDataList()"></el-button>
            </el-input>
          </el-form-item>
        </el-tooltip>
        <el-form-item>
          <el-button type="info" @click="exportHandle()">{{ $t('export') }}</el-button>
        </el-form-item>
        <el-form-item>
          <el-button v-if="$hasPermission('batch:formingprocesssampling:save')" type="primary" @click="addOrUpdateHandle()">{{ $t('add') }}</el-button>
        </el-form-item>
        <el-form-item>
          <el-button v-if="$hasPermission('batch:formingprocesssampling:delete')" type="danger" @click="deleteHandle()">{{ $t('deleteBatch') }}</el-button>
        </el-form-item>
      </el-form>
      <el-table v-loading="dataListLoading" width="500px" :data="dataList" border @selection-change="dataListSelectionChangeHandle" style="width: 100%;">
        <el-table-column type="selection" header-align="center" align="center" width="50"></el-table-column>
        <el-table-column prop="samplingDate" fixed label="抽检时间" header-align="center" align="center"></el-table-column>
        <el-table-column prop="subBatchNumber" fixed label="抽检批号" header-align="center" align="center">
          <template slot-scope="scope">
            {{$getDictLabel("size_category", scope.row.sizeCategory)+scope.row.batchNumber+'-'+scope.row.subBatchNumber}}
          </template>
        </el-table-column>
        <el-table-column prop="sampler" fixed label="抽检员" header-align="center" align="center">
          <template slot-scope="scope">
            {{$getEmployeesList(scope.row.sampler)}}
          </template>
        </el-table-column>
        <el-table-column prop="determine" label="判定" header-align="center" align="center">
          <template slot-scope="scope">
            {{$getDictLabel("specialty", scope.row.determine)}}
          </template>
        </el-table-column>
        <el-table-column prop="samplingQuantity" label="抽检数量" header-align="center" align="center"></el-table-column>
        <el-table-column prop="unqualifiedQuantity" label="不良数" header-align="center" align="center"></el-table-column>
        <el-table-column prop="bagWind" label="包风" header-align="center" align="center"></el-table-column>
        <el-table-column prop="deathPenalty" label="死料" header-align="center" align="center"></el-table-column>
        <el-table-column prop="chromatin" label="染黄" header-align="center" align="center"></el-table-column>
        <el-table-column prop="rupture" label="破裂" header-align="center" align="center"></el-table-column>
        <el-table-column prop="burr" label="压毛边" header-align="center" align="center"></el-table-column>
        <el-table-column prop="lackOfMaterial" label="缺料" header-align="center" align="center"></el-table-column>
        <el-table-column prop="exceptionDescription" label="异常说明" header-align="center" align="center"></el-table-column>
        <el-table-column prop="openException" label="开异常单" header-align="center" align="center"></el-table-column>
        <el-table-column prop="processingRecords" label="处理记录" header-align="center" align="center">
          <template slot-scope="scope">
            {{$getDictLabel("ok_ng", scope.row.processingRecords)}}
          </template>
        </el-table-column>
        <el-table-column prop="distillate" label="硬度标准" header-align="center" align="center"></el-table-column>
        <el-table-column prop="measuredHardness" label="实测硬度" header-align="center" align="center"></el-table-column>
        <el-table-column prop="remark" label="备注" header-align="center" align="center"></el-table-column>
        <el-table-column :label="$t('handle')" fixed="right" header-align="center" align="center" width="150">
          <template slot-scope="scope">
            <el-dropdown>
              <el-button type="primary">
                功能<i class="el-icon-arrow-down el-icon--right"></i>
              </el-button>
              <el-dropdown-menu slot="dropdown">
                <el-dropdown-item>
                  <el-button v-if="$hasPermission('batch:formingprocesssampling:update')" type="text" size="small" @click="addOrUpdateHandle(scope.row.id)">{{ $t('update') }}</el-button>
                </el-dropdown-item>
                <el-dropdown-item>
                  <el-button v-if="$hasPermission('batch:formingprocesssampling:delete')" type="text" size="small" @click="deleteHandle(scope.row.id)">{{ $t('delete') }}</el-button>
                </el-dropdown-item>
              </el-dropdown-menu>
            </el-dropdown>
          </template>
        </el-table-column>
      </el-table>
      <el-pagination
        :current-page="page"
        :page-sizes="[10, 20, 50, 100]"
        :page-size="limit"
        :total="total"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="pageSizeChangeHandle"
        @current-change="pageCurrentChangeHandle">
      </el-pagination>
      <!-- 弹窗, 新增 / 修改 -->
      <add-or-update v-if="addOrUpdateVisible" ref="addOrUpdate" @refreshDataList="getDataList"></add-or-update>
    </div>
  </el-card>
</template>

<script>
import mixinViewModule from '@/mixins/view-module'
import AddOrUpdate from './formingprocesssampling-add-or-update'
export default {
  mixins: [mixinViewModule],
  data () {
    return {
      mixinViewModuleOptions: {
        getDataListURL: '/batch/formingprocesssampling/page',
        getDataListIsPage: true,
        exportURL: '/batch/formingprocesssampling/export',
        deleteURL: '/batch/formingprocesssampling',
        deleteIsBatch: true
      },
      addOrUpdateVisible:false,
      dataForm: {
        id: '',
        paramStr: '',
      },
      // 判断是否还在继续输入
      timer: null,
    }
  },
  components: {
    AddOrUpdate
  },
  methods:{
    addOrUpdateHandle(id) {
      this.addOrUpdateVisible = true
      this.$nextTick(() => {
        this.$refs.addOrUpdate.dataForm.id = id
        this.$refs.addOrUpdate.init()
      })
    },
    //节流函数
    throttleFunction() {
      // 如果timer不为空就表明在持续输入,每次改变都清除定时，这样就可以实现只输入最后一次结果(注意每次间隔如果超过500毫秒就会输出)
      if (this.timer !== null) clearTimeout(this.timer)
      this.timer = setTimeout(() => {
        this.getDataList()
      }, 500)
    },
  }
}
</script>
