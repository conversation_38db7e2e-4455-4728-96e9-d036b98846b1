<template>
  <el-dialog :visible.sync="visible" :title="!dataForm.id ? $t('add') : $t('update')" :close-on-click-modal="false" :close-on-press-escape="false" append-to-body>
    <el-form :model="dataForm" :rules="dataRule" ref="dataForm" @keyup.enter.native="dataFormSubmitHandle()" :label-width="$i18n.locale === 'en-US' ? '120px' : '80px'">
      <div class="container">
                                                <el-form-item label="部门id" prop="deptId">
                                                  <el-select v-model="dataForm.deptId">
                                                    <el-option v-for="(item, index) in deptOptions"
                                                               :key="index"
                                                               :label="item.label"
                                                               :value="item.value">
                                                    </el-option>
                                                  </el-select>
            </el-form-item>
                                        <el-form-item label="机台" prop="machineId">
                <el-select v-model="dataForm.machineId" filterable placeholder="请选择机台" @change="selectMachineCode">
                  <el-option v-for="(item, index) in machineOptions"
                             :key="index"
                             :label="item.code + '(' + item.name + ')'"
                             :value="item.id">
                  </el-option>
                </el-select>
            </el-form-item>
<!--        <el-form-item label="打印机" prop="print">-->
<!--          <el-select v-model="dataForm.print"  placeholder="请选择">-->
<!--            <el-option v-for="item in printOptions"-->
<!--                       :key="item"-->
<!--                       :label="item"-->
<!--                       :value="item">-->
<!--            </el-option>-->
<!--          </el-select>-->
<!--        </el-form-item>-->
<!--                                        <el-form-item label="机台代码" prop="machineCode">-->
<!--              <el-input v-model="dataForm.machineCode" placeholder="机台代码"></el-input>-->
<!--            </el-form-item>-->
<!--                                        <el-form-item label="机台状态" prop="machineStatus">-->
<!--              <el-input v-model="dataForm.machineStatus" placeholder="机台状态 0：生产；1：异常；2：试模；3：上机中；4：下机中；5：维修；6：保养；7：待机；8：停机"></el-input>-->
<!--            </el-form-item>-->
<!--                                        <el-form-item label="机台状态说明" prop="machineStatusExplain">-->
<!--              <el-input v-model="dataForm.machineStatusExplain" placeholder="机台状态说明"></el-input>-->
<!--            </el-form-item>-->
<!--                                        <el-form-item label="品番id" prop="partId">-->
<!--              <el-input v-model="dataForm.partId" placeholder="品番id"></el-input>-->
<!--            </el-form-item>-->
<!--                                        <el-form-item label="品号编码" prop="partCode">-->
<!--              <el-input v-model="dataForm.partCode" placeholder="品号编码"></el-input>-->
<!--            </el-form-item>-->
<!--                                        <el-form-item label="客户品名" prop="designation">-->
<!--              <el-input v-model="dataForm.designation" placeholder="客户品名"></el-input>-->
<!--            </el-form-item>-->
<!--                                        <el-form-item label="客户代码" prop="customerCode">-->
<!--              <el-input v-model="dataForm.customerCode" placeholder="客户代码"></el-input>-->
<!--            </el-form-item>-->
<!--                                        <el-form-item label="模组id" prop="moduleId">-->
<!--              <el-input v-model="dataForm.moduleId" placeholder="模组id"></el-input>-->
<!--            </el-form-item>-->
<!--                                        <el-form-item label="模组编码" prop="moldCode">-->
<!--              <el-input v-model="dataForm.moldCode" placeholder="模组编码"></el-input>-->
<!--            </el-form-item>-->
<!--                                        <el-form-item label="当前生产批次id" prop="subBatchId">-->
<!--              <el-input v-model="dataForm.subBatchId" placeholder="当前生产批次id"></el-input>-->
<!--            </el-form-item>-->
<!--                                        <el-form-item label="当前生产批次号" prop="subBatchNumber">-->
<!--              <el-input v-model="dataForm.subBatchNumber" placeholder="当前生产批次号"></el-input>-->
<!--            </el-form-item>-->
<!--                                        <el-form-item label="本次批累积模数" prop="accumulate">-->
<!--              <el-input v-model="dataForm.accumulate" placeholder="本次批累积模数"></el-input>-->
<!--            </el-form-item>-->
<!--                                        <el-form-item label="异常次数" prop="exceptionCount">-->
<!--              <el-input v-model="dataForm.exceptionCount" placeholder="异常次数"></el-input>-->
<!--            </el-form-item>-->
<!--                                        <el-form-item label="操作员1" prop="operatorOne">-->
<!--              <el-input v-model="dataForm.operatorOne" placeholder="操作员1"></el-input>-->
<!--            </el-form-item>-->
<!--                                        <el-form-item label="操作员2" prop="operatorTwo">-->
<!--              <el-input v-model="dataForm.operatorTwo" placeholder="操作员2"></el-input>-->
<!--            </el-form-item>-->
<!--                                        <el-form-item label="上模时间" prop="upperMouldTime">-->
<!--              <el-input v-model="dataForm.upperMouldTime" placeholder="上模时间"></el-input>-->
<!--            </el-form-item>-->
<!--                                        <el-form-item label="上模人员" prop="upperMouldOperator">-->
<!--              <el-input v-model="dataForm.upperMouldOperator" placeholder="上模人员"></el-input>-->
<!--            </el-form-item>-->
<!--                                        <el-form-item label="预计下模时间" prop="predictMouldUnloadingTime">-->
<!--              <el-input v-model="dataForm.predictMouldUnloadingTime" placeholder="预计下模时间"></el-input>-->
<!--            </el-form-item>-->
<!--                                        <el-form-item label="模具生产总累计数" prop="moldProductionQuantity">-->
<!--              <el-input v-model="dataForm.moldProductionQuantity" placeholder="模具生产总累计数"></el-input>-->
<!--            </el-form-item>-->
<!--                                        <el-form-item label="备注" prop="remark">-->
<!--              <el-input v-model="dataForm.remark" placeholder="备注"></el-input>-->
<!--            </el-form-item>-->
                                                                                                                  </div>
    </el-form>
    <template slot="footer">
      <el-button @click="visible = false">{{ $t('cancel') }}</el-button>
      <el-button type="primary" @click="dataFormSubmitHandle()">{{ $t('confirm') }}</el-button>
    </template>
  </el-dialog>
</template>

<script>
import debounce from 'lodash/debounce'
import mixinViewModule from '@/mixins/view-module'
export default {
  mixins:[mixinViewModule],
  data () {
    return {
      visible: false,
      //创建该页面时，不调用数据查询接口
      mixinViewModuleOptions: {
        createdIsNeed: false,
      },
      dataForm: {
        id: '',
        deptId: '',
        machineId: '',
        machineCode: '',
        machineStatus: '',
        machineStatusExplain: '',
        partId: '',
        partCode: '',
        designation: '',
        customerCode: '',
        moduleId: '',
        moldCode: '',
        subBatchId: '',
        subBatchNumber: '',
        accumulate: '',
        exceptionCount: '',
        print:'',
        operatorOne: '',
        operatorTwo: '',
        upperMouldTime: '',
        upperMouldOperator: '',
        predictMouldUnloadingTime: '',
        moldProductionQuantity: '',
        remark: '',
        creator: '',
        createDate: '',
        updater: '',
        updateDate: '',
        disabled: ''
      },
      machineOptions:[],
      deptOptions:[
        {'label':'制一部','value':'1641701467113046018'},
        {'label':'制三部','value':'1641701649229725698'},
      ],
      // printOptions:[],
    }
  },
  computed: {
    dataRule () {
      return {
          id: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          deptId: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          machineId: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          machineCode: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          machineStatus: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          machineStatusExplain: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          partId: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          partCode: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          designation: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          customerCode: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          moduleId: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          moldCode: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          subBatchId: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          subBatchNumber: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          accumulate: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          exceptionCount: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          operatorOne: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          operatorTwo: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          upperMouldTime: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          upperMouldOperator: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          predictMouldUnloadingTime: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          moldProductionQuantity: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          remark: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
                }
    }
  },
  methods: {
    init () {
      this.visible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].resetFields()
        if (this.dataForm.id) {
          this.getInfo()
        }
      })
      this.getMachineList()
      // this.queryPrintInfo()
    },
    selectMachineCode(){
      let machine = this.machineOptions.filter(item => item.id === this.dataForm.machineId);
      this.dataForm.machineCode = machine[0].code;
    },


    // queryPrintInfo(){
    //   this.$axios.get('http://192.168.0.108/Bartender/api/v1/printers').then((resp => {
    //     console.log(resp.data.serverPrinters)
    //     this.printOptions = resp.data.serverPrinters;
    //   }))
    // },

    // 获取信息
    getInfo () {
      this.$http.get(`/batch/formingmachinevariation/${this.dataForm.id}`).then(({ data: res }) => {
        if (res.code !== 0) {
          return this.$message.error(res.msg)
        }
        this.dataForm = {
          ...this.dataForm,
          ...res.data
        }
      }).catch(() => {})
    },
    //获取机台列表
    getMachineList(){
      this.$http.get(`/machine/machine/queryMachineListByCode?code=` + "").then(({data: res}) => {
        if (res.code !== 0) {
          return this.$message.error(res.msg)
        }
        this.machineOptions = res.data
      })
    },
    // 表单提交
    dataFormSubmitHandle: debounce(function () {
      this.$refs['dataForm'].validate((valid) => {
        if (!valid) {
          return false
        }
        let message = '';
        if(this.dataForm.id){
          message = '此操作将修改该数据'
        }else {
          message = '此操作将新增一条数据'
        }
        this.$confirm(message+', 是否继续?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
          center: true
        }).then(() => {
          this.$http[!this.dataForm.id ? 'post' : 'put']('/batch/formingmachinevariation/', this.dataForm).then(({ data: res }) => {
            if (res.code !== 0) {
            return this.$message.error(res.msg)
            }
            this.$message({
          message: this.$t('prompt.success'),
            type: 'success',
                    duration: 500,
                    onClose: () => {
              this.visible = false
              this.$emit('refreshDataList')
            }
          })
          }).catch(() => {})
        }).catch(() => {
          this.$message({
            type: 'info',
            message: '已取消'
          });
        });
      })
    }, 1000, { 'leading': true, 'trailing': false })
  }
}
</script>
