<template>
  <el-card shadow="never" class="aui-card--fill">
    <div class="mod-batch__insertbatchsubbatch aui-popup" style="display: flex; justify-content: center; align-items: center;">
<!--      <el-autocomplete
          class="inline-input"
          v-model="dataForm.paramStr"
          :fetch-suggestions="selectParamStr"
          placement="bottom"
          :placeholder="placeholderText"
          :trigger-on-focus="false"
          @select="handleParamStr"
          style="width: 50%; margin-right: 10px;"
          clearable>
      </el-autocomplete>-->

<!--      <el-select
          v-model="value"
          filterable
          remote
          reserve-keyword
          placeholder="请输入关键词"
          :remote-method="remoteMethod"
          :loading="loading">
        <el-option
            v-for="(item,index) in options"
            :key="index"
            :label="item.label"
            :value="item.value">
        </el-option>
      </el-select>-->

<!--      <baidu-suggestion></baidu-suggestion>-->
<!--      <RemoteSelect  api="/fabricate/part/fuzzyQueryPart" optionName="designation" :rows="10"
                    :option="{ code: 'optionCode', name: 'optionName' }" v-model="dataForm.paramStr"
      ></RemoteSelect>-->






      <el-button type="primary" v-if="insertBatch">新增批号</el-button>
      <el-button type="primary" v-if="insertSubBatch">新增次批号</el-button>
      <div>
        <batch-add-or-update v-if="batchAddOrUpdateVisible" ref="batchAddOrUpdate" @refreshDataList="getDataList"></batch-add-or-update>
        <sub-batch-add-or-update v-if="subAddOrUpdateVisible" ref="subAddOrUpdate" @refreshDataList="getDataList"></sub-batch-add-or-update>
      </div>
    </div>
  </el-card>

</template>

<script>
import { MessageBox } from 'element-ui'
import BatchAddOrUpdate from "./batch-add-or-update.vue";
import SubBatchAddOrUpdate from "./subbatch-add-or-update.vue";
import RemoteSelect from "../../../components/remote-select/remoteSelect.vue";
import da from "element-ui/src/locale/lang/da";


export default {
  name: 'insertbatchsubbatch',
  data () {
    return {
      options: [],
      value: [],
      list: [],
      keyword: '', // 输入框的值
      suggestions: [], // 搜索建议的数组
      loading: false,
      subAddOrUpdateVisible:false,
      batchAddOrUpdateVisible:false,
      insertBatch:false,
      insertSubBatch:false,
      insertDesignation:false,
      placeholderText:this.$route.query.param != null ?this.$route.query.param:'请输入部品品名',
      mixinViewModuleOptions: {
        deleteIsBatch: true
      },
      dataForm: {
        paramStr: '',
        temporaryBatchNumber:'',
        id: '',
        productInventoryId: '',
        batchId: '',
        sizeCategory: '',
        batchNumber: '',
        selectBatchNumber: '',
        transferPerson: '',
        partId: '',
        receiver: '',
        designation: '',
        subBatchId: '',
        subBatchNumber: '',
        itemCategory: 0,
        transferUnit: '',
        transferUnitName: '',
        deptId: '',
        transferDeptName: '',
        transferOutQuantity: 0,
        transferee: '',
        receivingDepartment: '',
        receivingDeptName: '',
        recipient: '',
        receiveQuantity: 0,
        receivingTime: '',
        storageLocation: '',
        productCategoryTransferOut: 0,
        productCategoryTakeOver: 5,
        remark: '',
        disabled: '',
        creator: '',
        createDate: '',
        updater: '',
        updateDate: ''
      }
    }
  },
  created(){
    this.unmaskLayer()
  },
  mounted() {
    this.unmaskLayer()
  },
  watch:{
    placeholderText(newValue,oldValue){
      this.unmaskLayer()
    }
  },
  components: {
    RemoteSelect,
    BatchAddOrUpdate,
    SubBatchAddOrUpdate
  },
  methods:{
    // 处理输入框的输入事件，将输入值同步到 keyword 数据，并发送请求获取搜索建议
    handleInput(val) {
      this.keyword = val;
      if (val) {
        this.loading = true;
        this.$http.get(`/fabricate/part/fuzzyQueryPart?designation=` + val).then(({data: res}) => {
          this.loading = false;
          this.suggestions = res.data;
        });
      } else {
        this.suggestions = [];
      }
    },
    // 处理搜索建议的选择事件，将选择值同步到 keyword 数据，并跳转到百度搜索结果页面
    handleSelects(val) {
      this.keyword = val;
      console.log(val,"val================================")
    },
    // 处理搜索按钮的点击事件，跳转到百度搜索结果页面
    handleSearch() {
      console.log(this.keyword,"keyword-------------------------------")
    },
    // 处理输入框的聚焦事件，将 el-select 组件的下拉框显示出来
    handleFocus() {
      this.$refs.select.visible = true;
    },
    // 处理输入框的失焦事件，将 el-select 组件的下拉框隐藏起来
    handleBlur() {
      this.$refs.select.visible = false;
    },
    remoteMethod(query) {
      if (query !== '') {
        this.loading = true;
        setTimeout(() => {
          this.loading = false;
          this.selectBlur(query)
        }, 200);
      } else {
        this.options = [];
      }
    },
    selectBlur(queryString) {
      this.$http.get(`/fabricate/part/fuzzyQueryPart?designation=` + queryString).then(({data: res}) => {
        if (res.code !== 0) {
          return this.$message.error(res.msg)
        }
        this.options = res.data.map((obj) => {
          return {
            customerCode: obj.customerCode,
            value:obj.designation,
            label:obj.designation+ '('+
                (obj.batchNumber != null ?
                    (obj.sizeCategory+obj.batchNumber)
                    +
                    (obj.subBatchNumber != null ? '-'+obj.subBatchNumber : '(无对应次批号)'):'无对应批号')
                +')',
            partId: obj.id,
            designation: obj.designation,
            batchId: obj.batchId,
            batchNumber: obj.batchNumber,
            temporaryBatchNumber: obj.sizeCategory+obj.batchNumber+'-'+obj.subBatchNumber,
            subBatchNumber: obj.subBatchNumber,
            subBatchId: obj.subBatchId,
          }
        })
        this.timeout = setTimeout(() => {
        }, 3000 * Math.random());
      }).catch(() => {
      })
    },
    //新增批号或者次批号
    insertBatchOrSubBatch(logo,id){
      if(logo === '批号'){
        this.batchAddOrUpdateVisible = true
        this.$nextTick(() => {
          this.$refs.batchAddOrUpdate.dataForm.id = id
          this.$refs.batchAddOrUpdate.init()
        })
      }else {
        this.subAddOrUpdateVisible = true
        this.$nextTick(() => {
          console.log(id,"次========================")
          this.$refs.subAddOrUpdate.batchNumberId = id
          this.$refs.subAddOrUpdate.init()
        })
      }
    },
    showMessage(message,click,batchId) {
      MessageBox.confirm(message, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        if(click){
          this.insertBatchOrSubBatch("批号",batchId)
        }else {
          this.insertBatchOrSubBatch("次批号",batchId)
        }
      }).catch(() => {
        // 点击取消按钮后的回调函数
        console.log('点击了取消按钮')
        this.unmaskLayer()
        this.dataForm.paramStr = ''
      })
    },
    selectParamStr(param,db){
      if(this.placeholderText === '请输入部品品名'){
        this.getPartList(this.dataForm.paramStr,db)
      }else {
        this.selectButchNumberInfo(this.dataForm.paramStr,db)
      }
    },
    handleParamStr(item){
      if(this.placeholderText === '请输入部品品名'){
        this. partSelect(item)
      }else {
        this.handleSelect(item)
      }
    },
    //模糊查询批号
    selectButchNumberInfo(batchNumber, cb) {
      this.$http.get(`/batch/batch/selectButchNumberInfo/` + batchNumber).then(({data: res}) => {
        if (res.code !== 0) {
          return this.$message.error(res.msg)
        }
        const list = res.data.map((obj) => {
          return {
            batchId: obj.id,
            batchNumber: obj.batchNumber,
            value: obj.sizeCategory+obj.batchNumber+'-'+obj.subBatchNumber+'('+obj.designation+')',
            designation: obj.designation,
            selectBatchNumber: obj.sizeCategory+obj.batchNumber+'-'+obj.subBatchNumber,
            temporaryBatchNumber: obj.sizeCategory+obj.batchNumber+'-'+obj.subBatchNumber,
            subBatchNumber: obj.subBatchNumber,
            subBatchId: obj.subBatchId,
            partId:obj.partId
          }
        })
        this.timeout = setTimeout(() => {
          cb(list);
        }, 3000 * Math.random());
      }).catch(() => {
      })
    },
    //  输出选择日志
    handleSelect(item) {
      this.dataForm.partId = item.partId
      this.dataForm.batchId = item.batchId
      this.dataForm.batchNumber = item.batchNumber
      this.dataForm.designation = item.designation
      this.dataForm.selectBatchNumber = item.selectBatchNumber
      this.dataForm.temporaryBatchNumber = item.temporaryBatchNumber
      if(item.subBatchNumber != null && item.subBatchNumber != ''){
        MessageBox.confirm('该批号已有批次，是使用现有批次还是生成新批次使用？', '提示', {
          confirmButtonText: '生成新批次',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          console.log(item.batchId,"批号=====================")
          this.insertBatchOrSubBatch("次批号",item.batchId)
          return;
        }).catch(() => {

        })
        this.dataForm.subBatchNumber = item.subBatchNumber
      }else {
        this.showMessage("该批号还未生成批次是否进行生成次批次？",false,item.batchId)
        return;
      }
      this.dataForm.subBatchId = item.subBatchId

    },
    //  获取部品列表
    getPartList(queryString, cb) {
      console.log(cb)
      this.$http.get(`/fabricate/part/fuzzyQueryPart?designation=` + queryString).then(({data: res}) => {
        if (res.code !== 0) {
          return this.$message.error(res.msg)
        }
        const list = res.data.map((obj) => {
          return {
            customerCode: obj.customerCode,
            /* value: obj.designation + '(' + obj.customerCode + ')',*/
            value:obj.designation+ '('+
                (obj.batchNumber != null ?
                    (obj.sizeCategory+obj.batchNumber)
                    +
                    (obj.subBatchNumber != null ? '-'+obj.subBatchNumber : '(无对应次批号)'):'无对应批号')
                +')',
            partId: obj.id,
            designation: obj.designation,
            batchId: obj.batchId,
            batchNumber: obj.batchNumber,
            temporaryBatchNumber: obj.sizeCategory+obj.batchNumber+'-'+obj.subBatchNumber,
            subBatchNumber: obj.subBatchNumber,
            subBatchId: obj.subBatchId,
          }
        })
        this.timeout = setTimeout(() => {
          cb(list);
        }, 3000 * Math.random());
      }).catch(() => {
      })
    },
    //  输出选择日志
    partSelect(item) {
      this.dataForm.subBatchId = item.subBatchId
      this.dataForm.designation = item.designation
      this.dataForm.customerCode = item.customerCode
      this.dataForm.partId = item.partId
      this.dataForm.batchId = item.batchId
      if(item.batchNumber != null && item.batchNumber!= ''){
        MessageBox.confirm('该批号已有批次，是使用现有批次还是生成新批次使用？', '提示', {
          confirmButtonText: '生成新批次',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          console.log(item.batchId,"部品=====================")
          this.insertBatchOrSubBatch("次批号",item.batchId)
          return;
        }).catch(() => {

        })
        this.dataForm.batchNumber = item.batchNumber
      }else {
        this.showMessage("该番号还未生成对应批号，是否进行生成批号操作？",true,'')
        return;
      }
      if(item.subBatchNumber != null && item.subBatchNumber != ''){
        this.dataForm.temporaryBatchNumber = item.temporaryBatchNumber
      }else {
        this.showMessage("该批号还未生成批次，是否进行生成次批次？",false,item.batchId)
        return;
      }
      this.dataForm.subBatchNumber = item.subBatchNumber

    },
    //取消遮罩层
    unmaskLayer(){
      const el = document.querySelector('.v-modal')
      el.classList.remove('v-modal')
    },
    goBack() {
      localStorage.setItem("paramData", JSON.stringify(this.dataForm));
      this.$retures.$emit('param', 'asdfas')
      this.$router.go(-1)
    }
  }
}
</script>
<style>
.scroller {
  width: auto; /* 设置下拉框的宽度自适应 */
}

.item {
  padding: 10px; /* 设置每一项的内边距 */
}

.item:hover {
  background-color: #f5f5f5; /* 设置每一项的鼠标悬停背景色 */
}

.aui-popup {
  position: relative;
  background-color: #eaf5e2; /* 设置底色为淡绿色 */
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  animation: aui-popup 0.3s ease-out;
  width: 50%; /* 设置宽度为50% */
  height: 30%;
  margin: 0 auto;
  padding: 10px;
}

@media screen and (max-width: 768px) {
  .aui-popup {
    width: 30%; /* 在小屏幕下，设置宽度为30% */
  }
}

.aui-popup::before {
  content: '';
  position: absolute;
  top: -8px;
  left: 50%;
  transform: translateX(-50%);
  border: 8px solid transparent;
  border-bottom-color: #eaf5e2; /* 设置三角形的底色为淡绿色 */
  height: 30%;
}

@keyframes aui-popup {
  0% {
    opacity: 0;
    transform: scale(0.8);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}
</style>

<style lang="scss" scoped>
div,
input {
  box-sizing: border-box;
}
.sugg-container {
  position: relative;
  display: flex;
  font-size: 14px;
  width: 40em;
  .sugg-oinput {
    display: inline-block;
    height: 3em;
    width: 100%;
    .sugg-input {
      width: 100%;
      height: 100%;
      border: 1px solid #dedede;
      font-size: 1.6em;
      border-radius: 0.25em;
      transition: outline 0.3s;
      &:active {
        outline: #0071fe solid thin;
      }
      &:focus {
        outline: #0071fe solid thin;
      }
    }
  }
  .sugg-fruit {
    position: absolute;
    top: 3.2em;
    left: 0px;
    width: 100%;
    background: #fff;
    box-shadow: 0 0 10px 0 #999;
    border-radius: 0.4em;
    height: auto;
    max-height: 400px;
    width: auto;
    overflow: hidden;
    overflow-y: auto;
    .sugg-item {
      font-size: 1.8em;
      padding: 0.56em 1.12em;
      cursor: pointer;
      color: #333;
      &.active {
        background: #ccc;
      }
      &:hover {
        background: #ccc;
      }
    }
  }
  .sugg-enter-active,
  .sugg-leave-active {
    transition: all 0.5s;
  }
  .sugg-enter,
  .sugg-leave-to {
    opacity: 0;
  }
}
</style>