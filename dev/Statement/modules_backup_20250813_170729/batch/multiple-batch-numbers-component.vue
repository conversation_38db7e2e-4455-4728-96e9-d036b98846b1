<template>
  <div>
    <el-dialog :visible.sync="visible" title="生成批号" @closed="closeWindows" :close-on-click-modal="false" :close-on-press-escape="false" append-to-body>
      <el-form ref="dataForm" :label-width="$i18n.locale === 'en-US' ? '120px' : '80px'">
        <div class="container" v-for="(item,index) in multipleBatchNumbers" :key="index">
          <div class="item">
            <el-form-item label="胶料类别" prop="sizeCategory">
              <div>
                <ren-radio-tick v-model="item.sizeCategory" dict-type="size_category"></ren-radio-tick>
              </div>
            </el-form-item>
          </div>
          <div class="item">
            <el-form-item label="批号" prop="batchNumber">
              <el-input v-model="item.batchNumber" placeholder="批号"></el-input>
            </el-form-item>
          </div>
          <div class="item">
            <el-button type="danger" @click="deleteBatch(index)" icon="el-icon-delete" circle></el-button>
          </div>
        </div>

        <el-button type="primary" @click="add()">添加批号</el-button>
        <el-button type="primary" @click="closeWindows(1)">确认</el-button>
      </el-form>
    </el-dialog>
  </div>
</template>
<script>

import { remove } from 'lodash'

export default {
  name: 'MultipleBatchNumbersComponent',
  data () {
    return {
      visible: false,
      batchNumber: '',
      multipleBatchNumbers:[]
    }
  },
  props: {
    whether:{
      type:Boolean,
      default:true
    }
  },
  computed: {
    dataRule () {
      return {

      }
    }
  },
  mounted() {

  },
  methods: {
    deleteBatch(index) {
      console.log(index);
      console.log('删除中');
      this.multipleBatchNumbers = this.multipleBatchNumbers.slice(0, index).concat(this.multipleBatchNumbers.slice(index + 1));
      console.log(this.multipleBatchNumbers, '数组数据');
    },
    add() {
      // 获取数组的最后一个元素
      var last = this.multipleBatchNumbers[this.multipleBatchNumbers.length - 1];
      // 使用 Object.assign 方法复制最后一个元素的属性
      var copy = Object.assign({sizeCategory:'',batchNumber:this.batchNumber}, last);
      // 在数组的末尾添加复制的对象
      this.multipleBatchNumbers.push(copy);
    },
    init () {
      this.visible = true
    },
    closeWindows(logo){
      if(logo){
        this.$emit('return',this.multipleBatchNumbers)
      }
      this.visible = false
    },
  }
}
</script>

