<template>
  <el-card shadow="never" class="aui-card--fill">
    <div class="mod-batch__batch}">
      <el-form :inline="true" :model="dataForm" ref="dataForm" @keyup.enter.native="getDataList()">
        <el-form-item>
          <div >
            <el-tooltip content="请输入制造指令或者部品番号或者批号" placement="top">
              <el-input v-model="dataForm.paramStr" placeholder="查询关键字" @input="throttleFunction" clearable @clear="clearValue">
                <el-select v-model="dataForm.queryType" @change="getDataList()" style="width: 110px" slot="prepend" placeholder="请选择">
                  <el-option label="批号" :value="1"></el-option>
                  <el-option label="客户代码" :value="2"></el-option>
                  <el-option label="客户品号" :value="3"></el-option>
                  <el-option label="配方" :value="4"></el-option>
                  <el-option label="订单号" :value="5"></el-option>
                  <el-option label="制造指令" :value="6"></el-option>
                </el-select>
                <el-button slot="append" icon="el-icon-search" @click="getDataList()"></el-button>
              </el-input>
            </el-tooltip>
          </div>
        </el-form-item>
        <el-form-item label="排序方式:">
          <ren-select v-model="dataForm.sortOrder" style="width: 150px" @change="getDataList()" dict-type="sort_order"></ren-select>
        </el-form-item>
        <el-form-item label="查询范围">
          <ren-select v-model="dataForm.rangeQueries" style="width: 150px" @change="getDataList()" dict-type="range_queries"></ren-select>
        </el-form-item>
        <el-form-item label="查询时间">
          <div class="block">
            <el-date-picker
                v-model="startToEnd"
                @change="handleDateChange"
                @clear="getDataList"
                type="daterange"
                range-separator="至"
                start-placeholder="开始日期"
                value-format="yyyy-MM-dd"
                clearable
                end-placeholder="结束日期">
            </el-date-picker>
          </div>
        </el-form-item>
        <el-form-item>
          <el-button v-if="$hasPermission('batch:batch:save')" type="primary" @click="addOrUpdateHandle()">{{ $t('add') }}</el-button>
        </el-form-item>
        <el-form-item>
          <el-button v-if="$hasPermission('batch:batch:save')" type="primary" @click="clickToAddAgain()">再新增</el-button>
        </el-form-item>
        <el-dropdown  style="float:right;">
          <el-button type="primary">
            更多功能<i class="el-icon-arrow-down el-icon--right"></i>
          </el-button>
          <el-dropdown-menu slot="dropdown">
            <el-dropdown-item>
              <el-form-item>
                <el-button type="info" @click="exportHandle()">{{ $t('export') }}</el-button>
              </el-form-item>
            </el-dropdown-item>
            <el-dropdown-item>
              <el-form-item>
                <el-button v-if="$hasPermission('fabricate:part:export')" type="info" @click="exportTemplateHandle()">
                  {{ $t('exportTemplate') }}
                </el-button>
              </el-form-item>
            </el-dropdown-item>
            <el-dropdown-item>
              <el-form-item>
                <el-tooltip content="下载模板填入信息后导入" placement="top">
                  <el-upload
                      v-if="$hasPermission('fabricate:part:save')"
                      class="upload-demo"
                      :action="mixinViewModuleOptions.addBatchUrl"
                      :headers="headers"
                      :multiple="false"
                      :show-file-list="false"
                      :file-list="fileList"
                      :before-upload="beforeUpload"
                      :on-success="resultHandle"
                      :on-change="handleChange"
                      accept="'.xlsx','.xls'">
                    <el-button type="success">{{ $t('addBatch') }}</el-button>
                  </el-upload>
                </el-tooltip>
                <el-button type="text" v-show="unsuccessfulNameList" @click="dialogVisible = true">导入失败列表</el-button>
              </el-form-item>
            </el-dropdown-item>
            <el-dropdown-item>
              <el-form-item>
                <el-button v-if="$hasPermission('batch:batch:delete')" type="danger" @click="deleteHandle()">{{ $t('deleteBatch') }}</el-button>
              </el-form-item>
            </el-dropdown-item>
            <el-dropdown-item>
              <el-form-item>
                <el-button v-if="$hasPermission('batch:batch:save')" type="primary" @click="oldBatchAddOrUpdateHandle">新增旧批号</el-button>
              </el-form-item>
            </el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown>
      </el-form>
      <el-table v-loading="dataListLoading" :key="componentKey" ref="batchTable" height="700px" :data="dataList" border @selection-change="handleChangeRow" @sort-change="dataListSortChangeHandle" style="width: 100%;">
        <el-table-column type="selection" header-align="center" align="center" width="50"></el-table-column>
        <el-table-column prop="productionDate" label="生产日期" sortable="custom" header-align="center" align="center"></el-table-column>
        <el-table-column prop="orderNumber" label="订单号" sortable="custom" header-align="center" align="center"></el-table-column>
        <el-table-column prop="manufacturingInstructions" sortable="custom" label="制造指令" header-align="center" align="center"></el-table-column>
        <el-table-column prop="customerCode" label="客户代码" header-align="center" align="center"></el-table-column>
        <el-table-column prop="designation" label="客户品号" sortable="custom" header-align="center" align="center">
          <template slot-scope="scope">
            <span>{{scope.row.designation}}({{scope.row.moldGroup}})</span>
          </template>
        </el-table-column>
        <el-table-column prop="formulationCode" label="配方代码" sortable="custom" header-align="center" align="center"></el-table-column>
        <el-table-column prop="batchNumber" label="批号" sortable="custom" header-align="center" align="center">
          <template slot-scope="scope">
            <div @click="addOrUpdateBatch(scope.row.id,1)">
              {{scope.row.batchNumber}}
            </div>
          </template>
        </el-table-column>
<!--        <el-table-column  prop="moldNumber" label="模具编码" sortable="custom" header-align="center" align="center"></el-table-column>-->
        <el-table-column prop="subBatchCount" label="次批号数" header-align="center" align="center">
          <template slot-scope="scope">
            <div @click="querySecondaryBatch(scope.row)">
              <el-button type="text">{{ scope.row.subBatchCount}}</el-button>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="weight" label="重量" header-align="center" align="center"></el-table-column>
        <el-table-column :label="$t('handle')" fixed="right" header-align="center" align="center" width="150">
          <template slot-scope="scope">
            <el-dropdown>
              <el-button type="primary">
                功能<i class="el-icon-arrow-down el-icon--right"></i>
              </el-button>
              <el-dropdown-menu slot="dropdown">
                <el-dropdown-item>
                  <el-button v-if="$hasPermission('batch:batch:update')" type="text" size="small" @click="addOrUpdateBatch(scope.row.id)">{{ $t('update') }}</el-button>
                </el-dropdown-item>
                <el-dropdown-item>
                  <el-button v-if="$hasPermission('batch:subbatch:save')" type="text" size="small" @click="callFunction('',scope.row)">新增生产流程卡</el-button>
                </el-dropdown-item>
                <el-dropdown-item>
                  <el-button v-if="$hasPermission('batch:subbatch:save')" type="text" size="small" @click="modifyProductionProcess(scope.row.id,scope.row)">修改生产流程卡</el-button>
                </el-dropdown-item>
                <el-dropdown-item>
                  <el-button v-if="$hasPermission('batch:subbatch:save')" type="text" size="small" @click="subBatchNumber(scope.row.id)">次批号</el-button>
                </el-dropdown-item>
                <el-dropdown-item>
                  <el-button v-if="$hasPermission('batch:batch:delete')" type="text" size="small" @click="deleteHandle(scope.row.id)">{{ $t('delete') }}</el-button>
                </el-dropdown-item>
                <el-dropdown-item>
                  <el-button type="text" size="small" @click="dataFormSubmitHandle($getDictLabel('size_category', scope.row.sizeCategory)+ scope.row.batchNumber,scope.row.subBatchCount,scope.row)">生成流程卡</el-button>
                </el-dropdown-item>
<!--                <el-button type="primary" @click="dataFormSubmitHandle()" round>生成流程卡</el-button>-->
              </el-dropdown-menu>
            </el-dropdown>

          </template>
        </el-table-column>
      </el-table>
      <el-pagination
        :current-page="page"
        :page-sizes="[50, 100, 300, 500]"
        :page-size="limit"
        :total="total"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="pageSizeChangeHandle"
        @current-change="pageCurrentChangeHandle">
      </el-pagination>
      <!-- 弹窗, 新增 / 修改 -->
      <add-or-update v-if="addOrUpdateVisible" ref="addOrUpdate" @refreshDataList="getDataList"></add-or-update>
      <production-process-add-or-update v-if="subAddOrUpdateVisible" @dialog-closed="handleDialogClosed" ref="subAddOrUpdate" :inputDataFrom="data" @refreshDataList="refreshComponent"></production-process-add-or-update>
      <old-batch-add v-if="oldBatchAddVisible" ref="oldBatchAddParameter"></old-batch-add>
      <next-sub-batch v-if="nextSubBatchVisible" ref="nextSubBatchQuery" :batch-data="data" :permission-hiding="false" @refreshDataList="getDataList"></next-sub-batch>
      <el-dialog title='请选择要修改的次批号' :visible.sync="dialogVisibleSubBatch" @closed="closeDialog">
        <div v-for="item in tableData" @click="reviseProcessCard(item.subBatchId)">
          <el-button circle>
            {{item.subBatchNumber}}
          </el-button>
        </div>
      </el-dialog>
    </div>
  </el-card>
</template>

<script>
import mixinViewModule from '@/mixins/view-module'
import AddOrUpdate from './batch-add-or-update'
import Cookies from "js-cookie";
import oldBatchAdd from "./old-batch-add.vue";
import ProductionProcessAddOrUpdate from "./production-process-add-or-update.vue";
import nextSubBatch from "./next-sub-batch.vue";
import id from "element-ui/src/locale/lang/id";
import debounce from "lodash/debounce";
import PublicFunctions from '@/mixins/public-functions'

export default {
  mixins: [mixinViewModule],
  data () {
    return {
      keyword(){},
      tableData:{

      },
      cleanUp:{

      },
      componentKey: 0,
      batchData: {
        manufacturingOrderId: '',
        manufacturingInstructions: '',
        partId: '',
        designation:'',
        customerId: '',
        customerCode:'',
        formulaId: '',
        formulationCode:'',
        batchNumber: '',
        temporaryState: '',
        processingStatus: '',
        lockLotNumber: '',
        numberOfCallbacks: '',
        feeding: '',
        endOfCycle: 0,
        shippingDate: '',
        qualityInspection: '',
        founder: '',
        deliveryTime: '',
        productionPart: 2,
        manufacturingOrderDate: '',
        subBatchCount: 0,
        sizeCategory: '',
        weight: '',
        batchId: '',
        productionBatch:'',
        porductMappingId: '',
        manufacturingId: '',
        qualityInspectionId: '',
        instructionDate: '',
        materialConfirmation: '',
        banburyingOperator: '',
        mixingConditionTime: '',
        mixingTemperature: '',
        openingOperator: '',
        openingTime: '',
        openingTemperature: '',
        wideSize: '',
        dimensionsThick: '',
        productionWeight: '',
        dateOfManufacture: '',
        validityPeriod: '',
        weighing: 0,
        cutter: '',
        barWeightOne: '',
        barWeightTwo: '',
        numberOfLines: '',
        feedingOne: '',
        feedingTwo: '',
        feedingQuantity: '',
        feedback: '',
        letGo: '',
        hardnessStandard: '',
        measuredHardness: '',
        judgementResult: '',
        exceptionDescription: '',
        inspectors: ''
      },
      selectedId:'',
      dialogVisibleSubBatch:false,
      oldBatchAddVisible:false,
      subAddOrUpdateVisible:false,
      nextSubBatchVisible:false,
      message:'',
      headers: {'token': Cookies.get('token')},
      fileList: [{name: '', url: ''}],
      unsuccessfulNameList: '',//  导入失败名称列表
      mixinViewModuleOptions: {
        getDataListURL: '/batch/batch/page',
        getDataListIsPage: true,
        exportURL: '/batch/batch/export',
        deleteURL: '/batch/batch',
        deleteIsBatch: true,
        exportTemplateURL: '/batch/batch/export/template',
        addBatchUrl: window.SITE_CONFIG['apiURL'] + '/batch/batch/batchSave',
      },
      startToEnd: [],
      dataForm: {
        id: '',
        paramStr: this.$route.query.parameter != null ? this.$route.query.parameter:'',
        sortOrder:0,
        queryType:1,
        rangeQueries:'',
        startDate:'',
        endDate:'',
        manufacturingInstructions:'',//制造指令
        formulationCode:'',//配方代码
        designation:'',//客户品名
        partCode:'',//品番代码
        customerCode:'',//客户代码
        manufacturingTime:'2023-12-23 12:30:00',
        productionTime:'2023-12-24',
        moldingDate:'2023-12-27',
        weight:'',
      },
      // 判断是否还在继续输入
      timer: null,
      subBatchId:'',
      data:'',
      qualityInspection(row, column) {
        switch (row.qualityInspection) {
          case 0:
            return '未判定'
          case 1:
            return 'OK'
          case 2:
            return 'NG'
          case 3:
            return '特采'

        }
      },
    }
  },
  watch:{
    "dataForm.paramStr"(value){
      let str = value;
      let char = str.charAt(1);
      let isNumber = !isNaN(char);
      console.log(isNumber);
    },
    startToEnd(newVal) {
      console.log(newVal,'zhe')
      if (!newVal) {
        this.dataForm.startDate = ''
        this.dataForm.endDate = ''
        this.getDataList();
      }
    }
  },
  components: {
    ProductionProcessAddOrUpdate,
    AddOrUpdate,
    oldBatchAdd,
    nextSubBatch,
  },
  activated() {
    console.log("触发了")
    this.$nextTick(() => {
      if (this.$refs.batchTable) {
        this.$refs.batchTable.doLayout();
      }
    });
  },
  created(){
    this.$toRefresh.$on('subBatchInsert', this.refreshPage)
  },
  methods:{
    refreshComponent() {
      this.getDataList();
      this.componentKey += 1
    },
    //监听
    handleChangeRow(selection) {
      this.dataListSelections = selection;
      let slice = selection.slice(-1);
      // 获得当前选中的ID
      this.selectedId = slice.map(item => item.id)
    },
    clickToAddAgain(){
      if(!this.selectedId){
        this.$message.warning('请先选中一个批号')
      }else {
        this.addOrUpdateVisible = true
        this.$nextTick(() => {
          this.$refs.addOrUpdate.displayButton = true
          this.$refs.addOrUpdate.multipleBatch = true
          this.$refs.addOrUpdate.visible = true
          this.$http.get(`/batch/batch/${this.selectedId}`).then(({ data: res }) => {
            if (res.code !== 0) {
              return this.$message.error(res.msg)
            }
            this.$refs.addOrUpdate.dataForm = res.data
            this.$refs.addOrUpdate.dataForm.id = ''
            this.$refs.addOrUpdate.dataForm.materialBatchDetailsId = ''
            this.$refs.addOrUpdate.dataForm.createDate = ''
            this.$refs.addOrUpdate.dataForm.updateDate = ''
          }).catch(() => {})
        })
      }
    },
    handleDialogClosed() {
      // 调用子组件中的 myFunction 方法
      /*this.$refs.subAddOrUpdate.myFunction();*/
    },
    querySecondaryBatch(row){
      this.data = row
      this.nextSubBatchVisible = true
      this.$nextTick(() => {
        this.$refs.nextSubBatchQuery.batchId = row.id
        this.$refs.nextSubBatchQuery.permissionHiding = false
        this.$refs.nextSubBatchQuery.init()
      })
    },
    // 新增 / 修改
    addOrUpdateHandle(id) {
      this.addOrUpdateVisible = true
      this.$nextTick(() => {
        this.$refs.addOrUpdate.dataForm.id = id
        this.$refs.addOrUpdate.displayButton = true
        if(!id){
          this.$http.get(`/batch/batch/generateBatchNumber`).then(({data:res}) => {
            if (res.code !== 0) {
              return this.$message.error(res.msg)
            }
            this.$refs.addOrUpdate.dataForm.batchNumber = res.data
          })
        }
        this.$refs.addOrUpdate.init()
      })
    },
    //节流函数
    throttleFunction() {
      // 如果timer不为空就表明在持续输入,每次改变都清除定时，这样就可以实现只输入最后一次结果(注意每次间隔如果超过500毫秒就会输出)
      if (this.timer !== null) clearTimeout(this.timer)
      this.timer = setTimeout(() => {
        this.getDataList()
      }, 500)
    },
    clearValue(){
      this.dataForm.paramStr = '';
      //重新导航到当前路由，并将参数 parameter 设置为 null
      this.$router.replace({ query: { parameter: null } });
    },
    openDialog() { // 打开对话框
      this.dialogVisibleSubBatch = true;
    },
    closeDialog() { // 关闭对话框
      this.dialogVisibleSubBatch = false;
    },
    //新增旧批号
    oldBatchAddOrUpdateHandle() {
      this.oldBatchAddVisible = true
      this.$nextTick(() => {
        this.$refs.oldBatchAddParameter.init()
      })
    },
    // 修改
    addOrUpdateBatch(id,logo) {
      this.addOrUpdateVisible = true
      let display = true
      this.$nextTick(() => {
        this.$refs.addOrUpdate.dataForm.id = id
        if(logo===1){
          display = false
        }
        this.$refs.addOrUpdate.displayButton = display
        this.$refs.addOrUpdate.init()
      })
    },
    // 修改生产流程
    modifyProductionProcess(batchId,row){
      console.log(row)
      for (const key in row) {
        if (row.hasOwnProperty(key)) {
          if (this.batchData.hasOwnProperty(key)) {
            this.batchData[key] = row[key];
          }
        }
      }
      this.batchData.batchId = row.id
      this.data = this.batchData
      this.tableData = ''
      this.batchNumberSubBatch(batchId)
      if(this.tableData.subBatchId !== null){
        this.dialogVisibleSubBatch = true
      }else {
        alert("该批号尚未建立批次，请先建立批次！")
      }
    },
    // 查询出批号下对应的所有次批号
    batchNumberSubBatch(batchId){
      this.$http.get(`/batch/batch/batchNumberSubBatch/`+batchId).then(({data: res}) => {
        if (res.code !== 0) {
          return this.$message.error(res.msg)
        }
        this.tableData = {
          ...this.tableData,
          ...res.data
        }
      })
    },
    // 根据选择的次批号查询出对应的生产流程记录
    reviseProcessCard(subBatchId){
      this.dialogVisibleSubBatch = false
      this.$eventBus.$emit('batchIdChanged', this.dataForm.id)
      this.subAddOrUpdateVisible = true
      this.$nextTick(() => {
        this.$refs.subAddOrUpdate.dataForm.subBatchId = subBatchId
        this.$refs.subAddOrUpdate.informationVisible = true
        this.$refs.subAddOrUpdate.operate = false
        this.$refs.subAddOrUpdate.displayButton = false
        this.subBatchId = subBatchId;
        this.$refs.subAddOrUpdate.init()
      })
      /*this.$http.get(`/batch/forming/selectProductionProcess/`+subBatchId).then(({data: res}) => {
        if (res.code !== 0) {
          return this.$message.error(res.msg)
        }
        this.callFunction(res.data.id)

      })*/
    },
    callFunction (id,row) {
      if(row){
        this.data = row
      }
      this.$eventBus.$emit('batchIdChanged', this.dataForm.id)
      this.subAddOrUpdateVisible = true
      this.$nextTick(() => {
        this.$refs.subAddOrUpdate.dataForm.id = id
        if(id){
          this.$refs.subAddOrUpdate.informationVisible = true
          this.$refs.subAddOrUpdate.operate = false
          this.$refs.subAddOrUpdate.displayButton = false
        }else {
          console.log('新增=============')
          this.$refs.subAddOrUpdate.operate = true
          this.$refs.subAddOrUpdate.informationVisible = false
          this.$refs.subAddOrUpdate.displayButton = true
        }
          this.$refs.subAddOrUpdate.init()
      })
    },
    refreshPage() {
      window.location.reload()
    },
    subBatchNumber(id){
      this.$refs.queryBatchId = id
      this.$router.push({name: 'batch-subbatch',query:{batchId:id}})
    },
    beforeUpload(file) {
      this.message = this.$message({
        message: '上传中，请勿操作',
        center: true,
        duration: 0,
      });
    },
    handleChange(file, fileList) {
      this.fileList = fileList.slice(-3);

    },
    resultHandle(res) {
      if (res.code !== 0) {
        return this.$message.error(res.msg)
      }
      this.$message({
        message: this.$t('prompt.success'),
        type: 'success',
        duration: 500,
        onClose: () => {
          if (res.data) {
            this.unsuccessfulNameList = res.data
          }
          this.message.close()
          this.visible = false
          this.query()
        }
      })
    },
    dataFormSubmitHandle: debounce(function (batchNumber,subBatchNumber,rowData) {
      console.log(this.dataForm.technician,'技术员===============')
      console.log(subBatchNumber,'次批号===============')
      if(subBatchNumber==null){
        subBatchNumber=1
      }
      this.dataForm.batchNumber=batchNumber+'-'+subBatchNumber;
      this.dataForm.customerCode=rowData.customerCode,
          this.dataForm.manufacturingInstructions=rowData.manufacturingInstructions,
          this.dataForm.designation=rowData.designation,
          this.dataForm.formulationCode=rowData.formulationCode,
          this.dataForm.partCode=rowData.partCode,
          this.dataForm.weight=rowData.weight,
          this.dataForm.manufacturingTime=rowData.productionDate+' 00:00:00',
      // this.$refs.form.validate.
      this.$refs['dataForm'].validate((valid) => {
        if (!valid) {
          return false
        }
        if (!this.dataForm.batchNumber){
          this.$message({
            message:'请选择要打印的次批',
            type: 'warning',
            duration: 1000,
          })
          return ;
        }
        this.getPrintCount(); // 获取打印次数
        this.$confirm(`是否进行第${this.dataForm.printCount}次打印`, '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
          center: true
        }).then(() => {
          this.loadingPdf = true;
          console.log(this.dataForm.productionTime,'生产时间===============')
          this.$http['post']('/batch/subbatch/pdfGenerate', this.dataForm, { responseType: 'blob' } ).then(response  => {
            this.loadingPdf = false;
            const blob = new Blob([response.data], { type: 'application/pdf' });
            const url = window.URL.createObjectURL(blob);
            const newWindow = window.open(url); // 在新标签页打开文件

            const afterPrintListener = function () {
              this.loadingPdf = false;
              this.$message({
                type: 'success',
                message: '打印完成',
                duration: 500,
              });
              // 打印完成后更新打印次数
              this.getPrintCount();
              newWindow.removeEventListener('onafterprint',afterPrintListener)
            }.bind(this);


            newWindow.onload = function () {
              newWindow.print(); //  在新标签页打开的文件中执行打印操作
              window.URL.revokeObjectURL(url); // 释放 URL 对象
              newWindow.addEventListener('onafterprint', afterPrintListener);
            }
          }).catch(error => {
            this.loadingPdf = false;
            this.$message.error('PDF生成失败：' + error);
          })
        }).catch(() => {
          this.$message({
            type: 'info',
            message: '已取消'
          });
        });
      })
    }, 1000, {'leading': true, 'trailing': false}),

    // 查询本次为第几次打印
    getPrintCount(){
      this.$http.get(`/batch/subbatch/getPrintCount/${this.dataForm.batchNumber}`).then(({data: res}) =>{
        if (res.code !== 0) {
          return this.$message.error(res.msg)
        }
        this.dataForm.printCount = res.data+1
      })
    },
  }
}
</script>

<style scoped>
.input-with-select .el-input-group__prepend {
  background-color: #fff;
}
</style>
