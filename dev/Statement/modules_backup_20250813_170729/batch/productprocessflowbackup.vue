<template>
  <el-card shadow="never" class="aui-card--fill">
    <div class="mod-batch__productprocessflowbackup}">
      <el-form :inline="true" :model="dataForm" @keyup.enter.native="getDataList()">
        <el-form-item>
          <el-input v-model="dataForm.id" placeholder="id" clearable></el-input>
        </el-form-item>
        <el-form-item>
          <el-button @click="getDataList()">{{ $t('query') }}</el-button>
        </el-form-item>
        <el-form-item>
          <el-button type="info" @click="exportHandle()">{{ $t('export') }}</el-button>
        </el-form-item>
        <el-form-item>
          <el-button v-if="$hasPermission('batch:productprocessflowbackup:save')" type="primary" @click="addOrUpdateHandle()">{{ $t('add') }}</el-button>
        </el-form-item>
        <el-form-item>
          <el-button v-if="$hasPermission('batch:productprocessflowbackup:delete')" type="danger" @click="deleteHandle()">{{ $t('deleteBatch') }}</el-button>
        </el-form-item>
      </el-form>
      <el-table v-loading="dataListLoading" :data="dataList" border @selection-change="dataListSelectionChangeHandle" style="width: 100%;">
        <el-table-column type="selection" header-align="center" align="center" width="50"></el-table-column>
        <el-table-column prop="id" label="id" header-align="center" align="center"></el-table-column>
        <el-table-column prop="productInventoryId" label="库存总表id" header-align="center" align="center"></el-table-column>
        <el-table-column prop="batchId" label="批号id" header-align="center" align="center"></el-table-column>
        <el-table-column prop="subBatchId" label="次批号id" header-align="center" align="center"></el-table-column>
        <el-table-column prop="itemCategory" label="项目类别  0:场内(默认);1:转入;2:外加工;3:退货" header-align="center" align="center"></el-table-column>
        <el-table-column prop="transferUnit" label="转出单位" header-align="center" align="center"></el-table-column>
        <el-table-column prop="transferOutQuantity" label="转出数量" header-align="center" align="center"></el-table-column>
        <el-table-column prop="transferee" label="转出承人" header-align="center" align="center"></el-table-column>
        <el-table-column prop="receivingDepartment" label="接收部门" header-align="center" align="center"></el-table-column>
        <el-table-column prop="recipient" label="接收者(工号)" header-align="center" align="center"></el-table-column>
        <el-table-column prop="receiveQuantity" label="接收数量" header-align="center" align="center"></el-table-column>
        <el-table-column prop="receivingTime" label="接收时间(含时分)" header-align="center" align="center"></el-table-column>
        <el-table-column prop="storageLocation" label="存放位置" header-align="center" align="center"></el-table-column>
        <el-table-column prop="productCategory" label="产品货别 0:品制; 1:品检; 2:品修; 3:包装; 4:出货" header-align="center" align="center"></el-table-column>
        <el-table-column prop="remark" label="备注" header-align="center" align="center"></el-table-column>
        <el-table-column :label="$t('handle')" fixed="right" header-align="center" align="center" width="150">
          <template slot-scope="scope">
            <el-button v-if="$hasPermission('batch:productprocessflowbackup:update')" type="text" size="small" @click="addOrUpdateHandle(scope.row.id)">{{ $t('update') }}</el-button>
            <el-button v-if="$hasPermission('batch:productprocessflowbackup:delete')" type="text" size="small" @click="deleteHandle(scope.row.id)">{{ $t('delete') }}</el-button>
          </template>
        </el-table-column>
      </el-table>
      <el-pagination
        :current-page="page"
        :page-sizes="[10, 20, 50, 100]"
        :page-size="limit"
        :total="total"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="pageSizeChangeHandle"
        @current-change="pageCurrentChangeHandle">
      </el-pagination>
      <!-- 弹窗, 新增 / 修改 -->
      <add-or-update v-if="addOrUpdateVisible" ref="addOrUpdate" @refreshDataList="getDataList"></add-or-update>
    </div>
  </el-card>
</template>

<script>
import mixinViewModule from '@/mixins/view-module'
import AddOrUpdate from './productprocessflowbackup-add-or-update'
export default {
  mixins: [mixinViewModule],
  data () {
    return {
      mixinViewModuleOptions: {
        getDataListURL: '/batch/productprocessflowbackup/page',
        getDataListIsPage: true,
        exportURL: '/batch/productprocessflowbackup/export',
        deleteURL: '/batch/productprocessflowbackup',
        deleteIsBatch: true
      },
      dataForm: {
        id: ''
      }
    }
  },
  components: {
    AddOrUpdate
  }
}
</script>
