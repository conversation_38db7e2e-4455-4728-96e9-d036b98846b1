<template>
  <el-card shadow="never" class="aui-card--fill">
    <div class="mod-batch__processinspection}">
      <el-form :inline="true" :model="dataForm" @keyup.enter.native="getDataList()">
        <el-form-item>
          <el-input v-model="dataForm.paramStr" placeholder="次号查询" clearable @clear="onClear">
            <el-button slot="append" icon="el-icon-search" @click="getDataList()"></el-button>
          </el-input>
        </el-form-item>
        <el-form-item>
          <el-button v-if="$hasPermission('batch:processinspection:save')" type="primary" @click="addOrUpdateHandle()">{{ $t('add') }}</el-button>
        </el-form-item>
        <el-form-item>
          <el-select v-model="dataForm.temporary" @change="getDataList()">
            <el-option :value="0" label="已存"></el-option>
            <el-option :value="1" label="暂存"></el-option>
          </el-select>
        </el-form-item>
        <el-dropdown  style="float:right;">
          <el-button type="primary">
            更多功能<i class="el-icon-arrow-down el-icon--right"></i>
          </el-button>
          <el-dropdown-menu slot="dropdown">
            <el-dropdown-item>
              <el-form-item>
                <el-button type="info" @click="exportHandle()">{{ $t('export') }}</el-button>
              </el-form-item>
            </el-dropdown-item>
            <el-dropdown-item>
              <el-form-item>
                <el-button v-if="$hasPermission('batch:processinspection:delete')" type="danger" @click="deleteHandle()">{{ $t('deleteBatch') }}</el-button>
              </el-form-item>
            </el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown>
      </el-form>
      <el-table v-loading="dataListLoading" height="500px" :data="dataList" border @selection-change="dataListSelectionChangeHandle" style="width: 100%;">
        <el-table-column type="selection" header-align="center" align="center" width="50"></el-table-column>
        <el-table-column prop="subBatchNumber" fixed label="批号" header-align="center" align="center">
          <template slot-scope="scope">
            {{$getDictLabel('size_category',scope.row.sizeCategory) + scope.row.batchNumber}}
          </template>
        </el-table-column>
        <el-table-column prop="inspectors" label="检验员" header-align="center" align="center">
          <template slot-scope="scope">
            {{$getEmployeesList(scope.row.inspectors)}}
          </template>
        </el-table-column>
        <el-table-column prop="hardnessStandard" label="硬度标准" header-align="center" align="center"></el-table-column>
        <el-table-column prop="measuredHardness" label="实测硬度" header-align="center" align="center"></el-table-column>
        <el-table-column prop="exceptionDescription" label="异常说明" header-align="center" align="center"></el-table-column>
        <el-table-column prop="processingRecords" label="处理记录" header-align="center" align="center"></el-table-column>
        <el-table-column prop="determine" label="判定" header-align="center" align="center">
          <template slot-scope="scope">
            {{$getDictLabel('ok_ng',scope.row.determine)}}
          </template>
        </el-table-column>
        <el-table-column prop="samplingDate" label="抽检日期" header-align="center" align="center"></el-table-column>
        <el-table-column prop="remark" label="备注" header-align="center" align="center"></el-table-column>
        <el-table-column :label="$t('handle')" v-if="dataForm.temporary == 0" fixed="right" header-align="center" align="center" width="150">
          <template slot-scope="scope">
            <el-dropdown>
              <el-button type="primary">
                功能<i class="el-icon-arrow-down el-icon--right"></i>
              </el-button>
              <el-dropdown-menu slot="dropdown">
                <el-dropdown-item>
                  <el-button v-if="$hasPermission('batch:processinspection:update')" type="text" size="small" @click="addOrUpdateHandle(scope.row.id)">{{ $t('update') }}</el-button>
                </el-dropdown-item>
                <el-dropdown-item>
                  <el-button v-if="$hasPermission('batch:processinspection:delete')" type="text" size="small" @click="deleteHandle(scope.row.id)">{{ $t('delete') }}</el-button>
                </el-dropdown-item>
              </el-dropdown-menu>
            </el-dropdown>
          </template>
        </el-table-column>
      </el-table>
      <el-pagination
        :current-page="page"
        :page-sizes="[10, 20, 50, 100]"
        :page-size="limit"
        :total="total"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="pageSizeChangeHandle"
        @current-change="pageCurrentChangeHandle">
      </el-pagination>
      <!-- 弹窗, 新增 / 修改 -->
      <add-or-update v-if="addOrUpdateVisible" ref="addOrUpdate" @refreshDataList="getDataList"></add-or-update>
    </div>
  </el-card>
</template>

<script>
import mixinViewModule from '@/mixins/view-module'
import AddOrUpdate from './processinspection-add-or-update'
export default {
  mixins: [mixinViewModule],
  data () {
    return {
      mixinViewModuleOptions: {
        getDataListURL: '/batch/processinspection/page',
        getDataListIsPage: true,
        exportURL: '/batch/processinspection/export',
        deleteURL: '/batch/processinspection',
        deleteIsBatch: true
      },
      dataForm: {
        id: '',
        paramStr:'',
        temporary:0,
      }
    }
  },
  components: {
    AddOrUpdate
  },
  methods:{
    //清空后重新查询
    onClear(){
      this.query()
    },
  }
}
</script>
