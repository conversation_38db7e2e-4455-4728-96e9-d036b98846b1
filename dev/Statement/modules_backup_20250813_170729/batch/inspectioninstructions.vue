<template>
  <div class="mod-batch__inspectioninstructions}">
    <el-button v-if="$hasPermission('batch:inspectioninstructions:save')" :disabled="!permissionHiding" type="primary" icon="el-icon-plus" @click="addOrUpdateHandle()" circle></el-button>
    <span class="font_size" style="margin-left: 40%">
        生产进度：{{productionProgress}}%
    </span>
    <span class="font_size">
        预估完成度{{estimatedCompletion}}%
    </span>
    <el-table v-loading="dataListLoading" :data="dataList" border @selection-change="dataListSelectionChangeHandle" style="width: 100%;">
      <el-table-column type="selection" header-align="center" align="center" width="50">
        <template slot-scope="scope">
          <div>
            <!--已填写完成时显示-->
            <el-button type="success" icon="el-icon-check" style="font-size: 12px; padding: 6px;" circle></el-button>
          </div>
        </template>
      </el-table-column>
      <el-table-column prop="checkTheTime" fixed label="检查时间" header-align="center" align="center"></el-table-column>
      <el-table-column prop="state" fixed label="状态" header-align="center" align="center">
        <template slot-scope="scope">
          <div>
            {{$getDictLabel("check_status",scope.row.state)}}
          </div>
        </template>
      </el-table-column>
      <el-table-column prop="cumulativeModulus" label="累计模数" header-align="center" align="center"></el-table-column>
      <el-table-column prop="numberOfMoldHoles" label="当前孔数" header-align="center" align="center"></el-table-column>
      <el-table-column prop="bagWind" label="包风" header-align="center" align="center"></el-table-column>
      <el-table-column prop="bubbling" label="起泡" header-align="center" align="center"></el-table-column>
      <el-table-column prop="foreignBody" label="异物" header-align="center" align="center"></el-table-column>
      <el-table-column prop="deathPenalty" label="死料" header-align="center" align="center"></el-table-column>
      <el-table-column prop="lackOfMaterial" label="缺料" header-align="center" align="center"></el-table-column>
      <el-table-column prop="scratches" label="划痕" header-align="center" align="center"></el-table-column>
      <el-table-column prop="unfamiliar" label="不熟" header-align="center" align="center"></el-table-column>
      <el-table-column prop="desulfurization" label="脱硫" header-align="center" align="center"></el-table-column>
      <el-table-column prop="flowPattern" label="流纹" header-align="center" align="center"></el-table-column>
      <el-table-column prop="outOfShape" label="变形" header-align="center" align="center"></el-table-column>
      <el-table-column prop="other" label="其他" header-align="center" align="center"></el-table-column>
      <el-table-column prop="upperMold" label="上模" header-align="center" align="center"></el-table-column>
      <el-table-column prop="middleModel" label="中模" header-align="center" align="center"></el-table-column>
      <el-table-column prop="lowerMold" label="下模" header-align="center" align="center"></el-table-column>
      <el-table-column prop="vulcanizationTime" label="硫化时间" header-align="center" align="center"></el-table-column>
      <el-table-column prop="feedbackObject" label="反馈对象" header-align="center" align="center"></el-table-column>
      <el-table-column prop="illustrate" label="说明" header-align="center" align="center"></el-table-column>
      <el-table-column :label="$t('handle')" v-if="permissionHiding" fixed="right" header-align="center" align="center" width="100px">
        <template slot-scope="scope">
          <el-button v-if="$hasPermission('batch:inspectioninstructions:update')" type="primary" icon="el-icon-top" style="font-size: 12px; padding: 6px;" @click="addOrUpdateHandle(scope.row.id)" circle></el-button>
          <el-button v-if="$hasPermission('batch:inspectioninstructions:delete')" type="danger" icon="el-icon-minus" style="font-size: 12px; padding: 6px;" @click="deleteHandleView(scope.row.id,scope.row.subBatchId)" circle></el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 弹窗, 新增 / 修改 -->
    <add-or-update v-if="addOrUpdateVisible" ref="addOrUpdate" :object-information="objectInformation"  @calculatedValue="calculatedValue" :subBatchId="inputSubBatchId" @refreshDataList="getDataList"></add-or-update>
  </div>
</template>

<script>
import mixinViewModule from '@/mixins/view-module'
import AddOrUpdate from './inspectioninstructions-add-or-update'
export default {
  mixins: [mixinViewModule],
  props: {
    inputSubBatchId: {
      type:String,
      default:''
    },
    standardYield:{
      type:String,
      default:'',
    },
    dieHoleNumber:{
      type:String,
      default:''
    },
    objectInformation:{
      type:Object,
      required: true
    },
    permissionHiding:{
      type:Boolean,
      default:true
    }
  },
  data () {
    return {
      mixinViewModuleOptions: {
        getDataListURL: '/batch/inspectioninstructions/page',
        getDataListIsPage: true,
        exportURL: '/batch/inspectioninstructions/export',
        deleteURL: '/batch/inspectioninstructions',
        deleteIsBatch: true
      },
      productionProgress:0,
      estimatedCompletion:0,
      dataForm: {
        id: '',
        subBatchId:this.inputSubBatchId,
      },
      state:0,
    }
  },
  components: {
    AddOrUpdate
  },
  watch:{
    standardYield(newValue,oldValue){
      this.productionProgress = ((100/this.standardYield)*100)
    },
    inputSubBatchId(newValue,oldValue){
      this.dataForm.subBatchId = this.inputSubBatchId
      let count = 0
      const dataList = setInterval(()=>{
        count++
        this.getDataList()

        if(count >= 5){
          count = 0
          clearInterval(dataList)
        }
      },1000)
    }
  },
  methods:{
    calculatedValue(productionModulus,defectiveRate,productionQuantity,undesirable,productionPerHour){
      this.productionProgress = ((productionQuantity/this.standardYield)*100)
      let estimate = (((productionPerHour*10)/this.standardYield)*100)
      this.estimatedCompletion = estimate > 100 ? 100:estimate;
      this.$emit("calculatedValueFather",productionModulus,defectiveRate,productionQuantity,undesirable,this.productionProgress,this.estimatedCompletion)
    },
    deleteHandleView(id,subBatchId){
      this.deleteHandle(id)
      /*this.$http.get(`/batch/inspectioninstructions/getCalculate?subBatchId=`+subBatchId).then(({ data: res }) => {
        if (res.code !== 0) {
          return this.$message.error(res.msg)
        }
        this.$emit("calculatedValueFather",res.data.productionModulus,res.data.defectiveRate,res.data.actualCapacity,res.data.undesirable,productionPerHour)
      }).catch(() => {})*/
    },
    addOrUpdateHandle(id) {
      this.addOrUpdateVisible = true
      this.$nextTick(() => {
/*        this.queryIsThereAny(this.inputSubBatchId)*/
        this.$refs.addOrUpdate.dataForm.id = id
        this.$refs.addOrUpdate.dataForm.subBatchId=this.inputSubBatchId
        this.$refs.addOrUpdate.dataForm.state = this.state
        let s = this.formatTime(new Date(),this.inputSubBatchId);
        console.log(s,'时间是')
        this.$refs.addOrUpdate.dataForm.checkTheTime = s
        this.$refs.addOrUpdate.init()
      })
    },
    queryIsThereAny(subBatchId){
      this.$http.get(`/batch/inspectioninstructions/queryIsThereAny/${subBatchId}`).then(({data:res})=>{
        if (res.code !== 0) {
          return this.$message.error(res.msg)
        }
        if(res.data){
          this.state = 10
        }
      })
    },
    formatTime(date,subBatchId) {
      this.$http.get(`/batch/inspectioninstructions/maximumTimeQuery/`+subBatchId).then(({ data: res }) => {
        if (res.code !== 0) {
          return this.$message.error(res.msg)
        }
        if(res.data != []){
          let time = this.addTime(res.data);
          return time;
        }
      }).catch(() => {})
      let hours = date.getHours();
      let minutes = date.getMinutes();
      let seconds = date.getSeconds();
      hours = hours < 10 ? "0" + hours : hours;
      minutes = minutes < 10 ? "0" + minutes : minutes;
      seconds = seconds < 10 ? "0" + seconds : seconds;
      return hours + ":" + 0 + ":" + 0;
    },

  }
}
</script>

<style>

</style>
