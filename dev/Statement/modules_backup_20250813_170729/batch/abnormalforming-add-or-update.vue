<template>
  <el-dialog :visible.sync="visible" :title="!dataForm.id ? $t('add') : $t('update')" :close-on-click-modal="false" :close-on-press-escape="false" append-to-body>
    <el-form :model="dataForm" :rules="dataRule" ref="dataForm" @keyup.enter.native="dataFormSubmitHandle()" :label-width="$i18n.locale === 'en-US' ? '120px' : '80px'">
          <el-form-item label="次批号" prop="subBatchId">
          <el-input v-model="dataForm.subBatchId" placeholder="次批号"></el-input>
      </el-form-item>
          <el-form-item label="发生时间" prop="timeOfOccurrence">
          <el-input v-model="dataForm.timeOfOccurrence" placeholder="发生时间"></el-input>
      </el-form-item>
          <el-form-item label="原因分析" prop="causeAnalysis">
          <el-input v-model="dataForm.causeAnalysis" placeholder="原因分析"></el-input>
      </el-form-item>
          <el-form-item label="相片上传" prop="photoUpload">
          <el-input v-model="dataForm.photoUpload" placeholder="相片上传"></el-input>
      </el-form-item>
          <el-form-item label="改善对策" prop="improvementMeasures">
          <el-input v-model="dataForm.improvementMeasures" placeholder="改善对策"></el-input>
      </el-form-item>
          <el-form-item label="责任部门" prop="responsibleDepartment">
          <el-input v-model="dataForm.responsibleDepartment" placeholder="责任部门"></el-input>
      </el-form-item>
          <el-form-item label="责任者" prop="responsiblePerson">
          <el-input v-model="dataForm.responsiblePerson" placeholder="责任者"></el-input>
      </el-form-item>
          <el-form-item label="判定结果" prop="judgmentResults">
          <el-input v-model="dataForm.judgmentResults" placeholder="判定结果"></el-input>
      </el-form-item>
          <el-form-item label="备注" prop="remark">
          <el-input v-model="dataForm.remark" placeholder="备注"></el-input>
      </el-form-item>
                </el-form>
    <template slot="footer">
      <el-button @click="visible = false">{{ $t('cancel') }}</el-button>
      <el-button type="primary" @click="dataFormSubmitHandle()">{{ $t('confirm') }}</el-button>
    </template>
  </el-dialog>
</template>

<script>
import debounce from 'lodash/debounce'
export default {
  data () {
    return {
      visible: false,
      dataForm: {
        id: '',
        subBatchId: '',
        timeOfOccurrence: '',
        causeAnalysis: '',
        photoUpload: '',
        improvementMeasures: '',
        responsibleDepartment: '',
        responsiblePerson: '',
        judgmentResults: '',
        remark: '',
        disabled: '',
        creator: '',
        createDate: '',
        updater: '',
        updateDate: ''
      }
    }
  },
  computed: {
    dataRule () {
      return {
          id: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          subBatchId: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          timeOfOccurrence: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          causeAnalysis: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          photoUpload: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          improvementMeasures: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          responsibleDepartment: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          responsiblePerson: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          judgmentResults: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          remark: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
                }
    }
  },
  methods: {
    init () {
      this.visible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].resetFields()
        if (this.dataForm.id) {
          this.getInfo()
        }
      })
    },
    // 获取信息
    getInfo () {
      this.$http.get(`/batch/abnormalforming/${this.dataForm.id}`).then(({ data: res }) => {
        if (res.code !== 0) {
          return this.$message.error(res.msg)
        }
        this.dataForm = {
          ...this.dataForm,
          ...res.data
        }
      }).catch(() => {})
    },
    // 表单提交
    dataFormSubmitHandle: debounce(function () {
      this.$refs['dataForm'].validate((valid) => {
        if (!valid) {
          return false
        }
        this.$http[!this.dataForm.id ? 'post' : 'put']('/batch/abnormalforming/', this.dataForm).then(({ data: res }) => {
          if (res.code !== 0) {
            return this.$message.error(res.msg)
          }
          this.$message({
            message: this.$t('prompt.success'),
            type: 'success',
            duration: 500,
            onClose: () => {
              this.visible = false
              this.$emit('refreshDataList')
            }
          })
        }).catch(() => {})
      })
    }, 1000, { 'leading': true, 'trailing': false })
  }
}
</script>
