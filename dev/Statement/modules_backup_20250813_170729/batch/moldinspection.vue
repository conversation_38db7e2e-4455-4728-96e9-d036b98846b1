<template>
  <el-card shadow="never" class="aui-card--fill">
    <div class="mod-batch__moldinspection}">
      <el-form :inline="true" :model="dataForm" @keyup.enter.native="getDataList()">
        <el-form-item>
          <el-input v-model="dataForm.id" placeholder="id" clearable></el-input>
        </el-form-item>
        <el-form-item>
          <el-button @click="getDataList()">{{ $t('query') }}</el-button>
        </el-form-item>
        <el-form-item>
          <el-button type="info" @click="exportHandle()">{{ $t('export') }}</el-button>
        </el-form-item>
        <el-form-item>
          <el-button v-if="$hasPermission('batch:moldinspection:save')" type="primary" @click="addOrUpdateHandle()">{{ $t('add') }}</el-button>
        </el-form-item>
        <el-form-item>
          <el-button v-if="$hasPermission('batch:moldinspection:delete')" type="danger" @click="deleteHandle()">{{ $t('deleteBatch') }}</el-button>
        </el-form-item>
      </el-form>
      <el-table v-loading="dataListLoading" :data="dataList" border @selection-change="dataListSelectionChangeHandle" style="width: 100%;">
        <el-table-column type="selection" header-align="center" align="center" width="50"></el-table-column>
        <el-table-column prop="subBatchId" label="次批号" header-align="center" align="center"></el-table-column>
        <el-table-column prop="components" label="锁模螺丝、顶板、挂钩、耳朵无松动、脱落、变形、断裂等" header-align="center" align="center">
          <template slot-scope="scope">
            {{ $getDictLabel("normal_anomaly", scope.row.components) }}
          </template>
        </el-table-column>
        <el-table-column prop="cuttingMouth" label="模腔、切嘴无损伤(无模伤)、生锈等" header-align="center" align="center">
          <template slot-scope="scope">
            {{ $getDictLabel("normal_anomaly", scope.row.cuttingMouth) }}
          </template>
        </el-table-column>
        <el-table-column prop="moldCavityMoldFouling" label="模腔内无成形导致的模污，成形后无模污不良" header-align="center" align="center">
          <template slot-scope="scope">
            {{ $getDictLabel("normal_anomaly", scope.row.moldCavityMoldFouling) }}
          </template>
        </el-table-column>
        <el-table-column prop="guidePillarGuideSleeve" label="导柱导套无断、变形、抱死、脱落、松动、下沉且润滑良好" header-align="center" align="center">
          <template slot-scope="scope">
            {{ $getDictLabel("normal_anomaly", scope.row.guidePillarGuideSleeve) }}
          </template>
        </el-table-column>
        <el-table-column prop="mouldClosing" label="模具开合无异常/生产动作进行顺畅、无异声" header-align="center" align="center">
          <template slot-scope="scope">
            {{ $getDictLabel("normal_anomaly", scope.row.mouldClosing) }}
          </template>
        </el-table-column>
        <el-table-column prop="confirmationTime" label="确认时间" header-align="center" align="center"></el-table-column>
        <el-table-column :label="$t('handle')" fixed="right" header-align="center" align="center" width="150">
          <template slot-scope="scope">
            <el-button v-if="$hasPermission('batch:moldinspection:update')" type="text" size="small" @click="addOrUpdateHandle(scope.row.id)">{{ $t('update') }}</el-button>
            <el-button v-if="$hasPermission('batch:moldinspection:delete')" type="text" size="small" @click="deleteHandle(scope.row.id)">{{ $t('delete') }}</el-button>
          </template>
        </el-table-column>
      </el-table>
      <el-pagination
        :current-page="page"
        :page-sizes="[10, 20, 50, 100]"
        :page-size="limit"
        :total="total"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="pageSizeChangeHandle"
        @current-change="pageCurrentChangeHandle">
      </el-pagination>
      <!-- 弹窗, 新增 / 修改 -->
      <add-or-update v-if="addOrUpdateVisible" ref="addOrUpdate" @refreshDataList="getDataList"></add-or-update>
    </div>
  </el-card>
</template>

<script>
import mixinViewModule from '@/mixins/view-module'
import AddOrUpdate from './moldinspection-add-or-update'
export default {
  mixins: [mixinViewModule],
  data () {
    return {
      mixinViewModuleOptions: {
        getDataListURL: '/batch/moldinspection/page',
        getDataListIsPage: true,
        exportURL: '/batch/moldinspection/export',
        deleteURL: '/batch/moldinspection',
        deleteIsBatch: true
      },
      dataForm: {
        id: ''
      }
    }
  },
  components: {
    AddOrUpdate
  }
}
</script>
