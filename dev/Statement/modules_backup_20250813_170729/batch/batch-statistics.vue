
<template>
<div>
  <div>
    <el-drawer
    :visible.sync="drawer"
    class="drawer_d"
    title="成形生产统计"
    direction="ttb"
    size="100vh"
    >
    <div style="display: flex;align-items: center; margin-left: 3vw">
      <span style="font-size: x-large;font-weight: bold">选择时间范围:</span>
      <el-date-picker
          clearable
          @change="getDataList"
          v-model="dataFrom.dateTime"
          type="daterange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期">
      </el-date-picker>
      <el-select v-model="dataFrom.type" clearable @change="getDataList">
        <el-option v-for="item in searchOption"
                   :key="item.value"
                   :label="item.label"
                   :value="item.value">
        </el-option>
      </el-select>
    </div>  
    <div ref="main" style="width: 100vw; height: 75vh">
    </div>
    </el-drawer>
  </div>
</div>
</template>

<script>
import * as echarts from "echarts"
export default {
  name: "batch-statistics",
  data(){
    return{
      dataFrom:{
        type:'',
        dateTime:'',
      },
      drawer: false,
      dataX:[],
      dataY:[],
      searchOption:[
        {
          label:'硅胶',
          value:1,
        },
        {
          label:'橡胶',
          value:2,
        },
      ]
    }
  },
  methods:{
    init(){
      this.drawer = true
      this.$nextTick(()=>{
        this.initChart()
      })
    },

    //获取数据
    getDataList(){
      this.$http.get('batch/subbatch/getFormingStatistics',{params:{
        begin: this.dataFrom.dateTime[0],
        end: this.dataFrom.dateTime[1],
        type:this.dataFrom.type
        }}).then(({data: res}) =>{
        if (res.code !== 0) {
          return this.$message.error(res.msg)
        }
        this.dataX = res.data["1"]
        this.dataY = res.data["2"]
        this.initChart()
      })
    },

    initChart() {
      // 基于准备好的dom，初始化echarts实例
      const myChart = echarts.init(this.$refs.main);
      // 指定图表的配置项和数据
      const option = {
        tooltip: {},
        yAxis: {
          data: this.dataX,
          type: 'category'
        },
        xAxis: {
          type: 'value'
        },
        dataZoom: [
          {
            type: 'slider',
            yAxisIndex: 0,
            startValue: 0,
            endValue: this.dataX.length
          },
          {
            type: 'inside',
            yAxisIndex: 0
          }
        ],
        series: [{
          name: '成形数',
          type: 'bar',
          data: this.dataY,
          barWidth: '50%', // 可根据需要调整柱状图宽度
          label: {
            show: true,
            position: 'right', // 值显示在柱的尽头
            formatter: '{c}', // 显示数值
            fontSize: 12, // 字体大小，可以根据需要调整
            color: '#000' // 字体颜色，可以根据需要调整
          }
        }]
      };
      // 使用刚指定的配置项和数据显示图表。
      myChart.setOption(option);
    }

  }
}
</script>



<style scoped>

.drawer_d ::v-deep .el-drawer__header{
  text-align: center;
  font-size: xx-large;
  font-weight: bold;
}

</style>