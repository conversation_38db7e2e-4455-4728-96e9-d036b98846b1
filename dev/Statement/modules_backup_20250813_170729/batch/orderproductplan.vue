<template>
  <div>
    <div>
  <el-card shadow="never" class="aui-card--fill">
    <div class="mod-batch__orderproductplan}">
      <el-form :inline="true" :model="dataForm" @keyup.enter.native="getDataList()">
        <el-form-item>
          <el-tooltip content="请输入查询关键字" placement="top">
            <el-input v-model="dataForm.orderNumber" placeholder="查询关键字" @input="throttleFunction" clearable>
              <el-button slot="append" icon="el-icon-search" @click="getDataList()"></el-button>
            </el-input>
          </el-tooltip>
        </el-form-item>
        <el-form-item>
          <el-select @change="getDataList()" v-model="dataForm.status" clearable>
            <el-option v-for="item in statusOption"
                       :key="item.value"
                       :label="item.label"
                       :value="item.value">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button v-if="$hasPermission('batch:orderproductplan:save')" type="primary" @click="addOrUpdateHandle()">{{ $t('add') }}</el-button>
        </el-form-item>
        <el-dropdown  style="float:right;">
          <el-button type="primary">
            更多功能<i class="el-icon-arrow-down el-icon--right"></i>
          </el-button>
          <el-dropdown-menu slot="dropdown">
            <el-dropdown-item>
              <el-form-item>
                <el-button type="info" @click="exportHandle()">{{ $t('export') }}</el-button>
              </el-form-item>
            </el-dropdown-item>
            <el-dropdown-item>
              <el-form-item>
                <el-button v-if="$hasPermission('batch:orderproductplan:export')" type="info" @click="exportTemplateHandle()">
                  {{ $t('exportTemplate') }}
                </el-button>
              </el-form-item>
            </el-dropdown-item>
            <el-dropdown-item>
              <el-form-item>
                <el-tooltip content="下载模板填入信息后导入" placement="top">
                  <el-upload
                          v-if="$hasPermission('batch:orderproductplan:save')"
                          class="upload-demo"
                          :action="mixinViewModuleOptions.addBatchUrl"
                          :headers="headers"
                          :multiple="false"
                          :show-file-list="false"
                          :file-list="fileList"
                          :before-upload="beforeUpload"
                          :on-success="resultHandle"
                          :on-change="handleChange"
                          accept="'.xlsx','.xls'">
                    <el-button type="success">{{ $t('addBatch') }}</el-button>
                  </el-upload>
                </el-tooltip>
                <el-button type="text" v-show="unsuccessfulNameList" @click="dialogVisible = true">导入失败列表</el-button>
              </el-form-item>
            </el-dropdown-item>
            <el-dropdown-item>
              <el-form-item>
                <el-button v-if="$hasPermission('batch:orderproductplan:delete')" type="danger" @click="deleteHandle()">{{ $t('deleteBatch') }}</el-button>
              </el-form-item>
            </el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown>
      </el-form>
      <el-table v-loading="dataListLoading" height="500px" :data="dataList" border @selection-change="dataListSelectionChangeHandle" style="width: 100%;">
        <el-table-column type="selection" header-align="center" align="center" width="50"></el-table-column>
        <el-table-column prop="orderDate" label="下单时间" header-align="center" align="center"></el-table-column>
        <el-table-column prop="deliveryTime" label="订单交期" header-align="center" align="center"></el-table-column>
        <el-table-column prop="orderNumber" label="订单号" header-align="center" align="center">
          <template slot-scope="scope">
              <span @click="showCalendar(scope.row.orderNumber,scope.row.designation,scope.row)">{{scope.row.orderNumber}}</span>
          </template>
        </el-table-column>
        <el-table-column prop="designation" label="品名" header-align="center" align="center"></el-table-column>
        <el-table-column prop="orderQuantity" label="订单总数" header-align="center" align="center"></el-table-column>
        <el-table-column prop="moldName" label="模具名" header-align="center" align="center"></el-table-column>
        <el-table-column prop="formulationCode" label="配方代码" header-align="center" align="center"></el-table-column>
        <el-table-column prop="planStatus" label="状态" header-align="center" align="center">
          <template slot-scope="scope">
            {{scope.row.planStatus === 0 ? "未排产" : scope.row.planStatus === 1 ? "未排完" : "已排完" }}
          </template>
        </el-table-column>
        <el-table-column prop="planScheduleQuantity" label="计划排产数" header-align="center" align="center"></el-table-column>
        <el-table-column prop="productQuantity" label="实际生产数" header-align="center" align="center"></el-table-column>
        <el-table-column prop="productLackQuantity" label="生产订单欠数" header-align="center" align="center"></el-table-column>
        <el-table-column prop="finishingRate" label="达成率" header-align="center" align="center"></el-table-column>
        <el-table-column :label="$t('handle')" fixed="right" header-align="center" align="center" width="150">
          <template slot-scope="scope">
            <el-dropdown>
              <el-button type="primary">
                功能<i class="el-icon-arrow-down el-icon--right"></i>
              </el-button>
              <el-dropdown-menu slot="dropdown">
                <el-dropdown-item>
                  <el-button v-if="$hasPermission('batch:orderproductplan:update')" type="text" size="small" @click="addOrUpdateHandle(scope.row.id)">{{ $t('update') }}</el-button>
                </el-dropdown-item>
                <el-dropdown-item>
                  <el-button v-if="$hasPermission('batch:orderproductplan:delete')" type="text" size="small" @click="deleteHandle(scope.row.id)">{{ $t('delete') }}</el-button>
                </el-dropdown-item>
              </el-dropdown-menu>
            </el-dropdown>
          </template>
        </el-table-column>
      </el-table>
      <el-pagination
        :current-page="page"
        :page-sizes="[10, 20, 50, 100]"
        :page-size="limit"
        :total="total"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="pageSizeChangeHandle"
        @current-change="pageCurrentChangeHandle">
      </el-pagination>
      <!-- 弹窗, 新增 / 修改 -->
      <add-or-update v-if="addOrUpdateVisible" ref="addOrUpdate" @refreshDataList="getDataList"></add-or-update>
    </div>
  </el-card>
    </div>
    <div>
      <el-drawer class="drawer_a"
                 :title="drawerTitle"
                 :visible.sync="drawer"
                 direction="ttb"
                 size="60vh"
                 @close="drawerCloseHandle">
        <div>
          <div style="display: flex;position: fixed; top: 8vh">
            <div style="width: 12.5vw;display: flex;justify-content: flex-end;">
              <el-button @click="toToday" type="info"  class="custom-button">返回今天</el-button>
              <el-button @click="toOneMonthAgo" type="info"  class="custom-button">上一月</el-button>
            </div>
            <div style="width: 75vw;height: 5vh;display: flex; justify-content: center; border: 1px solid #35bb87; border-radius: 5px;background-color: #43deb5">
              <div v-for="item in a" style="width: 2.5vw;">
                <div @click="toAssignLate(item)" :style="returnDateStyle(item)">
                  <span style="font-size: x-large; font-weight: bold">{{item.getDate()}}</span>
                  <span style="font-size: large">{{getDayOfWeek(item)}}</span>
                </div>
              </div>
            </div>
            <div style="width: 12.5vw;">
              <el-button @click="toOneMonthLate" type="info" class="custom-button">下一月</el-button>
            </div>
          </div>
          <el-empty v-if="showEmpty" style="margin-top: 4.5vh" description="未添加排产计划" :image-size="200">
            <el-button @click="visible1 = true" type="primary">新增排产计划</el-button>
          </el-empty>
          <div style="margin-top: 4.5vh">
            <div v-for="(item,index1) in productPlanData" style="display: flex;align-items: center;">
              <div style="width: 12.5vw;height: 4vh; font-size: x-large;align-items: center; font-weight: bold; display: flex;justify-content: flex-end">{{item.machineCode}}:</div>
              <div @click="showDialogByDate(item,date)" v-for="(date,index2) in a">
                <div :style="returnStyleByDate(date,item,index2)"></div>
              </div>
            </div>
          </div>
        </div>
      </el-drawer>
    </div>
    <div>
      <el-dialog :visible.sync="visible" width="20vw" center :title="dialogByDateTitle" >
        <el-empty v-if="planDetailEmptylVisible" description="未排产"></el-empty>
        <div v-if="planDetailVisible" style="text-align: center">
          <el-descriptions  direction="horizontal" :column="1" border class="custom-descriptions">
            <el-descriptions-item label="批号">{{dialogByDateForm.batchNumber}}</el-descriptions-item>
            <el-descriptions-item label="计划">{{dialogByDateForm.productPlan}}</el-descriptions-item>
            <el-descriptions-item label="状态">{{dialogByDateForm.status}}</el-descriptions-item>
          </el-descriptions>
        </div>
      </el-dialog>
    </div>
    <div>
      <el-dialog :visible.sync="visible1" width="30vw" center title="新增排产计划">
        <el-form ref="productPlanForm" :model="productPlanForm" label-width="5vw">
          <el-form-item label="机台:">
          <el-select style="width: 19vw" @change="changeMachineCode" clearable v-model="productPlanForm.machineId">
            <el-option v-for="item in machineOptions"
             :key="item.value"
             :label="item.label"
             :value="item.value">
            </el-option>
          </el-select>
          </el-form-item>
          <el-form-item label="模具:">
            <el-select style="width: 19vw" @change="changeLimitingDateByCheckMold" clearable v-model="productPlanForm.moldId">
              <el-option v-for="item in moldOptions"
                         :key="item.value"
                         :label="item.label"
                         :value="item.value">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="排产日期:">
            <el-date-picker
                style="width: 19vw"
                v-model="productDate"
                type="daterange"
                range-separator="至"
                start-placeholder="起始日期"
                end-placeholder="结束日期"
                value-format="yyyy-MM-dd"
                @change="changeDate"
                :picker-options="pickerOptions">
            </el-date-picker>
          </el-form-item>
          <el-form-item v-if="startPlanVisible" label="起始计划">
            <div style="display: flex">
            <el-input @blur="showHanZiA" v-model="productPlanForm.startPlan" style="width: 9vw">
            </el-input>
            <div style="width: 9vw; text-align: center; font-weight: bold">{{startPlanHan}}</div>
            </div>
          </el-form-item>
          <el-form-item v-if="averagePlanVisible" label="日常计划">
            <div style="display: flex">
              <el-input @blur="showHanZiB" v-model="productPlanForm.averagePlan" style="width: 9vw">
              </el-input>
              <div style="width: 9vw; text-align: center; font-weight: bold">{{averagePlanHan}}</div>
            </div>
          </el-form-item>
          <el-form-item v-if="endPlanVisible" label="尾数计划">
            <div style="display: flex">
              <el-input @blur="showHanZiC" v-model="productPlanForm.endPlan" style="width: 9vw">
              </el-input>
              <div style="width: 9vw; text-align: center; font-weight: bold">{{endPlanHan}}</div>
            </div>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="dataFormSubmitHandle">立即创建</el-button>
            <el-button @click="visible1 = false">取消</el-button>
          </el-form-item>
        </el-form>
      </el-dialog>
    </div>
  </div>
</template>

<script>
import mixinViewModule from '@/mixins/view-module'
import AddOrUpdate from './orderproductplan-add-or-update'
import Cookies from "js-cookie";
import debounce from "lodash/debounce";
import fa from "element-ui/src/locale/lang/fa";
export default {
  mixins: [mixinViewModule],
  data () {
    return {
      message:'',
      visible: false,
      visible1: false,
      startPlanVisible: false,
      averagePlanVisible: false,
      endPlanVisible: false,
      planDetailEmptylVisible: false,
      planDetailVisible: false,
      headers: {'token': Cookies.get('token')},
      fileList: [{name: '', url: ''}],
      unsuccessfulNameList: '',//  导入失败名称列表
      mixinViewModuleOptions: {
        getDataListURL: '/batch/orderproductplan/page',
        getDataListIsPage: true,
        exportURL: '/batch/orderproductplan/export',
        deleteURL: '/batch/orderproductplan',
        deleteIsBatch: true,
        exportTemplateURL: '/batch/orderproductplan/export/template',
        addBatchUrl: window.SITE_CONFIG['apiURL'] + '/batch/orderproductplan/batchSave',
      },
      dataForm: {
        id: '',
        orderNumber: '',
        status:'',
      },
      statusOption:[
        {
          label:"未排产",
          value:0
        },
        {
          label:"未排完",
          value:1
        },
        {
          label:"已排完",
          value:2
        }
      ],
      startPlanHan:'',
      averagePlanHan:'',
      endPlanHan:'',
      showEmpty: false,
      dialogByDateTitle:'',
      dialogByDateForm:{
        batchId:'',
        batchNumber:'',
        productPlan:'',
        status:''
      },
      nowDate: this.getNowDate(),
      queryData:{
        dateValue: new Date(new Date().getFullYear(), new Date().getMonth(), new Date().getDate()),
        orderNumber: '',
        designation: '',
      },
      productPlanForm:{
        machineCode:'',
        machineId:'',
        moldId:'',
        moldCode:'',
        customerCode:'',
        orderNumber:'',
        partId:'',
        designation:'',
        type:'',
        formulationCode:'',
        productBeginTime:'',
        productEndTime:'',
        startPlan:'',
        averagePlan:'',
        endPlan:'',
      },
      productDate:'',
      productPlanData:[],
      a:[],
      //可选机台列表
      machineOptions:[],
      //可选模具列表
      moldOptions:[],
      drawer:false,
      drawerTitle:'',
      // 判断是否还在继续输入
      timer: null,
      limitingDate:'',
      pickerOptions: {
        disabledDate: (time) => {
          if(this.limitingDate) {
            // 指定的最早日期
            const startDate = new Date(this.limitingDate);
            const adjustedStartDate = new Date(startDate.getTime() + 24 * 60 * 60 * 1000);
            return time.getTime() < adjustedStartDate.getTime();
          }
            const startDate = new Date()
            return time.getTime() < startDate.getTime();
        }
      }
    }
  },
  components: {
    AddOrUpdate
  },
  methods:{
    showCalendar(orderNumber,designation,data){
      this.queryData.orderNumber = orderNumber
      this.queryData.designation = designation
      this.productPlanForm.customerCode = data.customerCode
      this.productPlanForm.orderNumber = data.orderNumber
      this.productPlanForm.partId = data.partId
      this.productPlanForm.designation = data.designation
      this.productPlanForm.type = data.type
      this.productPlanForm.formulationCode = data.formulationCode
      this.a = this.getDaysInMonth(this.queryData.dateValue)
      const firstDay = this.a[0];
      const endDay = this.a[29];
      this.drawerTitle = this.queryData.orderNumber + '(' + this.queryData.designation + ')' + firstDay.getFullYear() + '年' + (firstDay.getMonth() + 1) + '月' + firstDay.getDate() +
          '日~' + endDay.getFullYear() + '年' + (endDay.getMonth() + 1) + '月' + endDay.getDate() + '日排产计划'
      this.getMachineProductPlan(firstDay,endDay,this.queryData.orderNumber,this.queryData.designation);
      this.getMachineDataList()
      this.getMoldDataList()
      this.drawer = true;
    },
    //跳转至上一月
    toOneMonthAgo(){
      this.queryData.dateValue.setMonth(this.queryData.dateValue.getMonth() - 1)
      this.a = this.getDaysInMonth(this.queryData.dateValue)
      const firstDay = this.a[0];
      const endDay = this.a[29];
      this.drawerTitle = this.queryData.orderNumber + '(' + this.queryData.designation + ')' + firstDay.getFullYear() + '年' + (firstDay.getMonth() + 1) + '月' + firstDay.getDate() +
          '日~' + endDay.getFullYear() + '年' + (endDay.getMonth() + 1) + '月' + endDay.getDate() + '日排产计划'
      this.getMachineProductPlan(firstDay,endDay,this.queryData.orderNumber,this.queryData.designation);
    },
    //返回当前时间
    getNowDate(){
      return new Date(new Date().getFullYear(), new Date().getMonth(), new Date().getDate())
    },
    //查看指定日期计划
    showDialogByDate(item,date){
      this.dialogByDateTitle = '(' + this.queryData.designation + ')' + (date.getMonth() + 1) + '月' + date.getDate()  + '日计划'
      //判断该天是否有数据
      let filter = item.data.filter(item => new Date(item.date).getTime() === date.getTime());
      if(filter.length === 0){
        this.planDetailEmptylVisible = true
        this.planDetailVisible = false
      }else {
        this.planDetailEmptylVisible = false
        this.planDetailVisible = true
        this.dialogByDateForm.batchId = filter[0].batchId
        this.dialogByDateForm.batchNumber = filter[0].batchNumber
        this.dialogByDateForm.productPlan = filter[0].productPlan
        this.dialogByDateForm.status = filter[0].status
      }
      this.visible = true
    },
    //返回至当天
    toToday(){
      this.queryData.dateValue = new Date(this.nowDate)
      this.a = this.getDaysInMonth(this.queryData.dateValue)
      const firstDay = this.a[0];
      const endDay = this.a[29];
      this.drawerTitle = this.queryData.orderNumber + '(' + this.queryData.designation + ')' + firstDay.getFullYear() + '年' + (firstDay.getMonth() + 1) + '月' + firstDay.getDate() +
          '日~' + endDay.getFullYear() + '年' + (endDay.getMonth() + 1) + '月' + endDay.getDate() + '日排产计划'
      this.getMachineProductPlan(firstDay,endDay,this.queryData.orderNumber,this.queryData.designation);
    },
    //跳转至下一月
    toOneMonthLate(){
      this.queryData.dateValue.setMonth(this.queryData.dateValue.getMonth() + 1)
      this.a = this.getDaysInMonth(this.queryData.dateValue)
      const firstDay = this.a[0];
      const endDay = this.a[29];
      this.drawerTitle = this.queryData.orderNumber + '(' + this.queryData.designation + ')' + firstDay.getFullYear() + '年' + (firstDay.getMonth() + 1) + '月' + firstDay.getDate() +
          '日~' + endDay.getFullYear() + '年' + (endDay.getMonth() + 1) + '月' + endDay.getDate() + '日排产计划'
      this.getMachineProductPlan(firstDay,endDay,this.queryData.orderNumber,this.queryData.designation);
    },
    //跳转至指定日期的下一月
    toAssignLate(date){
      this.queryData.dateValue = date
      this.a = this.getDaysInMonth(date)
      const firstDay = this.a[0];
      const endDay = this.a[29];
      this.drawerTitle = this.queryData.orderNumber + '(' + this.queryData.designation + ')' + firstDay.getFullYear() + '年' + (firstDay.getMonth() + 1) + '月' + firstDay.getDate() +
          '日~' + endDay.getFullYear() + '年' + (endDay.getMonth() + 1) + '月' + endDay.getDate() + '日排产计划'
      this.getMachineProductPlan(firstDay,endDay,this.queryData.orderNumber,this.queryData.designation);
    },
    //获取机台排产计划
    getMachineProductPlan(startDate,endDate,orderNumber,designation){
      this.$http.get('batch/formingproductplan/getOrderProductPlan',{
        params:{
          startDate: startDate,
          endDate: endDate,
          orderNumber: orderNumber,
          designation: designation
        }
      }).then(({data: res}) =>{
        if(res.code !== 0){
          return this.$message.error(res.msg)
        }
        if(res.data.length === 0){
          this.showEmpty = true
        }else {
          this.showEmpty = false
        }
        this.productPlanData = res.data
      })
    },
    //获取指定日期到30天后的日期
    getDaysInMonth(currentDate) {
      // 创建一个数组，存储从当前日期到30天后的每一天的Date对象
      const dateArray = [];
      for (let i = 0; i < 30; i++) {
        // 创建一个新的Date对象，代表未来的某一天
        const futureDate = new Date(currentDate);
        // 增加天数
        futureDate.setDate(currentDate.getDate() + i);
        // 将Date对象直接添加到数组中
        dateArray.push(futureDate);
      }
      return dateArray;
    },
    //根据给定的值返回日期的样式
    returnDateStyle(date){
      let style = {
        display: "flex",
        justifyContent: "center",
        alignItems: "center",
        height: "100%",
        border: "1px solid #219f88",
        background: ''
      }
      const dayOfWeek = date.getDay();
      let nowDate = new Date(new Date().getFullYear(), new Date().getMonth(), new Date().getDate());
      let backgroundColor;
      if (dayOfWeek === 0) {
        // 周末
        backgroundColor = '#6c6ce8'; // 蓝色
      } else if(nowDate.getTime() === date.getTime()){
        // 当天
        backgroundColor = '#e1f811'; // 蓝色
      }else {
        // 周一到周六
        backgroundColor = '#43deb5'; // 绿色
      }
      style.background = backgroundColor
      return style
    },
    //根据给定的值返回对应样式
    returnStyleByDate(dateData,item,index){
      let style = {
        width: '2.5vw',
        height: '4vh',
        border: '1px solid rgba(8,46,63,0.48)',
        background:''
      }
      //判断该天是否有数据
      let filter = item.data.filter(item => new Date(item.date).getTime() === dateData.getTime());
      if(filter.length !== 0){
        console.log(filter[0].status)
        // style.background = filter[0].status === 0 ? '#4741ea' : '#0fa270'
        style.background = '#097c55'
      }
      return style
    },
    //计算两个日期相减的天数
    getDaysDiff(date1, date2) {
      // 将日期对象转换为毫秒数，然后计算差值
      let timeDiff = Math.abs(date1.getTime() - date2.getTime());
      // 将毫秒数转换为天数并返回
      return Math.ceil(timeDiff / (1000 * 3600 * 24));
    },
    getDayOfWeek(date) {
      let days = ['日', '一', '二', '三', '四', '五', '六'];
      return days[date.getDay()];
    },
    drawerCloseHandle(){
      this.productPlanForm = {
        machineCode:'',
        machineId:'',
        moldId:'',
        moldCode:'',
        customerCode:'',
        orderNumber:'',
        partId:'',
        designation:'',
        type:'',
        formulationCode:'',
        productBeginTime:'',
        productEndTime:'',
        startPlan:'',
        averagePlan:'',
        endPlan:'',
      }
      this.startPlanHan = ''
      this.averagePlanHan = ''
      this.endPlanHan = ''
      this.productDate = ''
      this.limitingDate = ''
      this.queryData.dateValue = new Date(new Date().getFullYear(), new Date().getMonth(), new Date().getDate())
      this.startPlanVisible = false
      this.averagePlanVisible = false
      this.endPlanVisible = false
      this.getDataList()
    },
    /**
     * 数字转成汉字
     * @params num === 要转换的数字
     * @return 汉字
     * */
    toChinesNum(num) {
      let changeNum = ['零', '一', '二', '三', '四', '五', '六', '七', '八', '九']
      let unit = ['', '十', '百', '千', '万']
      num = parseInt(num)
      let getWan = (temp) => {
        let strArr = temp.toString().split('').reverse()
        let newNum = ''
        let newArr = []
        strArr.forEach((item, index) => {
          newArr.unshift(item === '0' ? changeNum[item] : changeNum[item] + unit[index])
        })
        let numArr = []
        newArr.forEach((m, n) => {
          if (m !== '零') numArr.push(n)
        })
        if (newArr.length > 1) {
          newArr.forEach((m, n) => {
            if (newArr[newArr.length - 1] === '零') {
              if (n <= numArr[numArr.length - 1]) {
                newNum += m
              }
            } else {
              newNum += m
            }
          })
        } else {
          newNum = newArr[0]
        }

        return newNum
      }
      let overWan = Math.floor(num / 10000)
      let noWan = num % 10000
      if (noWan.toString().length < 4) {
        noWan = '0' + noWan
      }
      return overWan ? getWan(overWan) + '万' + getWan(noWan) : getWan(num)
    },
    showHanZiA(){
      if(this.productPlanForm.startPlan){
        this.startPlanHan = this.toChinesNum(this.productPlanForm.startPlan)
      }
    },
    showHanZiB(){
      if(this.productPlanForm.averagePlan){
        this.averagePlanHan = this.toChinesNum(this.productPlanForm.averagePlan)
      }
    },
    showHanZiC(){
      if(this.productPlanForm.endPlan){
        this.endPlanHan = this.toChinesNum(this.productPlanForm.endPlan)
      }
    },
    //选取日期时触发
    changeDate(){
      if(this.productDate){
        this.productPlanForm.productBeginTime = this.productDate[0]
        this.productPlanForm.productEndTime = this.productDate[1]
        // 计算两个日期之间的天数
        const beginTime = new Date(this.productPlanForm.productBeginTime);
        const endTime = new Date(this.productPlanForm.productEndTime);
        // 计算日期差异，注意：getTime()返回的时间是毫秒数
        const timeDiff = endTime.getTime() - beginTime.getTime();
        // 将毫秒数转换为天数，并加1
        const daysDiff = Math.ceil(timeDiff / (1000 * 3600 * 24)) + 1;
        if(daysDiff === 1){
          this.startPlanVisible = true
          this.averagePlanVisible = false
          this.endPlanVisible = false
        }else if(daysDiff === 2){
          this.startPlanVisible = true
          this.averagePlanVisible = true
          this.endPlanVisible = false
        }else {
          this.startPlanVisible = true
          this.averagePlanVisible = true
          this.endPlanVisible = true
        }
      }else {
        this.startPlanVisible = false
        this.averagePlanVisible = false
        this.endPlanVisible = false
      }
    },
    //提交生产计划排程
    dataFormSubmitHandle: debounce(function () {
        this.$http.post('batch/formingproductplan/saveProductPlan',this.productPlanForm).then(({data: res}) =>{
          if (res.code !== 0) {
            return this.$message.error(res.msg)
          }
          this.visible1 = false
          const firstDay = this.a[0];
          const endDay = this.a[29];
          this.getMachineProductPlan(firstDay,endDay,this.queryData.orderNumber,this.queryData.designation);
        })
    }, 1000, { 'leading': true, 'trailing': false }),
    //获取机台列表
    getMachineDataList(){
      this.$http.get(`batch/formingmachinevariation/getMachineListByPartId/${this.productPlanForm.partId}`).then(({data: res}) =>{
        if (res.code !== 0) {
          return this.$message.error(res.msg)
        }
        this.machineOptions = res.data.map((obj) => {
          return{
            label: obj.machineCode + "(" + (obj.productTime === null ? '暂无排产' : obj.productTime.substring(0, 10)) + ')',
            value: obj.machineId,
            data: obj
          }
        })
      })
    },
    //获取模具列表
    getMoldDataList(){
      this.$http.get(`fabricate/mold/getMoldListByProduct/${this.productPlanForm.partId}`).then(({data: res}) =>{
        if (res.code !== 0) {
          return this.$message.error(res.msg)
        }
        this.moldOptions = res.data.map((obj) => {
          return{
            label: obj.designation + "(" + obj.moldGroup + ')' + '(' + (obj.productTime === null ? '暂无排产' : obj.productTime.substring(0, 10)) + ')',
            value: obj.id,
            data: obj
          }
        })
      })
    },
    //选取机台时获取机台代码
    changeMachineCode(){
      if(this.productPlanForm.machineId){
        let find = this.machineOptions.find(item => item.value === this.productPlanForm.machineId);
        this.productPlanForm.machineCode = find.data.machineCode
        let date1 = new Date(find.data.productTime);
        let date2 = this.limitingDate === '' ? new Date() : new Date(this.limitingDate)
        console.log(date1)
        console.log(date2)
        if(date1.getTime() > date2.getTime()){
          this.limitingDate = new Date(date1)
        }
      }else {
        if(this.productPlanForm.moldId){
          let find = this.moldOptions.find(item => item.value === this.productPlanForm.moldId);
          this.limitingDate = find.data.productTime
        }else {
          this.limitingDate = ''
        }
      }
    },
    //选取模具时获取最新可排产时间
    changeLimitingDateByCheckMold(){
      if(this.productPlanForm.moldId){
        let find = this.moldOptions.find(item => item.value === this.productPlanForm.moldId);
        let date1 = new Date(find.data.productTime);
        let date2 = this.limitingDate === '' ? new Date() : new Date(this.limitingDate)
        console.log(date1)
        console.log(date2)
        if(date1.getTime() > date2.getTime()){
          this.limitingDate = new Date(date1)
        }
      }else {
        if(this.productPlanForm.machineId){
          let find = this.machineOptions.find(item => item.value === this.productPlanForm.moldId);
          this.limitingDate = find.data.productTime
        }else {
          this.limitingDate = ''
        }
      }
    },
    //节流函数
    throttleFunction() {
      // 如果timer不为空就表明在持续输入,每次改变都清除定时，这样就可以实现只输入最后一次结果(注意每次间隔如果超过500毫秒就会输出)
      if (this.timer !== null) clearTimeout(this.timer)
      this.timer = setTimeout(() => {
        this.getDataList()
      }, 500)
    },
    beforeUpload(file) {
      this.message = this.$message({
        message: '上传中，请勿操作',
        center: true,
        duration: 0,
      });
    },
    handleChange(file, fileList) {
      this.fileList = fileList.slice(-3);

    },
    resultHandle(res) {
      if (res.code !== 0) {
      this.message.close()
      return this.$message.error(res.msg)
      }
      this.message.close()
      this.$message({
        message: this.$t('prompt.success'),
        type: 'success',
        duration: 500,
        onClose: () => {
          if (res.data) {
            this.unsuccessfulNameList = res.data
          }
          this.visible = false
          this.query()
        }
      })
    },
  }
}
</script>
<style scoped>
.drawer_a ::v-deep .el-drawer__header{
  text-align: center;
  font-size: xx-large;
  color: #093891;
  font-weight: bold;
}

.custom-button {
  width: 6vw;
  height: 5vh;
}

.a-el-tooltip ::v-deep .el-tooltip__content {
  font-size: 16px; /* 调整为所需大小 */
  font-weight: bold; /* 设置为粗体 */
}


/* 覆盖表格单元格样式以居中文字 */
.custom-descriptions ::v-deep .el-descriptions__body table td,
.custom-descriptions ::v-deep .el-descriptions__body table th {
  text-align: center; /* 居中文字 */
  font-size: large; /* 修改文字大小 */
}
</style>
