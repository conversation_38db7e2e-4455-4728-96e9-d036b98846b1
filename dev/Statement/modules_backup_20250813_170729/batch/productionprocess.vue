<template>
  <el-card shadow="never" class="aui-card--fill">
    <div class="mod-batch__productionprocess}">
      <el-form :inline="true" :model="dataForm" @keyup.enter.native="getDataList()">
        <el-form-item>
          <el-input v-model="dataForm.paramStr" placeholder="查询关键字" @input="throttleFunction" clearable>
            <el-button slot="append" icon="el-icon-search" @click="getDataList()"></el-button>
          </el-input>
        </el-form-item>
        <el-form-item label="生产机器">
          <machine-component v-model="dataForm.machineId" placeholder="请选择生产机器"></machine-component>
        </el-form-item>
        <el-form-item label="生产批号">
          <batch-under-component v-model="dataForm.subBatchId" :display-or-not="false" placeholder="请选择批号"></batch-under-component>
        </el-form-item>
        <el-form-item label="部门">
          <ren-dept-tree v-model="dataForm.deptId" :placeholder="$t('dept.title')"  :query="true"></ren-dept-tree>
        </el-form-item>
        <el-form-item label="技术员">
          <employee-component v-model="dataForm.technician" :default-value="false" placeholder="请选择技术员"></employee-component>
        </el-form-item>
      </el-form>
      <el-table v-loading="dataListLoading" :data="dataList" border @selection-change="dataListSelectionChangeHandle" style="width: 100%;">
        <el-table-column prop="batchNumber" label="批号" header-align="center" align="center">
          <template slot-scope="scope">
            <div>
              {{$getDictLabel('size_category',scope.row.sizeCategory) + scope.row.batchNumber +'-'+scope.row.subBatchNumber}}
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="machineId" label="机台编号" header-align="center" align="center"></el-table-column>
        <el-table-column prop="" label="品名编号" header-align="center" align="center"></el-table-column>
        <el-table-column prop="designation" label="客户品名" header-align="center" align="center"></el-table-column>
        <el-table-column label="操作员" header-align="center" align="center">
          <template slot-scope="scope">
            {{ $getEmployeesList(scope.row.technician)}}
          </template>
        </el-table-column>
        <el-table-column prop="productionQuantity" label="目前生产数" header-align="center" align="center"></el-table-column>
        <el-table-column prop="productionProgress" label="进度%" header-align="center" align="center"></el-table-column>
        <el-table-column prop="estimatedCompletion" label="预估达成进度%" header-align="center" align="center"></el-table-column>
        <el-table-column prop="defectiveRate" label="不良率" header-align="center" align="center"></el-table-column>
        <el-table-column prop="productStatus" label="生产状态" header-align="center" align="center">
          <template slot-scope="scope">
            <div>
              {{$getDictLabel('management_category',scope.row.productStatus)}}
            </div>
          </template>
        </el-table-column>
        <el-table-column label="新增记录、查询明细" fixed="right" header-align="center" align="center" width="150">
          <template slot-scope="scope">
            <el-dropdown>
              <el-button type="primary">
                功能<i class="el-icon-arrow-down el-icon--right"></i>
              </el-button>
              <el-dropdown-menu slot="dropdown">
                <el-dropdown-item>
                  <el-button v-if="$hasPermission('batch:subbatch:save')" type="text" size="small" @click="addOrUpdateHandle(scope.row)">新增记录</el-button>
                </el-dropdown-item>
                <el-dropdown-item>
                  <el-button type="text" size="small" @click="callFunction(scope.row.batchId,scope.row.formingId)">查询明细</el-button>
                </el-dropdown-item>
              </el-dropdown-menu>
            </el-dropdown>
          </template>
        </el-table-column>
      </el-table>
      <el-pagination
        :current-page="page"
        :page-sizes="[10, 20, 50, 100]"
        :page-size="limit"
        :total="total"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="pageSizeChangeHandle"
        @current-change="pageCurrentChangeHandle">
      </el-pagination>
      <!-- 弹窗, 新增 / 修改 -->
      <add-or-update v-if="addOrUpdateVisible" ref="addOrUpdate" :permission-hiding="false" @refreshDataList="getDataList"></add-or-update>
      <InspectioninstructionsAddOrUpdate v-if="inspectioninstructionsVisible" ref="addInspectioninstructions" :object-information="data" @refreshDataList="getDataList"></InspectioninstructionsAddOrUpdate>
    </div>
  </el-card>
</template>

<script>
import mixinViewModule from '@/mixins/view-module'
import AddOrUpdate from "./production-process-add-or-update.vue";
import InspectioninstructionsAddOrUpdate from "./inspectioninstructions-add-or-update.vue";
import MachineComponent from "../../../components/shared-search/machine-component/src/machine-component.vue";
import fa from "element-ui/src/locale/lang/fa";
export default {
  computed: {
    fa() {
      return fa
    }
  },
  mixins: [mixinViewModule],
  data () {
    return {
      mixinViewModuleOptions: {
        getDataListURL: '/batch/subbatch/queryProductionProcess',
        getDataListIsPage: true,
      },
      dataForm: {
        id: '',
        paramStr: '',
        subBatchId:'',
        machineId:'',
        technician:'',
        deptId:'',
      },
      data:'',
      addOrUpdateVisible:false,
      inspectioninstructionsVisible:false,
      state:0,
    }
  },
  components: {
    MachineComponent,
    InspectioninstructionsAddOrUpdate,
    AddOrUpdate,
  },
  watch:{
    "dataForm.subBatchId"(){
      this.getDataList()
    },
    "dataForm.machineId"(){
      this.getDataList()
    },
    "dataForm.technician"(){
      this.getDataList()
    },
    "dataForm.deptId"(){
      this.getDataList()
    }
  },
  methods:{
    addOrUpdateHandle(row){
      this.data = row
      /*
      this.inspectioninstructionsVisible = true
      this.$nextTick(()=>{
        this.queryIsThereAny(row.subBatchId)
        this.$refs.addInspectioninstructions.dataForm.state = this.state
        this.$refs.addInspectioninstructions.dataForm.checkTheTime = this.formatTime(new Date(),row.subBatchId)
        this.$refs.addInspectioninstructions.init()
      })*/
      this.$router.push({name:'inspectioninstructions-h5',query:{objectInformation:this.data}})
    },
    queryIsThereAny(subBatchId){
      this.$http.get(`/batch/inspectioninstructions/queryIsThereAny/${subBatchId}`).then(({data:res})=>{
        if (res.code !== 0) {
          return this.$message.error(res.msg)
        }
        if(res.data){
           this.state = 10
        }
      })
    },
    callFunction (batchId,formingId) {
      this.addOrUpdateVisible = true
      this.$nextTick(() => {
        this.$refs.addOrUpdate.dataForm.id = formingId
        this.$refs.addOrUpdate.informationVisible = true
        this.$refs.addOrUpdate.displayButton = false
        this.$refs.addOrUpdate.init()
      })
    },
    //节流函数
    throttleFunction() {
      // 如果timer不为空就表明在持续输入,每次改变都清除定时，这样就可以实现只输入最后一次结果(注意每次间隔如果超过500毫秒就会输出)
      if (this.timer !== null) clearTimeout(this.timer)
      this.timer = setTimeout(() => {
        this.getDataList()
      }, 500)
    },
    //清空后重新查询
    onClear(){
      this.query()
    },
    formatTime(date,subBatchId) {
      this.$http.get(`/batch/inspectioninstructions/maximumTimeQuery/`+subBatchId).then(({ data: res }) => {
        if (res.code !== 0) {
          return this.$message.error(res.msg)
        }
        if(res.data != []){
          let time = this.addTime(res.data);
          return time;
        }
      }).catch(() => {})

      let hours = date.getHours();
      let minutes = date.getMinutes();
      let seconds = date.getSeconds();
      hours = hours < 10 ? "0" + hours : hours;
      minutes = minutes < 10 ? "0" + minutes : minutes;
      seconds = seconds < 10 ? "0" + seconds : seconds;
      return hours + ":" + 0 + ":" + 0;
    },
  }


}
</script>
