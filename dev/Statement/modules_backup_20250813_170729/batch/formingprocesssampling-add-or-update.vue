<template>
  <el-dialog :visible.sync="visible" :title="!dataForm.id ? '新增成型制程抽检' : '修改成型制程抽检'" :close-on-click-modal="false" :close-on-press-escape="false" append-to-body>
    <el-form :model="dataForm" :rules="dataRule" ref="dataForm" @keyup.enter.native="dataFormSubmitHandle()" :label-width="$i18n.locale === 'en-US' ? '120px' : '80px'">
      <div class="container">
        <el-form-item label="抽检日期" prop="samplingDate">
          <el-date-picker
              class="datePicker"
              v-model="dataForm.samplingDate"
              type="date"
              value-format="yyyy-MM-dd"
              placeholder="抽检日期">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="次批号" prop="subBatchId">
          <batch-under-component v-model="dataForm.subBatchId" placeholder="抽检次批号"></batch-under-component>
        </el-form-item>
        <el-form-item label="抽检员" prop="sampler">
          <employee-component v-model="dataForm.sampler" placeholder="抽检员"></employee-component>
        </el-form-item>
        <el-form-item label="判定" prop="determine">
          <ren-radio-group v-model="dataForm.determine" placeholder="判定 0:OK;1:NG;3:特采" dict-type="specialty"></ren-radio-group>
        </el-form-item>
        <el-form-item label="抽检数量" prop="samplingQuantity">
          <el-input-number :controls="false" v-model="dataForm.samplingQuantity" placeholder="抽检数量"></el-input-number>
        </el-form-item>
        <el-form-item label="不良数" prop="unqualifiedQuantity">
          <el-input-number :controls="false" v-model="dataForm.unqualifiedQuantity" placeholder="不合格数量"></el-input-number>
        </el-form-item>
        <el-form-item label="包风" prop="bagWind">
          <el-input-number :controls="false" v-model="dataForm.bagWind" placeholder="包风"></el-input-number>
        </el-form-item>
        <el-form-item label="死料" prop="deathPenalty">
          <el-input-number :controls="false" v-model="dataForm.deathPenalty" placeholder="死料"></el-input-number>
        </el-form-item>
        <el-form-item label="染黄" prop="chromatin">
          <el-input-number :controls="false" v-model="dataForm.chromatin" placeholder="染黄"></el-input-number>
        </el-form-item>
        <el-form-item label="破裂" prop="rupture">
          <el-input-number :controls="false" v-model="dataForm.rupture" placeholder="破裂"></el-input-number>
        </el-form-item>
        <el-form-item label="压毛边" prop="burr">
          <el-input-number :controls="false" v-model="dataForm.burr" placeholder="压毛边"></el-input-number>
        </el-form-item>
        <el-form-item label="缺料" prop="lackOfMaterial">
          <el-input-number :controls="false" v-model="dataForm.lackOfMaterial" placeholder="缺料"></el-input-number>
        </el-form-item>
        <el-form-item label="异常说明" prop="exceptionDescription">
          <el-input v-model="dataForm.exceptionDescription" placeholder="异常说明"></el-input>
        </el-form-item>
        <el-form-item label="开异常单" prop="openException">
          <el-input v-model="dataForm.openException" placeholder="开异常单(编号)"></el-input>
        </el-form-item>
        <el-form-item label="处理记录" prop="processingRecords">
          <ren-radio-group v-model="dataForm.processingRecords" placeholder="处理记录" dict-type="ok_ng"></ren-radio-group>
        </el-form-item>
        <el-form-item label="硬度标准" prop="distillate">
          <el-input v-model="dataForm.distillate" placeholder="硬度标准"></el-input>
        </el-form-item>
        <el-form-item label="实测硬度" prop="measuredHardness">
          <el-input v-model="dataForm.measuredHardness" placeholder="实测硬度"></el-input>
        </el-form-item>
      </div>
      <el-form-item label="备注" prop="remark">
        <el-input v-model="dataForm.remark" placeholder="备注"></el-input>
      </el-form-item>
    </el-form>
    <template slot="footer">
      <el-button @click="visible = false">{{ $t('cancel') }}</el-button>
      <el-button type="primary" @click="dataFormSubmitHandle()">{{ $t('confirm') }}</el-button>
    </template>
  </el-dialog>
</template>

<script>
import debounce from 'lodash/debounce'
import mixinViewModule from '@/mixins/view-module'
export default {
  mixins:[mixinViewModule],
  data () {
    return {
      visible: false,
      //创建该页面时，不调用数据查询接口
      mixinViewModuleOptions: {
        createdIsNeed: false,
      },
      dataForm: {
        id: '',
        samplingDate: this.formatDates(),
        subBatchId: '',
        sampler: '',
        determine: '',
        samplingQuantity: 0,
        unqualifiedQuantity: 0,
        bagWind: 0,
        deathPenalty: 0,
        chromatin: 0,
        rupture: 0,
        burr: 0,
        lackOfMaterial: 0,
        exceptionDescription: '',
        openException: '',
        processingRecords: '',
        distillate: '',
        measuredHardness: '',
        remark: '',
        disabled: '',
        creator: '',
        createDate: '',
        updater: '',
        updateDate: ''
      }
    }
  },
  computed: {
    dataRule () {
      return {
          subBatchId: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        processingRecords: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        determine: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        sampler: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
      }
    }
  },
  methods: {
    init () {
      this.visible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].resetFields()
        if (this.dataForm.id) {
          this.getInfo()
        }
      })
    },
    // 获取信息
    getInfo () {
      this.$http.get(`/batch/formingprocesssampling/${this.dataForm.id}`).then(({ data: res }) => {
        if (res.code !== 0) {
          return this.$message.error(res.msg)
        }
        this.dataForm = {
          ...this.dataForm,
          ...res.data
        }
      }).catch(() => {})
    },
    // 表单提交
    dataFormSubmitHandle: debounce(function () {
      this.$refs['dataForm'].validate((valid) => {
        if (!valid) {
          return false
        }
        this.$http[!this.dataForm.id ? 'post' : 'put']('/batch/formingprocesssampling/', this.dataForm).then(({ data: res }) => {
          if (res.code !== 0) {
            return this.$message.error(res.msg)
          }
          this.$message({
            message: this.$t('prompt.success'),
            type: 'success',
            duration: 500,
            onClose: () => {
              this.visible = false
              this.$emit('refreshDataList')
            }
          })
        }).catch(() => {})
      })
    }, 1000, { 'leading': true, 'trailing': false })
  }
}
</script>
