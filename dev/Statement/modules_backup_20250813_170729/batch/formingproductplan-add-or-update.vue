<template>
  <el-dialog :visible.sync="visible" :title="!dataForm.id ? $t('add') : $t('update')" :close-on-click-modal="false" :close-on-press-escape="false" append-to-body>
    <el-form :model="dataForm" :rules="dataRule" ref="dataForm" @keyup.enter.native="dataFormSubmitHandle()" :label-width="$i18n.locale === 'en-US' ? '120px' : '80px'">
      <div class="container">
                                                <el-form-item label="机台id" prop="machineId">
              <el-input v-model="dataForm.machineId" placeholder="机台id"></el-input>
            </el-form-item>
                                        <el-form-item label="客户代码" prop="customerCode">
              <el-input v-model="dataForm.customerCode" placeholder="客户代码"></el-input>
            </el-form-item>
                                        <el-form-item label="制造指令" prop="manufacturingInstructions">
              <el-input v-model="dataForm.manufacturingInstructions" placeholder="制造指令"></el-input>
            </el-form-item>
                                        <el-form-item label="订单号" prop="orderNumber">
              <el-input v-model="dataForm.orderNumber" placeholder="订单号"></el-input>
            </el-form-item>
                                        <el-form-item label="品番id" prop="partId">
              <el-input v-model="dataForm.partId" placeholder="品番id"></el-input>
            </el-form-item>
                                        <el-form-item label="品名" prop="designation">
              <el-input v-model="dataForm.designation" placeholder="品名"></el-input>
            </el-form-item>
                                        <el-form-item label="交期" prop="deliveryTime">
              <el-input v-model="dataForm.deliveryTime" placeholder="交期"></el-input>
            </el-form-item>
                                        <el-form-item label="配方代码" prop="formulationCode">
              <el-input v-model="dataForm.formulationCode" placeholder="配方代码"></el-input>
            </el-form-item>
                                        <el-form-item label="批号id" prop="batchId">
              <el-input v-model="dataForm.batchId" placeholder="批号id"></el-input>
            </el-form-item>
                                        <el-form-item label="批号" prop="batchNumber">
              <el-input v-model="dataForm.batchNumber" placeholder="批号"></el-input>
            </el-form-item>
                                        <el-form-item label="生产状态(0:正常 1:待上机)" prop="productStatis">
              <el-input v-model="dataForm.productStatis" placeholder="生产状态(0:正常 1:待上机)"></el-input>
            </el-form-item>
                                        <el-form-item label="计划" prop="productPlan">
              <el-input v-model="dataForm.productPlan" placeholder="计划"></el-input>
            </el-form-item>
                                                                                                                  </div>
    </el-form>
    <template slot="footer">
      <el-button @click="visible = false">{{ $t('cancel') }}</el-button>
      <el-button type="primary" @click="dataFormSubmitHandle()">{{ $t('confirm') }}</el-button>
    </template>
  </el-dialog>
</template>

<script>
import debounce from 'lodash/debounce'
import mixinViewModule from '@/mixins/view-module'
export default {
  mixins:[mixinViewModule],
  data () {
    return {
      visible: false,
      //创建该页面时，不调用数据查询接口
      mixinViewModuleOptions: {
        createdIsNeed: false,
      },
      dataForm: {
        id: '',
        machineId: '',
        customerCode: '',
        manufacturingInstructions: '',
        orderNumber: '',
        partId: '',
        designation: '',
        deliveryTime: '',
        formulationCode: '',
        batchId: '',
        batchNumber: '',
        productStatis: '',
        productPlan: '',
        disabled: '',
        creator: '',
        createDate: '',
        updater: '',
        updateDate: ''
      }
    }
  },
  computed: {
    dataRule () {
      return {
          id: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          machineId: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          customerCode: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          manufacturingInstructions: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          orderNumber: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          partId: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          designation: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          deliveryTime: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          formulationCode: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          batchId: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          batchNumber: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          productStatis: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          productPlan: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
                }
    }
  },
  methods: {
    init () {
      this.visible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].resetFields()
        if (this.dataForm.id) {
          this.getInfo()
        }
      })
    },
    // 获取信息
    getInfo () {
      this.$http.get(`/batch/formingproductplan/${this.dataForm.id}`).then(({ data: res }) => {
        if (res.code !== 0) {
          return this.$message.error(res.msg)
        }
        this.dataForm = {
          ...this.dataForm,
          ...res.data
        }
      }).catch(() => {})
    },
    // 表单提交
    dataFormSubmitHandle: debounce(function () {
      this.$refs['dataForm'].validate((valid) => {
        if (!valid) {
          return false
        }
        let message = '';
        if(this.dataForm.id){
          message = '此操作将修改该数据'
        }else {
          message = '此操作将新增一条数据'
        }
        this.$confirm(message+', 是否继续?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
          center: true
        }).then(() => {
          this.$http[!this.dataForm.id ? 'post' : 'put']('/batch/formingproductplan/', this.dataForm).then(({ data: res }) => {
            if (res.code !== 0) {
            return this.$message.error(res.msg)
            }
            this.$message({
          message: this.$t('prompt.success'),
            type: 'success',
                    duration: 500,
                    onClose: () => {
              this.visible = false
              this.$emit('refreshDataList')
            }
          })
          }).catch(() => {})
        }).catch(() => {
          this.$message({
            type: 'info',
            message: '已取消'
          });
        });
      })
    }, 1000, { 'leading': true, 'trailing': false })
  }
}
</script>
