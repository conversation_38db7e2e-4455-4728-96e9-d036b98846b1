<template>
  <div>
    <div>
  <el-card shadow="never" class="aui-card--fill">
    <div class="mod-batch__formingmachinevariation}">
      <el-form :inline="true" :model="dataForm" @keyup.enter.native="getDataList()">
        <el-form-item>
          <el-tooltip content="请输入机台代码" placement="top">
            <el-input v-model="dataForm.machineCode" placeholder="查询关键字" @input="throttleFunction" clearable>
              <el-button slot="append" icon="el-icon-search" @click="getDataList()"></el-button>
            </el-input>
          </el-tooltip>
        </el-form-item>
        <el-form-item>
          <el-select @change="getDataList" clearable v-model="dataForm.deptId">
            <el-option v-for="item in deptOption"
                       :key="item.value"
                       :label="item.label"
                       :value="item.value">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button v-if="$hasPermission('batch:formingmachinevariation:save')" type="primary" @click="addOrUpdateHandle()">{{ $t('add') }}</el-button>
<!--          <el-button type="info" @click="queryPrintInfo">查询打印机</el-button>-->
        </el-form-item>
<!--                <el-form-item>-->
<!--                  <el-button @click="showMachineProductStatus" type="primary">机台生产现况图示</el-button>-->
<!--                </el-form-item>-->
<!--        <el-form-item>-->
<!--          <el-button @click="showMachineProduct" type="primary">机台排产总况</el-button>-->
<!--        </el-form-item>-->
        <el-form-item>
          <el-button @click="showCarousel" type="info">机台生产现况</el-button>
        </el-form-item>
        <el-form-item>
          <el-button @click="showStatistics" type="primary">生产统计</el-button>
        </el-form-item>
        <el-dropdown  style="float:right;">
          <el-button type="primary">
            更多功能<i class="el-icon-arrow-down el-icon--right"></i>
          </el-button>
          <el-dropdown-menu slot="dropdown">
            <el-dropdown-item>
              <el-form-item>
                <el-button type="info" @click="exportHandle()">{{ $t('export') }}</el-button>
              </el-form-item>
            </el-dropdown-item>
            <el-dropdown-item>
              <el-form-item>
                <el-button v-if="$hasPermission('batch:formingmachinevariation:export')" type="info" @click="exportTemplateHandle()">
                  {{ $t('exportTemplate') }}
                </el-button>
              </el-form-item>
            </el-dropdown-item>
            <el-dropdown-item>
              <el-form-item>
                <el-tooltip content="下载模板填入信息后导入" placement="top">
                  <el-upload
                          v-if="$hasPermission('batch:formingmachinevariation:save')"
                          class="upload-demo"
                          :action="mixinViewModuleOptions.addBatchUrl"
                          :headers="headers"
                          :multiple="false"
                          :show-file-list="false"
                          :file-list="fileList"
                          :before-upload="beforeUpload"
                          :on-success="resultHandle"
                          accept="'.xlsx','.xls'">
                    <el-button type="success">{{ $t('addBatch') }}</el-button>
                  </el-upload>
                </el-tooltip>
                <el-button type="text" v-show="unsuccessfulNameList" @click="dialogVisible = true">导入失败列表</el-button>
              </el-form-item>
            </el-dropdown-item>
            <el-dropdown-item>
              <el-form-item>
                <el-button v-if="$hasPermission('batch:formingmachinevariation:delete')" type="danger" @click="deleteHandle()">{{ $t('deleteBatch') }}</el-button>
              </el-form-item>
            </el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown>
      </el-form>
      <el-table v-loading="dataListLoading" height="750px" ref="tableData" :data="dataList" border @selection-change="dataListSelectionChangeHandle" style="width: 100%;">
        <el-table-column type="selection" header-align="center" align="center" width="50"></el-table-column>
<!--        <el-table-column prop="id" label="id" header-align="center" align="center"></el-table-column>-->
        <el-table-column prop="deptName" label="部门" header-align="center" align="center"></el-table-column>
<!--        <el-table-column prop="machineId" label="机台id" header-align="center" align="center"></el-table-column>-->
        <el-table-column prop="machineName" label="机台名称" header-align="center" align="center" width="150"></el-table-column>
        <el-table-column prop="machineCode" label="机台代码"header-align="center" align="center" width="134">
<!--          <template slot-scope="scope">-->
<!--            <span @click="showCalendar(scope.row.machineCode)">{{scope.row.machineCode}}</span>-->
<!--          </template>-->
          <template slot-scope="scope">
            <span>{{scope.row.machineCode}}</span>
          </template>
        </el-table-column>
        <el-table-column prop="machineStatus" label="机台状态" header-align="center" align="center">
          <template slot-scope="scope">

          </template>
        </el-table-column>
<!--        <el-table-column prop="machineStatusExplain" label="机台状态说明" header-align="center" align="center"></el-table-column>-->
<!--        <el-table-column prop="partId" label="品番id" header-align="center" align="center"></el-table-column>-->
<!--        <el-table-column prop="partCode" label="品号编码" header-align="center" align="center"></el-table-column>-->
        <el-table-column prop="designation" label="客户品号" header-align="center" align="center" width="150"></el-table-column>
<!--        <el-table-column prop="customerCode" label="客户代码" header-align="center" align="center"></el-table-column>-->
<!--        <el-table-column prop="moduleId" label="模组id" header-align="center" align="center"></el-table-column>-->
        <el-table-column prop="moldCode" label="模组编码" header-align="center" align="center"></el-table-column>
<!--        <el-table-column prop="subBatchId" label="当前生产批次id" header-align="center" align="center"></el-table-column>-->
        <el-table-column prop="subBatchNumber" label="当前生产批次号" header-align="center" align="center" width="130"></el-table-column>
        <el-table-column prop="accumulate" label="本次批累积模数" header-align="center" align="center"></el-table-column>
<!--        <el-table-column prop="exceptionCount" label="异常次数" header-align="center" align="center"></el-table-column>-->
        <el-table-column prop="operatorOne" label="作业员" header-align="center" align="center" width="150">
          <template slot-scope="scope">
              {{$getEmployeesList(scope.row.operatorOne)}}
          </template>
        </el-table-column>
<!--        <el-table-column prop="operatorTwo" label="操作员2" header-align="center" align="center"></el-table-column>-->
<!--        <el-table-column prop="upperMouldTime" label="上模时间" header-align="center" align="center"></el-table-column>-->
<!--        <el-table-column prop="upperMouldOperator" label="上模人员" header-align="center" align="center"></el-table-column>-->
<!--        <el-table-column prop="predictMouldUnloadingTime" label="预计下模时间" header-align="center" align="center"></el-table-column>-->
<!--        <el-table-column prop="moldProductionQuantity" label="模具生产总累计数" header-align="center" align="center"></el-table-column>-->
        <el-table-column prop="remark" label="备注" header-align="center" align="center"></el-table-column>
<!--        <el-table-column prop="creator" label="创建者" header-align="center" align="center"></el-table-column>-->
<!--        <el-table-column prop="createDate" label="创建时间" header-align="center" align="center"></el-table-column>-->
<!--        <el-table-column prop="updater" label="更新者" header-align="center" align="center"></el-table-column>-->
<!--        <el-table-column prop="updateDate" label="更新时间" header-align="center" align="center"></el-table-column>-->
<!--        <el-table-column prop="disabled" label="是否失效 0:有效; 1:失效 (逻辑删除)" header-align="center" align="center"></el-table-column>-->
        <el-table-column :label="$t('handle')" fixed="right" header-align="center" align="center" width="150">
          <template slot-scope="scope">
            <el-dropdown>
              <el-button type="primary">
                功能<i class="el-icon-arrow-down el-icon--right"></i>
              </el-button>
              <el-dropdown-menu slot="dropdown">
                <el-dropdown-item>
                  <el-button v-if="$hasPermission('batch:formingmachinevariation:update')" @click="clearVariation(scope.row)" type="text" size="small" >清空稼动</el-button>
                </el-dropdown-item>
                <el-dropdown-item>
                  <el-button v-if="$hasPermission('batch:formingmachinevariation:delete')" type="text" size="small" @click="deleteHandle(scope.row.id)">{{ $t('delete') }}</el-button>
                </el-dropdown-item>
              </el-dropdown-menu>
            </el-dropdown>
          </template>
        </el-table-column>
      </el-table>
      <el-pagination
        :current-page="page"
        :page-sizes="[10, 20, 50, 100]"
        :page-size="limit"
        :total="total"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="pageSizeChangeHandle"
        @current-change="pageCurrentChangeHandle">
      </el-pagination>
      <!-- 弹窗, 新增 / 修改 -->
      <add-or-update v-if="addOrUpdateVisible" ref="addOrUpdate" @refreshDataList="getDataList"></add-or-update>
      <machine-status v-if="visible2" ref="machineStatus"></machine-status>
      <batch-statistics v-if="visible3" ref="batchStatistics"></batch-statistics>
      <machine-product-drawer v-if="visible1" ref="productDrawer"></machine-product-drawer>
      <forming-carousel v-if="visible4" ref="formingCarousel"></forming-carousel>
    </div>
  </el-card>
    </div>
    <div>
      <el-drawer class="drawer_a"
          :title="drawerTitle"
          :visible.sync="drawer"
          direction="ttb"
          size="60vh">
        <div>
          <div style="display: flex;position: fixed; top: 8vh">
            <div style="width: 12.5vw;display: flex;justify-content: flex-end;">
              <el-button @click="toToday" type="info"  class="custom-button">返回今天</el-button>
              <el-button @click="toOneMonthAgo" type="info"  class="custom-button">上一月</el-button>
            </div>
          <div style="width: 75vw;height: 5vh;display: flex; justify-content: center; border: 1px solid #35bb87;">
            <div v-for="item in a" style="width: 2.5vw;">
              <div @click="toAssignLate(item)" :style="returnDateStyle(item)">
                <span style="font-size: x-large; font-weight: bold">{{item.getDate()}}</span>
                <span style="font-size: large">{{getDayOfWeek(item)}}</span>
              </div>
            </div>
          </div>
            <div style="width: 12.5vw;">
              <el-button @click="toOneMonthLate" type="info" class="custom-button">下一月</el-button>
            </div>
          </div>
          <el-empty v-if="showEmpty" style="margin-top: 4.5vh" description="无排产计划" :image-size="200"></el-empty>
          <div style="margin-top: 4.5vh">
            <div v-for="(item,index1) in productPlanData" style="display: flex;align-items: center;">
              <div style="width: 12.5vw;height: 4vh; font-size: x-large;align-items: center; font-weight: bold; display: flex;justify-content: flex-end">{{item.designation}}:</div>
              <div @click="showDialogByDate(item,date)" v-for="(date,index2) in a">
                <div :style="returnStyleByDate(date,item,index2)"></div>
              </div>
            </div>
          </div>
        </div>
      </el-drawer>
    </div>
    <div>
      <el-dialog :visible.sync="visible" width="20vw" center :title="dialogByDateTitle" >
          <el-empty v-if="planDetailEmptylVisible" description="未排产"></el-empty>
        <div v-if="planDetailVisible" style="text-align: center">
          <el-descriptions  direction="horizontal" :column="1" border class="custom-descriptions">
            <el-descriptions-item label="批号">{{dialogByDateForm.batchNumber}}</el-descriptions-item>
            <el-descriptions-item label="计划">{{dialogByDateForm.productPlan}}</el-descriptions-item>
            <el-descriptions-item label="状态">{{dialogByDateForm.status}}</el-descriptions-item>
          </el-descriptions>
        </div>
      </el-dialog>
    </div>
  </div>
</template>

<script>
import mixinViewModule from '@/mixins/view-module'
import AddOrUpdate from './formingmachinevariation-add-or-update'
import FormingCarousel from "@/views/modules/batch/forming-carousel.vue";
import MachineProductDrawer from "@/views/modules/batch/machine-product-drawer.vue";
import MachineStatus from "@/views/modules/batch/machineStatus.vue";
import BatchStatistics from "@/views/modules/batch/batch-statistics.vue";
import Cookies from "js-cookie";

export default {
  mixins: [mixinViewModule],
  data () {
    return {
      message:'',
      visible: false,
      visible1:false,
      visible2:false,
      visible3:false,
      visible4:false,
      planDetailEmptylVisible: false,
      planDetailVisible: false,
      headers: {'token': Cookies.get('token')},
      fileList: [{name: '', url: ''}],
      unsuccessfulNameList: '',//  导入失败名称列表
      mixinViewModuleOptions: {
        getDataListURL: '/batch/formingmachinevariation/page',
        getDataListIsPage: true,
        exportURL: '/batch/formingmachinevariation/export',
        deleteURL: '/batch/formingmachinevariation',
        deleteIsBatch: true,
        exportTemplateURL: '/batch/formingmachinevariation/export/template',
        addBatchUrl: window.SITE_CONFIG['apiURL'] + '/batch/formingmachinevariation/batchSave',
      },
      dataForm: {
        id: '',
        machineCode: '',
        deptId:''
      },
      dialogByDateForm:{
          batchId:'',
          batchNumber:'',
          productPlan:'',
          status:''
      },
      showEmpty: false,
      productPlanData:[],
      a:[],
      deptOption:[
        {
          label:'制一部',
          value:'1641701467113046018',
        },
        {
          label: '制三部',
          value: '1641701649229725698'
        }
      ],
      drawerTitle:'',
      dialogByDateTitle:'',
      nowDate: this.getNowDate(),
      queryData:{
        dateValue: new Date(new Date().getFullYear(), new Date().getMonth(), new Date().getDate()),
        machineCode: '',
      },
      drawer:false,
      // 判断是否还在继续输入
      timer: null,
    }
  },
  components: {
    MachineProductDrawer,
    FormingCarousel,
    BatchStatistics,
    MachineStatus,
    AddOrUpdate
  },
  activated() {
    console.log("触发了")
    this.$nextTick(() => {
      if (this.$refs.tableData) {
        this.$refs.tableData.doLayout();
      }
    });
  },
  methods:{
    //查看机台总生产情况
    showMachineProduct(){
      this.visible1 = true
      this.$nextTick(() => {
        this.$refs.productDrawer.init()
      })
    },
    //清空机台稼动
    clearVariation(row){
      this.$confirm('是否清除机台' + row.machineCode + '稼动信息', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$http.put(`batch/formingmachinevariation/clearVariationInfo/${row.id}`).then(({data: res}) =>{
          if (res.code !== 0) {
            return this.$message.error(res.msg)
          }
        this.$message({
          type: 'success',
          message: '清除成功!'
        });
          this.query()
        })
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '已取消'
        });
      });
    },
    //查看生产统计
    showStatistics(){
      this.visible3 = true
      this.$nextTick(() =>{
        this.$refs.batchStatistics.init()
      })
    },
    //查看生产统计
    showCarousel(){
      this.visible4 = true
      this.$nextTick(() =>{
        this.$refs.formingCarousel.init()
      })
    },
    showMachineProductStatus(){
      this.visible2 = true
      this.$nextTick(() =>{
        this.$refs.machineStatus.init()
      })
    },
    //查看排产计划
    showCalendar(val){
      this.queryData.machineCode = val
      this.a = this.getDaysInMonth(this.queryData.dateValue)
      const firstDay = this.a[0];
      const endDay = this.a[29];
      this.drawerTitle = val + '机台' + firstDay.getFullYear() + '年' + (firstDay.getMonth() + 1) + '月' + firstDay.getDate() +
          '日~' + endDay.getFullYear() + '年' + (endDay.getMonth() + 1) + '月' + endDay.getDate() + '日排产计划'
      this.getMachineProductPlan(firstDay,endDay,val);
      this.drawer = true;
    },
    //查看指定日期计划
    showDialogByDate(item,date){
      this.dialogByDateTitle = '(' + item.designation + ')' + (date.getMonth() + 1) + '月' + date.getDate()  + '日计划'
      //判断该天是否有数据
      let filter = item.data.filter(item => new Date(item.date).getTime() === date.getTime());
      if(filter.length === 0){
        this.planDetailEmptylVisible = true
        this.planDetailVisible = false
      }else {
        this.planDetailEmptylVisible = false
        this.planDetailVisible = true
        this.dialogByDateForm.batchId = filter[0].batchId
        this.dialogByDateForm.batchNumber = filter[0].batchNumber
        this.dialogByDateForm.productPlan = filter[0].productPlan
        this.dialogByDateForm.status = filter[0].status
      }
      this.visible = true
    },
    getDayOfWeek(date) {
      let days = ['日', '一', '二', '三', '四', '五', '六'];
      return days[date.getDay()];
    },
    //获取机台排产计划
    getMachineProductPlan(startDate,endDate,machineCode){
      console.log(startDate,endDate)
      this.$http.get('batch/formingproductplan/getMachineProductPlan',{
        params:{
          startDate: startDate,
          endDate: endDate,
          machineCode : machineCode
        }
      }).then(({data: res}) =>{
        if(res.code !== 0){
          return this.$message.error(res.msg)
        }
        if(res.data.length === 0){
          this.showEmpty = true
        }else {
          this.showEmpty = false
        }
        this.productPlanData = res.data
      })
    },
    //节流函数
    throttleFunction() {
      // 如果timer不为空就表明在持续输入,每次改变都清除定时，这样就可以实现只输入最后一次结果(注意每次间隔如果超过500毫秒就会输出)
      if (this.timer !== null) clearTimeout(this.timer)
      this.timer = setTimeout(() => {
        this.getDataList()
      }, 500)
    },
    //显示日
    showDay(date){
      return date.getDate()
    },
    //返回当前时间
    getNowDate(){
      return new Date(new Date().getFullYear(), new Date().getMonth(), new Date().getDate())
    },
    //返回至当天
    toToday(){
      this.queryData.dateValue = new Date(this.nowDate)
      this.a = this.getDaysInMonth(this.queryData.dateValue)
      const firstDay = this.a[0];
      const endDay = this.a[29];
      this.drawerTitle = this.queryData.machineCode + '机台' + firstDay.getFullYear() + '年' + (firstDay.getMonth() + 1) + '月' + firstDay.getDate() +
          '日~' + endDay.getFullYear() + '年' + (endDay.getMonth() + 1) + '月' + endDay.getDate() + '日排产计划'
      this.getMachineProductPlan(firstDay,endDay,this.queryData.machineCode);
    },
    //跳转至上一月
    toOneMonthAgo(){
      this.queryData.dateValue.setMonth(this.queryData.dateValue.getMonth() - 1)
      this.a = this.getDaysInMonth(this.queryData.dateValue)
      const firstDay = this.a[0];
      const endDay = this.a[29];
      this.drawerTitle = this.queryData.machineCode + '机台' + firstDay.getFullYear() + '年' + (firstDay.getMonth() + 1) + '月' + firstDay.getDate() +
          '日~' + endDay.getFullYear() + '年' + (endDay.getMonth() + 1) + '月' + endDay.getDate() + '日排产计划'
      this.getMachineProductPlan(firstDay,endDay,this.queryData.machineCode);
    },
    //跳转至下一月
    toOneMonthLate(){
      this.queryData.dateValue.setMonth(this.queryData.dateValue.getMonth() + 1)
      this.a = this.getDaysInMonth(this.queryData.dateValue)
      const firstDay = this.a[0];
      const endDay = this.a[29];
      this.drawerTitle = this.queryData.machineCode + '机台' + firstDay.getFullYear() + '年' + (firstDay.getMonth() + 1) + '月' + firstDay.getDate() +
          '日~' + endDay.getFullYear() + '年' + (endDay.getMonth() + 1) + '月' + endDay.getDate() + '日排产计划'
      this.getMachineProductPlan(firstDay,endDay,this.queryData.machineCode);
    },
    //跳转至指定日期的下一月
    toAssignLate(date){
      this.queryData.dateValue = date
      this.a = this.getDaysInMonth(date)
      const firstDay = this.a[0];
      const endDay = this.a[29];
      this.drawerTitle = this.queryData.machineCode + '机台' + firstDay.getFullYear() + '年' + (firstDay.getMonth() + 1) + '月' + firstDay.getDate() +
          '日~' + endDay.getFullYear() + '年' + (endDay.getMonth() + 1) + '月' + endDay.getDate() + '日排产计划'
      this.getMachineProductPlan(firstDay,endDay,this.queryData.machineCode);
    },
    //获取指定日期到30天后的日期
    getDaysInMonth(currentDate) {
      // 创建一个数组，存储从当前日期到30天后的每一天的Date对象
      const dateArray = [];
      for (let i = 0; i < 30; i++) {
        // 创建一个新的Date对象，代表未来的某一天
        const futureDate = new Date(currentDate);
        // 增加天数
        futureDate.setDate(currentDate.getDate() + i);
        // 将Date对象直接添加到数组中
        dateArray.push(futureDate);
      }
      return dateArray;
    },
    //根据给定的值返回日期的样式
    returnDateStyle(date){
      let style = {
        display: "flex",
        justifyContent: "center",
        alignItems: "center",
        height: "100%",
        border: "1px solid #219f88",
        background: ''
      }
      const dayOfWeek = date.getDay();
      let nowDate = new Date(new Date().getFullYear(), new Date().getMonth(), new Date().getDate());
      let backgroundColor;
      if (dayOfWeek === 0) {
        // 周末
        backgroundColor = '#6c6ce8'; // 蓝色
      } else if(nowDate.getTime() === date.getTime()){
        // 当天
        backgroundColor = '#e1f811'; // 蓝色
      }else {
        // 周一到周六
        backgroundColor = '#43deb5'; // 绿色
      }

      style.background = backgroundColor
      return style
    },
    //根据给定的值返回对应样式
    returnStyleByDate(dateData,item,index){
        let style = {
          width: '2.5vw',
          height: '4vh',
          border: '1px solid rgba(8,46,63,0.48)',
          background:''
        }
      //判断该天是否有数据
      let filter = item.data.filter(item => new Date(item.date).getTime() === dateData.getTime());
      if(filter.length !== 0){
        console.log(filter[0].status)
        // style.background = filter[0].status === 0 ? '#4741ea' : '#0fa270'
        style.background = '#097c55'
      }
      return style
    },
    //计算两个日期相减的天数
    getDaysDiff(date1, date2) {
      // 将日期对象转换为毫秒数，然后计算差值
      let timeDiff = Math.abs(date1.getTime() - date2.getTime());
      // 将毫秒数转换为天数并返回
      return Math.ceil(timeDiff / (1000 * 3600 * 24));
    },
    beforeUpload(file) {
      this.message = this.$message({
        message: '上传中，请勿操作',
        center: true,
        duration: 0,
      });
    },
    resultHandle(res) {
      if (res.code !== 0) {
      return this.$message.error(res.msg)
      }
      this.$message({
        message: this.$t('prompt.success'),
        type: 'success',
        duration: 500,
        onClose: () => {
          if (res.data) {
            this.unsuccessfulNameList = res.data
          }
          this.visible = false
          this.query()
        }
      })
    },
  }
}
</script>
<style scoped>
  .drawer_a ::v-deep .el-drawer__header{
      text-align: center;
      font-size: xx-large;
      color: #093891;
      font-weight: bold;
  }

  .custom-button {
    width: 6vw;
    height: 5vh;
  }

  .a-el-tooltip ::v-deep .el-tooltip__content {
    font-size: 16px; /* 调整为所需大小 */
    font-weight: bold; /* 设置为粗体 */
  }

  /* 覆盖表格单元格样式以居中文字 */
  .custom-descriptions ::v-deep .el-descriptions__body table td,
  .custom-descriptions ::v-deep .el-descriptions__body table th {
    text-align: center; /* 居中文字 */
    font-size: large; /* 修改文字大小 */
  }
</style>
