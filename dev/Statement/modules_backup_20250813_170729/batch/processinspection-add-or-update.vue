<template>
  <el-dialog :visible.sync="visible" :title="!dataForm.id ? $t('add') : $t('update')" :close-on-click-modal="false" :close-on-press-escape="false" append-to-body>
    <el-form :model="dataForm" :rules="dataRule" ref="dataForm" @keyup.enter.native="dataFormSubmitHandle()" :label-width="$i18n.locale === 'en-US' ? '120px' : '80px'">
      <div class="container">
        <el-form-item label="批号编号" prop="batchId">
          <batch-under-component :default-first-option="true" :allow-create="true" v-model="dataForm.batchId" :display-or-not="false" placeholder="批号编号"></batch-under-component default-first-option>
        </el-form-item>
        <el-form-item label="检验员" prop="inspectors">
          <employee-component v-model="dataForm.inspectors" :departments="departments" placeholder="检验员"></employee-component>
        </el-form-item>
        <el-form-item label="硬度标准" prop="hardnessStandard">
          <el-input v-model="dataForm.hardnessStandard" placeholder="硬度标准(xx~xx)"></el-input>
        </el-form-item>
        <el-form-item label="实测硬度" prop="measuredHardness">
          <el-input v-model="dataForm.measuredHardness" placeholder="实测硬度"></el-input>
        </el-form-item>
        <el-form-item label="异常说明" prop="exceptionDescription">
          <el-input v-model="dataForm.exceptionDescription" placeholder="异常说明"></el-input>
        </el-form-item>
        <el-form-item label="处理记录" prop="processingRecords">
          <el-input v-model="dataForm.processingRecords" placeholder="处理记录"></el-input>
        </el-form-item>
        <el-form-item label="判定" prop="processingRecords">
          <ren-radio-group v-model="dataForm.determine" placeholder="处理记录" dict-type="ok_ng"></ren-radio-group>
        </el-form-item>
        <el-form-item label="抽检日期" prop="samplingDate">
          <el-date-picker
              class="datePicker"
              v-model="dataForm.samplingDate"
              type="date"
              value-format="yyyy-MM-dd"
              placeholder="抽检日期">
          </el-date-picker>
        </el-form-item>
      </div>
      <el-form-item label="备注" prop="remark">
        <el-input v-model="dataForm.remark" placeholder="备注"></el-input>
      </el-form-item>
    </el-form>
    <template slot="footer">
      <el-button @click="visible = false">{{ $t('cancel') }}</el-button>
      <el-button type="primary" @click="dataFormSubmitHandle()">{{ $t('confirm') }}</el-button>
    </template>
  </el-dialog>
</template>

<script>
import debounce from 'lodash/debounce'
import mixinViewModule from '@/mixins/view-module'
export default {
  mixins: [mixinViewModule],
  data () {
    return {
      visible: false,
      //创建该页面时，不调用数据查询接口
      mixinViewModuleOptions: {
        createdIsNeed: false,
      },
      departments: ['1641701892159619073'],
      dataForm: {
        id: '',
        batchId: '',
        inspectors: '',
        hardnessStandard: '',
        measuredHardness: '',
        exceptionDescription: '',
        processingRecords: '',
        determine: 0,
        samplingDate: this.formatDates(),
        remark: '',
      }
    }
  },
  computed: {
    dataRule () {
      return {
        batchId: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        inspectors: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        hardnessStandard: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        measuredHardness: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
      }
    }
  },
  methods: {
    init () {
      this.visible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].resetFields()
        if (this.dataForm.id) {
          this.getInfo()
        }
      })
    },
    // 获取信息
    getInfo () {
      this.$http.get(`/batch/processinspection/${this.dataForm.id}`).then(({ data: res }) => {
        if (res.code !== 0) {
          return this.$message.error(res.msg)
        }
        this.dataForm = {
          ...this.dataForm,
          ...res.data
        }
      }).catch(() => {})
    },
    // 表单提交
    dataFormSubmitHandle: debounce(function () {
      this.$refs['dataForm'].validate((valid) => {
        if (!valid) {
          return false
        }
        this.$http[!this.dataForm.id ? 'post' : 'put']('/batch/processinspection/', this.dataForm).then(({ data: res }) => {
          if (res.code !== 0) {
            return this.$message.error(res.msg)
          }
          this.$message({
            message: this.$t('prompt.success'),
            type: 'success',
            duration: 500,
            onClose: () => {
              this.visible = false
              this.$emit('refreshDataList')
            }
          })
        }).catch(() => {})
      })
    }, 1000, { 'leading': true, 'trailing': false })
  }
}
</script>
