<template>
  <el-dialog :visible.sync="visible" :title="!dataForm.id ? $t('add') : $t('update')" :close-on-click-modal="false" :close-on-press-escape="false" append-to-body>
    <el-form :model="dataForm" :rules="dataRule" ref="dataForm" @keyup.enter.native="dataFormSubmitHandle()" :label-width="$i18n.locale === 'en-US' ? '120px' : '80px'">
      <div>
        <p>
          1、锁模螺丝、顶板、挂钩、耳朵无松动、脱落、变形、断裂等。
        </p>
        <ren-radio-group v-model="dataForm.components" dict-type="normal_anomaly"></ren-radio-group>
      </div>

      <div>
        <p>
          2、模腔、切嘴无损伤(无模伤)、生锈等。
        </p>
        <ren-radio-group v-model="dataForm.cuttingMouth" dict-type="normal_anomaly"></ren-radio-group>
      </div>

      <div>
        <p>
          3、模腔内无成形导致的模污，成形后无模污不良。
        </p>
        <ren-radio-group v-model="dataForm.moldCavityMoldFouling" dict-type="normal_anomaly"></ren-radio-group>
      </div>

      <div>
        <p>
          4、导柱导套无断、变形、抱死、脱落、松动、下沉且润滑良好。
        </p>
        <ren-radio-group v-model="dataForm.guidePillarGuideSleeve" dict-type="normal_anomaly"></ren-radio-group>
      </div>

      <div>
        <p>
          5、模具开合无异常/生产动作进行顺畅、无异声。
        </p>
        <ren-radio-group v-model="dataForm.mouldClosing" dict-type="normal_anomaly"></ren-radio-group>
      </div>

      <el-form-item label="确认时间" prop="confirmationTime">
          <el-input v-model="dataForm.confirmationTime" placeholder="确认时间"></el-input>
      </el-form-item>
      <el-form-item label="备注" prop="remark">
          <el-input v-model="dataForm.remark" placeholder="备注"></el-input>
      </el-form-item>
    </el-form>
    <template slot="footer">
      <el-button @click="visible = false">{{ $t('cancel') }}</el-button>
      <el-button type="primary" @click="dataFormSubmitHandle()">{{ $t('confirm') }}</el-button>
    </template>
  </el-dialog>
</template>

<script>
import debounce from 'lodash/debounce'
export default {
  data () {
    return {
      visible: false,
      dataForm: {
        id: '',
        subBatchId: '',
        components: '',
        cuttingMouth: '',
        moldCavityMoldFouling: '',
        guidePillarGuideSleeve: '',
        mouldClosing: '',
        confirmationTime: '',
        remark: '',
        disabled: '',
        creator: '',
        createDate: '',
        updater: '',
        updateDate: ''
      }
    }
  },
  computed: {
    dataRule () {
      return {}
    }
  },
  methods: {
    init () {
      this.visible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].resetFields()
        if (this.dataForm.id) {
          this.getInfo()
        }
      })
    },
    // 获取信息
    getInfo () {
      this.$http.get(`/batch/moldinspection/${this.dataForm.id}`).then(({ data: res }) => {
        if (res.code !== 0) {
          return this.$message.error(res.msg)
        }
        this.dataForm = {
          ...this.dataForm,
          ...res.data
        }
      }).catch(() => {})
    },
    // 表单提交
    dataFormSubmitHandle: debounce(function () {
      this.$refs['dataForm'].validate((valid) => {
        if (!valid) {
          return false
        }
        this.$http[!this.dataForm.id ? 'post' : 'put']('/batch/moldinspection/', this.dataForm).then(({ data: res }) => {
          if (res.code !== 0) {
            return this.$message.error(res.msg)
          }
          this.$message({
            message: this.$t('prompt.success'),
            type: 'success',
            duration: 500,
            onClose: () => {
              this.visible = false
              this.$emit('refreshDataList')
            }
          })
        }).catch(() => {})
      })
    }, 1000, { 'leading': true, 'trailing': false })
  }
}
</script>
