<template>
  <el-dialog :visible.sync="visible" :title="!dataForm.id ? '新增机台生产记录表' : '修改机台生产记录表'" :close-on-click-modal="false" :close-on-press-escape="false" append-to-body>
    <el-form :model="dataForm" :rules="dataRule" ref="dataForm" @keyup.enter.native="dataFormSubmitHandle()" :label-width="$i18n.locale === 'en-US' ? '120px' : '80px'">
      <div class="container">
        <div>
          <el-form-item label="批号">
            <span class="font_size">
              {{$getSubBatchList(objectInformation.subBatchId)}}
            </span>
          </el-form-item>
        </div>

        <div>
          <el-form-item label="品名编号">
              <span class="font_size">
              {{$getPartList(objectInformation.partId)}}
            </span>
          </el-form-item>
        </div>

        <div>
          <el-form-item label="机台">
            <span class="font_size">
              {{$getMachineList(objectInformation.machineId)}}
            </span>
          </el-form-item>
        </div>
      </div>
      <div class="container">
        <div>
          <el-form-item label="检查时间" prop="checkTheTime">
            <el-time-picker
                style="width: 150px;"
                v-model="dataForm.checkTheTime"
                value-format="HH:mm:ss"
                :picker-options="{
                  start: '00:00',
                  step: '00:15',
                  end: '23:59'
                }"
                placeholder="选择时间">
            </el-time-picker>
          </el-form-item>
        </div>

        <div>
          <el-form-item label="状态" prop="state">
            <ren-select  v-model="dataForm.state" placeholder="状态" dict-type="check_status" class="inputSize"></ren-select>
          </el-form-item>
        </div>

        <div>
          <el-form-item label="累计模数" prop="cumulativeModulus">
            <el-input v-model.number="dataForm.cumulativeModulus" placeholder="累计模数" type="number" class="inputSize"></el-input>
          </el-form-item>
        </div>

        <div>
          <el-form-item label="当前模具孔数" prop="numberOfMoldHoles">
            <el-input v-model.number="dataForm.numberOfMoldHoles" placeholder="当前孔数" type="number" class="inputSize"></el-input>
          </el-form-item>
        </div>
      </div>
      <div class="container">
        <div>
          <el-form-item label="包风" prop="bagWind">
            <el-input v-model.number="dataForm.bagWind" placeholder="包风" type="number" class="inputSize"></el-input>
          </el-form-item>
        </div>

        <div>
          <el-form-item label="起泡" prop="bubbling">
            <el-input v-model.number="dataForm.bubbling" type="number" placeholder="起泡" class="inputSize"></el-input>
          </el-form-item>
        </div>

        <div>
          <el-form-item label="异物" prop="foreignBody">
            <el-input v-model.number="dataForm.foreignBody" type="number" placeholder="异物" class="inputSize"></el-input>
          </el-form-item>
        </div>

        <div>
          <el-form-item label="死料" prop="deathPenalty">
            <el-input v-model.number="dataForm.deathPenalty" type="number" placeholder="死料" class="inputSize"></el-input>
          </el-form-item>
        </div>

        <div>
          <el-form-item label="缺料" prop="lackOfMaterial">
            <el-input v-model.number="dataForm.lackOfMaterial" type="number" placeholder="缺料" class="inputSize"></el-input>
          </el-form-item>
        </div>

        <div>
          <el-form-item label="划痕" prop="scratches">
            <el-input v-model.number="dataForm.scratches" type="number" placeholder="划痕" class="inputSize"></el-input>
          </el-form-item>
        </div>

        <div>
          <el-form-item label="不熟" prop="unfamiliar">
            <el-input v-model.number="dataForm.unfamiliar" type="number" placeholder="不熟" class="inputSize"></el-input>
          </el-form-item>
        </div>

        <div>
          <el-form-item label="脱硫" prop="desulfurization">
            <el-input v-model.number="dataForm.desulfurization" type="number" placeholder="脱硫" class="inputSize"></el-input>
          </el-form-item>
        </div>

        <div>
          <el-form-item label="流纹" prop="flowPattern">
            <el-input v-model.number="dataForm.flowPattern" type="number" placeholder="流纹" class="inputSize"></el-input>
          </el-form-item>
        </div>

        <div>
          <el-form-item label="变形" prop="outOfShape">
            <el-input v-model.number="dataForm.outOfShape" type="number" placeholder="变形" class="inputSize"></el-input>
          </el-form-item>
        </div>

        <div>
          <el-form-item label="其他" prop="other">
            <el-input v-model.number="dataForm.state" type="number" placeholder="其他" class="inputSize"></el-input>
          </el-form-item>
        </div>
      </div>

      <div class="container">
        <div>
          <el-form-item label="上模" prop="upperMold">
            <el-input v-model="dataForm.upperMold" placeholder="上模"></el-input>
          </el-form-item>
        </div>

        <div>
          <el-form-item label="下模" prop="lowerMold">
            <el-input v-model="dataForm.lowerMold" placeholder="下模"></el-input>
          </el-form-item>
        </div>

        <div>
          <el-form-item label="中模" prop="middleModel">
            <el-input v-model="dataForm.middleModel" placeholder="中模"></el-input>
          </el-form-item>
        </div>
      </div>

      <div class="container">
        <div>
          <el-form-item label="硫化时间" prop="vulcanizationTime">
            <el-input v-model="dataForm.vulcanizationTime" placeholder="硫化时间"></el-input>
          </el-form-item>
        </div>

        <div>
          <el-form-item label="机台压力" prop="machinePressure">
            <el-input v-model="dataForm.machinePressure" placeholder="机台压力"></el-input>
          </el-form-item>
        </div>

        <div>
          <el-form-item label="反馈对象" prop="feedbackObject">
            <el-input v-model="dataForm.feedbackObject" placeholder="反馈对象"></el-input>
          </el-form-item>
        </div>
      </div>
      <el-form-item label="说明" prop="illustrate">
          <el-input v-model="dataForm.illustrate" placeholder="说明"></el-input>
      </el-form-item>
      <el-form-item label="备注" prop="remark">
          <el-input v-model="dataForm.remark" placeholder="备注"></el-input>
      </el-form-item>
                </el-form>
    <template slot="footer">
      <el-button @click="visible = false">{{ $t('cancel') }}</el-button>
      <el-button type="primary" @click="dataFormSubmitHandle()">{{ $t('confirm') }}</el-button>
    </template>
  </el-dialog>
</template>

<script>
import debounce from 'lodash/debounce'
import mixinViewModule from '@/mixins/view-module'
export default {
  mixins:[mixinViewModule],
  props: {
    objectInformation:{
      type:Object,
      required: false
    }
  },
  data () {
    return {
      visible: false,
      //创建该页面时，不调用数据查询接口
      mixinViewModuleOptions: {
        createdIsNeed: false,
      },
      dataForm: {
        id: '',
        subBatchId: this.objectInformation.subBatchId,
        checkTheTime: this.formatTime(),
        cumulativeModulus: 0,
        numberOfMoldHoles: 20,
        bagWind: 0,
        bubbling: 0,
        foreignBody: 0,
        deathPenalty: 0,
        lackOfMaterial: 0,
        scratches: 0,
        unfamiliar: 0,
        desulfurization: 0,
        flowPattern: 0,
        outOfShape: 0,
        other: 0,
        upperMold: '',
        middleModel: '',
        standardCapacity: this.objectInformation.standardCapacity,
        lowerMold: '',
        vulcanizationTime: '',
        state: 0,
        machinePressure:'',
        feedbackObject: '',
        illustrate: '',
        remark: '',
        disabled: '',
        creator: '',
        createDate: '',
        updater: '',
        updateDate: ''
      }
    }
  },
  computed: {
    dataRule () {
      return {
        numberOfMoldHoles: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
      }
    }
  },
  watch:{
    "dataForm.state"(newValue,oldValue){
      let date = new Date();
      this.dataForm.checkTheTime = this.formatTimes(date);
    },
  },
  methods: {
    init () {
      this.visible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].resetFields()
        if (this.dataForm.id) {
          this.getInfo()
        }
      })
    },
    // 获取信息
    getInfo () {
      this.$http.get(`/batch/inspectioninstructions/${this.dataForm.id}`).then(({ data: res }) => {
        if (res.code !== 0) {
          return this.$message.error(res.msg)
        }
        this.dataForm = {
          ...this.dataForm,
          ...res.data
        }
      }).catch(() => {})
    },
    // 表单提交
    dataFormSubmitHandle: debounce(function () {
      this.$refs['dataForm'].validate((valid) => {
        if (!valid) {
          return false
        }
        let message = '';
        if(this.dataForm.id){
          message = '此操作将修改该数据'
        }else {
          message = '此操作将新增一条数据'
        }
        this.$confirm(message+', 是否继续?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
          center: true
        }).then(() => {
          this.$http[!this.dataForm.id ? 'post' : 'put']('/batch/inspectioninstructions/', this.dataForm).then(({ data: res }) => {
            console.log(res)
            if (res.code !== 0) {
              return this.$message.error(res.msg)
            }
            this.$emit("calculatedValue",res.data.productionModulus,res.data.defectiveRate,res.data.actualCapacity,res.data.undesirable,res.data.productionPerHour)
            this.$message({
              message: this.$t('prompt.success'),
              type: 'success',
              duration: 500,
              onClose: () => {
                this.visible = false
                this.$emit('refreshDataList')
              }
            })
          }).catch(() => {})
        }).catch(() => {
          this.$message({
            type: 'info',
            message: '已取消'
          });
        });
      })
    }, 1000, { 'leading': true, 'trailing': false })
  }
}
</script>

<style>
  .inputSize{
    width: 100px;
  }
</style>
