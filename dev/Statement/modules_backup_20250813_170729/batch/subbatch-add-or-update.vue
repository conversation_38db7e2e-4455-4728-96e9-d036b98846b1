<template>
  <el-dialog :visible.sync="visible" :title="!dataForm.id ? '新增次批号' : '修改次批号'" :close-on-click-modal="false" :close-on-press-escape="false" append-to-body>
    <el-form :model="dataForm" :rules="dataRule" ref="dataForm" @keyup.enter.native="dataFormSubmitHandle()" :label-width="$i18n.locale === 'en-US' ? '120px' : '80px'">
      <div class="container">
        <el-form-item label="批号" prop="batchNumber">
          <batch-under-component v-model="dataForm.batchId" :display-or-not="false" placeholder="请选择批号"></batch-under-component>
        </el-form-item>
        <el-form-item label="次批号" prop="subBatchNumber">
          <el-input v-model="dataForm.subBatchNumber" placeholder="次批号"></el-input>
        </el-form-item>
        <el-form-item label="机台编号" prop="machineId">
          <machine-component v-model="dataForm.machineId" placeholder="机台编号"></machine-component>
        </el-form-item>
        <el-form-item label="条码" prop="barcode">
          <el-input v-model="dataForm.barcode" placeholder="条码"></el-input>
        </el-form-item>
        <el-form-item label="二维码" prop="orCode">
          <el-input v-model="dataForm.orCode" placeholder="二维码"></el-input>
        </el-form-item>
        <el-form-item label="库存总表id" prop="productInventoryId">
          <el-input v-model="dataForm.productInventoryId" placeholder="库存总表id"></el-input>
        </el-form-item>
        <el-form-item label="料重(kg)" prop="materialWeight">
          <el-input v-model="dataForm.materialWeight" placeholder="料重(kg)"></el-input>
        </el-form-item>
        <el-form-item label="生产模数" prop="productionModulus">
          <el-input v-model="dataForm.productionModulus" placeholder="生产模数"></el-input>
        </el-form-item>
        <el-form-item label="孔数" prop="numberOfHoles">
          <el-input v-model="dataForm.numberOfHoles" placeholder="孔数"></el-input>
        </el-form-item>
        <el-form-item label="制造时间" prop="manufacturingTime">
          <el-input v-model="dataForm.manufacturingTime" placeholder="制造时间"></el-input>
        </el-form-item>
        <el-form-item label="生产数量" prop="productionQuantity">
          <el-input v-model="dataForm.productionQuantity" placeholder="生产数量"></el-input>
        </el-form-item>
        <el-form-item label="生产损耗量" prop="productionLoss">
          <el-input v-model="dataForm.productionLoss" placeholder="生产损耗量"></el-input>
        </el-form-item>
        <el-form-item label="技术员" prop="technician">
          <el-input v-model="dataForm.technician" placeholder="技术员"></el-input>
        </el-form-item>
        <el-form-item label="成形数" prop="productRepairNumber">
          <el-input v-model="dataForm.formingNumber" placeholder="成形数"></el-input>
        </el-form-item>
        <el-form-item label="品修数" prop="productRepairNumber">
          <el-input v-model="dataForm.productRepairNumber" placeholder="品修数"></el-input>
        </el-form-item>
        <el-form-item label="品修数现存数" prop="qualityRepairNow">
          <el-input v-model="dataForm.qualityRepairNow" placeholder="品修数现存数"></el-input>
        </el-form-item>
        <el-form-item label="品检数" prop="qualityInspectionNumber">
          <el-input v-model="dataForm.qualityInspectionNumber" placeholder="品检数"></el-input>
        </el-form-item>
        <el-form-item label="品检现存数" prop="qualityInspectionAvailable">
          <el-input v-model="dataForm.qualityInspectionAvailable" placeholder="品检现存数"></el-input>
        </el-form-item>
        <el-form-item label="待包装数" prop="toBePacked">
          <el-input v-model="dataForm.toBePacked" placeholder="待包装数"></el-input>
        </el-form-item>
        <el-form-item label="待包装现存数" prop="awaitingPackaging">
          <el-input v-model="dataForm.awaitingPackaging" placeholder="待包装现存数"></el-input>
        </el-form-item>
        <el-form-item label="待出货数" prop="toBeShipped">
          <el-input v-model="dataForm.toBeShipped" placeholder="待出货数"></el-input>
        </el-form-item>
        <el-form-item label="产品状态" prop="productStatus">
          <ren-select v-model="dataForm.productStatus" placeholder="产品状态" dict-type="product_status"></ren-select>
        </el-form-item>
        <el-form-item label="包装箱号" prop="boxNumber">
          <el-input v-model="dataForm.boxNumber" placeholder="包装箱号"></el-input>
        </el-form-item>
        <el-form-item label="卡板编号" prop="cardNumber">
          <el-input v-model="dataForm.cardNumber" placeholder="卡板编号"></el-input>
        </el-form-item>
        <el-form-item label="出货日期" prop="shippingDate">
          <el-date-picker
              v-model="dataForm.shippingDate"
              type="datetime"
              value-format="yyyy-MM-dd HH:mm:ss"
              placeholder="请选择出货日期">
          </el-date-picker>
        </el-form-item>
      </div>
      <el-form-item label="备注" prop="remark">
          <el-input v-model="dataForm.remark" placeholder="备注"></el-input>
      </el-form-item>
      </el-form>
    <template slot="footer">
      <el-button @click="visible = false">{{ $t('cancel') }}</el-button>
      <el-button type="primary" @click="dataFormSubmitHandle()">{{ $t('confirm') }}</el-button>
    </template>
  </el-dialog>
</template>

<script>
import debounce from 'lodash/debounce'
export default {
  // name:'subBatchAddOrUpdate',
  data () {
    return {
      batchNumberId:'',
      visible: false,
      dataForm: {
        id: '',
        batchId:'',
        subBatchNumber:'',
        machineId:'',
        barcode: '',
        batchNumber: '',
        selectBatchNumber: '',
        orCode: '',
        productInventoryId: '',
        materialWeight: '',
        productionModulus: '',
        locks: 0,
        numberOfHoles: '',
        manufacturingTime: '',
        productionQuantity: '',
        productionLoss: '',
        technician: '',
        formingNumber: 0,
        productRepairNumber: 0,
        qualityRepairNow: 0,
        qualityInspectionNumber: 0,
        qualityInspectionAvailable: 0,
        toBePacked: 0,
        awaitingPackaging: 0,
        toBeShipped: 0,
        productStatus: 0,
        boxNumber: '',
        cardNumber: '',
        shippingDate: '',
        remark: '',
        disabled: '',
        creator: '',
        createDate: '',
        updater: '',
        updateDate: ''
      },
      content:'',
    }
  },
  computed: {
    dataRule () {
      return {
        batchNumber: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        subBatchNumber: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        machineId: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ]
      }
    }
  },
  mounted() {
    if(this.dataForm.batchId != undefined && this.dataForm.batchId != ''){
      this.getBatchNumber()
      this.maxSubBatchNumber()
    }
  },

  watch:{
    batchNumberId (newValue,oldValue){
      this.getBatchNumber(newValue)
      this.maxSubBatchNumber(newValue)
    }
  },
  methods: {
    //模糊查询批号
    selectButchNumberInfo(batchNumber, cb) {
      this.$http.get(`/batch/batch/selectButchNumberInfo/` + batchNumber).then(({data: res}) => {
        if (res.code !== 0) {
          return this.$message.error(res.msg)
        }
        const list = res.data.map((obj) => {
          return {
            batchId: obj.id,
            batchNumber: obj.batchNumber,
            value: obj.sizeCategory+obj.batchNumber,
          }
        })
        this.timeout = setTimeout(() => {
          cb(list);
        }, 300 * Math.random());
      }).catch(() => {
      })
    },
    //  输出选择日志
    handleSelect(item) {
      this.dataForm.batchId = item.batchId
      this.dataForm.batchNumber = item.batchNumber
    },
    getBatchNumber(batchId) {
      this.$http.get(`/batch/batch/${batchId}`).then(({ data: res }) => {
        if (res.code !== 0) {
          return this.$message.error(res.msg)
        }
        this.dataForm.selectBatchNumber = res.data.sizeCategory+res.data.batchNumber
      }).catch(() => {})
    },
    maxSubBatchNumber(batchId) {
      this.$http.get(`/batch/subbatch/maxSubBatchNumber/${batchId}`).then(({ data: res }) => {
        if (res.code !== 0) {
          return this.$message.error(res.msg)
        }
        this.dataForm.subBatchNumber = Number(res.data.subBatchNumber)
      }).catch(() => {})
    },
    init () {
      this.visible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].resetFields()
        if (this.dataForm.id) {
          this.getInfo()
        }
      })
    },
    // 获取信息
    getInfo () {
      this.$http.get(`/batch/subbatch/${this.dataForm.id}`).then(({ data: res }) => {
        if (res.code !== 0) {
          return this.$message.error(res.msg)
        }
        this.dataForm = {
          ...this.dataForm,
          ...res.data
        }
       this.content = JSON.stringify(res.data);
      }).catch(() => {})
    },
    // 表单提交
    dataFormSubmitHandle: debounce(function () {
      //表单提交后刷新批号页面
      this.$toRefresh.$emit('subBatchInsert')
      this.$refs['dataForm'].validate((valid) => {
        if (!valid) {
          return false
        }
        this.$http[!this.dataForm.id ? 'post' : 'put']('/batch/subbatch/', this.dataForm).then(({ data: res }) => {
          if (res.code !== 0) {
            return this.$message.error(res.msg)
          }
          if(!this.dataForm.id){
            this.$emit("callbackFlow",res.data)
          }
          this.$message({
            message: this.$t('prompt.success'),
            type: 'success',
            duration: 500,
            onClose: () => {
              this.visible = false
              this.$emit('refreshDataList')
            }
          })
        }).catch(() => {})
      })
    }, 1000, { 'leading': true, 'trailing': false })
  }
}
</script>
