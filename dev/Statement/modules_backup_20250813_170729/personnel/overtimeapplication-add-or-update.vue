<template>
  <el-dialog :visible.sync="visible" :title="!dataForm.id ? '加班申请单' : '修改加班申请'" :close-on-click-modal="false"
             :close-on-press-escape="false" append-to-body>
    <div v-if="!dataForm.id">
      <el-form :inline="true" :model="formParameters">
       <el-form-item>
         <el-radio v-model="formParameters.overtimeType" :label="1" border>部门加班申请</el-radio>
         <el-radio v-model="formParameters.overtimeType" :label="0" border>个人加班申请</el-radio>
       </el-form-item>
        <el-form-item v-if="formParameters.overtimeType == 1" label="申请部门">
          <ren-dept-tree v-model="formParameters.deptId" :placeholder="$t('dept.title')" :dept-name.sync="formParameters.deptName"></ren-dept-tree>
        </el-form-item>
        <el-form-item label="加班日期">
          <el-date-picker
              v-model="formParameters.overtimeDate"
              type="datetimerange"
              align="right"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              :default-time="['12:00:00', '08:00:00']">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="共计小时">
          <el-input v-model="formParameters.totalTime"
                      placeholder="共计小时"></el-input>
        </el-form-item>
        <el-form-item label="申请人">
         <employee-component v-model="formParameters.creating"></employee-component>
        </el-form-item>
        <el-form-item label="加班事由">
          <el-input v-model="formParameters.reason" placeholder="事由"></el-input>
        </el-form-item>
        <el-form-item label="薪点">
          <el-input v-model="formParameters.salaryPoint" placeholder="薪点"></el-input>
        </el-form-item>
        <el-form-item label="申请日期">
          <el-date-picker
              v-model="formParameters.applyDate"
              type="date"
              placeholder="选择日期">
          </el-date-picker>
        </el-form-item>
      </el-form>
      <el-button type="primary" icon="el-icon-plus" @click="addOrUpdateHandle()" circle></el-button>
      <el-table height="500px" :data="dataList" border @selection-change="dataListSelectionChangeHandle" style="width: 100%;">
        <el-table-column type="selection" header-align="center" align="center" width="50"></el-table-column>
        <el-table-column prop="deptId" label="部门" header-align="center" align="center"></el-table-column>
        <el-table-column prop="jobTitle" label="职称" header-align="center" align="center"></el-table-column>
        <el-table-column prop="leavePerson" label="工号" header-align="center" align="center"></el-table-column>
        <el-table-column prop="leavePerson" label="姓名" header-align="center" align="center"></el-table-column>
        <el-table-column prop="overtimeType" label="加班别" header-align="center" align="center"></el-table-column>
        <el-table-column prop="applyDate" label="申请日期" header-align="center" align="center"></el-table-column>
        <el-table-column prop="startDate" label="加班日期时间起" header-align="center" align="center"></el-table-column>
        <el-table-column prop="endDate" label="加班日期时间止" header-align="center" align="center"></el-table-column>
        <el-table-column prop="totalTime" label="共计时间" header-align="center" align="center"></el-table-column>
        <el-table-column prop="salaryPoint" label="薪点" header-align="center" align="center"></el-table-column>
        <el-table-column prop="reason" label="事由说明" header-align="center" align="center"></el-table-column>
        <el-table-column prop="agent" label="实际工时" header-align="center" align="center"></el-table-column>
        <el-table-column :label="$t('handle')" fixed="right" header-align="center" align="center" width="150">
          <template slot-scope="scope">
            <el-dropdown>
              <el-button type="primary">
                功能<i class="el-icon-arrow-down el-icon--right"></i>
              </el-button>
              <el-dropdown-menu slot="dropdown">
                <el-dropdown-item>
                  <el-button v-if="$hasPermission('personnel:overtimeapplication:update')" type="text" size="small" @click="addOrUpdateHandle(scope.row.id)">{{ $t('update') }}</el-button>
                </el-dropdown-item>
                <el-dropdown-item>
                  <el-button v-if="$hasPermission('personnel:overtimeapplication:delete')" type="text" size="small" @click="deleteHandle(scope.row.id)">{{ $t('delete') }}</el-button>
                </el-dropdown-item>
              </el-dropdown-menu>
            </el-dropdown>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <div v-else>
      <el-form :model="dataForm" :rules="dataRule" ref="dataForm" @keyup.enter.native="dataFormSubmitHandle()"
               :label-width="$i18n.locale === 'en-US' ? '120px' : '80px'">
        <div class="container">
          <el-form-item label="部门" prop="deptId">
            <el-input v-model="dataForm.deptId" placeholder="部门"></el-input>
          </el-form-item>
          <el-form-item label="职称" prop="jobTitle">
            <el-input v-model="dataForm.jobTitle" placeholder="职称"></el-input>
          </el-form-item>
          <el-form-item label="加班人员" prop="leavePerson">
            <el-input v-model="dataForm.leavePerson" placeholder="加班人员"></el-input>
          </el-form-item>
          <el-form-item label="加班类型" prop="overtimeType">
            <el-input v-model="dataForm.overtimeType" placeholder="加班类型 0:个人加班申请 1:部门加班申请"></el-input>
          </el-form-item>
          <el-form-item label="申请日期" prop="applyDate">
            <el-input v-model="dataForm.applyDate" placeholder="申请日期"></el-input>
          </el-form-item>
          <el-form-item label="加班日期时间起" prop="startDate">
            <el-input v-model="dataForm.startDate" placeholder="加班日期时间起"></el-input>
          </el-form-item>
          <el-form-item label="加班日期时间止" prop="endDate">
            <el-input v-model="dataForm.endDate" placeholder="加班日期时间止"></el-input>
          </el-form-item>
          <el-form-item label="共计时间" prop="totalTime">
            <el-input v-model="dataForm.totalTime" placeholder="共计时间"></el-input>
          </el-form-item>
          <el-form-item label="薪点" prop="salaryPoint">
            <el-input v-model="dataForm.salaryPoint" placeholder="薪点"></el-input>
          </el-form-item>
          <el-form-item label="事由说明" prop="reason">
            <el-input v-model="dataForm.reason" placeholder="事由说明"></el-input>
          </el-form-item>
          <el-form-item label="实际工时" prop="agent">
            <el-input v-model="dataForm.agent" placeholder="实际工时"></el-input>
          </el-form-item>
          <el-form-item label="申请人" prop="creating">
            <el-input v-model="dataForm.creating" placeholder="作成"></el-input>
          </el-form-item>
        </div>
      </el-form>
      <template slot="footer">
        <el-button @click="visible = false">{{ $t('cancel') }}</el-button>
        <el-button type="primary" @click="dataFormSubmitHandle()">{{ $t('confirm') }}</el-button>
      </template>
    </div>
  </el-dialog>
</template>

<script>
import debounce from 'lodash/debounce'
import mixinViewModule from '@/mixins/view-module'

export default {
  mixins: [mixinViewModule],
  data() {
    return {
      visible: false,
      //创建该页面时，不调用数据查询接口
      mixinViewModuleOptions: {
        createdIsNeed: false,
      },
      formParameters:{
        overtimeType:0,
        deptId:'',
        deptName:'',
        startDate:'',
        endDate:'',
        overtimeDate:'',
        totalTime:'',
        creating:'',
        reason:'',
        salaryPoint:'',
        applyDate:this.formatDates(),
      },
      dataForm: {
        id: '',
        deptId: '',
        jobTitle: '',
        leavePerson: '',
        overtimeType: '',
        applyDate: '',
        startDate: '',
        endDate: '',
        totalTime: '',
        salaryPoint: '',
        reason: '',
        agent: '',
        creating: '',
        remark: '',
        disabled: '',
        creator: '',
        createDate: '',
        updater: '',
        updateDate: ''
      }
    }
  },
  computed: {
    dataRule() {
      return {
        deptId: [
          {required: true, message: this.$t('validate.required'), trigger: 'blur'}
        ],
        jobTitle: [
          {required: true, message: this.$t('validate.required'), trigger: 'blur'}
        ],
        leavePerson: [
          {required: true, message: this.$t('validate.required'), trigger: 'blur'}
        ],
        overtimeType: [
          {required: true, message: this.$t('validate.required'), trigger: 'blur'}
        ],
        applyDate: [
          {required: true, message: this.$t('validate.required'), trigger: 'blur'}
        ],
        startDate: [
          {required: true, message: this.$t('validate.required'), trigger: 'blur'}
        ],
        endDate: [
          {required: true, message: this.$t('validate.required'), trigger: 'blur'}
        ],
        totalTime: [
          {required: true, message: this.$t('validate.required'), trigger: 'blur'}
        ],
        salaryPoint: [
          {required: true, message: this.$t('validate.required'), trigger: 'blur'}
        ],
        reason: [
          {required: true, message: this.$t('validate.required'), trigger: 'blur'}
        ],
        agent: [
          {required: true, message: this.$t('validate.required'), trigger: 'blur'}
        ],
        creating: [
          {required: true, message: this.$t('validate.required'), trigger: 'blur'}
        ],
      }
    }
  },
  watch:{
    "formParameters.deptId"(){
      this.$http.get(`/personnel/overtimeapplication/${this.dataForm.id}`).then(({data: res}) => {
        if (res.code !== 0) {
          return this.$message.error(res.msg)
        }
        this.dataForm = {
          ...this.dataForm,
          ...res.data
        }
      }).catch(() => {
        this.$message.error(this.$t('reminder.errormessage'))
      })
    }
  },
  methods: {
    // 添加加班人员
    addPeople(){

    },
    init() {
      this.visible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].resetFields()
        if (this.dataForm.id) {
          this.getInfo()
        }
      })
    },
    // 获取信息
    getInfo() {
      this.$http.get(`/personnel/overtimeapplication/${this.dataForm.id}`).then(({data: res}) => {
        if (res.code !== 0) {
          return this.$message.error(res.msg)
        }
        this.dataForm = {
          ...this.dataForm,
          ...res.data
        }
      }).catch(() => {
      })
    },
    // 表单提交
    dataFormSubmitHandle: debounce(function () {
      this.$refs['dataForm'].validate((valid) => {
        if (!valid) {
          return false
        }
        let message = '';
        if (this.dataForm.id) {
          message = '此操作将修改该数据'
        } else {
          message = '此操作将新增一条数据'
        }
        this.$confirm(message + ', 是否继续?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
          center: true
        }).then(() => {
          this.$http[!this.dataForm.id ? 'post' : 'put']('/personnel/overtimeapplication/', this.dataForm).then(({data: res}) => {
            if (res.code !== 0) {
              return this.$message.error(res.msg)
            }
            this.$message({
              message: this.$t('prompt.success'),
              type: 'success',
              duration: 500,
              onClose: () => {
                this.visible = false
                this.$emit('refreshDataList')
              }
            })
          }).catch(() => {
          })
        }).catch(() => {
          this.$message({
            type: 'info',
            message: '已取消'
          });
        });
      })
    }, 1000, {'leading': true, 'trailing': false})
  }
}
</script>
