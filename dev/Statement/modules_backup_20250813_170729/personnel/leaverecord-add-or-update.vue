<template>
  <el-dialog :visible.sync="visible" :title="!dataForm.id ? $t('add') : $t('update')" :close-on-click-modal="false"
             :close-on-press-escape="false" append-to-body>
    <el-form :model="dataForm" :rules="dataRule" ref="dataForm" @keyup.enter.native="dataFormSubmitHandle()"
             :label-width="$i18n.locale === 'en-US' ? '120px' : '80px'">
      <div class="container">
        <el-form-item label="假别" prop="leaveType">
          <ren-select v-model="dataForm.leaveType" placeholder="假别" dict-type="leave_type"></ren-select>
        </el-form-item>
        <el-form-item label="职称" prop="jobTitle">
          <el-input v-model="dataForm.jobTitle" placeholder="职称"></el-input>
        </el-form-item>
        <el-form-item label="工号" prop="leavePerson">
          <employee-component v-model="dataForm.leavePerson" @employeeData="employeeData"
                              placeholder="工号"></employee-component>
        </el-form-item>
        <el-form-item label="姓名" prop="leaveName">
          <el-input v-model="dataForm.leaveName" placeholder="姓名"></el-input>
        </el-form-item>
        <el-form-item prop="deptName" :label="$t('user.deptName')">
          <ren-dept-tree v-model="dataForm.deptId" :placeholder="$t('dept.title')" :dept-name.sync="dataForm.deptName"></ren-dept-tree>
        </el-form-item>
      </div>
      <div class="container">
        <el-form-item label="日期时间起" prop="startDate">
          <el-date-picker
              v-model="dataForm.startDate"
              type="datetime"
              placeholder="日期时间起"
              value-format="yyyy-MM-dd HH:mm:ss"
              default-time="08:00:00">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="日期时间止" prop="endDate">
          <el-date-picker
              v-model="dataForm.endDate"
              type="datetime"
              placeholder="日期时间止"
              value-format="yyyy-MM-dd HH:mm:ss"
              default-time="20:00:00">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="总时数" prop="totalLeaveTime">
          <el-input v-model="dataForm.totalLeaveTime" placeholder="总时数">
            <template slot="append">天</template>
          </el-input>
        </el-form-item>
      </div>
      <el-form-item label="事由说明" prop="reason">
        <el-input v-model="dataForm.reason" placeholder="事由说明"></el-input>
      </el-form-item>
      <div class="container">
        <!--        <el-form-item label="年度累计时间" prop="cumulativeTime">
                  <el-input v-model="dataForm.cumulativeTime" placeholder="年度累计时间"></el-input>
                </el-form-item>-->

        <el-form-item label="代理人" prop="agent">
          <employee-component v-model="dataForm.agent" placeholder="代理人"></employee-component>
        </el-form-item>
        <el-form-item label="作成" prop="creating">
          <employee-component v-model="dataForm.creating" placeholder="作成"></employee-component>
        </el-form-item>
      </div>
    </el-form>
    <template slot="footer">
      <el-button @click="visible = false">{{ $t('cancel') }}</el-button>
      <el-button type="primary" @click="dataFormSubmitHandle()">{{ $t('confirm') }}</el-button>
    </template>
  </el-dialog>
</template>

<script>
import debounce from 'lodash/debounce'
import mixinViewModule from '@/mixins/view-module'
import publicFunctions from '@/mixins/public-functions'

export default {
  mixins: [mixinViewModule, publicFunctions],
  data() {
    return {
      visible: false,
      //创建该页面时，不调用数据查询接口
      mixinViewModuleOptions: {
        createdIsNeed: false,
      },
      dataForm: {
        id: '',
        leaveType: 0,
        deptId: '',
        deptName: '',
        jobTitle: '',
        leaveName: '',
        leavePerson: '',
        startDate: '',
        endDate: '',
        totalLeaveTime: '',
        /*cumulativeTime: '',*/
        reason: '',
        agent: '',
        creating: '',
        remark: '',
        disabled: '',
        creator: '',
        createDate: '',
        updater: '',
        updateDate: ''
      }
    }
  },
  computed: {
    dataRule() {
      return {
        leaveType: [
          {required: true, message: this.$t('validate.required'), trigger: 'blur'}
        ],
        deptId: [
          {required: true, message: this.$t('validate.required'), trigger: 'blur'}
        ],
      }
    },
    totalLeaveTime() {
      const start = new Date(this.dataForm.startDate);
      const end = new Date(this.dataForm.endDate);
      const diff = end - start;
      const days = Math.floor(diff / (1000 * 60 * 60 * 24))+1;
      const hours = Math.floor((diff % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
      const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60));
      if (days > 0) {
        this.dataForm.totalLeaveTime = `${days}天`;
      } else if (hours > 0) {
        this.dataForm.totalLeaveTime = `${hours}小时`;
      } else {
        this.dataForm.totalLeaveTime = `${minutes}分钟`;
      }
      return this.dataForm.totalLeaveTime;
      /*this.dataForm.totalLeaveTime = this.calculate(this.dataForm.startDate, this.dataForm.endDate)
      return this.dataForm.totalLeaveTime;*/
    }
  },
  methods: {
    calculate(startTime, endTime) {
      if (startTime >= endTime) {
        return 0
      }
      console.log('开始计算')
      const lastDay = this.getLastDay(endTime.getFullYear(), endTime.getMonth() + 1)
      startTime = this.carryTime(startTime)
      endTime = this.carryTime(endTime)
      let totalTime = 0
      if (startTime.getDay() === 6 || startTime.getDay() === 0) {
        totalTime = endTime.getDate() - startTime.getDate()
      } else {
        totalTime = Math.floor(((endTime - startTime) / (3600 * 1000)) / 24)
      }
      const tempStartTime = new Date()
      tempStartTime.setTime(startTime.getTime())
      let temp = 0
      if (endTime.getDate() === lastDay) {
        while (tempStartTime.getDate() <= endTime.getDate()) {
          if (tempStartTime.getDay() === 6 && tempStartTime.getDay() === 0) {
            totalTime--
          }
          tempStartTime.setDate(tempStartTime.getDate() + 1)
          if (tempStartTime.getDate() === 31) {
            break
          }
        }
      } else {
        while (tempStartTime.getDate() <= endTime.getDate()) {
          if (tempStartTime.getDay() === 6 || tempStartTime.getDay() === 0) {
            totalTime--
          }
          tempStartTime.setDate(tempStartTime.getDate() + 1)
        }
      }
      if (startTime.getHours() <= 12 && endTime.getHours() >= 13) {
        temp += -0.1
      }
      if (startTime.getHours() * 60 + startTime.getMinutes() !== endTime.getHours() * 60 + endTime.getMinutes()) {
        do {
          if (startTime.getDay() === 6 || startTime.getDay() === 0) {
            startTime.setDate(startTime.getDate() + 1)
            startTime.setHours(9)
            startTime.setMinutes(0)
            continue
          }
          if (startTime.getHours() * 60 + startTime.getMinutes() >= 9 * 60 && startTime.getHours() * 60 + startTime.getMinutes() < 18 * 60) {
            temp += 0.05
          }
          startTime.setTime(startTime.getTime() + 0.5 * 3600 * 1000)
        } while (startTime.getHours() * 60 + startTime.getMinutes() !== endTime.getHours() * 60 + endTime.getMinutes())
      }
      totalTime += Math.floor(temp / 0.8)
      totalTime += temp % 0.8
      totalTime = Math.round(totalTime * 100) / 100
      const days = Math.floor(totalTime)
      const hours = Math.round((totalTime - days) * 100) / 10
      const duration = days * 8 + hours
      return days + '天' + hours + '小时'
    },
    carryTime(date) {
      if (date.getMinutes() > 0 && date.getMinutes() < 15) {
        date.setMinutes(0)
      }
      if (date.getMinutes() >= 15 && date.getMinutes() < 30) {
        date.setMinutes(30)
      }
      if (date.getMinutes() > 30 && date.getMinutes() < 45) {
        date.setMinutes(30)
      }
      if (date.getMinutes() >= 45) {
        date.setHours(date.getHours() + 1)
        date.setMinutes(0)
      }
      return date
    },
    // 获取每个月最后一天
    getLastDay(year, month) {
      let new_year = year // 取当前的年份
      let new_month = month++// 取下一个月的第一天，方便计算（最后一天不固定）
      if (month > 12) { // 如果当前大于12月，则年份转到下一年
        new_month -= 12 // 月份减
        new_year++ // 年份增
      }
      const new_date = new Date(new_year, new_month, 1)
      return (new Date(new_date.getTime() - 1000 * 60 * 60 * 24)).getDate()// 获取当月最后一天日期
    },
    employeeData(data) {
      this.dataForm.leaveName = this.labelName(data.label)
      let dept = this.$getDepartment(data.value);
      this.dataForm.deptId = dept.deptId
      this.dataForm.deptName = dept.deptName
    },
    init() {
      this.visible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].resetFields()
        if (this.dataForm.id) {
          this.getInfo()
        }
      })
    },
    // 获取信息
    getInfo() {
      this.$http.get(`/personnel/leaverecord/${this.dataForm.id}`).then(({data: res}) => {
        if (res.code !== 0) {
          return this.$message.error(res.msg)
        }
        this.dataForm = {
          ...this.dataForm,
          ...res.data
        }
      }).catch(() => {
      })
    },
    // 表单提交
    dataFormSubmitHandle: debounce(function () {
      this.$refs['dataForm'].validate((valid) => {
        if (!valid) {
          return false
        }
        let message = '';
        if (this.dataForm.id) {
          message = '此操作将修改该数据'
        } else {
          message = '此操作将新增一条数据'
        }
        this.$confirm(message + ', 是否继续?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
          center: true
        }).then(() => {
          this.$http[!this.dataForm.id ? 'post' : 'put']('/personnel/leaverecord/', this.dataForm).then(({data: res}) => {
            if (res.code !== 0) {
              return this.$message.error(res.msg)
            }
            this.$message({
              message: this.$t('prompt.success'),
              type: 'success',
              duration: 500,
              onClose: () => {
                this.visible = false
                this.$emit('refreshDataList')
              }
            })
          }).catch(() => {
          })
        }).catch(() => {
          this.$message({
            type: 'info',
            message: '已取消'
          });
        });
      })
    }, 1000, {'leading': true, 'trailing': false})
  }
}
</script>
