{"name": "yuqun-ui", "version": "5.2.0", "private": true, "scripts": {"serve": "vue-cli-service serve", "build": "vue-cli-service build", "build:prod": "vue-cli-service build --mode production", "build:sit": "vue-cli-service build --mode production.sit", "build:uat": "vue-cli-service build --mode production.uat"}, "babel": {"presets": ["@babel/preset-env"], "plugins": ["@babel/plugin-syntax-import-meta"]}, "dependencies": {"@mdi/font": "^7.2.96", "@zxing/library": "^0.20.0", "axios": "^0.19.2", "babel-eslint": "^8.0.1", "babel-plugin-component": "^1.1.1", "core-js": "^3.6.5", "dayjs": "^1.11.13", "echarts": "^5.5.1", "element-theme": "^2.0.1", "element-ui": "^2.15.7", "event-source-polyfill": "^1.0.31", "html5-qrcode": "^2.3.8", "js-cookie": "^2.2.1", "js-md5": "^0.8.3", "jsbarcode": "^3.11.5", "lib-flexible": "^0.3.2", "lodash": "^4.17.19", "moment": "^2.29.4", "qrcode": "^1.5.3", "qrcodejs2": "^0.0.2", "qs": "^6.9.4", "quill": "^1.3.7", "sass": "^1.26.5", "sass-loader": "^9.0.2", "screenfull": "^4.2.1", "suggestion-baidu": "^0.0.2", "svg-sprite-loader": "^5.0.0", "vant": "^2.13.0", "vue": "^2.6.11", "vue-cron": "^1.0.9", "vue-i18n": "^8.18.2", "vue-qrcode-reader": "^3.1.8", "vue-router": "3.0.7", "vue-virtual-scroller": "^1.1.2", "vuex": "^3.6.2"}, "devDependencies": {"@babel/plugin-syntax-import-meta": "^7.10.4", "@babel/preset-env": "^7.22.20", "@vue/cli-plugin-babel": "^4.4.6", "@vue/cli-service": "^4.4.6", "element-theme-chalk": "^2.15.7", "natives": "^1.1.6", "vue-template-compiler": "^2.6.11"}, "browserslist": ["> 1%", "last 2 versions", "not ie <= 10"], "engines": {"node": ">= 8.11.1", "npm": ">= 5.6.0"}}