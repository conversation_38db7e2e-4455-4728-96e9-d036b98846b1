<template>
  <div>
    <el-cascader v-model="employees"
                 ref="myCascader"
                 :placeholder="placeholder"
                 @change="interceptAfterChange"
                 :options="options"
                 :show-all-levels="showAllLevels"
                 filterable
                 :clearable="clearable"
                  style="width: 13vw;">
    </el-cascader>
  </div>
</template>
<script>
export default {
  name: 'EmployeeComponent',
  data () {
    return {
      options:[],
      employees:'',
      //默认路径
      path:`/sys/dept/departmentEmployees`,
    }
  },
  model:{
    prop:'value',
  },
  props: {
    placeholder: {
      type: String,
      required: false,
    },
    defaultValue: {
      type: Boolean,
      default:true
    },
    showAllLevels: {
      type: Boolean,
      default: false,
    },
    filterable: {
      type: Boolean,
      default: false,
    },
    clearable: {
      type: Boolean,
      default: true,
    },
    value: {
      type: String,
      default: '',
    },
    //查询部门  成形 制一:1641701467113046018 制三:1641701649229725698 品修 制二:1641701552458743810 品检 品管:1641701892159619073
    departments:{
      type:Array,
      default:function () {
        return []
      }
    }
  },
  mounted() {
    console.log(this.departments.length !== 0)
    if(this.departments.length !== 0){
      this.path=`/sys/dept/departmentEmployeesArray`
      this.searchForEmployeesArray()
    }else {
      this.searchForEmployees()
    }
    if(this.defaultValue){
      this.setDefaultValues()
    }
  },
  watch:{
    value(newValue){
      if (newValue) {
        // 如果父组件传递过来的值不为空，则使用这个值
        this.employees = newValue;
        this.$emit('input', this.employees);
        this.getEmployeeName(newValue)
      }
    },
    departments(newValue){
      if(newValue){
        this.path=`/sys/dept/departmentEmployeesArray`
        this.searchForEmployeesArray()
      }
    }
  },
  methods: {
    getEmployeeName(value){
      if(value){
        //拿到下拉列表value所对应的整个对象的数据
        const selectedItem = window.SITE_CONFIG['employeesList'].find(item => item.value === value);
        console.log(selectedItem,'员工数据===================')
        this.$emit('employeeData', selectedItem);
      }
    },
    //设置默认值
    setDefaultValues(){
      let user = JSON.parse(sessionStorage.getItem('users')); // 从session中获取数据
      if(!this.value){
        this.employees = user.id != null ? user.id : window.SITE_CONFIG['users'].id
      }
      this.$emit('input', this.employees);
      this.getEmployeeName(this.employees)
    },
    searchForEmployeesArray(){
      this.$http.post(this.path,this.departments).then(({ data: res }) => {
        if (res.code !== 0) {
          return this.$message.error(res.msg)
        }
        this.options = res.data
      }).catch(() => {})
    },
    searchForEmployees(){
      this.$http.get(this.path).then(({ data: res }) => {
        if (res.code !== 0) {
          return this.$message.error(res.msg)
        }
        this.options = res.data
      }).catch(() => {})
    },
     interceptAfterChange(value){
      const lastOption = value[value.length - 1];
      if(lastOption === "111111111111"){
        this.path = `/sys/dept/departmentEmployees`
        this.searchForEmployees()
        // 获取选项的路径
        const path = this.options.map(item => item.value);
        // 展开选项
        this.$refs.myCascader.expand(path);
      }else if(value.length > 0) {
        this.employees = lastOption
        this.$emit('input', this.employees);
        this.getEmployeeName(this.employees)
      }
    }
  }
}
</script>
