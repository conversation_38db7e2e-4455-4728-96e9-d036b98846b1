<template>
  <div>
    <el-select
        :value="value"
        @change="changeReturn"
        :filterable="filterable"
        :remote="remote"
        :remote-method="customFilter"
        :allow-create="allowCreate"
        :default-first-option="defaultFirstOption"
        :clearable="clearable"
        :disabled="disabled"
        :placeholder="placeholder"
        :loading="loading"
        >
      <el-option
          v-for="item in showOptions"
          :key="item.value"
          :label="item.label"
          :value="item.value">
      </el-option>
    </el-select>
  </div>
</template>
<script>
import {MessageBox} from "element-ui";
import {getDictLabel} from "@/utils/index"
import th from "element-ui/src/locale/lang/th";

export default {
  name: 'BatchUnderComponent',
  data () {
    return {
      showOptions:[],
      options: [],
      loading: false,
      path:''
    }
  },
  model:{
    prop:'value',
  },
  props: {
    placeholder: {
      type: String,
      default:'请输入批号'
    },
    // 是否显示有无建立次批
    displayOrNot:{
      type:Boolean,
      default:true
    },
    clearable: {
      type: Boolean,
      default: true,
    },
    disabled: {
      type: Boolean,
      default: false,
    },
    remote: {
      type: Boolean,
      default: false,
    },
    filterable: {
      type: Boolean,
      default: true,
    },
    allowCreate: {
      type: Boolean,
      default: false,
    },
    defaultFirstOption: {
      type: Boolean,
      default: false,
    },
    value: {
      type: String,
      default: '',
    },
  },
  mounted() {
    if(this.displayOrNot){
      this.selectButchNumberInfo();
      console.log("上=======================")
    }else {
      this.selectBatchNumberList();
      console.log("下=======================")
    }
  },

  methods: {
    changeReturn(value){
      //拿到下拉列表value所对应的整个对象的数据
      const selectedItem = this.options.find(item => item.value === value);
      console.log("触发组件change事件")
      this.$emit('input',value)
      this.$emit('batchSubBatchData',selectedItem)
    },
    handleVisibleChange(visible) {
      if (visible) {
        // 下拉框显示时执行的操作
        console.log('下拉框显示了');
        // 这里可以触发相应的事件或执行其他操作
        this.selectButchNumberInfo()
      }
    },
    //自定义查询方法
    customFilter(val){
      let filterList = this.options.filter((item) => {
        return item.label.includes(val)
      });
      if(filterList.length > 200){
        this.showOptions = filterList.slice(0,200)
      }else {
        this.showOptions = filterList
      }
    },
    //查询批号以及次批
    selectButchNumberInfo(partId) {
      console.log(partId,'0000000000000000000')
      if(partId){
        this.path = `/batch/batch/querySecondaryBatch?partId=${partId}`
      }else {
        this.path = `/batch/batch/querySecondaryBatch`
      }
      this.$http.get(this.path).then(({data: res}) => {
        if (res.code !== 0) {
          return this.$message.error(res.msg)
        }
        this.options = res.data.map((obj) => {
          return {
            label: getDictLabel("size_category",obj.sizeCategory)+obj.batchNumber+
                (obj.subBatchNumber != null ? ('-'+obj.subBatchNumber) : '(未建立次批)')+'('+obj.designation+'('+obj.moldGroup+')'+')',
            value: obj.subBatchId,
            data:obj
          }
        })
        this.showOptions = this.options.slice(0,200)
      }).catch(() => {
      })
    },
    selectBatchNumberList(){
      this.$http.get(`/batch/batch/selectBatchNumberList`).then(({data: res}) => {
        if (res.code !== 0) {
          return this.$message.error(res.msg)
        }
        this.options = res.data.map((obj) => {
          return {
            label: getDictLabel("size_category",obj.sizeCategory)+obj.batchNumber,
            value: obj.id,
            data:obj
          }
        })
        this.showOptions = this.options.slice(0,200)
      }).catch(() => {
      })
    },
    //  输出选择日志
    handleSelect(item) {
      this.dataForm.partId = item.partId
      this.dataForm.batchId = item.batchId
      if(item.batchNumber != null && item.batchNumber != ''){
        MessageBox.confirm('查无此批号，是否进行生成？', '提示', {
          confirmButtonText: '生成批号',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          this.oldBatchAddOrUpdateHandle()
          return;
        }).catch(() => {
          this.initializations()
        })
        this.dataForm.batchNumber = item.batchNumber
      }
      this.dataForm.designation = item.designation
      this.dataForm.selectBatchNumber = item.selectBatchNumber
      this.dataForm.temporaryBatchNumber = item.temporaryBatchNumber
      if(item.subBatchNumber != null && item.subBatchNumber != ''){
        MessageBox.confirm('该批号已有批次，是使用现有批次还是生成新批次使用？', '提示', {
          confirmButtonText: '生成新批次',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          this.insertBatchOrSubBatch("次批号",item.batchId)
          return;
        }).catch(() => {

        })
        this.dataForm.subBatchNumber = item.subBatchNumber
      }else {
        this.showMessage("该批号还未生成批次是否进行生成次批次？",false,item.batchId)
        return;
      }
      this.dataForm.subBatchId = item.subBatchId
    },
  }
}
</script>

