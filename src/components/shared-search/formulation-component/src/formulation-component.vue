<template>
  <div>
    <el-select
        :value="value"
        @change="changeReturn"
        :filterable="filterable"
        :remote="remote"
        :clearable="clearable"
        :placeholder="placeholder"
        :loading="loading">
      <el-option
          v-for="item in options"
          :key="item.value"
          :label="item.label"
          :value="item.value">
      </el-option>
    </el-select>
  </div>
</template>
<script>
import text from "quill/blots/text";

export default {
  name: 'FormulationComponent',
  data () {
    return {
      options: [],
      list: [],
      loading: false,
    }
  },
  model:{
    prop:'value'
  },
  props: {
    placeholder: {
      type: String,
      required: false,
    },
    clearable: {
      type: Boolean,
      default: true,
    },
    remote: {
      type: Boolean,
      default: false,
    },
    filterable: {
      type: Boolean,
      default: true,
    },
    value: {
      type: String,
      default: '',
    },
  },
  mounted() {
    this.getFormulasList()
  },
  methods: {
    changeReturn(value){
      //拿到下拉列表value所对应的整个对象的数据
      const selectedItem = this.options.find(item => item.value === value);
      this.$emit('input',value)
      this.$emit('FormulasData',selectedItem.data)
    },
    //  获取配方列表
    getFormulasList() {
      this.$http.get(`/produce/formulation/queryAllFormulasCode`).then(({data: res}) => {
        if (res.code !== 0) {
          return this.$message.error(res.msg)
        }
         this.options = res.data.map((obj) => {
          return {
            label: obj.formulationCode,
            value: obj.id,
            data:obj
          }
        })
      }).catch(() => {
      })
    },
  }
}
</script>

