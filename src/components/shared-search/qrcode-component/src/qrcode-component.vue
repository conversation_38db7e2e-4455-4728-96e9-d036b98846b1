<template>
  <div class="QRCode" :style="{'width':width, 'height':height}">
    <canvas :width="width" :height="height" :fontSize="fontSize" ref="qrcode"></canvas>
  </div>

</template>
<script>
import QRCode from "qrcode";


export default {
  name: "QrcodeComponent",
  props: {
    content: {
      type: Object,
      require: true
    },
    fontSize: {
      type: Number,
      default: 10
    },
    width: {
      type: Number,
      default: 20
    },
    height: {
      type: Number,
      default: 20
    },
  },
  data(){
    return{
      dataForms:{
        BatchId:'',
        batchNumber:'',
        sizeCategory:'',
        subBatchId:'',
        subBatchNumber:'',
      },
      data:''
    }
  },
  computed: {},
  mounted() {
    this.dataInitialization();
    this.generateQRCode();
  },
  methods: {
    dataInitialization(){
      for (const key in this.content) {
        if (this.content.hasOwnProperty(key)) {
          if (this.dataForms.hasOwnProperty(key)) {
            this.dataForms[key] = this.content[key];
          }
        }
      }
    },
    generateQRCode() {
      const canvas = this.$refs.qrcode;
      const ctx = canvas.getContext('2d');

      // Set canvas size
      canvas.width = this.width;
      canvas.height = this.height;

      // Generate QR Code
      QRCode.toDataURL(JSON.stringify(this.dataForms), {
        margin: 1,
        width: this.width,
        height: this.height,
        errorCorrectionLevel: 'H'
      }).then((url) => {
        // Draw QR Code on canvas
        const img = new Image();
        img.src = url;
        img.onload = () => {
          ctx.drawImage(img, 0, 0, this.width, this.height);

          // Add text below QR Code
          ctx.fillStyle = '#000000';
          ctx.font = `16px ${this.font}`;
          ctx.textAlign = 'center';
          ctx.fillText(JSON.stringify(this.dataForms), this.width / 2, this.height + 20);
        };
      });
    },
 /*   init() {
      let elementById = document.getElementById(this.canvasId);
      let width = this.width,
          height = this.height;
      QRCode.toCanvas(
          elementById,
          this.content,
          { width: 100, margin: 0, scale: 2 },
          error => {
            console.log(error)
          }
      );

    },
    getUUID() {
      let d = new Date().getTime();
      let uuid = "xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(
          /[xy]/g,
          function(c) {
            let r = (d + Math.random() * 16) % 16 | 0;
            d = Math.floor(d / 16);
            return (c == "x" ? r : (r & 0x7) | 0x8).toString(16);
          }
      );
      return uuid;
    }*/
    //下载二维码
    // loadCode() {
    //   let [F, S, a] = [
    //     navigator.userAgent.indexOf("Firefox") > -1,
    //     document.getElementById(this.canvasId).toDataURL("image/png"),
    //     document.createElement("a")
    //   ];
    //   // var dataurl = showel.toDataURL();
    //   var arr = S.split(","),
    //     mime = arr[0].match(/:(.*?);/)[1],
    //     bstr = atob(arr[1]),
    //     n = bstr.length,
    //     u8arr = new Uint8Array(n);
    //   while (n--) {
    //     u8arr[n] = bstr.charCodeAt(n);
    //   }
    //   var file = new File([u8arr], this.codeName + ".png", { type: mime });
    //   $A.FU(file, data => {
    //     // alert(1)
    //     // window.location.href = data;
    //     [a.href, a.download] = [data, this.codeName];
    //     // a.download = '二维码';
    //     if (F) {
    //       let evt = document.createEvent("MouseEvents");
    //       evt.initEvent("click", true, true);
    //       a.dispatchEvent(evt);
    //     } else {
    //       a.click();
    //     }
    //   });
    // },
    // insertContentLoad(content, size) {
    //   const ele = document.createElement("canvas");
    //   ele.style.width = size.width || "100" + "px";
    //   ele.style.height = size.height || "100" + "px";
    //   QRCode.toCanvas(
    //     ele,
    //     content,
    //     {
    //       width: size.width || "100",
    //       height: size.height || "100",
    //       toSJISFunc: QRCode.toSJIS
    //     },
    //     error => {}
    //   );
    //   let [F, S, a] = [
    //     navigator.userAgent.indexOf("Firefox") > -1,
    //     ele.toDataURL("image/png"),
    //     document.createElement("a")
    //   ];
    //   [a.href, a.download] = [S, size.name];
    //   // a.download = '二维码';
    //   if (F) {
    //     let evt = document.createEvent("MouseEvents");
    //     evt.initEvent("click", true, true);
    //     a.dispatchEvent(evt);
    //   } else {
    //     a.click();
    //   }
    // }
  },
  watch: {
    /*content(val) {
      this.init();
    }*/
  }
};
</script>
<style lang="scss" scoped>
/*.QRCode {
  display: inline-block;
  position: relative;
  overflow: hidden;
  .QQMode {
    position: absolute;
    left: 0;
    bottom: 100%;
    right: 0;
    height: 0;
    background-color: rgba(0, 0, 0, 0.45);
    transition: all 200ms ease-in-out;
    cursor: pointer;
    color: #fff;
    display: flex;
    justify-content: space-around;
    align-items: center;
    font-size: 20px;
    font-weight: bolder;
    box-sizing: border-box;
    padding: 10px;
  }
}
.QRCode:hover .QQMode {
  bottom: 0;
  height: 100%;
}*/
</style>
