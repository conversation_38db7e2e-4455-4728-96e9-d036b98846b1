<template>
  <div>
    <el-upload
        :action="action"
        :on-change="handleChange"
        :list-type="listType"
        ref="upload"
        :headers="headers"
        :file-list="imageRemarks"
        :auto-upload="autoUpload"
        :multiple="true"
        :before-remove="() => false"
        :limit="limit">
      <i slot="default" class="el-icon-plus"></i>
      <div slot="file" slot-scope="{file}">
        <img class="el-upload-list__item-thumbnail" :src="file.url" alt="">
        <span class="el-upload-list__item-actions">
          <span class="el-upload-list__item-preview" @click="handlePictureCardPreview(file)">
            <i class="el-icon-zoom-in"></i>
          </span>
          <span v-if="!disabled" class="el-upload-list__item-delete" @click="handleDownload(file)">
            <i class="el-icon-download"></i>
          </span>
          <span v-if="!disabled" class="el-upload-list__item-delete" @click="handleRemove(file)">
            <i class="el-icon-delete"></i>
          </span>
          <el-input type="text" v-model="file.remark" placeholder="图片备注" @change="handleChange(file)" clearable></el-input>
        </span>
      </div>
    </el-upload>
    <el-dialog :visible.sync="dialogVisible" append-to-body>
      <img width="100%" :src="dialogImageUrl" alt="">
    </el-dialog>
  </div>
</template>
<script>
import Cookies from "js-cookie";

export default {
  name: 'ImageUploadComponent',
  data() {
    return {
      headers: {'token': Cookies.get('token')},
      dialogVisible: false,
      disabled: false,
      imageRemarks: [],
      imageFile: [],
      dialogImageUrl: '',
      remark: [],
    };
  },
  props: {
    action: {
      type: String,
      default: '#',
    },
    limit: {
      type: Number,
      default: 1,
    },
    listType: {
      type: String,
      default: 'picture-card',
    },
    autoUpload: {
      type: Boolean,
      default: false,
    },
  },
  watch:{
    imageRemarks(){
      this.imageFile = new FormData();
      this.imageRemarks.forEach(files => {
        this.imageFile.append('files', files.raw, files.name);
      });
      // 将文件对象传给父组件v-model所绑定的属性
      this.$emit("input",this.imageRemarks);
      //将文件信息传给父组件
      this.$emit("pictureInformation",this.imageFile)
    }
  },
  methods: {
    submitUpload() {
      console.log(this.imageRemarks,'图片数据')
      this.uploadAll()
      this.imageRemarks=[]
    },
    uploadAll() {
      const formData = new FormData()
      this.imageRemarks.forEach(item => {
        formData.append('file', item.raw)
        formData.append('remark', item.remark)
        formData.append('id','123456')
      })
      this.upload(formData)
    },
    upload(formData) {
      // 创建请求实例
      const req = new XMLHttpRequest()

      // 监听上传进度
      req.upload.onprogress = event => {
        console.log(event,'正在上传中')
        // 处理上传进度
      }

      // 定义响应 handling
      req.onload = event => {
        console.log(event,'响应结果')
        // 处理响应结果
      }

      // 开始上传
      req.open('POST', this.action)

      // 添加请求头
      req.setRequestHeader('token',Cookies.get('token'))

      // 发送请求
      req.send(formData)
    },
    handleChange(file) {
      const index = this.imageRemarks.findIndex(image => image.url === file.url);
      if (index !== -1) {
        // 修改已存在的图片对象
        this.imageRemarks[index].remark = file.remark;
      } else {
        // 添加新的图片对象
        this.imageRemarks.push(file);
      }
      // 将文件对象传给父组件v-model所绑定的属性
      this.$emit("input",this.imageRemarks);
    },
    handleRemove(file) {
      const index = this.imageRemarks.findIndex(image => image.url === file.url);
      if (index !== -1) {
        this.imageRemarks.splice(index, 1);
        this.remark.splice(index, 1);
      }
    },
    handlePictureCardPreview(file) {
      this.dialogImageUrl = file.url
      this.dialogVisible = true;
    },
    handleDownload(file) {
      console.log(file);
    }
  }
}
</script>

<style>
.upload-container {
  display: inline-block;
  width: 104px;
  height: 104px;
}

.image-container {
  display: flex;
  flex-wrap: wrap;
}

.image-item {
  position: relative;
  width: 100px;
  height: 100px;
  margin: 5px;
  cursor: pointer;
}

.image-item.selected:before {
  content: "";
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 1;
}

.image-item img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.image-item input[type="text"] {
  position: absolute;
  bottom: 5px;
  left: 50%;
  transform: translateX(-50%);
  width: 90%;
}
</style>
<!--<template>
  <div>
    <el-upload
        action="https://jsonplaceholder.typicode.com/posts/"
        list-type="picture-card"
        :on-preview="handlePictureCardPreview"
        :on-remove="handleRemove"
        :before-remove="beforeRemove"
        :before-upload="beforeUpload"
        :multiple="true"
        :limit="10"
        :on-exceed="handleExceed"
        :file-list="fileList"
        @change="handleChange"
    >
      <i class="el-icon-plus"></i>
    </el-upload>
    <el-dialog :visible.sync="dialogVisible" append-to-body>
      <img width="100%" :src="dialogImageUrl" alt="" />
    </el-dialog>
    <div v-for="(file, index) in fileList" :key="index">
      <el-input v-model="file.remark" placeholder="请输入图片备注"></el-input>
    </div>
  </div>
</template>

<script>



export default {
  name:'ImageUploadComponent',
  data() {
    return {
      fileList: [],
      dialogImageUrl: '',
      dialogVisible: false,
    };
  },
  watch:{
    fileList:{
      handler(newVal, oldVal) {
        console.log('进来了',newVal,oldVal)
        if (newVal.length > oldVal.length) {
          // 数组长度增加的逻辑
          console.log('数组长度增加了');
        }
      },
      deep: true, // 监听数组内部变化
    },
  },
  methods: {
    beforeUpload(fileList){
      console.log(fileList)
      /*this.$set(this, 'fileList',fileList);*/
    },
    handleChange(file, fileList) {
      console.log('el-upload来了')
      console.log(file,fileList,'数据')
      this.fileList = fileList;
    },
    handleRemove(file, fileList) {
      console.log(file, fileList);
    },
    handlePictureCardPreview(file) {
      console.log(this.fileList,'图片1')
      this.dialogImageUrl = file.url;
      this.dialogVisible = true;
    },
    beforeRemove(file, fileList) {
      this.$set(this, 'fileList', fileList);
      return this.$confirm(`确定移除 ${ file.name }？`);
    },
    handleExceed(files, fileList) {
      this.$message.warning(`最多只能上传 10 张图片，已选择 ${ files.length } 张图片，共选择了 ${ files.length + fileList.length } 张图片`);
    },
  }
}
</script>-->


