<template>
  <div>
    <el-select
        :value="value"
        @change="changeReturn"
        :filterable="filterable"
        :remote="remote"
        :clearable="clearable"
        :placeholder="placeholder"
        :allow-create="allowCreate"
        :default-first-option="defaultFirstOption"
        :disabled="disabled"
        :loading="loading">
      <el-option
          v-for="item in options"
          :key="item.value"
          :label="item.label"
          :value="item.value">
      </el-option>
    </el-select>
  </div>
</template>
<script>
export default {
  name: 'PartNumberComponent',

  data () {
    return {
      options: [],
      list: [],
      loading: false,
      path:`/fabricate/part/getPartDesignation`
    }
  },
  model:{
    prop:'value'
  },
  props: {
    placeholder: {
      type: String,
      default:'请输入品号',
    },
    clearable: {
      type: Boolean,
      default: true,
    },
    disabled: {
      type: Boolean,
      default: false,
    },
    remote: {
      type: Boolean,
      default: false,
    },
    filterable: {
      type: Boolean,
      default: true,
    },
    allowCreate: {
      type: <PERSON>olean,
      default: false,
    },
    defaultFirstOption: {
      type: Boolean,
      default: false,
    },
    value: {
      type: String,
      default: '',
    },
    customerId:{
      type:String,
      default:''
    }
  },
  mounted() {
    if(1===1){
      this.getPartList()
    }else {
      this.getPartList()
    }
  },
  watch:{
    customerId(value){
      if(value){
        this.path = `/fabricate/part/getPartDesignation?customerId=${this.customerId}`
        this.getPartList()
      }
    }
  },
  methods: {
    changeReturn(value){
      //拿到下拉列表value所对应的整个对象的数据
      const selectedItem = this.options.find(item => item.value === value);
      this.$emit('input',value)
      if (value){
        this.$emit('partData',selectedItem.data)
      }else {
        this.$emit('partData','')
      }
      this.$emit('change-event', value);
      this.$emit('input', value);
    },
    // 获取部品列表
    getPartList() {
      this.$http.get(this.path).then(({data: res}) => {
        if (res.code !== 0) {
          return this.$message.error(res.msg)
        }
         this.options = res.data.map((obj) => {
          return {
            label: obj.designation,
            value: obj.id,
            data:obj
          }
        })
      }).catch(() => {
      })
    },
  }
}
</script>

