<template>
  <el-dialog :visible.sync="visible" title="数据导出" :close-on-click-modal="false" :close-on-press-escape="false" center append-to-body width="30%">
    <div>
      <div>
        <span class="font_size">按日期导出:</span>
        <el-date-picker
            v-model="day"
            type="daterange"
            value-format="yyyy-MM-dd"
            range-separator="-"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            :picker-options="pickerOptionsDay" style="margin-left: 15px">
        </el-date-picker>
      </div>
      <div style="margin-top: 30px">
        <span class="font_size">按月份导出:</span>
        <el-date-picker
            v-model="month"
            type="monthrange"
            value-format="yyyy-MM"
            range-separator="-"
            start-placeholder="开始月份"
            end-placeholder="结束月份"
            :picker-options="pickerOptionsMonth" style="margin-left: 15px; width: 350px">
        </el-date-picker>
      </div>
      <div style="margin-top: 20px; text-align: center">
        <el-button type="info" @click="exportHandle()">{{ $t('export') }}</el-button>
      </div>
    </div>
  </el-dialog>
</template>
<script>
import qs from "qs";
import Cookies from "js-cookie";

export default {
  name: 'ExcelexportComponent',
  data () {
    return {
      visible:false,
      accordingTo:'',
      day:'',
      month:'',
      year:'',
      pickerOptionsDay: {
        shortcuts: [{
          text: '最近一周',
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
            picker.$emit('pick', [start, end]);
          }
        }, {
          text: '最近一个月',
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
            picker.$emit('pick', [start, end]);
          }
        }, {
          text: '最近三个月',
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
            picker.$emit('pick', [start, end]);
          }
        }]
      },
      pickerOptionsMonth: {
        shortcuts: [{
          text: '本月',
          onClick(picker) {
            picker.$emit('pick', [new Date(), new Date()]);
          }
        }, {
          text: '今年至今',
          onClick(picker) {
            const end = new Date();
            const start = new Date(new Date().getFullYear(), 0);
            picker.$emit('pick', [start, end]);
          }
        }, {
          text: '最近六个月',
          onClick(picker) {
            const end = new Date();
            const start = new Date();
            start.setMonth(start.getMonth() - 6);
            picker.$emit('pick', [start, end]);
          }
        }]
      },
      dataForm:{
        startDate:'',
        endDate:'',
      },
      mixinViewModuleOptions:{
        exportURL:''
      }
    }
  },
  mounted() {

  },
  activated() {

  },
  computed:{

  },
  watch:{
    day(newVal, oldVal) {
      if (newVal) {
        this.month = '';
        this.year = '';
        this.splitArray(newVal)
      }
    },
    month(newVal, oldVal) {
      if (newVal) {
        this.day = '';
        this.year = '';
        this.splitArray(newVal)
      }
    },
    year(newVal, oldVal) {
      if (newVal) {
        this.day = '';
        this.month = '';
        this.splitArray(newVal)
      }
    }
  },
  methods:{
    splitArray (array){
      console.log(array)
      this.dataForm.startDate = array[0];
      this.dataForm.endDate = array[1];
    },
    // 导出
    exportHandle() {
      var params = qs.stringify({
        'token': Cookies.get('token'),
        ...this.dataForm
      })
      console.log('未调先调================')
      window.location.href = `${window.SITE_CONFIG['apiURL']}${this.mixinViewModuleOptions.exportURL}?${params}`
      this.visible = false
    },
    init(){
      this.visible = true;
    }
  }
}
</script>
