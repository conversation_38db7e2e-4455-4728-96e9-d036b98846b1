<template>
  <div>
    <el-select
        :value="value"
        @change="changeReturn"
        :filterable="filterable"
        :remote="remote"
        :clearable="clearable"
        :placeholder="placeholder"
        :loading="loading">
      <el-option
          v-for="item in options"
          :key="item.value"
          :label="item.label"
          :value="item.value">
      </el-option>
    </el-select>
  </div>
</template>
<script>
import text from "quill/blots/text";

export default {
  name: 'BusinessOrderComponent',
  data () {
    return {
      options: [],
      loading: false,
      parameter: '',
    }
  },
  model:{
    prop:'value'
  },
  props: {
    placeholder: {
      type: String,
      required: false,
    },
    // 是否查询客户名称
    queryName: {
      type: Boolean,
      default: false,
    },
    clearable: {
      type: Boolean,
      default: true,
    },
    remote: {
      type: Boolean,
      default: false,
    },
    filterable: {
      type: Boolean,
      default: true,
    },
    value: {
      type: String,
      default: '',
    },
  },
  mounted() {
    if(true){
      this.getOrderList()
    }
  },
  methods: {
    changeReturn(value){
      //拿到下拉列表value所对应的整个对象的数据
      const selectedItem = this.options.find(item => item.value === value);
      this.$emit('input',value)
      if (value){
        this.$emit('orderData',selectedItem.data)
      }else {
        this.$emit('orderData','')
      }
    },
    // 获取订单信息
    getOrderList() {
      this.$http.get(`/orders/businessorder/getOrderList`).then(({ data: res }) => {
        if (res.code !== 0) {
          return this.$message.error(res.msg)
        }
        this.options = res.data.map((obj) => {
          return {
            value: obj.id,
            label: obj.orderNumber,
            data: obj,
          }
        })
      }).catch(() => {})
    },
  }
}
</script>

