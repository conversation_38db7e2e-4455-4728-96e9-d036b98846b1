<template>
  <div>
    <el-select
        :value="value"
        @change="changeReturn"
        :filterable="filterable"
        :remote="remote"
        :clearable="clearable"
        :placeholder="placeholder"
        :loading="loading">
      <el-option
          v-for="(item,index) in options"
          :key="index"
          :label="item.label"
          :value="item.value">
      </el-option>
    </el-select>
  </div>
</template>
<script>
import {MessageBox} from "element-ui";
import {getDictLabel} from "@/utils/index"

export default {
  name: 'PartInspectionBatchComponent',
  data () {
    return {
      options: [],
      loading: false,
    }
  },
  model:{
    prop:'value'
  },
  props: {
    placeholder: {
      type: String,
      required: false,
    },
    clearable: {
      type: Boolean,
      default: true,
    },
    remote: {
      type: Boolean,
      default: false,
    },
    filterable: {
      type: Boolean,
      default: true,
    },
    value: {
      type: String,
      default: '',
    },
  },
  mounted() {
      this.getPartList();
  },

  methods: {
    changeReturn(value){
      console.log(value)
      //拿到下拉列表value所对应的整个对象的数据
      const selectedItem = this.options.find(item => item.value === value);
      console.log(selectedItem,"partdata===============")
      this.$emit('input',value)
      this.$emit('partSubBatchData',selectedItem.data)
    },
    //  获取部品列表
    getPartList(queryString) {
      this.$http.get(`/fabricate/part/fuzzyQueryPart?designation=` + queryString).then(({data: res}) => {
        if (res.code !== 0) {
          return this.$message.error(res.msg)
        }
        this.options = res.data.map((obj) => {
          return {
            label:obj.designation+ '('+
                (obj.batchNumber != null ?
                    ((getDictLabel("size_category",obj.sizeCategory))+obj.batchNumber)
                    +
                    (obj.subBatchNumber != null ? '-'+obj.subBatchNumber : '(未建立次批)'):'未建立批号')
                +')',
            value:obj.partId,
            data:obj
          }
        })
      }).catch(() => {
      })
    },
  }
}
</script>

