<template>
  <el-select
      :value="value"
      @change="changeReturn"
      :filterable="filterable"
      :remote="remote"
      :clearable="clearable"
      :multiple="multiple"
      :placeholder="placeholder"
      :loading="loading">
    <el-option
        v-for="item in options"
        :key="item.value"
        :label="item.label"
        :value="item.value">
    </el-option>
  </el-select>
</template>
<script>
export default {
  name: 'MachineComponent',
  data () {
    return {
      options:[],
      loading: false,
    }
  },
  model:{
    prop:'value',
    event:'input'
  },
  props: {
    placeholder: {
      type: String,
      required: false,
    },
    clearable: {
      type: Boolean,
      default: true,
    },
    remote: {
      type: Boolean,
      default: false,
    },
    multiple: {
      type: Boolean,
      default: false,
    },
    filterable: {
      type: Boolean,
      default: true,
    },
    value: {
      type: String,
      default: '',
    },
  },
  mounted() {
    this.queryMachineNumberList()
  },
  methods: {
    changeReturn(value){
      //拿到下拉列表value所对应的整个对象的数据
      const selectedItem = this.options.find(item => item.value === value);
      console.log(selectedItem,'值是')
      this.$emit('input',value)
      this.$emit('machineData',selectedItem.data)
    },
    queryMachineNumberList(){
      this.$http.get(`/machine/machine/queryMachineNumberList`).then(({ data: res }) => {
        if (res.code !== 0) {
          return this.$message.error(res.msg)
        }
        this.options = res.data.map((obj) => {
          return {
            label: obj.code+'('+obj.name+')',
            value: obj.id,
            data: obj
          }
        })
      }).catch(() => {})
    }
  }
}
</script>
