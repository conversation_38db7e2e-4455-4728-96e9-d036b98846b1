<template>
  <div>
    <el-cascader :value="value"
                 ref="myCascader"
                 :placeholder="placeholder"
                 @change="interceptAfterChange"
                 :options="options"
                 :props="{ multiple: true }"
                 :show-all-levels="showAllLevels"
                 filterable
                 :clearable="clearable">
    </el-cascader>
  </div>
</template>
<script>
export default {
  name: 'EmployeeComponentList',
  data() {
    return {
      options: [],
      employees: [],
      //默认路径
      path: `/sys/dept/departmentEmployees`,
    }
  },
  model: {
    prop: 'value',
  },
  props: {
    placeholder: {
      type: String,
      required: false,
    },
    defaultValue: {
      type: Boolean,
      default: true
    },
    showAllLevels: {
      type: Boolean,
      default: false,
    },
    filterable: {
      type: Boolean,
      default: false,
    },
    clearable: {
      type: Boolean,
      default: true,
    },
    value: {
      type: Array,
      default: function () {
        return []
      }
    },
    //查询部门  成形 制一:1641701467113046018 制三:1641701649229725698 品修 制二:1641701552458743810 品检 品管:1641701892159619073
    departments: {
      type: Array,
      default: function () {
        return []
      }
    }
  },
  mounted() {
    console.log(this.departments.length !== 0)
    if (this.departments.length !== 0) {
      this.path = `/sys/dept/departmentEmployeesArray`
      this.searchForEmployeesArray()
    } else {
      this.searchForEmployees()
    }
    if (this.defaultValue) {
      this.setDefaultValues()
    }
  },
  watch: {
    value(newValue) {
      console.log(newValue.length, '数组长度')
      if (newValue.length > 0) {
        // 如果父组件传递过来的值不为空，则使用这个值
        console.log(newValue, '值===============')
        this.$emit('input', newValue);
      }
    },
    departments(newValue) {
      if (newValue) {
        this.path = `/sys/dept/departmentEmployeesArray`
        this.searchForEmployeesArray()
      }
    }
  },
  methods: {
    getEmployeeName(value) {
      console.log(value, "valus的值")
      if (value.length !== 0) {
        const selectedItem = []
        for (let i = 0; i < value.length; i++) {
          let array = []
          if (value.length === 1) {
            array = value[0]
          } else {
            array = value[i]
          }
          if (array.length === 1) {
            //拿到下拉列表value所对应的整个对象的数据
            selectedItem.push(window.SITE_CONFIG['employeesList'].find(item => item.value === array[0]))
          } else {
            if (array.length > 1) {
              //拿到下拉列表value所对应的整个对象的数据
              selectedItem.push(window.SITE_CONFIG['employeesList'].find(item => item.value === array[1]))
            }
          }
        }
        console.log(selectedItem, 'selectedItem')
        this.$emit('employeeData', selectedItem);
      }
    },
    //设置默认值
    setDefaultValues() {
      let user = JSON.parse(sessionStorage.getItem('users')); // 从session中获取数据
      if (this.value.length === 0) {
        let employeesId = user.id != null ? user.id : window.SITE_CONFIG['users'].id
        this.$emit('input', [[window.SITE_CONFIG['users'].deptId, employeesId]]);
        this.getEmployeeName([[window.SITE_CONFIG['users'].deptId, employeesId]])
      }

    },
    searchForEmployeesArray() {
      this.$http.post(this.path, this.departments).then(({data: res}) => {
        if (res.code !== 0) {
          return this.$message.error(res.msg)
        }
        this.options = res.data
      }).catch(() => {
      })
    },
    searchForEmployees() {
      this.$http.get(this.path).then(({data: res}) => {
        if (res.code !== 0) {
          return this.$message.error(res.msg)
        }
        this.options = res.data
      }).catch(() => {
      })
    },
    interceptAfterChange(value) {
      const lastOption = value[value.length - 1];
      if (lastOption === "111111111111") {
        this.path = `/sys/dept/departmentEmployees`
        this.searchForEmployees()
        // 获取选项的路径
        const path = this.options.map(item => item.value);
        // 展开选项
        this.$refs.myCascader.expand(path);
      } else if (value.length > 0) {
        this.$emit('input', value);
        this.getEmployeeName(value)
      }
    }
  }
}
</script>
