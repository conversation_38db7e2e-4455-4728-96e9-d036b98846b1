<template>
  <svg :width="0.1" :height="height" :fontSize="fontSize" :displayValue="displayValue" :margin="margin" ref="barcode"></svg>
</template>

<script>
import JsBarcode from 'jsbarcode'

export default {
  name:'BarcodeComponent',
  props: {
    value: {
      type: String,
      required: true
    },
    width: {
      type: Number,
      default: 1
    },
    height: {
      type: Number,
      default: 20
    },
    fontSize:{
      type: Number,
      default: 10
    },
    margin:{
      type: Number,
      default: 0
    },
    displayValue:{
      type: Boolean,
      default: true
    }
  },
  mounted() {
    JsBarcode(this.$refs.barcode, this.value, {
      width: this.width,
      height: this.height,
      fontSize:this.fontSize,
      displayValue:this.displayValue,
      margin:this.margin
    })
  }
}
</script>
