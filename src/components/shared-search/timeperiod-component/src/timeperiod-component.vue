<template>
  <div class="container">
    <el-select v-model="selectedPeriod" @change="updateHours" placeholder="请选择时段">
      <el-option value="上午">上午</el-option>
      <el-option value="下午">下午</el-option>
      <el-option value="晚上">晚上</el-option>
    </el-select>

    <el-select v-model="selectedHour" placeholder="请选择小时">
      <el-option v-for="hour in hours" :key="hour" :value="hour" :label="hour"></el-option>
    </el-select>

    <el-select v-model="selectedMinute" @change="updateMinutes" placeholder="请选择分钟区间">
      <el-option label="0-15" value="0-15"></el-option>
      <el-option label="16-30" value="16-30"></el-option>
      <el-option label="31-45" value="31-45"></el-option>
      <el-option label="46-60" value="46-60"></el-option>
    </el-select>

    <el-select v-model="selectedMinuteDetail" placeholder="请选择具体分钟">
      <el-option v-for="minute in minutes" :key="minute" :value="minute" :label="minute"></el-option>
    </el-select>
  </div>
</template>

<script>
export default {
  name:'TimePeriodComponent',
  data() {
    return {
      selectedPeriod: '',
      selectedHour: '',
      selectedMinute: '',
      selectedMinuteDetail: '',
      hours: [],
      minutes: []
    }
  },
  methods: {
    updateHours() {
      if (this.selectedPeriod === '上午') {
        this.hours = Array.from({length: 6}, (_, i) => (i + 7)+'点');
      } else if (this.selectedPeriod === '下午') {
        this.hours = Array.from({length: 6}, (_, i) => (i + 13)+'点');
      }else if(this.selectedPeriod === '晚上'){
        this.hours = Array.from({length: 4}, (_, i) => (i + 17)+'点');
      }
    },
    updateMinutes() {
      if (this.selectedMinute === '0-15') {
        this.minutes = Array.from({length: 16}, (_, i) => (i)+'分');
      } else if (this.selectedMinute === '16-30') {
        this.minutes = Array.from({length: 15}, (_, i) => (i + 16)+'分');
      } else if (this.selectedMinute === '31-45') {
        this.minutes = Array.from({length: 15}, (_, i) => (i + 31)+'分');
      } else if (this.selectedMinute === '46-60') {
        this.minutes = Array.from({length: 15}, (_, i) => (i + 46)+'分');
      }
    }
  }
}
</script>
