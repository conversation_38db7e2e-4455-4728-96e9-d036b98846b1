<template>
<el-radio-group :value="value+''" @input="$emit('input', $event)">
    <el-radio class="radio" :label="data.dictValue" v-for="data in dataList" :key="data.dictValue" :label-position="'top'">
      {{ data.dictLabel }}
    </el-radio>
  </el-radio-group>

</template>
<script>
import { getDictDataList } from '@/utils'
export default {
  name: 'RenRadioGroup',
  data () {
    return {
      dataList: getDictDataList(this.dictType)
    }
  },
  props: {
    value: [Number, String],
    dictType: String
  }
}
</script>

<style scoped>
.radio{
  margin: 5px;
}
</style>