<template>
    <div>
      <div>
        <el-input v-model="inputText" placeholder="请输入查询数据" @input="judgmentCall">
          <el-button slot="append" icon="el-icon-search" @click=""></el-button>
        </el-input>
      </div>
      <ul>
        <li v-for="(item,index) in listData" :key="index" @click="clickToAssign(item)">
          {{item.value}}
        </li>
      </ul>
    </div>

</template>

<script>

export default {
  name: 'InputSelect',
  props:{
    /*queryData:{
      type:String,
      default() {
        return{}
      }
    },*/
    queryType:{
      type:Number,
      default() {
        return {}
      }
    },
    required: true
  },
  data () {
    return {
      visible: false,
      inputText:'',
      listData:[]
    }
  },
  methods: {
    judgmentCall(){
      switch (this.queryType) {
        case 1:
            alert("这是客户")
            return;
        case 2:
            this.getFormulationCode(this.inputText)
            return;
      }
    },
    clickToAssign(item){

    },
    // 获取配方代码
    getFormulationCode(formulationCode){
      this.$http.get(`produce/formulation/selectFormulationCodeInfo?formulationCode=`+formulationCode).then(({data: res}) => {
        if (res.code !== 0) {
          return this.$message.error(res.msg)
        }
        this.listData = res.data.map((obj) => {
          return {
            formulationCode: obj.formulationCode,
            value: obj.formulationCode + '(' + obj.formulationCode + ')',
            formulaId : obj.id,
          }
        })
      }).catch(() => {
      })
    },
    //  输出选择日志
    formulationSelect(item) {
      this.dataForm.formulationCode = item.formulationCode
      this.dataForm.formulaId = item.formulaId
    },
    // 获取客户代码
    getCustomerCode(customerCode,cb){
      this.$http.get(`customer/customer/selectCode?code=`+customerCode).then(({data: res}) => {
        if (res.code !== 0) {
          return this.$message.error(res.msg)
        }
        const list = res.data.map((obj) => {
          return {
            customerCode: obj.code,
            value: obj.code + '(' + obj.code + ')',
            customerId : obj.id,
          }
        })
        this.timeout = setTimeout(() => {
          cb(list);
        }, 3000 * Math.random());
      }).catch(() => {
      })
    },
    //  输出选择日志
    customerSelect(item) {
      this.dataForm.customerCode = item.customerCode
      this.dataForm.customerId = item.customerId
    },
  }
}
</script>

<style scoped>
ul{
  padding: 0;
  margin-top: 6px;
}
li{
  list-style: none;
  margin: 4px;
}
li:hover{
  position: relative; /* 设置为相对定位 */
  z-index: 1; /* 设置z-index属性 */
  background-color: #f0f0f0; /* 设置背景颜色 */
  box-shadow: 0px 5px 10px rgba(0, 0, 0, 0.2); /* 设置阴影效果 */
}
</style>