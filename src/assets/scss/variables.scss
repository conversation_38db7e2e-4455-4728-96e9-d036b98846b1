// Base
$base--line-height: 1.15;

// Navbar
$navbar--height: 50px;

// Sidebar
$sidebar--width: 230px;
$sidebar--width-fold: 64px;
$sidebar--background-color-dark: #263238;
$sidebar--text-color-dark: #8a979e;
$sidebar--menu-item-height: 48px;

// Content
$content--padding: 15px; 
$content--background-color: #f1f4f5;
$content--card-header-height: 60px;
$content--tabs-header-height: 38px;
// Content, 填充整屏高度(非tabs状态) = 整屏高度 - 导航条高度 - aui-content上下内边距高度
$content--fill-height: calc(100vh - #{$navbar--height} - #{$content--padding * 2});
// Content, 填充整屏高度(是tabs状态) = 整屏高度 - 导航条高度 - tabs组件header高度 - tabs组件content上下内边距高度
$content--fill-height-tabs: calc(100vh - #{$navbar--height} - #{$content--tabs-header-height} - #{$content--padding * 2});
