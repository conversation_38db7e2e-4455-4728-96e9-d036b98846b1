import Vue from 'vue'
import Vuex from 'vuex'
import Element from 'element-ui'
import App from '@/App'
import i18n from '@/i18n'
import router from '@/router'
import store from '@/store'
import '@/icons'
import '@/element-ui/theme/index.css'
import '@/assets/scss/aui.scss'
import http from '@/utils/request'
import renRadioGroup from '@/components/ren-radio-group'
import renSelect from '@/components/ren-select'
import renDeptTree from '@/components/ren-dept-tree'
import renRegionTree from '@/components/ren-region-tree'
import renUserSelectJobTitle from "@/components/ren-user-select-job-title";
import renSelectProductCategory from "@/components/ren-select-product-category";
import renSelectItemCategory from "@/components/ren-select-item-category";
import renSelectSizeCategory from "@/components/ren-select-size-category";
import remoteSelect from "@/components/remote-select/remoteSelect.vue";
import employeeComponent from "@/components/shared-search/employee-component";
import employeeComponentList from "@/components/shared-search/employee-component-list";
import partNumberComponent from "@/components/shared-search/part-number-component";
import imageUploadComponent from "@/components/shared-search/image-upload-component";
import customerComponent from "@/components/shared-search/customer-component";
import batchUnderComponent from "@/components/shared-search/batch-under-component";
import partInspectionBatchComponent from "@/components/shared-search/part-inspection-batch-component";
import machineComponent from "@/components/shared-search/machine-component";
import renRadioTick from "@/components/shared-search/ren-radio-tick";
import {
  hasPermission, getDictLabel,
  getEmployeesList,getSubBatchList,
  getPartList,getMachineList,
  getDictValue,getInspectionAging,
  getDepartment
} from '@/utils'
import inputSelect from "@/components/inpututil/input-select.vue";
import qrcodeComponent from "@/components/shared-search/qrcode-component";
import barcodeComponent from "@/components/shared-search/barcode-component";
import formulationComponent from "@/components/shared-search/formulation-component";
import manufacturingInstructionsComponent from "@/components/shared-search/manufacturingInstructions-component";
import numerickeypadComponent from "@/components/shared-search/numerickeypad-component";
import timeperiodComponent from "@/components/shared-search/timeperiod-component";
import businessorderComponent from "@/components/shared-search/businessorder-component";

import 'lib-flexible/flexible'

import { RecycleScroller } from 'vue-virtual-scroller'
import cloneDeep from 'lodash/cloneDeep'
import BaiduSuggestion from 'suggestion-baidu'
Vue.component('BaiduSuggestion', BaiduSuggestion)
import { NavBar } from 'vant';

// 注册 van-nav-bar 组件
Vue.use(NavBar);

Vue.use(Vuex)

import qs from 'qs'
Vue.prototype.$qs= qs;

import axios from 'axios';
Vue.prototype.$axios = axios;


Vue.component('RecycleScroller', RecycleScroller)



Vue.config.productionTip = false

Vue.prototype.$eventBus = new Vue()


Vue.prototype.$toRefresh = new Vue()

// 挂载全局，计算生产模数以及不良率
Vue.prototype.$calculate = new Vue()





Vue.use(Element, {
  size: 'default',
  i18n: (key, value) => i18n.t(key, value)
})

Vue.use(renRadioGroup)
Vue.use(renSelect)
Vue.use(renDeptTree)
Vue.use(renRegionTree)
Vue.use(renUserSelectJobTitle)
Vue.use(renSelectProductCategory)
Vue.use(renSelectItemCategory)
Vue.use(renSelectSizeCategory)
Vue.use(remoteSelect)
Vue.use(inputSelect)
Vue.use(employeeComponent)
Vue.use(employeeComponentList)
Vue.use(partNumberComponent)
Vue.use(imageUploadComponent)
Vue.use(customerComponent)
Vue.use(batchUnderComponent)
Vue.use(partInspectionBatchComponent)
Vue.use(machineComponent)
Vue.use(qrcodeComponent)
Vue.use(barcodeComponent)
Vue.use(formulationComponent)
Vue.use(manufacturingInstructionsComponent)
Vue.use(renRadioTick)
Vue.use(numerickeypadComponent)
Vue.use(timeperiodComponent)
Vue.use(businessorderComponent)



// 挂载全局
Vue.prototype.$http = http
Vue.prototype.$hasPermission = hasPermission
Vue.prototype.$getDictLabel = getDictLabel
Vue.prototype.$getEmployeesList = getEmployeesList
Vue.prototype.$getSubBatchList = getSubBatchList
Vue.prototype.$getMachineList = getMachineList
Vue.prototype.$getPartList = getPartList
Vue.prototype.$getDictValue = getDictValue
Vue.prototype.$getInspectionAging = getInspectionAging
Vue.prototype.$getDepartment = getDepartment
// 员工图片
Vue.prototype.$userFilePath = 'http://192.168.10.113/files/'
// 产品图片
Vue.prototype.$productFilePath = 'http://192.168.10.113/files/product/'

Vue.prototype.$filePath = 'http://192.168.10.113/files/'
// 成形预设
Vue.prototype.$formingDefault = ["1641701467113046018", "1641701649229725698"]
// 品修、包装预设
Vue.prototype.$productRepairDefault = ["1641701552458743810"]
// 品检、品保预设
Vue.prototype.$inspectionDefault = ["1641701892159619073"]

/*// 持久化插件
const myPlugin = {
  install(store) {
    // 保存状态到 localStorage
    store.subscribe(mutation => {
      localStorage.setItem('vuex-state', JSON.stringify(store.state));
    });
  }
};*/




// 保存整站vuex本地储存初始状态
window.SITE_CONFIG['storeState'] = cloneDeep(store.state)

// 获取旧批号数据
Vue.prototype.$bus = new Vue();

// 根据部门
Vue.prototype.$deptSelect = new Vue();

// 获得当前登录对象信息
Vue.prototype.$users = new Vue();

Vue.prototype.$retures = new Vue()


new Vue({
  i18n,
  router,
  store,
  render: h => h(App)
}).$mount('#app')




