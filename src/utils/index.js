import Cookies from 'js-cookie'
import store from '@/store'

/**
 * 权限
 * @param {*} key
 */
export function hasPermission(key) {
    return window.SITE_CONFIG['permissions'].indexOf(key) !== -1 || false
}

/**
 * 获取字典数据列表
 * @param dictType  字典类型
 */
export function getDictDataList(dictType) {
    const type = window.SITE_CONFIG['dictList'].find((element) => (element.dictType === dictType))
    if (type) {
        return type.dataList
    } else {
        return []
    }
}

export function getInspectionAging(partId,inspectionType) {
    let selectedItem = window.SITE_CONFIG['timeLimitList'].find(item => item.partId === partId);
    let aging = 0;
    console.log(selectedItem,'时效数据')
    if (selectedItem){
        switch (inspectionType) {
            case 1:
                aging = selectedItem.firstInspection;
                break;
            case 2:
                aging = Number(selectedItem.secondInspection);
                break;
            case 3:
                aging = Number(selectedItem.threeInspections);
                break;
            case 4:
                aging = selectedItem.fourInspections;
                break;
            case 5:
                aging = selectedItem.returnInspectionOne;
                break;
            case 6:
                aging = selectedItem.returnInspectionTwo;
                break;
            case 7:
                aging = selectedItem.returnInspectionThree;
                break;
            case 8:
                aging = selectedItem.returnInspectionFour;
                break;
            default:
                aging = 0;
        }
    }
    return aging;
}

/**
 * 获取员工数据列表
 * @param deptId 员工id
 */
export function getEmployeesList(deptId) {
    const item = window.SITE_CONFIG['employeesList'].find((element) => (element.value === deptId))
    if (item) {
        return item.label
    } else {
        return deptId
    }
}

/**
 * 获取员工部门数据
 * @param userId 员工id
 */
export function getDepartment(userId) {
    const item = window.SITE_CONFIG['employeesLists'].find((element) => (
        element.children.find((item) => item.value === userId))
    )
    if (item) {
        const dept = {deptId:item.value,deptName:item.label};
        return dept
    } else {
        return;
    }
}

/**
 * 获取机台数据列表
 * @param machineId 机台id
 */
export function getMachineList(machineId) {
    const item = window.SITE_CONFIG['machineList'].find((element) => (element.id === machineId))
    if (item) {
        return item.code
    } else {
        return machineId
    }
}

/**
 * 获取部品数据列表
 * @param partId 部品id
 */
export function getPartList(partId) {
    const item = window.SITE_CONFIG['partList'].find((element) => (element.id === partId))
    if (item) {
        return item.designation
    } else {
        return partId
    }
}

/**
 * 获取次批数据列表
 * @param subBatchId 次批号id
 * @returns {*}
 */
export function getSubBatchList(subBatchId) {
    const item = window.SITE_CONFIG['subBatchList'].find((element) => (element.subBatchId === subBatchId))
    if (item) {
        return getDictLabel("size_category", item.sizeCategory) + item.batchNumber + '-' + item.subBatchNumber
    } else {
        return subBatchId
    }

}

/**
 * 获取字典名称
 * @param dictType  字典类型
 * @param dictValue  字典值
 */
export function getDictLabel(dictType, dictValue) {
    const type = window.SITE_CONFIG['dictList'].find((element) => (element.dictType === dictType))
    if (type) {
        const val = type.dataList.find((element) => (element.dictValue === dictValue + ''))
        if (val) {
            return val.dictLabel
        } else {
            return dictValue
        }
    } else {
        return dictValue
    }
}

/**
 * 获取字典值
 * @param dictType 字典类型
 * @param dictLabel 字典名称
 */
export function getDictValue(dictType, dictLabel) {
    const type = window.SITE_CONFIG['dictList'].find((element) => (element.dictType === dictType))
    if (type) {
        const val = type.dataList.find((element) => (element.dictLabel === dictLabel + ''))
        if (val) {
            return val.dictValue
        } else {
            return dictLabel
        }
    } else {
        return dictLabel
    }
}

export function getNavigationList() {

}

/**
 * 清除登录信息
 */
export function clearLoginInfo() {
    store.commit('resetStore')
    Cookies.remove('token')
    window.SITE_CONFIG['dynamicMenuRoutesHasAdded'] = false
}

/**
 * 获取uuid
 */
export function getUUID() {
    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, c => {
        return (c === 'x' ? (Math.random() * 16 | 0) : ('r&0x3' | '0x8')).toString(16)
    })
}


/**
 * 获取svg图标(id)列表
 */
export function getIconList() {
    var res = []
    var list = document.querySelectorAll('svg symbol')
    for (var i = 0; i < list.length; i++) {
        res.push(list[i].id)
    }

    return res
}

/**
 * 树形数据转换
 * @param {*} data
 * @param {*} id
 * @param {*} pid
 */
export function treeDataTranslate(data, id = 'id', pid = 'pid') {
    var res = []
    var temp = {}
    for (var i = 0; i < data.length; i++) {
        temp[data[i][id]] = data[i]
    }
    for (var k = 0; k < data.length; k++) {
        if (!temp[data[k][pid]] || data[k][id] === data[k][pid]) {
            res.push(data[k])
            continue
        }
        if (!temp[data[k][pid]]['children']) {
            temp[data[k][pid]]['children'] = []
        }
        temp[data[k][pid]]['children'].push(data[k])
        data[k]['_level'] = (temp[data[k][pid]]._level || 0) + 1
    }
    return res
}

export function uploadImgToBase64(file) {
    return new Promise((resolve, reject) => {
        const reader = new FileReader()
        reader.readAsDataURL(file)
        reader.onload = function () {  // 图片转base64完成后返回reader对象
            resolve(reader)
        }
        reader.onerror = reject
    })
}
