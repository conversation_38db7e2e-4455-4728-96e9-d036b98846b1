<template>
  <nav class="aui-navbar" :class="`aui-navbar--${$store.state.navbarLayoutType}`">
<!--线上使用-->
    <div class="aui-navbar__header">
      <h1 class="aui-navbar__brand" @click="$router.push({ name: 'home' })">
        <a class="aui-navbar__brand-mini" href="javascript:;">{{ $t('brand.mini') }}</a>
        <a class="aui-navbar__brand-lg" href="javascript:;">{{ $t('brand.lg') }}</a>
      </h1>
    </div>

<!--测试使用-->
<!--    <div class="aui-navbar__header" style="background-color: red">
      <h1 class="aui-navbar__brand" @click="$router.push({ name: 'home' })">
        <a class="aui-navbar__brand-mini" href="javascript:;">{{ $t('brand.mini') }}</a>
        <a class="aui-navbar__brand-lg" href="javascript:;" onclick="redirectToPage()">{{ $t('brand.lg') }}测试专用</a>
      </h1>
    </div>-->
    <div class="aui-navbar__body">
      <el-menu class="aui-navbar__menu mr-auto" mode="horizontal">
        <el-menu-item index="1" @click="$store.state.sidebarFold = !$store.state.sidebarFold">
          <svg class="icon-svg aui-navbar__icon-menu aui-navbar__icon-menu--switch" aria-hidden="true"><use xlink:href="#icon-outdent"></use></svg>
        </el-menu-item>
      </el-menu>
<!--      <el-menu class="aui-navbar__menu" mode="horizontal">
        <el-menu-item index="0">
          <span @click="routingReturn" style="float:right;">返回主页面</span>
        </el-menu-item>
      </el-menu>-->
      <el-menu class="aui-navbar__menu" mode="horizontal">
        <el-menu-item index="4" @click="fullscreenHandle()">
          <svg class="icon-svg aui-navbar__icon-menu" aria-hidden="true"><use xlink:href="#icon-fullscreen"></use></svg>
        </el-menu-item>
        <el-menu-item index="5" class="aui-navbar__avatar">
          <el-dropdown placement="bottom" :show-timeout="0">
            <span class="el-dropdown-link">
              <img v-if="show" src="@/assets/img/avatar.png">
              <img v-else :src="imagePath">
              <span>{{ $store.state.user.name }}</span>
              <i class="el-icon-arrow-down"></i>
            </span>
            <el-dropdown-menu slot="dropdown">
              <el-dropdown-item @click.native="updatePasswordHandle()">{{ $t('updatePassword.title') }}</el-dropdown-item>
              <el-dropdown-item @click.native="logoutHandle()">{{ $t('logout') }}</el-dropdown-item>
              <el-dropdown-item @click.native="scanCode()">扫一扫</el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown>
        </el-menu-item>
      </el-menu>
    </div>
    <!-- 弹窗, 修改密码 -->
    <update-password v-if="updatePasswordVisible" ref="updatePassword"></update-password>
  </nav>
</template>
<script>
import screenfull from 'screenfull'
import UpdatePassword from './main-navbar-update-password'
import { clearLoginInfo } from '@/utils'

function redirectToPage() {
  window.location.href = "/";
}

export default {
  inject: ['refresh'],
  data () {
    return {
      updatePasswordVisible: false,
      messageTip: false,
      show:true,
      imagePath:window.SITE_CONFIG['users']?.documentId === null ? "" : this.$filePath + window.SITE_CONFIG['users'].documentId+window.SITE_CONFIG['users'].documentType,
    }
  },
  components: {
    UpdatePassword
  },
  mounted() {
    // 如果有图片则显示图片
    if(window.SITE_CONFIG['users']?.documentId){
      this.show = false
    }
  },
  methods: {
    // 扫一扫
    scanCode() {
      console.log('浏览器信息', navigator.userAgent);
      this.$router.push({
        path: '/qrcode-scanning'
      });
    },
    routingReturn(){
      this.$router.push({name:'new-page'})
    },
    // 全屏
    fullscreenHandle () {
      if (!screenfull.enabled) {
        return this.$message({
          message: this.$t('fullscreen.prompt'),
          type: 'warning',
          duration: 500
        })
      }
      screenfull.toggle()
    },
    // 修改密码
    updatePasswordHandle () {
      this.updatePasswordVisible = true
      this.$nextTick(() => {
        this.$refs.updatePassword.init()
      })
    },
    // 退出
    logoutHandle () {
      this.$confirm(this.$t('prompt.info', { 'handle': this.$t('logout') }), this.$t('prompt.title'), {
        confirmButtonText: this.$t('confirm'),
        cancelButtonText: this.$t('cancel'),
        type: 'warning'
      }).then(() => {
        this.$http.post('/logout').then(({ data: res }) => {
          if (res.code !== 0) {
            return this.$message.error(res.msg)
          }
          clearLoginInfo()
          this.$router.push({ name: 'login' })
        }).catch(() => {})
      }).catch(() => {})
    }
  }
}
</script>
