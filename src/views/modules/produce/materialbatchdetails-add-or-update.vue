<template>
  <el-dialog :visible.sync="visible" :title="!dataForm.id ? $t('add') : $t('update')" :close-on-click-modal="false" :close-on-press-escape="false" append-to-body>
    <el-form :model="dataForm" :rules="dataRule" ref="dataForm" @keyup.enter.native="dataFormSubmitHandle()" :label-width="$i18n.locale === 'en-US' ? '120px' : '80px'">
          <el-form-item label="批号管制编号" prop="batchId">
          <el-input v-model="dataForm.batchId" placeholder="批号管制编号"></el-input>
      </el-form-item>
      <el-form-item label="部品番号" prop="designation">
        <el-autocomplete
            class="inline-input"
            v-model="dataForm.designation"
            :fetch-suggestions="getPartList"
            placement="bottom"
            placeholder="请输入内容"
            :trigger-on-focus="false"
            @select="partSelect"></el-autocomplete>
      </el-form-item>
      <el-form-item label="生产指令" prop="manufacturingInstructions">
        <el-autocomplete
            class="inline-input"
            v-model="dataForm.manufacturingInstructions"
            :fetch-suggestions="getLikemanufacturing"
            placement="bottom"
            placeholder="请输入生产指令"
            :trigger-on-focus="false"
            @select="handleSelectManufacturing">
        </el-autocomplete>
      </el-form-item>
      <el-form-item prop="instructionDate" label="指令日期(含时分)">
        <el-date-picker
            v-model="dataForm.instructionDate"
            type="datetime"
            value-format="yyyy-MM-dd HH:mm:ss"
            placeholder="选择日期">
        </el-date-picker>
      </el-form-item>
          <el-form-item label="材料确认者" prop="materialConfirmation">
          <el-input v-model="dataForm.materialConfirmation" placeholder="材料确认者"></el-input>
      </el-form-item>
          <el-form-item label="密炼员" prop="banburyingOperator">
          <el-input v-model="dataForm.banburyingOperator" placeholder="密炼员"></el-input>
      </el-form-item>
      <el-form-item prop="mixingConditionTime" label="密炼条件时间(含时分)">
        <el-date-picker
            v-model="dataForm.mixingConditionTime"
            type="datetime"
            value-format="yyyy-MM-dd HH:mm:ss"
            placeholder="选择日期">
        </el-date-picker>
      </el-form-item>
          <el-form-item label="密炼条件温度" prop="mixingTemperature">
          <el-input v-model="dataForm.mixingTemperature" placeholder="密炼条件温度"></el-input>
      </el-form-item>
          <el-form-item label="开炼员" prop="openingOperator">
          <el-input v-model="dataForm.openingOperator" placeholder="开炼员"></el-input>
      </el-form-item>
      <el-form-item prop="openingTime" label="开炼条件时间(分)">
        <el-date-picker
            v-model="dataForm.openingTime"
            type="datetime"
            value-format="yyyy-MM-dd HH:mm:ss"
            placeholder="选择日期">
        </el-date-picker>
      </el-form-item>
          <el-form-item label="开炼条件温度" prop="openingTemperature">
          <el-input v-model="dataForm.openingTemperature" placeholder="开炼条件温度"></el-input>
      </el-form-item>
          <el-form-item label="出片尺寸宽(mm)" prop="wideSize">
          <el-input v-model="dataForm.wideSize" placeholder="出片尺寸宽(mm)"></el-input>
      </el-form-item>
          <el-form-item label="出片尺寸厚(mm)" prop="dimensionsThick">
          <el-input v-model="dataForm.dimensionsThick" placeholder="出片尺寸厚(mm)"></el-input>
      </el-form-item>
          <el-form-item label="生产重量(kg)" prop="productionWeight">
          <el-input v-model="dataForm.productionWeight" placeholder="生产重量(kg)"></el-input>
      </el-form-item>
      <el-form-item prop="dateOfManufacture" label="制造日期(含时分)">
        <el-date-picker
            v-model="dataForm.dateOfManufacture"
            type="datetime"
            value-format="yyyy-MM-dd HH:mm:ss"
            placeholder="选择日期">
        </el-date-picker>
      </el-form-item>
          <el-form-item label="有效期限(天)" prop="validityPeriod">
          <el-input v-model="dataForm.validityPeriod" placeholder="有效期限(天)"></el-input>
      </el-form-item>
          <el-form-item label="称料(Y/N)" prop="weighing">
          <el-input v-model="dataForm.weighing" placeholder="称料(Y/N)"></el-input>
      </el-form-item>
          <el-form-item label="切料员" prop="cutter">
          <el-input v-model="dataForm.cutter" placeholder="切料员"></el-input>
      </el-form-item>
          <el-form-item label="条重1(g)" prop="barWeightOne">
          <el-input v-model="dataForm.barWeightOne" placeholder="条重1(g)"></el-input>
      </el-form-item>
          <el-form-item label="条重2(g)" prop="barWeightTwo">
          <el-input v-model="dataForm.barWeightTwo" placeholder="条重2(g)"></el-input>
      </el-form-item>
          <el-form-item label="条数" prop="numberOfLines">
          <el-input v-model="dataForm.numberOfLines" placeholder="条数"></el-input>
      </el-form-item>
          <el-form-item label="是否放行(T/F)" prop="letGo">
          <el-input v-model="dataForm.letGo" placeholder="是否放行(T/F)"></el-input>
      </el-form-item>
          <el-form-item label="硬度标准(XX~XX)" prop="hardnessStandard">
          <el-input v-model="dataForm.hardnessStandard" placeholder="硬度标准(XX~XX)"></el-input>
      </el-form-item>
          <el-form-item label="实测硬度" prop="measuredHardness">
          <el-input v-model="dataForm.measuredHardness" placeholder="实测硬度"></el-input>
      </el-form-item>
          <el-form-item label="判定结果" prop="judgementResult">
          <el-input v-model="dataForm.judgementResult" placeholder="判定结果"></el-input>
      </el-form-item>
          <el-form-item label="异常说明" prop="exceptionDescription">
          <el-input v-model="dataForm.exceptionDescription" placeholder="异常说明"></el-input>
      </el-form-item>
          <el-form-item label="检验员" prop="inspectors">
          <el-input v-model="dataForm.inspectors" placeholder="检验员"></el-input>
      </el-form-item>
          <el-form-item label="备注" prop="remark">
          <el-input v-model="dataForm.remark" placeholder="备注"></el-input>
      </el-form-item>
      </el-form>
    <template slot="footer">
      <el-button @click="visible = false">{{ $t('cancel') }}</el-button>
      <el-button type="primary" @click="dataFormSubmitHandle()">{{ $t('confirm') }}</el-button>
    </template>
  </el-dialog>
</template>

<script>
import debounce from 'lodash/debounce'
export default {
  data () {
    return {
      visible: false,
      dataForm: {
        id: '',
        batchId: '',
        batchNumber: '',
        productionBatch:'',
        porductMappingId: '',
        designation:'',
        manufacturingId: '',
        manufacturingInstructions:'',
        qualityInspectionId: '',
        instructionDate: '',
        materialConfirmation: '',
        banburyingOperator: '',
        mixingConditionTime: '',
        mixingTemperature: '',
        openingOperator: '',
        openingTime: '',
        openingTemperature: '',
        wideSize: '',
        dimensionsThick: '',
        productionWeight: '',
        dateOfManufacture: '',
        validityPeriod: '',
        weighing: '',
        cutter: '',
        barWeightOne: '',
        barWeightTwo: '',
        numberOfLines: '',
        letGo: '',
        hardnessStandard: '',
        measuredHardness: '',
        judgementResult: '',
        exceptionDescription: '',
        inspectors: '',
        createDate: '',
        updateDate: '',
        remark: '',
        disabled: '',
        creator: '',
        updater: ''
      }
    }
  },
  computed: {
    dataRule () {
      return {
        designation: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        manufacturingInstructions: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ]
      }
    }
  },
  methods: {
    getLikemanufacturing(manufacturingInstructions,cb){
      this.$http.get(`fabricate/manufacturingOrder/selectLike?manufacturingInstructions=`+manufacturingInstructions).then(({data: res}) => {
        console.log("res",res)
        if (res.code !== 0) {
          return this.$message.error(res.msg)
        }
        const list = res.data.map((obj) => {
          console.log("obj",obj)
          return {
            manufacturingInstructions: obj.manufacturingInstructions,
            value: obj.manufacturingInstructions + '(' + obj.manufacturingInstructions + ')',
            manufacturingId : obj.id,
          }
        })
        this.timeout = setTimeout(() => {
          cb(list);
        }, 3000 * Math.random());
      }).catch(() => {
      })
    },
    //  输出选择日志
    handleSelectManufacturing(item) {
      console.log("item",item)
      this.dataForm.manufacturingInstructions = item.manufacturingInstructions
      this.dataForm.manufacturingId = item.manufacturingId
    },
    //  获取量产类型部品列表
    getPartList(queryString, cb) {
      this.$http.get(`/fabricate/part/getMPAll/` + queryString).then(({data: res}) => {
        if (res.code !== 0) {
          return this.$message.error(res.msg)
        }
        const list = res.data.map((obj) => {
          return {
            value: obj.designation + '(' + obj.designation + ')',
            partId: obj.id,
            designation: obj.designation
          }
        })
        this.timeout = setTimeout(() => {
          cb(list);
        }, 3000 * Math.random());
      }).catch(() => {
      })
    },
    //  输出选择日志
    partSelect(item) {
      this.dataForm.designation = item.designation
      this.dataForm.porductMappingId = item.partId
    },
    init () {
      this.visible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].resetFields()
        if (this.dataForm.id) {
          this.getInfo()
        }
      })
    },
    // 获取信息
    getInfo () {
      this.$http.get(`/produce/materialbatchdetails/${this.dataForm.id}`).then(({ data: res }) => {
        if (res.code !== 0) {
          return this.$message.error(res.msg)
        }
        this.dataForm = {
          ...this.dataForm,
          ...res.data
        }
      }).catch(() => {})
    },
    // 表单提交
    dataFormSubmitHandle: debounce(function () {
      this.$refs['dataForm'].validate((valid) => {
        if (!valid) {
          return false
        }
        this.$http[!this.dataForm.id ? 'post' : 'put']('/produce/materialbatchdetails/', this.dataForm).then(({ data: res }) => {
          if (res.code !== 0) {
            return this.$message.error(res.msg)
          }
          this.$message({
            message: this.$t('prompt.success'),
            type: 'success',
            duration: 500,
            onClose: () => {
              this.visible = false
              this.$emit('refreshDataList')
            }
          })
        }).catch(() => {})
      })
    }, 1000, { 'leading': true, 'trailing': false })
  }
}
</script>
