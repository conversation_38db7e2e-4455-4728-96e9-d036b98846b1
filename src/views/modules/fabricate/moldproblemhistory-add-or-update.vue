<template>
  <el-dialog :visible.sync="visible" :title="!dataForm.id ? $t('add') : $t('update')" :close-on-click-modal="false" :close-on-press-escape="false" append-to-body>
    <el-form :model="dataForm" :rules="dataRule" ref="dataForm" @keyup.enter.native="dataFormSubmitHandle()" :label-width="$i18n.locale === 'en-US' ? '120px' : '80px'">
      <div class="container">
        <el-form-item label="模具" prop="moldId">
          <machine-component v-model="dataForm.moldId" placeholder="模具"></machine-component>
        </el-form-item>
        <el-form-item label="客户id" prop="customerId">
          <el-input v-model="dataForm.customerId" placeholder="客户id"></el-input>
        </el-form-item>
        <el-form-item label="使用起始日" prop="startingDate">
          <el-input v-model="dataForm.startingDate" placeholder="使用起始日"></el-input>
        </el-form-item>
        <el-form-item label="使用结束日" prop="endDate">
          <el-input v-model="dataForm.endDate" placeholder="使用结束日"></el-input>
        </el-form-item>
        <el-form-item label="使用前点检-模污、模伤" prop="spotCheckModel">
          <el-input v-model="dataForm.spotCheckModel" placeholder="使用前点检-模污、模伤"></el-input>
        </el-form-item>
        <el-form-item label="使用前点检-导柱、导套" prop="spotCheckGuide">
          <el-input v-model="dataForm.spotCheckGuide" placeholder="使用前点检-导柱、导套"></el-input>
        </el-form-item>
        <el-form-item label="使用前点检-挂钩、插棒" prop="spotCheckHang">
          <el-input v-model="dataForm.spotCheckHang" placeholder="使用前点检-挂钩、插棒"></el-input>
        </el-form-item>
        <el-form-item label="使用模数小计" prop="moduleSubtotal">
          <el-input v-model="dataForm.moduleSubtotal" placeholder="使用模数小计"></el-input>
        </el-form-item>
        <el-form-item label="累计模数" prop="cumulativeModulus">
          <el-input v-model="dataForm.cumulativeModulus" placeholder="累计模数"></el-input>
        </el-form-item>
        <el-form-item label="本次使用模具状态" prop="moldStatus">
          <el-input v-model="dataForm.moldStatus" placeholder="本次使用模具状态"></el-input>
        </el-form-item>
        <el-form-item label="使用人 sys_user_id" prop="usingPeople">
          <el-input v-model="dataForm.usingPeople" placeholder="使用人 sys_user_id"></el-input>
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="dataForm.remark" placeholder="备注"></el-input>
        </el-form-item>
      </div>
    </el-form>
    <template slot="footer">
      <el-button @click="visible = false">{{ $t('cancel') }}</el-button>
      <el-button type="primary" @click="dataFormSubmitHandle()">{{ $t('confirm') }}</el-button>
    </template>
  </el-dialog>
</template>

<script>
import debounce from 'lodash/debounce'
import mixinViewModule from '@/mixins/view-module'
export default {
 data () {
    return {
      visible: false,
      //创建该页面时，不调用数据查询接口
      mixinViewModuleOptions: {
        createdIsNeed: false,
      },
      dataForm: {
        id: '',
        moldId: '',
        customerId: '',
        startingDate: '',
        endDate: '',
        spotCheckModel: '',
        spotCheckGuide: '',
        spotCheckHang: '',
        moduleSubtotal: '',
        cumulativeModulus: '',
        moldStatus: '',
        usingPeople: '',
        remark: '',
        disabled: '',
        creator: '',
        createDate: '',
        updater: '',
        updateDate: ''
      }
    }
  },
  computed: {
    dataRule () {
      return {
          id: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          moldId: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          customerId: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          startingDate: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          endDate: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          spotCheckModel: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          spotCheckGuide: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          spotCheckHang: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          moduleSubtotal: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          cumulativeModulus: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          moldStatus: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          usingPeople: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          remark: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
                }
    }
  },
  methods: {
    init () {
      this.visible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].resetFields()
        if (this.dataForm.id) {
          this.getInfo()
        }
      })
    },
    // 获取信息
    getInfo () {
      this.$http.get(`/fabricate/moldproblemhistory/${this.dataForm.id}`).then(({ data: res }) => {
        if (res.code !== 0) {
          return this.$message.error(res.msg)
        }
        this.dataForm = {
          ...this.dataForm,
          ...res.data
        }
      }).catch(() => {})
    },
    // 表单提交
    dataFormSubmitHandle: debounce(function () {
      this.$refs['dataForm'].validate((valid) => {
        if (!valid) {
          return false
        }
        this.$http[!this.dataForm.id ? 'post' : 'put']('/fabricate/moldproblemhistory/', this.dataForm).then(({ data: res }) => {
          if (res.code !== 0) {
            return this.$message.error(res.msg)
          }
          this.$message({
            message: this.$t('prompt.success'),
            type: 'success',
            duration: 500,
            onClose: () => {
              this.visible = false
              this.$emit('refreshDataList')
            }
          })
        }).catch(() => {})
      })
    }, 1000, { 'leading': true, 'trailing': false })
  }
}
</script>
