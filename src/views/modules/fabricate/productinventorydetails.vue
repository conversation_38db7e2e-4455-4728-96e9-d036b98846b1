<template>
  <el-card shadow="never" class="aui-card--fill">
    <div class="mod-fabricate__productinventorydetails}">
      <el-form :inline="true" :model="dataForm" @keyup.enter.native="getDataList()">
        <el-form-item>
          <el-input v-model="dataForm.id" placeholder="id" clearable @clear="onClear">
            <el-button slot="append" icon="el-icon-search" @click="getDataList()"></el-button>
          </el-input>
        </el-form-item>
<!--        <el-form-item>
          <el-button @click="getDataList()">{{ $t('query') }}</el-button>
        </el-form-item>-->
        <el-form-item>
          <el-button type="info" @click="exportHandle()">{{ $t('export') }}</el-button>
        </el-form-item>
        <el-form-item>
          <el-button v-if="$hasPermission('fabricate:productinventorydetails:save')" type="primary" @click="addOrUpdateHandle()">{{ $t('add') }}</el-button>
        </el-form-item>
        <el-form-item>
          <el-button v-if="$hasPermission('fabricate:productinventorydetails:delete')" type="danger" @click="deleteHandle()">{{ $t('deleteBatch') }}</el-button>
        </el-form-item>
      </el-form>
      <el-table v-loading="dataListLoading" height="600px" :data="dataList" border @selection-change="dataListSelectionChangeHandle" style="width: 100%;">
        <el-table-column type="selection" header-align="center" align="center" width="50"></el-table-column>
        <el-table-column prop="id" label="id" header-align="center" align="center"></el-table-column>
        <el-table-column prop="productInventoryId" label="产品库存id" header-align="center" align="center"></el-table-column>
        <el-table-column prop="transferredUnit" label="转入单位" header-align="center" align="center"></el-table-column>
        <el-table-column prop="uncheckedNum" label="未检" header-align="center" align="center"></el-table-column>
        <el-table-column prop="qualityRepairNum" label="品修" header-align="center" align="center"></el-table-column>
        <el-table-column prop="qualityInspectionNum" label="品检" header-align="center" align="center"></el-table-column>
        <el-table-column prop="endProductNum" label="成品" header-align="center" align="center"></el-table-column>
        <el-table-column prop="inventoryNum" label="库存" header-align="center" align="center"></el-table-column>
        <el-table-column prop="transferredNum" label="转入数" header-align="center" align="center"></el-table-column>
        <el-table-column prop="quantityReceived" label="入货量" header-align="center" align="center"></el-table-column>
        <el-table-column prop="shipmentQuantity" label="出货量" header-align="center" align="center"></el-table-column>
        <el-table-column prop="pantsNum" label="次品数" header-align="center" align="center"></el-table-column>
        <el-table-column prop="location" label="目前所在地" header-align="center" align="center"></el-table-column>
        <el-table-column prop="remark" label="备注" header-align="center" align="center"></el-table-column>
        <el-table-column prop="batchBasicId" label="批号基本档id" header-align="center" align="center"></el-table-column>
        <el-table-column prop="disabled" label="是否失效 0:有效; 1:失效 (逻辑删除)" header-align="center" align="center"></el-table-column>
        <el-table-column prop="creator" label="创建者" header-align="center" align="center"></el-table-column>
        <el-table-column prop="createDate" label="创建时间" header-align="center" align="center"></el-table-column>
        <el-table-column prop="updater" label="更新者" header-align="center" align="center"></el-table-column>
        <el-table-column prop="updateDate" label="更新时间" header-align="center" align="center"></el-table-column>
        <el-table-column :label="$t('handle')" fixed="right" header-align="center" align="center" width="150">
          <template slot-scope="scope">
            <el-dropdown>
              <el-button type="primary">
                功能<i class="el-icon-arrow-down el-icon--right"></i>
              </el-button>
              <el-dropdown-menu slot="dropdown">
                <el-dropdown-item>
                  <el-button v-if="$hasPermission('fabricate:productinventorydetails:update')" type="text" size="small" @click="addOrUpdateHandle(scope.row.id)">{{ $t('update') }}</el-button>
                </el-dropdown-item>
                <el-dropdown-item>
                  <el-button v-if="$hasPermission('fabricate:productinventorydetails:delete')" type="text" size="small" @click="deleteHandle(scope.row.id)">{{ $t('delete') }}</el-button>
                </el-dropdown-item>
              </el-dropdown-menu>
            </el-dropdown>
          </template>
        </el-table-column>
      </el-table>
      <el-pagination
        :current-page="page"
        :page-sizes="[10, 20, 50, 100]"
        :page-size="limit"
        :total="total"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="pageSizeChangeHandle"
        @current-change="pageCurrentChangeHandle">
      </el-pagination>
      <!-- 弹窗, 新增 / 修改 -->
      <add-or-update v-if="addOrUpdateVisible" ref="addOrUpdate" @refreshDataList="getDataList"></add-or-update>
    </div>
  </el-card>
</template>

<script>
import mixinViewModule from '@/mixins/view-module'
import AddOrUpdate from './productinventorydetails-add-or-update'
export default {
  mixins: [mixinViewModule],
  data () {
    return {
      mixinViewModuleOptions: {
        getDataListURL: '/fabricate/productinventorydetails/page',
        getDataListIsPage: true,
        exportURL: '/fabricate/productinventorydetails/export',
        deleteURL: '/fabricate/productinventorydetails',
        deleteIsBatch: true
      },
      dataForm: {
        id: ''
      }
    }
  },
  components: {
    AddOrUpdate
  },
  methods:{
    //清空后重新查询
    onClear(){
      this.query()
    },
  }
}
</script>
