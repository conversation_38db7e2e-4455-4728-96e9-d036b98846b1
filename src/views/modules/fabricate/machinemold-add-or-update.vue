<template>
  <el-dialog :visible.sync="visible" :title="!dataForm.id ? $t('add') : $t('update')" :close-on-click-modal="false" :close-on-press-escape="false" append-to-body>
    <el-form :model="dataForm" :rules="dataRule" ref="dataForm" @keyup.enter.native="dataFormSubmitHandle()" :label-width="$i18n.locale === 'en-US' ? '120px' : '80px'">
      <div class="container">
          <el-form-item label="品号:" prop="partId">
            <el-select v-model="dataForm.partId"  filterable @input="partChangeReturn" @visible-change="partHandleVisibleChange" placeholder="请选择"  style="width: 227px">
              <el-option
                  v-for="(item,index) in partOptions"
                  :key="index"
                  :label="item.label"
                  :value="item.value">
              </el-option>
            </el-select>
          </el-form-item>
        <el-form-item label="模具:" prop="moldId">
          <el-select v-model="dataForm.moldId"  filterable @input="moldChangeReturn" @visible-change="moldHandleVisibleChange" placeholder="请选择"  style="width: 227px">
            <el-option
                v-for="(item,index) in moldOptions"
                :key="index"
                :label="item.label"
                :value="item.value">
            </el-option>
          </el-select>
        </el-form-item>
             <el-form-item label="机器: " prop="machineId">
               <el-select v-model="dataForm.machineId"  filterable @input="machineChangeReturn" @visible-change="machineHandleVisibleChange" placeholder="请选择"   style="width: 227px">
                 <el-option
                     v-for="(item,index) in machineOptions"
                     :key="index"
                     :label="item.label"
                     :value="item.value">
                 </el-option>
               </el-select>
            </el-form-item>
        <el-form-item label="状态: " prop="machineId">
          <el-select v-model="dataForm.machineStatus"  filterable placeholder="请选择"  style="width: 227px">
            <el-option
                v-for="item in machineStatusOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value">
            </el-option>
            </el-select>
            </el-form-item>
            <el-form-item label="次批号" prop="subBatchId">
              <el-select v-model="dataForm.subBatchId" filterable @input="subBatchChangeReturn" @visible-change="subBatchHandleVisibleChange" placeholder="请选择"  style="width: 227px">
                <el-option
                    v-for="item in subBatchList"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value">
                </el-option>
              </el-select>
            </el-form-item>
             <el-form-item label="异常次数" prop="abnormalCont">
              <el-input v-model="dataForm.abnormalCont" placeholder="异常次数" style="width: 227px"></el-input>
            </el-form-item>
        <el-form-item label="操作员1: " prop="operatorOne">
          <el-cascader
              v-model="dataForm.operatorOne"
              :options="operatorOptions"
              :props="{ value: 'id',label: 'name',children: 'userList'}"
              :show-all-levels="false"
              @change="handleChooseChange($event, 'operatorOne')"
              style="width: 227px">
          </el-cascader>
        </el-form-item>
        <el-form-item label="操作员2: " >
          <el-cascader
              v-model="dataForm.operatorTwo"
              :options="operatorOptions"
              :props="{ value: 'id',label: 'name',children: 'userList'}"
              :show-all-levels="false"
              @change="handleChooseChange($event, 'operatorTwo')"
              style="width: 227px">
          </el-cascader>
        </el-form-item>
           <el-form-item label="上模时间" prop="putTime">
             <el-date-picker
                 v-model="dataForm.putTime"
                 type="datetime"
                 placeholder="选择日期时间" style="width: 227px">
             </el-date-picker>
           </el-form-item>
        <el-form-item label="预计下模时间" prop="takeDownTime">
          <el-date-picker
              v-model="dataForm.takeDownTime"
              type="datetime"
              placeholder="选择日期时间" style="width: 227px">
          </el-date-picker>
        </el-form-item>
          <el-form-item label="备注说明">
            <el-input
                type="textarea"
                :rows="2"
                placeholder="请输入内容"
                v-model="dataForm.remark" style="width: 600px">
            </el-input>
            </el-form-item>
                                                                                                                  </div>
    </el-form>
    <template slot="footer">
      <el-button @click="closeDialog">{{ $t('cancel') }}</el-button>
      <el-button type="primary" @click="dataFormSubmitHandle()">{{ $t('confirm') }}</el-button>
    </template>
  </el-dialog>
</template>

<script>
import debounce from 'lodash/debounce'
import mixinViewModule from '@/mixins/view-module'
export default {
  mixins: [mixinViewModule],
  data() {
    return {
      visible: false,
      //创建该页面时，不调用数据查询接口
      mixinViewModuleOptions: {
        createdIsNeed: false,
      },
      dataForm: {
        id: '',
        machineId: '',
        machineCode: '',
        machineStatus: '',
        partId: '',
        partCode: '',
        designation: '',
        moldId: '',
        moldCode: '',
        subBatchId: '',
        subBatchNumber: '',
        abnormalCont: '',
        operatorOne: '',
        operatorOneName: '',
        operatorTwo: '',
        operatorTwoName: '',
        putTime: '',
        takeDownTime: '',
        remark: '',
        disabled: '',
        creator: '',
        createDate: '',
        updater: '',
        updateDate: '',
      },
      machineOptions: [],
      partOptions: [],
      moldOptions: [],
      operatorOptions: [],
      subBatchList:[],
      machineStatusOptions: [
        {value: 0, label: '生产'},
        {value: 1, label: '异常'},
        {value: 2, label: '试模'},
        {value: 3, label: '上模'},
        {value: 4, label: '下模'},
        {value: 5, label: '维修'},
        {value: 6, label: '保养'},
        {value: 7, label: '待机'},
        {value: 8, label: '暂停'},
      ],
    }
  },
  computed: {
    dataRule() {
      return {
        id: [
          {required: true, message: this.$t('validate.required'), trigger: 'blur'}
        ],
        machineId: [
          {required: true, message: this.$t('validate.required'), trigger: 'blur'}
        ],
        machineStatus: [
          {required: true, message: this.$t('validate.required'), trigger: 'blur'}
        ],
        partId: [
          {required: true, message: this.$t('validate.required'), trigger: 'blur'}
        ],
        moldId: [
          {required: true, message: this.$t('validate.required'), trigger: 'blur'}
        ],
        operatorOne: [
          {required: true, message: this.$t('validate.required'), trigger: 'blur'}
        ],
        subBatchId: [
          {required: true, message: this.$t('validate.required'), trigger: 'blur'}
        ],
        abnormalCont: [
          {required: true, message: this.$t('validate.required'), trigger: 'blur'}
        ],
        putTime: [
          {required: true, message: this.$t('validate.required'), trigger: 'blur'}
        ],
        takeDownTime: [
          {required: true, message: this.$t('validate.required'), trigger: 'blur'}
        ]
      }
    }
  },
  mounted() {
    this.getMoldList();
    this.getPartList();
    this.getOperatorList();
    this.getMachineList();
    this.getSubBatchList();
  },
  methods: {
    init() {
      this.visible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].resetFields()
        if (this.dataForm.id) {
          this.getInfo()
        }
      })
    },
    handleChooseChange(selectedValues, operator) {
      const lastSelectedValue = selectedValues[selectedValues.length - 1];
      // 提取选中的最后一个节点的值（子节点的值）
      if (operator === 'operatorOne') {
        this.dataForm.operatorOne = lastSelectedValue;
        } else if (operator === 'operatorTwo') {
        this.dataForm.operatorTwo = lastSelectedValue;
      }
    },
    partChangeReturn(value) {
      this.getPartList(value)
    },
    partHandleVisibleChange(visible) {
      if (visible) {
        // 下拉框显示时执行的操作
        console.log('下拉框显示了');
        // 这里可以触发相应的事件或执行其他操作
        this.getPartList();
      }
    },
    //查询操作员树状图
    getOperatorList() {
      this.$http.get('/sys/user/deptUserTree').then(({data: res}) => {
        if (res.code !== 0) {
          return this.$message.error(res.msg);
        }
        this.operatorOptions = res.data
      }).catch(() => {
        //错误处理
      })
    },
    //查询匹配品番列表
    getPartList(designation) {
      // 根据参数是否存在来确定发送的请求
      const url = designation ? `/fabricate/part/getPartListByDesignation?designation=${designation}` : '/fabricate/part/getPartListByDesignation';
      console.log('请求参数:' + url)
      this.$http.get(url).then(({data: res}) => {
        if (res.code !== 0) {
          return this.$message.error(res.msg);
        }
        this.partOptions = res.data.map((obj) => {
          return {
            label: obj.designation,
            value: obj.id,
            data: obj
          };
        });
      }).catch(() => {
        // 错误处理
      });
    },

    subBatchChangeReturn(value) {
      this.getSubBatchList(value)
    },
    subBatchHandleVisibleChange(visible) {
      if (visible) {
        // 下拉框显示时执行的操作
        console.log('下拉框显示了');
        // 这里可以触发相应的事件或执行其他操作
        this.getSubBatchList();
      }
    },
    /**
     *   获取次批次列表
     */
    getSubBatchList(designation) {
      // 根据参数是否存在来确定发送的请求
      const url = designation ? `/batch/subbatch/querySuBatchNumber?param=${designation}` : '/batch/subbatch/querySuBatchNumber';
      console.log('请求参数:' + url)
      this.$http.get(url).then(({data: res}) => {
        if (res.code !== 0) {
          return this.$message.error(res.msg);
        }
        this.subBatchList = res.data.map((obj) => {
          return {
            label: obj.subBatchNumber,
            value: obj.id,
            data: obj
          };
        });
      }).catch(() => {
        // 错误处理
      });
    },


    moldChangeReturn(value) {
      this.getMoldList(value)
    },
    moldHandleVisibleChange(visible) {
      if (visible) {
        // 下拉框显示时执行的操作
        console.log('下拉框显示了');
        // 这里可以触发相应的事件或执行其他操作
        this.getMoldList();
      }
    },
    getMoldList(moldCode) {
      // 根据参数是否存在来确定发送的请求
      const url = moldCode ? `/fabricate/mold/moldList?moldCode=${moldCode}` : '/fabricate/mold/moldList';
      console.log('请求参数:' + url)
      this.$http.get(url).then(({data: res}) => {
        if (res.code !== 0) {
          return this.$message.error(res.msg);
        }
        this.moldOptions = res.data.map((obj) => {
          return {
            label: obj.moldCode,
            value: obj.id,
            data: obj
          };
        });
      }).catch(() => {
        // 错误处理
      });
    },
    machineChangeReturn(value) {
      this.getMachineList(value)
    },
    machineHandleVisibleChange(visible) {
      if (visible) {
        // 下拉框显示时执行的操作
        console.log('下拉框显示了');
        // 这里可以触发相应的事件或执行其他操作
        this.getMachineList();
      }
    },
    getMachineList(code) {
      // 根据参数是否存在来确定发送的请求
      const url = code ? `/machine/machine/queryMachineListByCode?code=${code}` : '/machine/machine/queryMachineListByCode';
      console.log('请求参数:' + url)
      this.$http.get(url).then(({data: res}) => {
        if (res.code !== 0) {
          return this.$message.error(res.msg);
        }
        this.machineOptions = res.data.map((obj) => {
          return {
            label: obj.code,
            value: obj.id,
            data: obj
          };
        });
      }).catch(() => {
        // 错误处理
      });
    },
    // 获取信息
    getInfo() {
      this.$http.get(`/fabricate/machinemold/${this.dataForm.id}`).then(({data: res}) => {
        if (res.code !== 0) {
          return this.$message.error(res.msg)
        }
        this.dataForm = {
          ...this.dataForm,
          ...res.data
        }
      }).catch(() => {
      })
    },
    //关闭对话框
    closeDialog() {
      this.dataForm = {};
      this.visible = false;
    },
    // 表单提交
    dataFormSubmitHandle: debounce(function () {
      this.$refs['dataForm'].validate((valid) => {
        if (!valid) {
          return false
        }
        if(this.dataForm.operatorOne === this.dataForm.operatorTwo){
          return this.$message.error('操作员1和操作员2不能相同');
        }

        if(new Date(this.dataForm.putTime) > new Date(this.dataForm.takeDownTime)){
          return this.$message.error('下模时间不能早于上模时间');
        }
        let message = '';
        if (this.dataForm.id) {
          message = '此操作将修改该数据'
        } else {
          message = '此操作将新增一条数据'
        }
        this.$confirm(message + ', 是否继续?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
          center: true
        }).then(() => {
          this.$http[!this.dataForm.id ? 'post' : 'put']('/fabricate/machinemold/', this.dataForm).then(({data: res}) => {
            if (res.code !== 0) {
              return this.$message.error(res.msg)
            }
            this.$message({
              message: this.$t('prompt.success'),
              type: 'success',
              duration: 500,
              onClose: () => {
                this.visible = false
                this.$emit('refreshDataList')
              }
            })
          }).catch(() => {
          })
        }).catch(() => {
          this.$message({
            type: 'info',
            message: '已取消'
          });
        });
      })
    }, 1000, {'leading': true, 'trailing': false})
  }
}
</script>
