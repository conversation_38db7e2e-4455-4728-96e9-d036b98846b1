<template>
  <el-dialog :visible.sync="visible" :title="!dataForm.id ? '新增模具组件' : '修改模具组件'" :close-on-click-modal="false"
             :close-on-press-escape="false" append-to-body width="40vw" @close="closeDialog">
    <el-form :model="dataForm" :rules="dataRule" ref="dataForm" @keyup.enter.native="dataFormSubmitHandle()"
             :label-width="$i18n.locale === 'en-US' ? '120px' : '80px'">
      <div>
        <el-form-item label="组件编码" style="width: 30vw">
          <span class="font_size">{{ dataForm.head }}</span>
          <el-select v-model="dataForm.gum" placeholder="请选择" style="width: 100px; margin-left: 6px">
            <el-option v-for="item in gum"
                       :key="item.value"
                       :label="item.label"
                       :value="item.value"></el-option>
          </el-select>
          <el-select v-model="dataForm.represent" placeholder="请选择" style="width: 100px; margin-left: 6px">
            <el-option v-for="item in represent"
                       :key="item.value"
                       :label="item.label"
                       :value="item.value"></el-option>
          </el-select>
          <el-input v-model="dataForm.serialNumber" placeholder="编号流水号" style="width: 150px; margin-left: 6px"
                    @input="serialNumberValidateInput">
          </el-input>
          <el-input v-model="dataForm.groupId" placeholder="模具组编号" style="width: 110px; margin-left: 6px"
                    @input="groupIdValidateInput">
          </el-input>
        </el-form-item>
      </div>
      <div style="display: flex">
        <el-form-item label="所属模具" prop="moldId">
          <el-select v-model="dataForm.moldId" @change="fillCode" filterable clearable>
            <el-option v-for="item in moldOption"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="是否共用">
          <el-switch
              v-model="dataForm.shared"
              active-color="#13ce66"
              inactive-color="#f5f5f5"
              active-text="是"
              :active-value="true"
              inactive-text="否"
              :inactive-value="false">
          </el-switch>
        </el-form-item>
      </div>
      <el-form-item v-if="dataForm.shared" label="共用模具" prop="sharedMoldId">
        <el-select v-model="dataForm.sharedMoldId" @change="coverCode" filterable clearable>
          <el-option v-for="item in moldOption"
                     :key="item.value"
                     :label="item.label"
                     :value="item.value"
          ></el-option>
        </el-select>
      </el-form-item>
    </el-form>
    <template slot="footer">
      <el-button @click="visible = false">{{ $t('cancel') }}</el-button>
      <el-button type="primary" @click="dataFormSubmitHandle()">{{ $t('confirm') }}</el-button>
    </template>
  </el-dialog>
</template>

<script>
import debounce from 'lodash/debounce'
import mixinViewModule from '@/mixins/view-module'
import th from "element-ui/src/locale/lang/th";

export default {
  mixins: [mixinViewModule],
  data() {
    return {
      visible: false,
      //创建该页面时，不调用数据查询接口
      mixinViewModuleOptions: {
        createdIsNeed: false,
      },
      dataForm: {
        id: '',
        moldId: '',
        head: 'MJ',
        serialNumber: '',
        gum: '',
        represent: '',
        shared:false,
        sharedMoldId:'',
        groupId: '',
        subassemblyCode: '',
        subassemblyName: '',
        disabled: '',
        creator: '',
        createDate: '',
        updater: '',
        updateDate: ''
      },
      gum: [{
        value: 'R',
        label: 'R (橡胶)'
      }, {
        value: 'S',
        label: 'S (硅胶)'
      }, {
        value: 'L',
        label: 'L (液态)'
      }, {
        value: 'I',
        label: 'I (铁件)'
      }],
      represent: [{
        value: 'X',
        label: 'X (模心)'
      }, {
        value: 'U',
        label: 'U (上模)'
      }, {
        value: 'D',
        label: 'D (下模)'
      }, {
        value: 'M',
        label: 'M (中模)'
      }, {
        value: 'J',
        label: 'J (配件)'
      }, {
        value: 'Z',
        label: 'Z (治具)'
      }],
      moldOption:[],
    }
  },
  computed: {
    dataRule() {
      return {
        id: [
          {required: true, message: this.$t('validate.required'), trigger: 'blur'}
        ],
        moldId: [
          {required: true, message: this.$t('validate.required'), trigger: 'blur'}
        ],
        sharedMoldId: [
          {required: true, message: this.$t('validate.required'), trigger: 'blur'}
        ],
        subassemblyCode: [
          {required: true, message: this.$t('validate.required'), trigger: 'blur'}
        ],
        subassemblyName: [
          {required: true, message: this.$t('validate.required'), trigger: 'blur'}
        ],
      }
    }
  },
  methods: {
    init() {
      this.visible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].resetFields()
        if (this.dataForm.id) {
          this.getInfo()
        }
        this.getMoldOptions()
      })
    },
    serialNumberValidateInput() {
      // 只允许输入5个大写字母或数字
      this.dataForm.serialNumber = this.dataForm.serialNumber.replace(/[^A-Z0-9]/g, '').toUpperCase().slice(0, 5);
    },
    groupIdValidateInput() {
      // 只允许输入两个数字或大写字母
      this.dataForm.groupId = this.dataForm.groupId.replace(/[^A-Z0-9]/g, '').toUpperCase().slice(0, 2);
    },
    //选取模具后自动填充模具代码
    fillCode(){
      if(this.dataForm.moldId && this.dataForm.shared === false){
        let find = this.moldOption.find(item => item.value === this.dataForm.moldId);
        if(find.data.moldCode.length === 12){
          this.dataForm.head = find.data.moldCode.slice(0, 2)
          this.dataForm.gum = find.data.moldCode.slice(2, 3)
          this.dataForm.serialNumber = find.data.moldCode.slice(4, 9)
          this.dataForm.groupId = find.data.moldCode.slice(10, 12)
        }
      }
    },
    //选取共用模具时填充模具代码
    coverCode(){
      if(this.dataForm.sharedMoldId){
        if(this.dataForm.sharedMoldId === this.dataForm.moldId){
          this.dataForm.sharedMoldId = ''
          return this.$message.error("所属模具与共用模具不能相同")
        }
        let find = this.moldOption.find(item => item.value === this.dataForm.sharedMoldId);
        if(find.data.moldCode.length === 12){
          this.dataForm.head = find.data.moldCode.slice(0, 2)
          this.dataForm.gum = find.data.moldCode.slice(2, 3)
          this.dataForm.serialNumber = find.data.moldCode.slice(4, 9)
          this.dataForm.groupId = find.data.moldCode.slice(10, 12)
        }
      }
    },
    closeDialog(){
      console.log("关闭对话框")
      this.dataForm = {
        id: '',
        moldId: '',
        head: 'MJ',
        serialNumber: '',
        gum: '',
        represent: '',
        shared:false,
        sharedMoldId:'',
        groupId: '',
        subassemblyCode: '',
        subassemblyName: '',
        disabled: '',
        creator: '',
        createDate: '',
        updater: '',
        updateDate: ''
      }
    },
    // 获取信息
    getInfo() {
      this.$http.get(`/batch/moldsubassembly/${this.dataForm.id}`).then(({data: res}) => {
        if (res.code !== 0) {
          return this.$message.error(res.msg)
        }
        this.dataForm = {
          ...this.dataForm,
          ...res.data
        }
      }).catch(() => {
      })
    },
    //获取摸具列表
    getMoldOptions(){
      this.$http.get('fabricate/mold/getMoldList').then(({data: res}) =>{
        if(res.code !== 0){
          return this.$message.error(res.msg)
        }
        this.moldOption = res.data.map((obj) => {
          return{
            label: obj.designation+'('+obj.moldGroup+')',
            value: obj.id,
            data:obj
          }
        })
      })
    },
    // 表单提交
    dataFormSubmitHandle: debounce(function () {
      this.$refs['dataForm'].validate((valid) => {
        if (!valid) {
          return false
        }
        if ((!this.dataForm.gum ||
            !this.dataForm.represent ||
            !(this.dataForm.serialNumber && /^[A-Z0-9]{5}$/.test(this.dataForm.serialNumber)) ||
            !(this.dataForm.groupId && /^[A-Z0-9]{2}$/.test(this.dataForm.groupId)))){
          return this.$message.error('模具编码必须填写完整')
        }
        this.dataForm.subassemblyCode = this.dataForm.head+this.dataForm.gum + this.dataForm.represent + this.dataForm.serialNumber + '-' + this.dataForm.groupId
        let message = '';
        if (this.dataForm.id) {
          message = '此操作将修改该数据'
        } else {
          message = '此操作将新增一条数据'
        }
        this.$confirm(message + ', 是否继续?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
          center: true
        }).then(() => {
          this.$http[!this.dataForm.id ? 'post' : 'put']('/batch/moldsubassembly/', this.dataForm).then(({data: res}) => {
            if (res.code !== 0) {
              return this.$message.error(res.msg)
            }
            this.$message({
              message: this.$t('prompt.success'),
              type: 'success',
              duration: 500,
              onClose: () => {
                this.visible = false
                this.$emit('refreshDataList')
              }
            })
          }).catch(() => {
          })
        }).catch(() => {
          this.$message({
            type: 'info',
            message: '已取消'
          });
        });
      })
    }, 1000, {'leading': true, 'trailing': false})
  }
}
</script>
