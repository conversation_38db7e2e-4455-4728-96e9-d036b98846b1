<template>
  <el-dialog :visible.sync="visible" :title="!dataForm.id ? $t('add') : $t('update')" :close-on-click-modal="false" :close-on-press-escape="false" append-to-body>
    <el-form :model="dataForm" :rules="dataRule" ref="dataForm" @keyup.enter.native="dataFormSubmitHandle()" :label-width="$i18n.locale === 'en-US' ? '120px' : '80px'">
          <el-form-item label="产品库存id" prop="productInventoryId">
          <el-input v-model="dataForm.productInventoryId" placeholder="产品库存id"></el-input>
      </el-form-item>
          <el-form-item label="转入单位" prop="transferredUnit">
          <el-input v-model="dataForm.transferredUnit" placeholder="转入单位"></el-input>
      </el-form-item>
          <el-form-item label="未检" prop="uncheckedNum">
          <el-input v-model="dataForm.uncheckedNum" placeholder="未检"></el-input>
      </el-form-item>
          <el-form-item label="品修" prop="qualityRepairNum">
          <el-input v-model="dataForm.qualityRepairNum" placeholder="品修"></el-input>
      </el-form-item>
          <el-form-item label="品检" prop="qualityInspectionNum">
          <el-input v-model="dataForm.qualityInspectionNum" placeholder="品检"></el-input>
      </el-form-item>
          <el-form-item label="成品" prop="endProductNum">
          <el-input v-model="dataForm.endProductNum" placeholder="成品"></el-input>
      </el-form-item>
          <el-form-item label="库存" prop="inventoryNum">
          <el-input v-model="dataForm.inventoryNum" placeholder="库存"></el-input>
      </el-form-item>
          <el-form-item label="转入数" prop="transferredNum">
          <el-input v-model="dataForm.transferredNum" placeholder="转入数"></el-input>
      </el-form-item>
          <el-form-item label="入货量" prop="quantityReceived">
          <el-input v-model="dataForm.quantityReceived" placeholder="入货量"></el-input>
      </el-form-item>
          <el-form-item label="出货量" prop="shipmentQuantity">
          <el-input v-model="dataForm.shipmentQuantity" placeholder="出货量"></el-input>
      </el-form-item>
          <el-form-item label="次品数" prop="pantsNum">
          <el-input v-model="dataForm.pantsNum" placeholder="次品数"></el-input>
      </el-form-item>
          <el-form-item label="目前所在地" prop="location">
          <el-input v-model="dataForm.location" placeholder="目前所在地"></el-input>
      </el-form-item>
          <el-form-item label="备注" prop="remark">
          <el-input v-model="dataForm.remark" placeholder="备注"></el-input>
      </el-form-item>
          <el-form-item label="批号基本档id" prop="batchBasicId">
          <el-input v-model="dataForm.batchBasicId" placeholder="批号基本档id"></el-input>
      </el-form-item>
          <el-form-item label="是否失效 0:有效; 1:失效 (逻辑删除)" prop="disabled">
          <el-input v-model="dataForm.disabled" placeholder="是否失效 0:有效; 1:失效 (逻辑删除)"></el-input>
      </el-form-item>
              <el-form-item label="更新者" prop="updater">
          <el-input v-model="dataForm.updater" placeholder="更新者"></el-input>
      </el-form-item>
          <el-form-item label="更新时间" prop="updateDate">
          <el-input v-model="dataForm.updateDate" placeholder="更新时间"></el-input>
      </el-form-item>
      </el-form>
    <template slot="footer">
      <el-button @click="visible = false">{{ $t('cancel') }}</el-button>
      <el-button type="primary" @click="dataFormSubmitHandle()">{{ $t('confirm') }}</el-button>
    </template>
  </el-dialog>
</template>

<script>
import debounce from 'lodash/debounce'
export default {
  data () {
    return {
      visible: false,
      dataForm: {
        id: '',
        productInventoryId: '',
        transferredUnit: '',
        uncheckedNum: '',
        qualityRepairNum: '',
        qualityInspectionNum: '',
        endProductNum: '',
        inventoryNum: '',
        transferredNum: '',
        quantityReceived: '',
        shipmentQuantity: '',
        pantsNum: '',
        location: '',
        remark: '',
        batchBasicId: '',
        disabled: '',
        creator: '',
        createDate: '',
        updater: '',
        updateDate: ''
      }
    }
  },
  computed: {
    dataRule () {
      return {
        productInventoryId: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        transferredUnit: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        uncheckedNum: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        qualityRepairNum: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        qualityInspectionNum: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        endProductNum: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        inventoryNum: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        transferredNum: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        quantityReceived: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        shipmentQuantity: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        pantsNum: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        location: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        remark: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        batchBasicId: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        disabled: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        updater: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        updateDate: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ]
      }
    }
  },
  methods: {
    init () {
      this.visible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].resetFields()
        if (this.dataForm.id) {
          this.getInfo()
        }
      })
    },
    // 获取信息
    getInfo () {
      this.$http.get(`/fabricate/productinventorydetails/${this.dataForm.id}`).then(({ data: res }) => {
        if (res.code !== 0) {
          return this.$message.error(res.msg)
        }
        this.dataForm = {
          ...this.dataForm,
          ...res.data
        }
      }).catch(() => {})
    },
    // 表单提交
    dataFormSubmitHandle: debounce(function () {
      this.$refs['dataForm'].validate((valid) => {
        if (!valid) {
          return false
        }
        this.$http[!this.dataForm.id ? 'post' : 'put']('/fabricate/productinventorydetails/', this.dataForm).then(({ data: res }) => {
          if (res.code !== 0) {
            return this.$message.error(res.msg)
          }
          this.$message({
            message: this.$t('prompt.success'),
            type: 'success',
            duration: 500,
            onClose: () => {
              this.visible = false
              this.$emit('refreshDataList')
            }
          })
        }).catch(() => {})
      })
    }, 1000, { 'leading': true, 'trailing': false })
  }
}
</script>
