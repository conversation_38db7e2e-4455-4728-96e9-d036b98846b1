<template>
  <el-dialog :visible.sync="visible" :title="!dataForm.id ? '新增产品库存' : '修改产品库存'" :close-on-click-modal="false"
             :close-on-press-escape="false" append-to-body>
    <el-form :model="dataForm" :rules="dataRule" ref="dataForm" @keyup.enter.native="dataFormSubmitHandle()"
             :label-width="$i18n.locale === 'en-US' ? '120px' : '80px'">
      <div class="container">
        <div class="box">
          <el-form-item prop="code">
            <template>
              <span style="font-size: 16px;">产品编号：</span>
            </template>
            <div style="font-size: 16px; color: black;font-weight: bold;min-width: 180px">
              {{dataForm.code}}
            </div>
          </el-form-item>
        </div>
      </div>
      <div class="container">
        <div style="background-color:#bcbec2">
          <div>
            <el-form-item label="库存总数:" prop="inventoryNum">
              <div style="font-size: 16px; color: black;font-weight: bold;">
                {{calculateInventoryNum | numberWithCommas}}
              </div>
            </el-form-item>
          </div>
          <div>
            <el-form-item label="无订单数:" prop="inventoryNum">
              <el-input-number :controls="false" v-model="dataForm.numberOfReserves" placeholder="备存数"></el-input-number>
            </el-form-item>
          </div>
          <div>
            <el-form-item label="转入数" prop="transferredNum">
              <el-input-number :controls="false" v-model="dataForm.transferredNum" placeholder="转入数"></el-input-number>
            </el-form-item>
          </div>
          <div></div>
        </div>

        <div style="background-color: #dfe6e9">
          <div>
            <el-form-item label="成品总数:" prop="endProductNum">
              <div style="font-size: 16px; color: black;font-weight: bold;">
                {{calculateEndProductNum | numberWithCommas}}
              </div>
            </el-form-item>
          </div>
          <div>
            <el-form-item label="待包装数" prop="toBePacked">
              <el-input-number :controls="false" v-model="dataForm.toBePacked" placeholder="待包装数"></el-input-number>
            </el-form-item>
          </div>
          <div>
            <el-form-item label="待出货数" prop="endProductNum">
              <el-input-number :controls="false" v-model="dataForm.toBeShipped" placeholder="待出货数"></el-input-number>
            </el-form-item>
          </div>
        </div>


        <div style="background-color: #f1f2f6">
          <div>
            <el-form-item label="半成品数:" prop="semiFinishedProduct">
              <div class="left-align">
                {{calculateSemiFinishedProduct | numberWithCommas}}
              </div>
            </el-form-item>
          </div>
          <div>
            <el-form-item label="成形数" prop="uncheckedNum">
              <el-input-number :controls="false" v-model="dataForm.uncheckedNum" placeholder="成形数"></el-input-number>
            </el-form-item>
          </div>
          <div>
            <el-form-item label="品修数" prop="qualityRepairNum">
              <el-input-number :controls="false" v-model="dataForm.qualityRepairNum" placeholder="品修数"></el-input-number>
            </el-form-item>
          </div>
          <div>
            <el-form-item label="品检数" prop="qualityInspectionNum">
              <el-input-number :controls="false" v-model="dataForm.qualityInspectionNum" placeholder="品检数"></el-input-number>
            </el-form-item>
          </div>
        </div>
        <el-form-item label="备注" prop="remark">
          <el-input type="textarea" :rows="4" v-model="dataForm.remark" placeholder="备注"></el-input>
        </el-form-item>
      </div>



    </el-form>
    <template slot="footer">
      <el-button @click="visible = false">{{ $t('cancel') }}</el-button>
      <el-button type="primary" @click="dataFormSubmitHandle()">{{ $t('confirm') }}</el-button>
    </template>
  </el-dialog>
</template>

<script>
import debounce from 'lodash/debounce'

export default {
  data() {
    return {
      visible: false,
      dataForm: {
        id: '',
        partId: '',
        code:'',
        partDesignation: '',
        customerId:'',
        documentId:'',
        customerCode: '',
        uncheckedNum: 0,
        qualityRepairNum: 0,
        qualityInspectionNum: 0,
        semiFinishedProduct: 0,
        endProductNum: 0,
        inventoryNum: 0,
        numberOfReserves: 0,
        isolationNumber: 0,
        transferredNum: 0,
        toBeShipped: 0,
        toBePacked: 0,
        remark: '',
        disabled: '',
        creator: '',
        createDate: '',
        updater: '',
        updateDate: ''
      }
    }
  },
  filters:{
    // 自定义千位分隔符
    numberWithCommas (value) {
      // 将数字转换为字符串并使用正则表达式添加千位分隔符
      return String(value).replace(/\B(?=(\d{3})+(?!\d))/g, ',')
    },

  },
  computed: {
    formattedTransferredNum: {
      get() {
        return this.formatNumber(this.dataForm.transferredNum);
      },
      set(val) {
        let num = val.replace(/,/g, '');
        if (!num || isNaN(num)) {
          this.dataForm.transferredNum = null;
        } else {
          this.dataForm.transferredNum = Number(num);
        }
      }
    },

    calculateEndProductNum: function() {
      return Number(this.dataForm.toBeShipped) + Number(this.dataForm.toBePacked);
    },
    calculateSemiFinishedProduct: function() {
      return Number(this.dataForm.uncheckedNum) + Number(this.dataForm.qualityRepairNum) + Number(this.dataForm.qualityInspectionNum);
    },
    calculateInventoryNum: function() {
      return Number(this.dataForm.endProductNum) + Number(this.dataForm.semiFinishedProduct) + Number(this.dataForm.transferredNum);
    },
    dataRule() {
      return {
        partId: [
          {required: true, message: this.$t('validate.required'), trigger: 'blur'}
        ],
      }
    }
  },
  watch: {
    'dataForm.transferredNum'(val) {
      if (!val && val !== 0) {
        this.dataForm.transferredNum = 0;
      }
    },
    'dataForm.toBePacked'(val) {
      if (!val && val !== 0) {
        this.dataForm.toBePacked = 0;
      }
    },
    'dataForm.uncheckedNum'(val) {
      if (!val && val !== 0) {
        this.dataForm.uncheckedNum = 0;
      }
    },
    'dataForm.toBeShipped'(val) {
      if (!val && val !== 0) {
        this.dataForm.toBeShipped = 0;
      }
    },
    'dataForm.qualityRepairNum'(val) {
      if (!val && val !== 0) {
        this.dataForm.qualityRepairNum = 0;
      }
    },
    'dataForm.qualityInspectionNum'(val) {
      if (!val && val !== 0) {
        this.dataForm.qualityInspectionNum = 0;
      }
    },
    calculateEndProductNum: function(val) {
      console.log(val)
      this.endProductNum = val;
    },
    calculateSemiFinishedProduct: function(val) {
      console.log(val)
      this.semiFinishedProduct = val;
    },
    calculateInventoryNum: function(val) {
      this.inventoryNum = val ;
    },
  },
  methods: {
    thousandSeparator(row, column, cellValue) {
      return cellValue.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ",");
    },
    formatNumber(value) {
      if (!value && value !== 0) return '';
      value = value.toString().replace(/,/g, '');
      let intPart = value.split('.')[0];
      let decimalPart = value.split('.')[1] || '';
      let reg = /(\d{1,3})(?=(\d{3})+$)/g;
      intPart = intPart.replace(reg, '$1,');
      return intPart + (decimalPart ? '.' + decimalPart : '');
    },
    getLikePartList(customerCode, cb) {
      this.$http.get(`customer/customer/selectCode?code=` + customerCode).then(({data: res}) => {
        if (res.code !== 0) {
          return this.$message.error(res.msg)
        }
        const list = res.data.map((obj) => {
          return {
            customerCode: obj.code,
            value: obj.code + '(' + obj.code + ')',
            customerId: obj.id,
          }
        })
        this.timeout = setTimeout(() => {
          cb(list);
        }, 3000 * Math.random());
      }).catch(() => {
      })
    },
    //  输出选择日志
    handleSelect(item) {
      this.dataForm.customerCode = item.customerCode
      this.dataForm.customerId = item.customerId
    },
    //  获取量产类型部品列表
    getPartList(queryString, cb) {
      this.$http.get(`/fabricate/part/getMPAll/` + queryString).then(({data: res}) => {
        if (res.code !== 0) {
          return this.$message.error(res.msg)
        }
        const list = res.data.map((obj) => {
          return {
            customerCode: obj.customerCode,
            value: obj.designation + '(' + obj.customerCode + ')',
            partId: obj.id,
            partDesignation: obj.designation
          }
        })
        this.timeout = setTimeout(() => {
          cb(list);
        }, 3000 * Math.random());
      }).catch(() => {
      })
    },
    //  输出选择日志
    partSelect(item) {
      this.dataForm.partDesignation = item.partDesignation
      this.dataForm.customerCode = item.customerCode
      this.dataForm.partId = item.partId
    },
    init() {
      this.visible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].resetFields()
        if (this.dataForm.id) {
          this.getInfo()
        }
      })
    },
    // 获取信息
    getInfo() {
      this.$http.get(`/fabricate/productInventory/${this.dataForm.id}`).then(({data: res}) => {
        if (res.code !== 0) {
          return this.$message.error(res.msg)
        }
        this.dataForm = {
          ...this.dataForm,
          ...res.data
        }
      }).catch(() => {
      })
    },
    // 表单提交
    dataFormSubmitHandle: debounce(function () {
      this.dataForm.uncheckedNum = this.dataForm.uncheckedNum || 0;
      this.dataForm.qualityRepairNum = this.dataForm.qualityRepairNum || 0;
      this.dataForm.qualityInspectionNum = this.dataForm.qualityInspectionNum || 0;
      this.dataForm.endProductNum = this.dataForm.endProductNum || 0;
      this.dataForm.inventoryNum = this.dataForm.inventoryNum || 0;
      this.dataForm.transferredNum = this.dataForm.transferredNum || 0;
      this.$refs['dataForm'].validate((valid) => {
        if (!valid) {
          return false
        }
        this.$http[!this.dataForm.id ? 'post' : 'put']('/fabricate/productInventory/', this.dataForm).then(({data: res}) => {
          if (res.code !== 0) {
            return this.$message.error(res.msg)
          }
          this.$message({
            message: this.$t('prompt.success'),
            type: 'success',
            duration: 500,
            onClose: () => {
              this.visible = false
              this.$emit('refreshDataList')
            }
          })
        }).catch(() => {
        })
      })
    }, 1000, {'leading': true, 'trailing': false})
  }
}
</script>

<style scoped>
.left-align{
  font-size: 16px;
  color: black;
  font-weight: bold;
}
.container {
  display: flex;
  flex-wrap: wrap;
  gap: 20px;
}
.box {
  width: calc(50% - 10px);
  margin: 5px;
}

@media screen and (max-width: 668px) {
  .container {
    display: block;  /* Change container display to block */
  }
  .box {
    width: 100%;  /* Change item width to 100% */
  }
}
.item {
  width: calc(33.33% - 10px);
  margin: 5px;
}

@media screen and (max-width: 768px) {
  .container {
    display: block;  /* Change container display to block */
  }
  .item {
    width: 100%;  /* Change item width to 100% */
  }
}

@media (min-width: 768px) and (max-width: 1023px) {
  .container {
    display: block;  /* Change container display to block */
  }
  .item {
    width: calc(50% - 10px);
  }
}

@media (min-width: 1024px) {
  .item {
    width: calc(33.33% - 10px);
  }
}
</style>

