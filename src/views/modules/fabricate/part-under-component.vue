<template>
  <div>
    <el-select
        @input="changeReturn"
        placeholder="输入品番"
        >
      <el-option
          v-for="(item,index) in options"
          :key="index"
          :label="item.label"
          :value="item.value">
      </el-option>
    </el-select>
  </div>
</template>
<script>


export default {
  name: 'PartUnderComponent',
  data() {
    return {
      options: [],
    }
  },
  mounted() {
    this.getPartList('')
  },
  methods: {
    changeReturn(value) {
      //拿到下拉列表value所对应的整个对象的数据
      this.getPartList(value)
    },
    //查询匹配品番列表
    getPartList(designation){
      if(designation){
        this.$http.get(`/fabricate/part/selectPartList?getPartListByDesignation=${designation}`).then(({data: res}) => {
          if (res.code !== 0) {
            return this.$message.error(res.msg)
          }
          this.options = res.data.map((obj) => {
            return {
              label: obj.designation,
              value: obj.id,
              data:obj
            }
          })
        }).catch(() => {

        })
      }else {
        this.$http.get(`/fabricate/part/selectPartList`).then(({data: res}) => {
          if (res.code !== 0) {
            return this.$message.error(res.msg)
          }
          this.options = res.data.map((obj) => {
            return {
              label: obj.designation,
              value: obj.id,
              data:obj
            }
          })
        }).catch(() => {

        })
      }
    }
  }
}
</script>

