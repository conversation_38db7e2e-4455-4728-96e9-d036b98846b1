<template>
  <el-card shadow="never" class="aui-card--fill">
    <div class="mod-batch__conductshipment}">
      <el-form :inline="true" :model="dataForm" @keyup.enter.native="getDataList()">
        <el-form-item>
          <el-tooltip content="请输入或者部品番号或者批号" placement="top">
            <el-input v-model="dataForm.paramStr" placeholder="查询关键字" clearable @clear="getDataList()">
              <el-button slot="append" icon="el-icon-search" @click="getDataList()"></el-button>
            </el-input>
          </el-tooltip>
        </el-form-item>
        <el-form-item>
          <el-select v-model="optionValue" filterable :clearable="true" @clear="getDataList()" placeholder="请选择客户">
            <el-option
                v-for="item in options"
                :key="item.value"
                :label="item.label"
                :value="item.value">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button v-if="$hasPermission('batch:conductshipment:save')" :type="isChecked ? 'primary' : 'info'" :disabled="!isChecked" @click="addOrUpdateHandle()">出货</el-button>
        </el-form-item>
      </el-form>

      <el-table v-loading="dataListLoading" height="600px" @select="handleSelect" ref="tables" :data="dataList" border @selection-change="dataListSelectionChange"  @row-click="getCurrentSelection" style="width: 100%;">
        <el-table-column  type="selection"  header-align="center" align="center" width="50"></el-table-column>
        <el-table-column prop="designation" label="部品品名" header-align="center" align="center"></el-table-column>
        <el-table-column prop="customerCode" label="客户" header-align="center" align="center"></el-table-column>
        <el-table-column prop="batchNumber" label="批号" header-align="center" align="center">
          <template slot-scope="scope">
            <div>
              {{$getDictLabel("size_category", scope.row.sizeCategory)+ scope.row.batchNumber}}
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="totalQuantity" label="每包数量" header-align="center" align="center"></el-table-column>
        <el-table-column prop="totalQuantity" label="每箱数量" header-align="center" align="center"></el-table-column>
        <el-table-column prop="totalQuantity" label="产品单重" header-align="center" align="center"></el-table-column>
        <el-table-column prop="totalQuantity" label="内标签" header-align="center" align="center"></el-table-column>
        <el-table-column prop="totalQuantity" label="外标签" header-align="center" align="center"></el-table-column>
        <el-table-column prop="totalQuantity" label="总数量" header-align="center" align="center"></el-table-column>
      </el-table>
      <el-pagination
        :current-page="page"
        :page-sizes="[10, 20, 50, 100]"
        :page-size="limit"
        :total="total"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="pageSizeChangeHandle"
        @current-change="pageCurrentChangeHandle">
      </el-pagination>
      <conductshipment-add v-if="addOrUpdateVisible"  @callbackSubBatch="callbackSubBatch" ref="addOrUpdate"></conductshipment-add>
    </div>
  </el-card>
</template>

<script>
import mixinViewModule from '@/mixins/view-module'
import conductshipmentAdd from "./conductshipment-add.vue";

export default {
  mixins: [mixinViewModule],
  data () {
    return {
      addOrUpdateVisible:false,
      mixinViewModuleOptions: {
        getDataListURL: '/batch/batch/checkIfShipmentIsPossible',
        getDataListIsPage: true,
        deleteIsBatch: true,
      },
      dataForm: {
        id: '',
        paramStr: '',
        customerId: '',
      },
      shipmentSubBatch:[],
      options:'',
      optionValue:'',
      isChecked: false,
      selectedData: [],
      dataListId:[],
      qualityInspection(row, column) {
        switch (row.qualityInspection) {
          case 0:
            return '未判定'
          case 1:
            return 'OK'
          case 2:
            return 'NG'
          case 3:
            return '特采'
        }
      },
    }
  },
  components: {
    conductshipmentAdd
  },
  mounted() {
    this.searchForCustomers('')
  },
  watch:{
    optionValue(newValue,oldValue){
      this.dataForm.customerId = newValue
      this.getDataList()
    }
  },
  methods:{
    callbackSubBatch(items){
      items.forEach(item => {
        const exists = this.shipmentSubBatch.some(targetItem => targetItem.id === item.id);
        if (!exists) {
          this.shipmentSubBatch.push(item);
        }
      });
      console.log(this.shipmentSubBatch,"this.shipmentSubBatch选择的ICP============")
    },
    getCurrentSelection(row){
      console.log(row,"row==================")
    },
    fetchSubBatches(row) {
      // use axios or other library to send a request to the server
      // pass the row information as a parameter
      this.$http.get('/batch/subbatch/queryShipmentQuantity?batchId='+row.batchId)
          .then(response => {
            // assign the response data to the subBatches property of the row
            row.subBatches = response.data;
            // expand the row
            this.$refs.tables.toggleRowExpansion(row, true);
          })
          .catch(error => {
            // handle error
            console.log(error);
          });
    },
    dataListSelectionChange(val){
      this.dataListId = val
    },
    searchForCustomers(value){
      this.$http.get(`customer/customer/selectCode?code=` + value).then(({data: res}) => {
        if (res.code !== 0) {
          return this.$message.error(res.msg)
        }
        this.options = res.data.map((obj) => {
          return {
            value: obj.id,
            label:obj.code,
          }
        })
      }).catch(() => {
      })
    },
    handleSelect(selection,row) {
      this.selectedData = selection;
      this.isChecked = this.selectedData.length > 0;
      this.shipmentBatch(row.id)
    },
    shipmentBatch(batchId){
      this.addOrUpdateVisible = true
      this.$nextTick(() => {
        this.$refs.addOrUpdate.batchId = batchId
            this.$refs.addOrUpdate.init()
      })
    },
    addOrUpdateHandle() {
      this.$http.post(`/batch/shippingdetails/addShipmentDetails`,this.selectedData).then(({data: res}) => {
        if (res.code !== 0) {
          return this.$message.error(res.msg)
        }
        this.$message({
          message: '出货单已生成',
          type: 'success'
        });
        this.$router.push({name: 'shipping-shippingform'})
      }).catch(() => {
      })
      // 更新isChecked属性
      this.isChecked = this.selectedData.length > 0;
    }
  }
}
</script>
