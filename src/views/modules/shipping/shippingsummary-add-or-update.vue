<template>
  <el-dialog :visible.sync="visible" :title="!dataForm.id ? $t('add') : $t('update')" :close-on-click-modal="false" :close-on-press-escape="false" append-to-body>
    <el-form :model="dataForm" :rules="dataRule" ref="dataForm" @keyup.enter.native="dataFormSubmitHandle()" :label-width="$i18n.locale === 'en-US' ? '120px' : '80px'">
      <el-form-item label="客户" prop="customerId">
            <customer-component v-model="dataForm.customerId"></customer-component>
      </el-form-item>
      <el-form-item label="品名" prop="partId">
            <part-number-component v-model="dataForm.partId" clearable placeholder="关键词搜索"></part-number-component>
      </el-form-item>
      <el-form-item label="订单id" prop="orderFormId">
          <el-input v-model="dataForm.orderFormId" placeholder="订单id"></el-input>
      </el-form-item>
      <el-form-item label="订单数量" prop="withinTheLabel">
          <el-input v-model="dataForm.withinTheLabel" placeholder="订单数量"></el-input>
      </el-form-item>
      <el-form-item label="实际出货数" prop="actualShipmentQuantity">
          <el-input v-model="dataForm.actualShipmentQuantity" placeholder="实际出货数"></el-input>
      </el-form-item>
      <el-form-item label="净重" prop="offLabel">
          <el-input v-model="dataForm.offLabel" placeholder="净重"></el-input>
      </el-form-item>
      <el-form-item label="毛重" prop="grossWeight">
          <el-input v-model="dataForm.grossWeight" placeholder="毛重"></el-input>
      </el-form-item>
      <el-form-item label="每箱包装数" prop="number">
          <el-input v-model="dataForm.number" placeholder="每箱包装数"></el-input>
      </el-form-item>
      <el-form-item label="箱数" prop="numberOfBoxes">
          <el-input v-model="dataForm.numberOfBoxes" placeholder="箱数"></el-input>
      </el-form-item>
      <el-form-item label="箱子尺寸" prop="boxSize">
          <el-input v-model="dataForm.boxSize" placeholder="箱子尺寸"></el-input>
      </el-form-item>
      <el-form-item label="单重" prop="singleWeight">
          <el-input v-model="dataForm.singleWeight" placeholder="单重"></el-input>
      </el-form-item>
      <el-form-item label="出货方式" prop="shippingMethod">
          <el-input v-model="dataForm.shippingMethod" placeholder="出货方式"></el-input>
      </el-form-item>
      <el-form-item label="备注" prop="remark">
          <el-input v-model="dataForm.remark" placeholder="备注"></el-input>
      </el-form-item>
    </el-form>
    <template slot="footer">
      <el-button @click="visible = false">{{ $t('cancel') }}</el-button>
      <el-button type="primary" @click="dataFormSubmitHandle()">{{ $t('confirm') }}</el-button>
    </template>
  </el-dialog>
</template>

<script>
import debounce from 'lodash/debounce'
export default {
  data () {
    return {
      visible: false,
      dataForm: {
        id: '',
        batchId: '',
        customerId: '',
        partId: '',
        orderFormId: '',
        withinTheLabel: '',
        actualShipmentQuantity: '',
        offLabel: '',
        grossWeight: '',
        number: '',
        numberOfBoxes: '',
        boxSize: '',
        singleWeight: '',
        shippingMethod: '',
        remark: '',
        disabled: '',
        creator: '',
        createDate: '',
        updater: '',
        updateDate: ''
      }
    }
  },
  computed: {
    dataRule () {
      return {}
    }
  },
  methods: {
    init () {
      this.visible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].resetFields()
        if (this.dataForm.id) {
          this.getInfo()
        }
      })
    },
    // 获取信息
    getInfo () {
      this.$http.get(`/shipping/shippingsummary/${this.dataForm.id}`).then(({ data: res }) => {
        if (res.code !== 0) {
          return this.$message.error(res.msg)
        }
        this.dataForm = {
          ...this.dataForm,
          ...res.data
        }
      }).catch(() => {})
    },
    // 表单提交
    dataFormSubmitHandle: debounce(function () {
      this.$refs['dataForm'].validate((valid) => {
        if (!valid) {
          return false
        }
        this.$http[!this.dataForm.id ? 'post' : 'put']('/shipping/shippingsummary/', this.dataForm).then(({ data: res }) => {
          if (res.code !== 0) {
            return this.$message.error(res.msg)
          }
          this.$message({
            message: this.$t('prompt.success'),
            type: 'success',
            duration: 500,
            onClose: () => {
              this.visible = false
              this.$emit('refreshDataList')
            }
          })
        }).catch(() => {})
      })
    }, 1000, { 'leading': true, 'trailing': false })
  }
}
</script>
