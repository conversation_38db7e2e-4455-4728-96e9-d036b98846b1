<template>
  <el-dialog :visible.sync="visible" :title="!dataForm.id ? $t('add') : $t('update')" :close-on-click-modal="false" :close-on-press-escape="false" append-to-body>
    <el-form :model="dataForm" :rules="dataRule" ref="dataForm" @keyup.enter.native="dataFormSubmitHandle()" :label-width="$i18n.locale === 'en-US' ? '120px' : '80px'">
          <el-form-item label="批号id" prop="batchId">
          <el-input v-model="dataForm.batchId" placeholder="批号id"></el-input>
      </el-form-item>
          <el-form-item label="出货状态" prop="shipmentStatus">
          <el-input v-model="dataForm.shipmentStatus" placeholder="出货状态"></el-input>
      </el-form-item>
          <el-form-item label="运送方式" prop="shippingMethod">
          <el-input v-model="dataForm.shippingMethod" placeholder="运送方式"></el-input>
      </el-form-item>
          <el-form-item label="栈板个数" prop="numberOfPallets">
          <el-input v-model="dataForm.numberOfPallets" placeholder="栈板个数"></el-input>
      </el-form-item>
          <el-form-item label="出货日期" prop="shippingDate">
          <el-input v-model="dataForm.shippingDate" placeholder="出货日期"></el-input>
      </el-form-item>
          <el-form-item label="备注" prop="remark">
          <el-input v-model="dataForm.remark" placeholder="备注"></el-input>
      </el-form-item>
                </el-form>
    <template slot="footer">
      <el-button @click="visible = false">{{ $t('cancel') }}</el-button>
      <el-button type="primary" @click="dataFormSubmitHandle()">{{ $t('confirm') }}</el-button>
    </template>
  </el-dialog>
</template>

<script>
import debounce from 'lodash/debounce'
export default {
  data () {
    return {
      visible: false,
      dataForm: {
        id: '',
        batchId: '',
        shipmentStatus: '',
        shippingMethod: '',
        numberOfPallets: '',
        shippingDate: '',
        remark: '',
        disabled: '',
        creator: '',
        createDate: '',
        updater: '',
        updateDate: ''
      }
    }
  },
  computed: {
    dataRule () {
      return {
          id: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          batchId: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          shipmentStatus: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          shippingMethod: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          numberOfPallets: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          shippingDate: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          remark: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
                }
    }
  },
  methods: {
    init () {
      this.visible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].resetFields()
        if (this.dataForm.id) {
          this.getInfo()
        }
      })
    },
    // 获取信息
    getInfo () {
      this.$http.get(`/batch/shippingform/${this.dataForm.id}`).then(({ data: res }) => {
        if (res.code !== 0) {
          return this.$message.error(res.msg)
        }
        this.dataForm = {
          ...this.dataForm,
          ...res.data
        }
      }).catch(() => {})
    },
    // 表单提交
    dataFormSubmitHandle: debounce(function () {
      this.$refs['dataForm'].validate((valid) => {
        if (!valid) {
          return false
        }
        this.$http[!this.dataForm.id ? 'post' : 'put']('/batch/shippingform/', this.dataForm).then(({ data: res }) => {
          if (res.code !== 0) {
            return this.$message.error(res.msg)
          }
          this.$message({
            message: this.$t('prompt.success'),
            type: 'success',
            duration: 500,
            onClose: () => {
              this.visible = false
              this.$emit('refreshDataList')
            }
          })
        }).catch(() => {})
      })
    }, 1000, { 'leading': true, 'trailing': false })
  }
}
</script>
