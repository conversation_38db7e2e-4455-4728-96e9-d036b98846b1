<template>
  <el-card shadow="never" class="aui-card--fill">
    <div class="mod-shipping__shippingsummary}">
      <el-form :inline="true" :model="dataForm" @keyup.enter.native="getDataList()">
        <el-form-item>
          <el-input v-model="dataForm.id" placeholder="id" clearable></el-input>
        </el-form-item>
        <el-form-item>
          <el-button @click="getDataList()">{{ $t('query') }}</el-button>
        </el-form-item>
        <el-form-item>
          <el-button type="info" @click="exportHandle()">{{ $t('export') }}</el-button>
        </el-form-item>
        <el-form-item>
          <el-button v-if="$hasPermission('shipping:shippingsummary:export')" type="info" @click="exportTemplateHandle()">
            {{ $t('exportTemplate') }}
          </el-button>
        </el-form-item>
        <el-form-item>
          <el-tooltip content="下载模板填入信息后导入" placement="top">
            <el-upload
                    v-if="$hasPermission('shipping:shippingsummary:save')"
                    class="upload-demo"
                    :action="mixinViewModuleOptions.addBatchUrl"
                    :headers="headers"
                    :multiple="false"
                    :show-file-list="false"
                    :file-list="fileList"
                    :before-upload="beforeUpload"
                    :on-success="resultHandle"
                    :on-change="handleChange"
                    accept="'.xlsx','.xls'">
              <el-button type="success">{{ $t('addBatch') }}</el-button>
            </el-upload>
          </el-tooltip>
          <el-button type="text" v-show="unsuccessfulNameList" @click="dialogVisible = true">导入失败列表</el-button>
        </el-form-item>
        <el-form-item>
          <el-button v-if="$hasPermission('shipping:shippingsummary:save')" type="primary" @click="addOrUpdateHandle()">{{ $t('add') }}</el-button>
        </el-form-item>
        <el-form-item>
          <el-button v-if="$hasPermission('shipping:shippingsummary:delete')" type="danger" @click="deleteHandle()">{{ $t('deleteBatch') }}</el-button>
        </el-form-item>
      </el-form>
      <el-table v-loading="dataListLoading" height="600px" :data="dataList" border @selection-change="dataListSelectionChangeHandle" style="width: 100%;">
        <el-table-column type="selection" header-align="center" align="center" width="50"></el-table-column>
        <el-table-column prop="id" label="id" header-align="center" align="center"></el-table-column>
        <el-table-column prop="batchId" label="客户id" header-align="center" align="center"></el-table-column>
        <el-table-column prop="partId" label="品番id" header-align="center" align="center"></el-table-column>
        <el-table-column prop="orderFormId" label="订单id" header-align="center" align="center"></el-table-column>
        <el-table-column prop="withinTheLabel" label="订单数量" header-align="center" align="center"></el-table-column>
        <el-table-column prop="actualShipmentQuantity" label="实际出货数" header-align="center" align="center"></el-table-column>
        <el-table-column prop="offLabel" label="净重" header-align="center" align="center"></el-table-column>
        <el-table-column prop="grossWeight" label="毛重" header-align="center" align="center"></el-table-column>
        <el-table-column prop="number" label="每箱包装数" header-align="center" align="center"></el-table-column>
        <el-table-column prop="numberOfBoxes" label="箱数" header-align="center" align="center"></el-table-column>
        <el-table-column prop="boxSize" label="箱子尺寸" header-align="center" align="center"></el-table-column>
        <el-table-column prop="singleWeight" label="单重" header-align="center" align="center"></el-table-column>
        <el-table-column prop="shippingMethod" label="出货方式" header-align="center" align="center"></el-table-column>
        <el-table-column prop="remark" label="备注" header-align="center" align="center"></el-table-column>
        <el-table-column :label="$t('handle')" fixed="right" header-align="center" align="center" width="150">
          <template slot-scope="scope">
            <el-dropdown>
              <el-button type="primary">
                功能<i class="el-icon-arrow-down el-icon--right"></i>
              </el-button>
              <el-dropdown-menu slot="dropdown">
                <el-dropdown-item>
                  <el-button v-if="$hasPermission('shipping:shippingsummary:update')" type="text" size="small" @click="addOrUpdateHandle(scope.row.id)">{{ $t('update') }}</el-button>
                </el-dropdown-item>
                <el-dropdown-item>
                  <el-button v-if="$hasPermission('shipping:shippingsummary:delete')" type="text" size="small" @click="deleteHandle(scope.row.id)">{{ $t('delete') }}</el-button>
                </el-dropdown-item>
              </el-dropdown-menu>
            </el-dropdown>
          </template>
        </el-table-column>
      </el-table>
      <el-pagination
        :current-page="page"
        :page-sizes="[10, 20, 50, 100]"
        :page-size="limit"
        :total="total"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="pageSizeChangeHandle"
        @current-change="pageCurrentChangeHandle">
      </el-pagination>
      <!-- 弹窗, 新增 / 修改 -->
      <add-or-update v-if="addOrUpdateVisible" ref="addOrUpdate" @refreshDataList="getDataList"></add-or-update>
    </div>
  </el-card>
</template>

<script>
import mixinViewModule from '@/mixins/view-module'
import AddOrUpdate from './shippingsummary-add-or-update'
import Cookies from "js-cookie";
export default {
  mixins: [mixinViewModule],
  data () {
    return {
      message:'',
      headers: {'token': Cookies.get('token')},
      fileList: [{name: '', url: ''}],
      unsuccessfulNameList: '',//  导入失败名称列表
      mixinViewModuleOptions: {
        getDataListURL: '/shipping/shippingsummary/page',
        getDataListIsPage: true,
        exportURL: '/shipping/shippingsummary/export',
        deleteURL: '/shipping/shippingsummary',
        deleteIsBatch: true,
        exportTemplateURL: 'shipping/shippingsummary/export/template',
        addBatchUrl: window.SITE_CONFIG['apiURL'] + 'shipping/shippingsummary/batchSave',
      },
      dataForm: {
        id: '',
        paramStr: '',
      }
    }
  },
  components: {
    AddOrUpdate
  },
  methods:{
    beforeUpload(file) {
      this.message = this.$message({
        message: '上传中，请勿操作',
        center: true,
        duration: 0,
      });
    },
    handleChange(file, fileList) {
      this.fileList = fileList.slice(-3);

    },
    resultHandle(res) {
      if (res.code !== 0) {
      return this.$message.error(res.msg)
      }
      this.$message({
        message: this.$t('prompt.success'),
        type: 'success',
        duration: 500,
        onClose: () => {
          if (res.data) {
            this.unsuccessfulNameList = res.data
          }
          this.visible = false
          this.query()
        }
      })
    },
  }
}
</script>
