<template>
  <el-card shadow="never" class="aui-card--fill">
    <div class="mod-search__search}">
      <el-form :inline="true" :model="dataForm" @keyup.enter.native="getDataList()">
        <el-form-item>
          <el-tooltip content="请输入关键字" placement="top">
            <el-input v-model="dataForm.paramStr" placeholder="查询关键字" @input="throttleFunction" clearable @clear="getDataList()">
              <el-button slot="append" icon="el-icon-search" @click="getDataList()"></el-button>
            </el-input>
          </el-tooltip>
        </el-form-item>
      </el-form>
      <el-table v-loading="dataListLoading" :data="dataList" border @selection-change="dataListSelectionChangeHandle" style="width: 100%;">
        <el-table-column prop="customerCode" label="客户代码" header-align="center" align="center">
          <template slot-scope="scope">
            <div @click="routingParameters(1,scope.row.customerCode)">
              {{scope.row.customerCode}}
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="designation" label="品名" header-align="center" align="center">
          <template slot-scope="scope">
            <div @click="routingParameters(2,scope.row.designation)">
              {{scope.row.designation}}
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="batchNumber" label="批号" header-align="center" align="center">
          <template slot-scope="scope">
            <div v-if="scope.row.batchNumber" @click="routingParameters(3,scope.row.batchNumber)">
              {{$getDictLabel("size_category", scope.row.sizeCategory)+ scope.row.batchNumber}}
            </div>
            <div v-else>
              <span style="color: gold">暂无批号</span>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="subBatchNumber" label="次批号" header-align="center" align="center">
          <template slot-scope="scope">
            <div @click="routingParameters(4,scope.row.subBatchNumber,scope.row.batchId)">
              {{scope.row.subBatchNumber}}
            </div>
          </template>
        </el-table-column>
        <el-table-column :label="$t('handle')" fixed="right" header-align="center" align="center" width="150">
          <template slot-scope="scope">

          </template>
        </el-table-column>
      </el-table>
      <el-pagination
        :current-page="page"
        :page-sizes="[10, 20, 50, 100]"
        :page-size="limit"
        :total="total"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="pageSizeChangeHandle"
        @current-change="pageCurrentChangeHandle">
      </el-pagination>
    </div>
  </el-card>
</template>

<script>
import mixinViewModule from '@/mixins/view-module'
export default {
  mixins: [mixinViewModule],
  data () {
    return {
      mixinViewModuleOptions: {
        getDataListURL: '/batch/subbatch/searchDataList',
        getDataListIsPage: true,
        deleteIsBatch: true
      },
      dataForm: {
        id: '',
        paramStr: '',
      },
      // 判断是否还在继续输入
      timer: null
    }
  },
  components: {

  },
  methods: {
    //防抖函数
    throttleFunction() {
      // 如果timer不为空就表明在持续输入,每次改变都清除定时，这样就可以实现只输入最后一次结果(注意每次间隔如果超过500毫秒就会输出)
      if (this.timer !== null) clearTimeout(this.timer)
      this.timer = setTimeout(() => {
        this.getDataList()
      }, 500)
    },
    routingParameters(logotype,value,batchId){
      switch (logotype) {
        case 1:
          this.$router.push({name:'customer-customer',query:{parameter:value}})
          break;
        case 2:
          this.$router.push({name:'fabricate-part',query:{parameter:value}})
          break;
        case 3:
          this.$router.push({name:'batch-batch',query:{parameter:value}})
          break;
        case 4:
          this.$router.push({name:'batch-subbatch',query:{subBatchId:value,batchId:batchId}})
          break;
      }
    }
  }
}
</script>
