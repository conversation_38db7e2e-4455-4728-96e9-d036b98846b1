<template>
  <div>
    <div class="tabs-container">
      <el-menu
          v-model="activeMenu"
          :default-active="'1'"
          class="el-menu-demo custom-menu "
          mode="horizontal"
          @select="handleSelect">
        <el-menu-item index="0" class="wide-item" disabled>
          <h2 class="login-brand">{{ $t('brand.lg') }}</h2>
        </el-menu-item>
        <el-menu-item index="1" class="wide-item">表单</el-menu-item>
        <el-menu-item index="2" disabled class="wide-item">财务</el-menu-item>
        <el-menu-item index="3" disabled class="wide-item">人事</el-menu-item>
        <el-menu-item index="4" disabled class="wide-item">公告</el-menu-item>
        <el-menu-item index="4" disabled class="wide-item">个人信息</el-menu-item>
        <el-menu-item index="5" class="wide-item">
          进入后台
        </el-menu-item>
      </el-menu>
    </div>
    <div>
      <el-main>
        <router-view></router-view>
      </el-main>
    </div>
  </div>
</template>
<script>

export default {
  name: "new-page",
  data() {
    return {
      activeMenu: '1',
      dataList:[
        {title:'厂务',path:'navigate',disabled:true},
        {title:'财务',path:'',disabled:false},
        {title:'人事',path:'',disabled:false},
        {title:'公告',path:'',disabled:false},
        {title:'个人信息',path:'',disabled:false},
        {title:'进入后台',path:'home',disabled:true},
      ]
    }
  },
  computed: {
    toIndex(){  // 根据路径绑定到对应的一级菜单，防止页面刷新重新跳回第一个
      return '/' + this.$route.path.split('/')[1];
    },
  },
  mounted() {
    this.$router.push({name:'navigate'});
  },
  created() {
    // 获取字典列表, 添加并全局变量保存
    this.$http.get('/sys/dict/type/all').then(({ data: res }) => {
      if (res.code !== 0) {
        return
      }
      window.SITE_CONFIG['dictList'] = res.data
    }).catch(() => {})
  },
  methods:{
    handleSelect(index) {
      switch (index){
        case '1':
          this.$router.push({name:'navigate'});
          break;
        case '2':
          this.$router.push({name:''});
          break;
        case '3':
          this.$router.push({name:''});
          break;
        case '4':
          this.$router.push({name:''});
          break;
        case '5':
          this.$router.push({name:'home'});
          break;
      }
    }
  }
}
</script>

<style scoped>
.tabs-container {
  display: flex;
  justify-content: center;
}
.custom-menu{
  width: 60%;
}
.wide-item {
  width: 10%;
  text-align: center;
}
</style>
