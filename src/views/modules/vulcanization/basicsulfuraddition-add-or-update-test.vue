<template>
  <el-dialog :visible.sync="visible" :title="!dataForm.id ? $t('add') : $t('update')" :close-on-click-modal="false" :close-on-press-escape="false" append-to-body>
    <el-form :model="dataForm" :rules="dataRule" ref="dataForm" @keyup.enter.native="dataFormSubmitHandle()" :label-width="$i18n.locale === 'en-US' ? '120px' : '80px'">
      <div class="container">
        <div class="box">
          <el-form-item label="部品名称" prop="partName">
            <el-input v-model="dataForm.partName" placeholder="部品名称"></el-input>
          </el-form-item>
        </div>

        <div class="box">
          <el-form-item label="部品番号" prop="designation">
            <el-autocomplete
                class="custom-autocomplete"
                v-model="dataForm.designation"
                :fetch-suggestions="getPartList"
                placement="bottom"
                placeholder="请输入内容"
                :trigger-on-focus="false"
                @select="partSelect"
                style="width: auto"
            ></el-autocomplete>
          </el-form-item>
        </div>
      </div>
      <div class="container">
        <div class="box">
          <el-form-item label="版本别" prop="version">
            <el-input v-model="dataForm.version" placeholder="版本别"></el-input>
          </el-form-item>
        </div>
        <div class="box">
          <el-form-item label="模具编号" prop="moldId">
            <el-input v-model="dataForm.moldId" placeholder="模具编号"></el-input>
          </el-form-item>
        </div>
      </div>
      <div class="container">
        <div class="box">
          <el-form-item label="材质" prop="material">
            <el-select v-model="dataForm.material" placeholder="材质">
              <el-option
                  v-for="item in material"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value">
              </el-option>
            </el-select>
          </el-form-item>
        </div>

        <div class="box">
          <el-form-item label="客户代码" prop="customerCode">
            <el-autocomplete
                class="custom-autocomplete"
                v-model="dataForm.customerCode"
                :fetch-suggestions="getCustomerCode"
                placement="bottom"
                placeholder="请输入客户代码"
                :trigger-on-focus="false"
                @select="customerSelect"
                disabled
            ></el-autocomplete>
          </el-form-item>
        </div>
      </div>
      <div class="container">
        <div class="box">
          <el-form-item label="配方编号" prop="formulationCode">
            <el-autocomplete
                class="custom-autocomplete"
                v-model="dataForm.formulationCode"
                :fetch-suggestions="getFormulationCode"
                placement="bottom"
                placeholder="请输入配方代码"
                :trigger-on-focus="false"
                @select="formulationSelect"
            ></el-autocomplete>
          </el-form-item>
        </div>

        <div class="box">
          <el-form-item label="加硫作业标准书编号" prop="standardBookId" label-width="200px">
            <el-input v-model="dataForm.standardBookId" placeholder="加硫作业标准书编号"></el-input>
          </el-form-item>
        </div>
      </div>

      <div class="container">
        <div class="box">
          <el-form-item label="成型机型(吨)">
            <el-input v-model="dataForm.formingMachine" placeholder="成型机型(吨)"></el-input>
          </el-form-item>
        </div>

        <div class="box">
          <el-form-item label="金型">
            <el-input v-model="dataForm.moldHole" style="border: none;width: 150px"></el-input>[孔]
            *
            <el-input v-model="dataForm.moldSurface" style="border: none;width: 150px"></el-input>[面]
          </el-form-item>
        </div>
      </div>

      <div class="container">
        <div class="box">
          <el-form-item label="材料寸法">
            长<el-input  v-model="dataForm.materialDimensionsLong" style="border: none;width: 150px"></el-input>
            士<el-input  v-model="dataForm.moldDimensionsThreeLongMan" style="border: none;width: 150px"></el-input>mm
            <hr/>
            厚<el-input v-model="dataForm.materialDimensionsThickness" style="border: none;width: 150px"></el-input>
            士<el-input v-model="dataForm.materialDimensionsAtsushi" style="border: none;width: 150px"></el-input>mm
          </el-form-item>
        </div>

        <div class="box">
          <el-form-item label="材料重量">
            <el-input v-model="dataForm.materialWeightOne" style="border: none;width: 150px"></el-input>
            ——
            <el-input v-model="dataForm.materialWeightTwo" style="border: none;width: 150px"></el-input>g/条
          </el-form-item>
        </div>
      </div>

      <div class="container">
        <div class="box">
          <el-form-item label="成品重量">
            <el-input v-model="dataForm.finishedProductWeight" style="border: none;width: 150px"></el-input>kg
          </el-form-item>
        </div>

        <div class="box">
          <el-form-item label="金型实际温度">
            上<el-input v-model="dataForm.moldTemperatureTop" style="border: none;width: 150px"></el-input>℃
            <hr/>
            下<el-input v-model="dataForm.moldTemperatureBelow" style="border: none;width: 150px"></el-input>℃
          </el-form-item>
        </div>
      </div>

      <div class="container">
        <div class="box">
          <el-form-item label="加热治具温度">
            <el-input v-model="dataForm.heatingFixtureTemperatureOne" placeholder="加热治具温度1(℃)" style="border: none;width: 150px"></el-input>士
            <el-input v-model="dataForm.heatingFixtureTemperatureTwo" placeholder="加热治具温度2(℃)" style="border: none;width: 150px"></el-input>℃
          </el-form-item>
        </div>

        <div class="box">
          <el-form-item label="硫化时间">

            <el-input v-model="dataForm.vulcanizationTimeOne" style="border: none;width: 150px"></el-input>
            ——
            <el-input v-model="dataForm.vulcanizationTimeTwo" style="border: none;width: 150px"></el-input>秒
          </el-form-item>
        </div>
      </div>

      <div class="container">
        <div class="box">
          <el-form-item label="冷模超时报警">
            <el-input v-model="dataForm.coldMoldTimeoutAlarmHead" style="border: none;width: 150px"></el-input>/
            <el-input v-model="dataForm.coldMoldTimeoutAlarmEnd" style="border: none;width: 150px"></el-input>
          </el-form-item>
        </div>

        <div class="box">
          <el-form-item label="机器压力">
            <el-input v-model="dataForm.machinePressure" style="border: none;width: 150px"></el-input>kgf/cm2
          </el-form-item>
        </div>
      </div>

      <div class="container">
        <div class="box">
          <el-form-item label="机台编号">
            <el-input v-model="dataForm.machineId" placeholder="机台编号"></el-input>
          </el-form-item>
        </div>

        <div class="box">
          <el-form-item label="金型寸法">
            <el-input v-model="dataForm.moldDimensionsOne" style="border: none;width: 80px"></el-input>X
            <el-input v-model="dataForm.moldDimensionsTwo" style="border: none;width: 80px"></el-input>X
            <el-input v-model="dataForm.moldDimensionsThree" style="border: none;width: 80px"></el-input>mm
          </el-form-item>
        </div>
      </div>

      <div class="container">
        <div class="box">
          <el-form-item label="材料形状">
            <el-input v-model="dataForm.materialShape"></el-input>条
          </el-form-item>
        </div>

        <div class="box">
          <el-form-item label="共排">
            <el-input v-model="dataForm.rowTogether"></el-input>条
          </el-form-item>
        </div>
      </div>

      <div class="container">
        <div class="box">
          <el-form-item label="排气">
            <el-input v-model="dataForm.exhaust" style="border: none;"></el-input>次
          </el-form-item>
        </div>

        <div class="box">
          <el-form-item label="成形压力">
            <el-input v-model="dataForm.formingPressureHead" style="border: none;"></el-input>——
            <el-input v-model="dataForm.formingPressureEnd" style="border: none;"></el-input>
          </el-form-item>
        </div>
      </div>

      <div class="container">
        <div class="box">
          <el-form-item label="抽真空压力(cmHg)">
            <el-input v-model="dataForm.vacuumPressure"></el-input>
          </el-form-item>
        </div>

        <div class="box">
          <el-form-item label="模具清洗">
            <el-input v-model="dataForm.moldCleaning"></el-input>
          </el-form-item>
        </div>
      </div>

      <div class="container">
        <div class="box">
          <el-form-item label="清洗频率">
            <el-input v-model="dataForm.cleaningFrequency"></el-input>
          </el-form-item>
        </div>

        <div class="box">
          <el-form-item label="离型剂">
            <el-radio v-model="dataForm.releaseAgent" label="0">有</el-radio>
            <el-radio v-model="dataForm.releaseAgent" label="1">无</el-radio>
          </el-form-item>
        </div>
      </div>

      <div class="container">
        <div class="box">
          <el-form-item label="注意事项" prop="precautions">
            <el-input v-model="dataForm.precautions"></el-input>
          </el-form-item>

          <!--          <el-form-item label="注意事项圖片1" prop="precautions">
                      <el-upload
                          class="upload-demo"
                          :action="uploadUrl"
                          :before-upload="handleBeforeUpload"
                          :on-success="handleSuccess"
                          :on-error="handleError"
                          :on-remove="handleRemove"
                          :file-list="dataForm.fileList"
                          multiple
                          list-type="picture-card"
                          :auto-upload="false"
                      >
                        <i class="el-icon-plus"></i>
                        <div slot="file" slot-scope="{ file }">
                          <el-input
                              v-model="file.remark"
                              placeholder="请输入备注信息"
                              clearable
                              append-icon="el-icon-edit"
                              size="mini"
                              class="input-remark"
                          >
                          </el-input>
                        </div>
                        <div slot="tip" class="el-upload__tip">只能上传jpg/png文件，且不超过500kb</div>
                      </el-upload>
                    </el-form-item>-->
        </div>
      </div>

      <div class="container">
        <div class="box">
          <el-form-item label="半成品装箱数" prop="semiFinishedProductNumberBoxes">
            <el-input v-model="dataForm.semiFinishedProductNumberBoxes" style="width: 150px"></el-input>模/周转箱
          </el-form-item>
        </div>

        <div class="box">
          <el-form-item label="节拍时间(秒)" prop="taktTime">
            <el-input v-model="dataForm.taktTime" placeholder="节拍时间(秒)" style="width: 150px"></el-input>
          </el-form-item>
        </div>
      </div>

      <div class="container">
        <div class="box">
          <el-form-item label="产效目标" prop="taktTime">
            <el-input v-model="dataForm.takeEffect" style="width: 150px"></el-input>模/小时
          </el-form-item>
        </div>



        <div class="box">
          <el-form-item label="不良率" prop="defectiveRate">
            <el-input v-model="dataForm.defectiveRate" style="width: 150px"></el-input>%
          </el-form-item>
        </div>
      </div>

      <div class="container">
        <div class="box">
          <el-form-item label="备注" prop="remark">
            <el-input v-model="dataForm.remark"></el-input>
          </el-form-item>
        </div>
      </div>
    </el-form>
    <template slot="footer">
      <el-button @click="visible = false">{{ $t('cancel') }}</el-button>
      <el-button type="primary" @click="dataFormSubmitHandle()">{{ $t('confirm') }}</el-button>
    </template>
  </el-dialog>
</template>

<script>
import debounce from 'lodash/debounce'
export default {
  data () {
    return {
      uploadUrl: 'your-upload-url',
      fileList: [],
      showDialog: false,
      description: '',

      dialogImageUrl: '',
      dialogVisible: false,
      visible: false,
      dataForm: {
        id: '',
        customerId: '',
        customerCode: '',
        partId: '',
        designation: '',
        partName: '',
        moldId: '',
        standardBookId: '',
        version: 'A0',
        formulationId: '',
        formulationCode: '',
        material: '',
        formingMachine: '',
        fileList: [],
        machineId: '',
        moldHole: '',
        moldSurface: '',
        moldDimensionsOne: '',
        moldDimensionsTwo: '',
        moldDimensionsThree: '',
        materialDimensionsLong: '',
        moldDimensionsThreeLongMan: '',
        materialDimensionsThickness: '',
        materialDimensionsAtsushi: '',
        taktTime: '',
        releaseAgentUsageFrequency: '',
        vacuumPressure: '',
        formingPressureEnd: '',
        formingPressureHead: '',
        coldMoldTimeoutAlarmEnd: '',
        coldMoldTimeoutAlarmHead: '',
        heatingFixtureTemperatureTwo: '',
        heatingFixtureTemperatureOne: '',
        moldActualTemperatureDownTwo: '',
        moldActualTemperatureDownOne: '',
        moldActualTemperaturePreviewTwo: '',
        moldActualTemperaturePreviewOne: '',
        moldActualTemperatureSuperiorTwo: '',
        moldActualTemperatureSuperiorOne: '',
        materialWeightOne: '',
        materialWeightTwo: '',
        finishedProductWeight: '',
        materialShape: '',
        moldTemperatureTop: '',
        moldTemperatureBelow: '',
        vulcanizationTimeOne: '',
        vulcanizationTimeTwo: '',
        rowTogether: '',
        exhaust: '',
        machinePressure: '',
        moldCleaning: '',
        cleaningFrequency: '',
        releaseAgent: '',
        kind: '',
        frequency: '',
        plating: '',
        precautions: '',
        precautionsPictureOne: '',
        precautionsPictureTow: '',
        precautionsPictureThree: '',
        semiFinishedProductNumberBoxes: '',
        takeEffect: '',
        defectiveRate: '',
        remark: '',
        disabled: '',
        creator: '',
        createDate: '',
        updater: '',
        updateDate: ''
      },
      material:[{
        value: 1,
        label: '硅胶'
          }, {
          value: 2,
              label: '橡胶'
        }, {
          value: 3,
              label: '液态'
        }, {
          value: 4,
              label: '其他'
        }],
    }
  },
  computed: {
    dataRule () {
      return {

      }
    }
  },
  methods: {
    handleBeforeUpload(file) {
      if (file.raw.type !== 'image/jpeg' && file.raw.type !== 'image/png') {
        this.$message.error('只能上传jpg/png文件');
        return false;
      }
      if (file.size > 500 * 1024) {
        this.$message.error('文件大小不能超过500kb');
        return false;
      }
      if (this.fileList.length === 0) {
        this.fileList.push(file);
        return false;
      } else {
        this.showDialog = true;
        return false;
      }
    },
    handleSuccess(response, file) {
      console.log('上传成功', response, file);
    },
    handleError(error, file) {
      console.log('上传失败', error, file);
    },
    handleRemove(file, fileList) {
      console.log('移除文件', file, fileList);
    },
    handleBeforeClose(done) {
      if (this.description.trim() === '') {
        this.$message.error('请输入文字说明');
        done(false);
      } else {
        done();
      }
    },
    handleUpload() {
      // 执行上传操作，将 this.fileList 和 this.description 一并上传
      // 上传成功后关闭对话框，清空 this.fileList 和 this.description
      this.showDialog = false;
      this.fileList = [];
      this.description = '';
    },
    // 获取客户代码
    getCustomerCode(customerCode,cb){
      this.$http.get(`customer/customer/selectCode?code=`+customerCode).then(({data: res}) => {
        if (res.code !== 0) {
          return this.$message.error(res.msg)
        }
        const list = res.data.map((obj) => {
          return {
            customerCode: obj.code,
            value: obj.code + '(' + obj.code + ')',
            customerId : obj.id,
          }
        })
        this.timeout = setTimeout(() => {
          cb(list);
        }, 3000 * Math.random());
      }).catch(() => {
      })
    },
    //  输出选择日志
    customerSelect(item) {
      this.dataForm.customerCode = item.customerCode
      this.dataForm.customerId = item.customerId
    },
    // 获取配方代码
    getFormulationCode(formulationCode,cb){
      this.$http.get(`produce/formulation/selectFormulationCodeInfo?formulationCode=`+formulationCode).then(({data: res}) => {
        if (res.code !== 0) {
          return this.$message.error(res.msg)
        }
        const list = res.data.map((obj) => {
          return {
            formulationCode: obj.formulationCode,
            value: obj.formulationCode + '(' + obj.formulationCode + ')',
            formulaId : obj.id,
          }
        })
        this.timeout = setTimeout(() => {
          cb(list);
        }, 3000 * Math.random());
      }).catch(() => {
      })
    },
    //  输出选择日志
    formulationSelect(item) {
      this.dataForm.formulationCode = item.formulationCode
      this.dataForm.formulaId = item.formulaId
    },
    //  获取量产类型部品列表
    getPartList(queryString, cb) {
      this.$http.get(`/fabricate/part/getPartDesignation/` + queryString).then(({data: res}) => {
        if (res.code !== 0) {
          return this.$message.error(res.msg)
        }
        const list = res.data.map((obj) => {
          return {
            customerCode: obj.customerCode,
            value: obj.designation + '(' + obj.customerCode + ')',
            partId: obj.id,
            designation: obj.designation,
            customerId:obj.customerId
          }
        })
        this.timeout = setTimeout(() => {
          cb(list);
        }, 3000 * Math.random());
      }).catch(() => {
      })
    },
    //  输出选择日志
    partSelect(item) {
      console.log(item)
      this.dataForm.designation = item.designation
      this.dataForm.partId = item.partId
      this.dataForm.customerId = item.customerId
      this.dataForm.customerCode = item.customerCode
    },
    init () {
      this.visible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].resetFields()
        if (this.dataForm.id) {
          this.getInfo()
        }
      })
    },
    // 获取信息
    getInfo () {
      this.$http.get(`/vulcanization/basicsulfuraddition/${this.dataForm.id}`).then(({ data: res }) => {
        if (res.code !== 0) {
          return this.$message.error(res.msg)
        }
        this.dataForm = {
          ...this.dataForm,
          ...res.data
        }
      }).catch(() => {})
    },
    // 表单提交
    dataFormSubmitHandle: debounce(function () {
      this.$refs['dataForm'].validate((valid) => {
        if (!valid) {
          return false
        }
        this.$http[!this.dataForm.id ? 'post' : 'put']('/vulcanization/basicsulfuraddition/', this.dataForm).then(({ data: res }) => {
          if (res.code !== 0) {
            return this.$message.error(res.msg)
          }
          this.$message({
            message: this.$t('prompt.success'),
            type: 'success',
            duration: 500,
            onClose: () => {
              this.visible = false
              this.$emit('refreshDataList')
            }
          })
        }).catch(() => {})
      })
    }, 1000, { 'leading': true, 'trailing': false })
  }
}
</script>
<style scoped>
.container {
  display: flex; /* 设置父元素为flex容器 */
  flex-wrap: wrap; /* 允许子元素换行 */
  justify-content: space-between; /* 设置子元素在主轴上两端对齐 */
  align-items: center; /* 设置子元素在侧轴上居中对齐 */
  padding: 10px; /* 设置父元素的内边距 */
  background-color: lightblue; /* 设置父元素的背景色 */
}

.logo {
  width: 100px; /* 设置logo的宽度 */
  height: 50px; /* 设置logo的高度 */
}

.nav {
  display: flex; /* 设置导航栏为flex容器 */
  list-style: none; /* 去掉列表的圆点 */
}

.nav li {
  margin-left: 20px; /* 设置列表项的左边距 */
  border: 1px solid black; /* 给列表项添加边框 */
}

.input {
  flex: 1; /* 设置输入框的缩放比例为1，即平分剩余空间 */
  margin-left: 20px; /* 设置输入框的左边距 */
  border: 1px solid black; /* 给输入框添加边框 */
}

@media screen and (max-width: 600px) {
  /* 当屏幕宽度小于600px时，改变flex属性 */
  .container {
    flex-direction: column; /* 设置父元素为垂直排列 */
  }

  .nav {
    flex-direction: column; /* 设置导航栏为垂直排列 */
    align-items: center; /* 设置导航栏在主轴上居中对齐 */
  }

  .nav li {
    margin-top: 10px; /* 设置列表项的上边距 */
    margin-left: 0; /* 去掉列表项的左边距 */
    box-shadow: inset -1px -1px black, inset 1px 1px black; /* 给列表项添加内阴影来模拟边框 */
    border: none; /* 去掉列表项的边框 */
  }

  .input {
    width: 80%; /* 设置输入框的宽度为父元素的80% */
    margin-top: 10px; /* 设置输入框的上边距 */
    margin-left: 0; /* 去掉输入框的左边距 */
    box-shadow: inset -1px -1px black, inset 1px 1px black; /* 给输入框添加内阴影来模拟边框 */
    border: none; /* 去掉输入框的边框 */
  }
}
</style>
