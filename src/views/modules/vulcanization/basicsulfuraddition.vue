<template>
  <el-card shadow="never" class="aui-card--fill">
    <div class="mod-vulcanization__basicsulfuraddition}">
      <el-form :inline="true" :model="dataForm" @keyup.enter.native="getDataList()">
        <el-form-item>
          <el-input v-model="dataForm.id" placeholder="加硫作业书编号" clearable @clear="onClear">
            <el-button slot="append" icon="el-icon-search" @click="getDataList()"></el-button>
          </el-input>
        </el-form-item>
<!--        <el-form-item>
          <el-button @click="getDataList()">{{ $t('query') }}</el-button>
        </el-form-item>-->
<!--        <el-form-item>
          <el-button type="info" @click="exportHandle()">{{ $t('export') }}</el-button>
        </el-form-item>
        <el-form-item>
          <el-button v-if="$hasPermission('vulcanization:basicsulfuraddition:export')" type="info" @click="exportTemplateHandle()">
            {{ $t('exportTemplate') }}
          </el-button>
        </el-form-item>-->
<!--        <el-form-item>
          <el-tooltip content="下载模板填入信息后导入" placement="top">
            <el-upload
                v-if="$hasPermission('vulcanization:basicsulfuraddition:save')"
                class="upload-demo"
                :action="mixinViewModuleOptions.addBatchUrl"
                :headers="headers"
                :multiple="false"
                :show-file-list="false"
                :file-list="fileList"
                :before-upload="beforeUpload"
                :on-success="resultHandle"
                :on-change="handleChange"
                accept="'.xlsx','.xls'">
              <el-button type="success">{{ $t('addBatch') }}</el-button>
            </el-upload>
          </el-tooltip>
          <el-button type="text" v-show="unsuccessfulNameList" @click="dialogVisible = true">导入失败列表</el-button>
        </el-form-item>-->
        <el-form-item>
          <el-button v-if="$hasPermission('vulcanization:basicsulfuraddition:save')" type="primary" @click="addOrUpdateHandle()">{{ $t('add') }}</el-button>
        </el-form-item>
        <el-dropdown  style="float:right;">
          <el-button type="primary">
            更多功能<i class="el-icon-arrow-down el-icon--right"></i>
          </el-button>
          <el-dropdown-menu slot="dropdown">
            <el-dropdown-item>
              <el-form-item>
                <el-button v-if="$hasPermission('vulcanization:basicsulfuraddition:delete')" type="danger" @click="deleteHandle()">{{ $t('deleteBatch') }}</el-button>
              </el-form-item>
            </el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown>
      </el-form>
      <el-table v-loading="dataListLoading" :data="dataList" border @selection-change="dataListSelectionChangeHandle" style="width: 100%;">
        <el-table-column type="selection" header-align="center" align="center" width="50"></el-table-column>
        <el-table-column prop="customerCode" label="客户代码" header-align="center" align="center"></el-table-column>
        <el-table-column prop="designation" label="客户品名" header-align="center" align="center"></el-table-column>
        <el-table-column prop="partName" label="客户品名" header-align="center" align="center"></el-table-column>
<!--        <el-table-column prop="moldId" label="模具编号" header-align="center" align="center"></el-table-column>-->
        <el-table-column prop="standardBookId" label="加硫作业标准书编号" header-align="center" align="center"></el-table-column>
        <el-table-column prop="version" label="版本别" header-align="center" align="center"></el-table-column>
        <el-table-column prop="formulationCode" label="配方代码" header-align="center" align="center"></el-table-column>
        <el-table-column prop="material" label="材质" header-align="center" align="center"></el-table-column>
<!--        <el-table-column prop="formingMachine" label="成型机型(吨)" header-align="center" align="center"></el-table-column>-->
<!--        <el-table-column prop="machineId" label="机台编号" header-align="center" align="center"></el-table-column>-->
<!--        <el-table-column prop="moldHole" label="金型(X孔)" header-align="center" align="center"></el-table-column>
        <el-table-column prop="moldSurface" label="金型(X面)" header-align="center" align="center"></el-table-column>
        <el-table-column prop="moldDimensionsOne" label="金型寸法(X mm)" header-align="center" align="center"></el-table-column>
        <el-table-column prop="moldDimensionsTwo" label="金型寸法(X mm)" header-align="center" align="center"></el-table-column>
        <el-table-column prop="moldDimensionsThree" label="金型寸法(X mm)" header-align="center" align="center"></el-table-column>
        <el-table-column prop="materialDimensionsLong" label="材料寸法(长 mm)" header-align="center" align="center"></el-table-column>
        <el-table-column prop="moldDimensionsThreeLongMan" label="材料寸法(长士 mm)" header-align="center" align="center"></el-table-column>
        <el-table-column prop="materialDimensionsThickness" label="材料寸法(厚 mm)" header-align="center" align="center"></el-table-column>
        <el-table-column prop="materialDimensionsAtsushi" label="材料寸法(厚士 mm)" header-align="center" align="center"></el-table-column>
        <el-table-column prop="materialWeightOne" label="材料重量1" header-align="center" align="center"></el-table-column>
        <el-table-column prop="materialWeightTwo" label="材料重量2" header-align="center" align="center"></el-table-column>
        <el-table-column prop="finishedProductWeight" label="成品重量" header-align="center" align="center"></el-table-column>
        <el-table-column prop="materialShape" label="材料形状" header-align="center" align="center"></el-table-column>
        <el-table-column prop="moldTemperatureTop" label="金型温度(上)" header-align="center" align="center"></el-table-column>
        <el-table-column prop="moldTemperatureBelow" label="金型温度(下)" header-align="center" align="center"></el-table-column>
        <el-table-column prop="vulcanizationTimeOne" label="硫化时间1" header-align="center" align="center"></el-table-column>
        <el-table-column prop="vulcanizationTimeTwo" label="硫化时间1" header-align="center" align="center"></el-table-column>
        <el-table-column prop="rowTogether" label="共排" header-align="center" align="center"></el-table-column>
        <el-table-column prop="exhaust" label="排气" header-align="center" align="center"></el-table-column>
        <el-table-column prop="machinePressure" label="机器压力" header-align="center" align="center"></el-table-column>
        <el-table-column prop="moldCleaning" label="模具清洗" header-align="center" align="center"></el-table-column>
        <el-table-column prop="cleaningFrequency" label="清洗频率" header-align="center" align="center"></el-table-column>
        <el-table-column prop="releaseAgent" label="离型剂 0:有;1:无" :formatter="releaseAgentFormat" header-align="center" align="center"></el-table-column>
        <el-table-column prop="kind" label="种类" header-align="center" align="center"></el-table-column>
        <el-table-column prop="frequency" label="频率" header-align="center" align="center"></el-table-column>
        <el-table-column prop="plating" label="电镀 0:有;1:无" :formatter="platingFormat" header-align="center" align="center"></el-table-column>
        <el-table-column prop="precautions" label="注意事项" header-align="center" align="center"></el-table-column>
        <el-table-column prop="precautionsPictureOne" label="注意事项圖片1" header-align="center" align="center"></el-table-column>
        <el-table-column prop="precautionsPictureTow" label="注意事项圖片1" header-align="center" align="center"></el-table-column>
        <el-table-column prop="precautionsPictureThree" label="注意事项圖片1" header-align="center" align="center"></el-table-column>
        <el-table-column prop="semiFinishedProductNumberBoxes" label="半成品装箱数(周转箱)" header-align="center" align="center"></el-table-column>
        <el-table-column prop="takeEffect" label="生效(模/小时)" header-align="center" align="center"></el-table-column>
        <el-table-column prop="defectiveRate" label="不良率(%)" header-align="center" align="center"></el-table-column>-->
        <el-table-column prop="remark" label="备注" header-align="center" align="center"></el-table-column>
        <el-table-column :label="$t('handle')" fixed="right" header-align="center" align="center" width="150">
          <template slot-scope="scope">
            <el-button v-if="$hasPermission('vulcanization:basicsulfuraddition:update')" type="text" size="small" @click="addOrUpdateHandle(scope.row.id)">{{ $t('update') }}</el-button>
            <el-button v-if="$hasPermission('vulcanization:basicsulfuraddition:delete')" type="text" size="small" @click="deleteHandle(scope.row.id)">{{ $t('delete') }}</el-button>
          </template>
        </el-table-column>
      </el-table>
      <el-pagination
        :current-page="page"
        :page-sizes="[10, 20, 50, 100]"
        :page-size="limit"
        :total="total"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="pageSizeChangeHandle"
        @current-change="pageCurrentChangeHandle">
      </el-pagination>
      <!-- 弹窗, 新增 / 修改 -->
      <add-or-update v-if="addOrUpdateVisible" ref="addOrUpdate" @refreshDataList="getDataList"></add-or-update>
    </div>
  </el-card>
</template>

<script>
import mixinViewModule from '@/mixins/view-module'
import AddOrUpdate from './basicsulfuraddition-add-or-update'
import Cookies from "js-cookie";
export default {
  mixins: [mixinViewModule],
  data () {
    return {
      message:'',
      headers: {'token': Cookies.get('token')},
      fileList: [{name: '', url: ''}],
      unsuccessfulNameList: '',//  导入失败名称列表
      mixinViewModuleOptions: {
        getDataListURL: '/vulcanization/basicsulfuraddition/page',
        getDataListIsPage: true,
        exportURL: '/vulcanization/basicsulfuraddition/export',
        deleteURL: '/vulcanization/basicsulfuraddition',
        deleteIsBatch: true,
        exportTemplateURL: '/vulcanization/basicsulfuraddition/export/template',
        addBatchUrl: window.SITE_CONFIG['apiURL'] + '/vulcanization/basicsulfuraddition/batchSave',
      },
      dataForm: {
        id: ''
      }
    }
  },
  components: {
    AddOrUpdate
  },
  methods:{
    //清空后重新查询
    onClear(){
      this.query()
    },
    materialFormat(row, column) {
      switch (row.material) {
        case 1:
          return '硅胶'
        case 2:
          return '橡胶'
        case 3:
          return '液态'
        case 4:
          return "其他"
      }
    },
    platingFormat(row, column){
      switch (row.plating) {
        case 0:
          return "有"
        case 1:
          return "无"
      }
    },
    releaseAgentFormat(row, column){
      switch (row.releaseAgent) {
        case 0:
          return "有"
        case 1:
          return "无"
      }
    },
    beforeUpload(file) {
      this.message = this.$message({
        message: '上传中，请勿操作',
        center: true,
        duration: 0,
      });
    },
    handleChange(file, fileList) {
      this.fileList = fileList.slice(-3);

    },
    resultHandle(res) {
      if (res.code !== 0) {
        return this.$message.error(res.msg)
      }
      this.$message({
        message: this.$t('prompt.success'),
        type: 'success',
        duration: 500,
        onClose: () => {
          if (res.data) {
            this.unsuccessfulNameList = res.data
          }
          this.visible = false
          this.query()
        }
      })
    },
  }
}
</script>
