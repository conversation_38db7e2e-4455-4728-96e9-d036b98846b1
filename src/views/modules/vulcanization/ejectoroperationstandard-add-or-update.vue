<template>
  <el-dialog :visible.sync="visible" :title="!dataForm.id ? '新增射出作业书' : '修改射出作业书'" :close-on-click-modal="false" :close-on-press-escape="false" append-to-body>
    <el-form :model="dataForm" :rules="dataRule" ref="dataForm" @keyup.enter.native="dataFormSubmitHandle()" :label-width="$i18n.locale === 'en-US' ? '120px' : '80px'">
      <el-descriptions class="margin-top" :column="column" :content-style="content_style"  border>
        <el-descriptions-item label="部品名称">
          <el-input v-model="dataForm.partName" placeholder="部品名称"></el-input>
        </el-descriptions-item>
        <el-descriptions-item label="材质">
          <el-input v-model="dataForm.material" placeholder="材质"></el-input>
        </el-descriptions-item>
        <el-descriptions-item label="部品番号">
          <part-number-component v-model="dataForm.partId" placeholder="部品番号"></part-number-component>
        </el-descriptions-item>
        <el-descriptions-item label="配方编号">
          <el-input v-model="dataForm.formulationId" placeholder="配方编号"></el-input>
        </el-descriptions-item>
        <el-descriptions-item label="版本">
          <el-input v-model="dataForm.version" placeholder="版本"></el-input>
        </el-descriptions-item>
        <el-descriptions-item label="机台编号">
          <el-input v-model="dataForm.machineId" placeholder="机台编号"></el-input>
        </el-descriptions-item>
        <el-descriptions-item label="文件编号">
          <el-input v-model="dataForm.fileNo" placeholder="文件编号"></el-input>
        </el-descriptions-item>
        <el-descriptions-item label="模具尺寸">
          <el-input v-model="dataForm.dieSizeOne" placeholder="模具尺寸1" class="input_width_onethird"></el-input>
          X
          <el-input v-model="dataForm.dieSizeTwo" placeholder="模具尺寸2"  class="input_width_onethird"></el-input>
          X
          <el-input v-model="dataForm.dieSizeThree" placeholder="模具尺寸3"  class="input_width_onethird"></el-input>
        </el-descriptions-item>
        <el-descriptions-item label="成形机">
          <el-input v-model="dataForm.formingMachine" placeholder="成形机"></el-input>
        </el-descriptions-item>
        <el-descriptions-item label="模具">
          <el-input v-model="dataForm.moldNumber" placeholder="模具孔数" class="input_width_half"></el-input>
          [孔数]*
          <el-input v-model="dataForm.moldFace" placeholder="模具面" class="input_width_half"></el-input>
          [面]
        </el-descriptions-item>
        <el-descriptions-item label="材料尺寸宽" :span="2">
          宽
          <el-input v-model="dataForm.materialSizeWidth" placeholder="材料尺寸宽" class="input_width_half"></el-input>
          ±
          <el-input v-model="dataForm.materialSizeWidthSuitable" placeholder="材料尺寸宽±" class="input_width_half"></el-input><br/>
          厚
          <el-input v-model="dataForm.materialSizeThick" placeholder="材料尺寸厚" class="input_width_half"></el-input>
          ±
          <el-input v-model="dataForm.materialSizeThickSuitable" placeholder="材料尺寸厚±" class="input_width_half"></el-input>
        </el-descriptions-item>
        <el-descriptions-item></el-descriptions-item>
      </el-descriptions>
    </el-form>
    <template slot="footer">
      <el-button @click="visible = false">{{ $t('cancel') }}</el-button>
      <el-button type="primary" @click="dataFormSubmitHandle()">{{ $t('confirm') }}</el-button>
    </template>
  </el-dialog>
</template>

<script>
import debounce from 'lodash/debounce'
import mixinViewModule from '@/mixins/view-module'
export default {
  mixins:[mixinViewModule],
  data () {
    return {
      visible: false,
      //创建该页面时，不调用数据查询接口
      mixinViewModuleOptions: {
        createdIsNeed: false,
      },
      column: 2,
      windowWidth: window.innerWidth,
      content_style: {
        'min-width': '200px',
        'word-break': 'break-all'
      },
      label_style: {
        'color': '#000',
        'text-align': 'center',
        'font-weight': '600',
        'height': '40px',
        'background-color': '#d6dbe1',
        'min-width': '100px',
        'word-break': 'keep-all'
      },
      requiredValidate:['partId','formulationId'],
      dataForm: {
        id: '',
        partName: '',
        partId: '',
        version: '',
        material: '',
        formulationId: '',
        fileNo: '',
        machineId: '',
        dieSizeOne: '',
        dieSizeTwo: '',
        dieSizeThree: '',
        formingMachine: '',
        moldNumber: '',
        moldFace: '',
        materialSizeWidth: '',
        materialSizeWidthSuitable: '',
        materialSizeThick: '',
        materialSizeThickSuitable: '',
        shelfLife: '',
        assignmentStandardBookId: '',
        shapingSettingsLocation: '',
        shapingSettingsLocationSuitable: '',
        shapingSettingsPressure: '',
        shapingSettingsPressureSuitable: '',
        shapingSettingsSpeed: '',
        shapingSettingsSpeedSuitable: '',
        lockingSettingsLocation: '',
        lockingSettingsLocationSuitable: '',
        lockingSettingsPressure: '',
        lockingSettingsPressureSuitable: '',
        lockingSettingsSpeed: '',
        lockingSettingsSpeedSuitable: '',
        forwardShotLocation: '',
        forwardShotLocationSuitable: '',
        forwardShotPressure: '',
        forwardShotPressureSuitable: '',
        forwardShotSpeed: '',
        forwardShotSpeedSuitable: '',
        feedingSectionLocation: '',
        feedingSectionLocationSuitable: '',
        feedingSectionPressure: '',
        feedingSectionPressureSuitable: '',
        feedingSectionSpeed: '',
        feedingSectionSpeedSuitable: '',
        backShotLocation: '',
        backShotLocationSuitable: '',
        backShotPressure: '',
        backShotPressureSuitable: '',
        backShotSpeed: '',
        backShotSpeedSuitable: '',
        packingSectionLocation: '',
        packingSectionLocationSuitable: '',
        packingSectionPressure: '',
        packingSectionPressureSuitable: '',
        packingSectionSpeed: '',
        packingSectionSpeedSuitable: '',
        endOfEjectionLocation: '',
        endOfEjectionLocationSuitable: '',
        endOfEjectionPressure: '',
        endOfEjectionPressureSuitable: '',
        endOfEjectionSpeed: '',
        endOfEjectionSpeedSuitable: '',
        injectionSectionThreeLocation: '',
        injectionSectionThreeLocationSuitable: '',
        injectionSectionThreePressure: '',
        injectionSectionThreePressureSuitable: '',
        injectionSectionThreeSpeed: '',
        injectionSectionThreeSpeedSuitable: '',
        injectionSectionTwoLocation: '',
        injectionSectionTwoLocationSuitable: '',
        injectionSectionTwoPressure: '',
        injectionSectionTwoPressureSuitable: '',
        injectionSectionTwoSpeed: '',
        injectionSectionTwoSpeedSuitable: '',
        injectionSectionOneLocation: '',
        injectionSectionOneLocationSuitable: '',
        injectionSectionOnePressure: '',
        injectionSectionOnePressureSuitable: '',
        injectionSectionOneSpeed: '',
        injectionSectionOneSpeedSuitable: '',
        modulusSettingValue: '',
        modulusSettingValueSuitable: '',
        modulusActualValue: '',
        modulusActualValueSuitable: '',
        upperMoldHeatingBothSidesValue: '',
        upperMoldHeatingBothSidesValueSuitable: '',
        upperMoldHeatingBothSidesActualValue: '',
        upperMoldHeatingBothSidesActualValueSuitable: '',
        lowerMiddleHeatingSetValue: '',
        lowerMiddleHeatingSetValueSuitable: '',
        lowerMiddleHeatingActualValue: '',
        lowerMiddleHeatingActualValueSuitable: '',
        moldTemperatureSetValue: '',
        moldTemperatureSetValueSuitable: '',
        moldTemperatureActualValue: '',
        moldTemperatureActualValueSuitable: '',
        feedTubeSetValue: '',
        feedTubeSetValueSuitable: '',
        feedTubeActualValue: '',
        feedTubeActualValueSuitable: '',
        injectionTubeSetValue: '',
        injectionTubeSetValueSuitable: '',
        injectionTubeActualValue: '',
        injectionTubeActualValueSuitable: '',
        upAndDownSetValue: '',
        upAndDownSetValueSuitable: '',
        upAndDownActualValue: '',
        upAndDownActualValueSuitable: '',
        dropPositioningSetValue: '',
        dropPositioningSetValueSuitable: '',
        dropPositioningActualValue: '',
        dropPositioningActualValueSuitable: '',
        vulcanizationTime: '',
        vulcanizationTimeSuitable: '',
        injectionDelayTime: '',
        injectionDelayTimeSuitable: '',
        compressTime: '',
        compressTimeSuitable: '',
        feedDelayTime: '',
        feedDelayTimeSuitable: '',
        returnTime: '',
        returnTimeSuitable: '',
        exhaustTimes: '',
        exhaustTimesSuitable: '',
        firstStopOnExhaust: '',
        firstStopOnExhaustSuitable: '',
        stopAtTheFirstExhaust: '',
        stopAtTheFirstExhaustSuitable: '',
        firstExhaustDistance: '',
        firstExhaustDistanceSuitable: '',
        firstExhaustTimes: '',
        firstExhaustTimesSuitable: '',
        secondExhaustStop: '',
        secondExhaustStopSuitable: '',
        secondStop: '',
        secondStopSuitable: '',
        secondExhaustDistance: '',
        secondExhaustDistanceSuitable: '',
        theSecondExhaustTimes: '',
        theSecondExhaustTimesSuitable: '',
        exhaustWaitingTime: '',
        exhaustWaitingTimeSuitable: '',
        exhaustRisePressure: '',
        exhaustRisePressureSuitable: '',
        exhaustRisingSpeed: '',
        exhaustRisingSpeedSuitable: '',
        exhaustDropPressure: '',
        exhaustDropPressureSuitable: '',
        exhaustDescendingSpeed: '',
        exhaustDescendingSpeedSuitable: '',
        moldCleaning: '',
        cleaningFrequency: '',
        cleaningFrequencySuitable: '',
        precautions: '',
        picture: '',
        holeNumberDistribution: '',
        defectiveRateTarget: '',
        productivityTarget: '',
        semiFinishedProductNumber: '',
        approve: '',
        confirm: '',
        create: '',
        remark: '',
        disabled: '',
        creator: '',
        createDate: '',
        updater: '',
        updateDate: ''
      }
    }
  },
  computed: {
    dataRule () {
      return {
          id: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          partName: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          partId: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          version: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          material: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          formulationId: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          fileNo: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          machineId: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          dieSizeOne: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          dieSizeTwo: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          dieSizeThree: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          formingMachine: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          moldNumber: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          moldFace: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          materialSizeWidth: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          materialSizeWidthSuitable: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          materialSizeThick: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          materialSizeThickSuitable: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          shelfLife: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          assignmentStandardBookId: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          shapingSettingsLocation: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          shapingSettingsLocationSuitable: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          shapingSettingsPressure: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          shapingSettingsPressureSuitable: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          shapingSettingsSpeed: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          shapingSettingsSpeedSuitable: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          lockingSettingsLocation: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          lockingSettingsLocationSuitable: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          lockingSettingsPressure: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          lockingSettingsPressureSuitable: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          lockingSettingsSpeed: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          lockingSettingsSpeedSuitable: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          forwardShotLocation: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          forwardShotLocationSuitable: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          forwardShotPressure: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          forwardShotPressureSuitable: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          forwardShotSpeed: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          forwardShotSpeedSuitable: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          feedingSectionLocation: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          feedingSectionLocationSuitable: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          feedingSectionPressure: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          feedingSectionPressureSuitable: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          feedingSectionSpeed: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          feedingSectionSpeedSuitable: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          backShotLocation: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          backShotLocationSuitable: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          backShotPressure: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          backShotPressureSuitable: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          backShotSpeed: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          backShotSpeedSuitable: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          packingSectionLocation: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          packingSectionLocationSuitable: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          packingSectionPressure: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          packingSectionPressureSuitable: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          packingSectionSpeed: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          packingSectionSpeedSuitable: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          endOfEjectionLocation: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          endOfEjectionLocationSuitable: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          endOfEjectionPressure: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          endOfEjectionPressureSuitable: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          endOfEjectionSpeed: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          endOfEjectionSpeedSuitable: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          injectionSectionThreeLocation: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          injectionSectionThreeLocationSuitable: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          injectionSectionThreePressure: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          injectionSectionThreePressureSuitable: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          injectionSectionThreeSpeed: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          injectionSectionThreeSpeedSuitable: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          injectionSectionTwoLocation: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          injectionSectionTwoLocationSuitable: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          injectionSectionTwoPressure: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          injectionSectionTwoPressureSuitable: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          injectionSectionTwoSpeed: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          injectionSectionTwoSpeedSuitable: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          injectionSectionOneLocation: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          injectionSectionOneLocationSuitable: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          injectionSectionOnePressure: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          injectionSectionOnePressureSuitable: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          injectionSectionOneSpeed: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          injectionSectionOneSpeedSuitable: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          modulusSettingValue: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          modulusSettingValueSuitable: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          modulusActualValue: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          modulusActualValueSuitable: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          upperMoldHeatingBothSidesValue: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          upperMoldHeatingBothSidesValueSuitable: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          upperMoldHeatingBothSidesActualValue: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          upperMoldHeatingBothSidesActualValueSuitable: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          lowerMiddleHeatingSetValue: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          lowerMiddleHeatingSetValueSuitable: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          lowerMiddleHeatingActualValue: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          lowerMiddleHeatingActualValueSuitable: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          moldTemperatureSetValue: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          moldTemperatureSetValueSuitable: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          moldTemperatureActualValue: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          moldTemperatureActualValueSuitable: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          feedTubeSetValue: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          feedTubeSetValueSuitable: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          feedTubeActualValue: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          feedTubeActualValueSuitable: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          injectionTubeSetValue: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          injectionTubeSetValueSuitable: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          injectionTubeActualValue: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          injectionTubeActualValueSuitable: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          upAndDownSetValue: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          upAndDownSetValueSuitable: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          upAndDownActualValue: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          upAndDownActualValueSuitable: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          dropPositioningSetValue: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          dropPositioningSetValueSuitable: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          dropPositioningActualValue: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          dropPositioningActualValueSuitable: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          vulcanizationTime: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          vulcanizationTimeSuitable: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          injectionDelayTime: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          injectionDelayTimeSuitable: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          compressTime: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          compressTimeSuitable: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          feedDelayTime: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          feedDelayTimeSuitable: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          returnTime: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          returnTimeSuitable: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          exhaustTimes: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          exhaustTimesSuitable: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          firstStopOnExhaust: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          firstStopOnExhaustSuitable: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          stopAtTheFirstExhaust: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          stopAtTheFirstExhaustSuitable: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          firstExhaustDistance: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          firstExhaustDistanceSuitable: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          firstExhaustTimes: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          firstExhaustTimesSuitable: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          secondExhaustStop: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          secondExhaustStopSuitable: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          secondStop: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          secondStopSuitable: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          secondExhaustDistance: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          secondExhaustDistanceSuitable: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          theSecondExhaustTimes: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          theSecondExhaustTimesSuitable: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          exhaustWaitingTime: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          exhaustWaitingTimeSuitable: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          exhaustRisePressure: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          exhaustRisePressureSuitable: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          exhaustRisingSpeed: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          exhaustRisingSpeedSuitable: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          exhaustDropPressure: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          exhaustDropPressureSuitable: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          exhaustDescendingSpeed: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          exhaustDescendingSpeedSuitable: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          moldCleaning: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          cleaningFrequency: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          cleaningFrequencySuitable: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          precautions: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          picture: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          holeNumberDistribution: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          defectiveRateTarget: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          productivityTarget: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          semiFinishedProductNumber: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          approve: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          confirm: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          create: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          remark: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
                }
    }
  },
  methods: {
    init () {
      this.visible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].resetFields()
        if (this.dataForm.id) {
          this.getInfo()
        }
      })
    },
    // 获取信息
    getInfo () {
      this.$http.get(`/vulcanization/ejectoroperationstandard/${this.dataForm.id}`).then(({ data: res }) => {
        if (res.code !== 0) {
          return this.$message.error(res.msg)
        }
        this.dataForm = {
          ...this.dataForm,
          ...res.data
        }
      }).catch(() => {})
    },
    // 表单提交
    dataFormSubmitHandle: debounce(function () {
      this.$refs['dataForm'].validate((valid) => {
        if (!valid) {
          return false
        }
        let message = '';
        if(this.dataForm.id){
          message = '此操作将修改该数据'
        }else {
          message = '此操作将新增一条数据'
        }
        this.$confirm(message+', 是否继续?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
          center: true
        }).then(() => {
          this.$http[!this.dataForm.id ? 'post' : 'put']('/vulcanization/ejectoroperationstandard/', this.dataForm).then(({ data: res }) => {
            if (res.code !== 0) {
            return this.$message.error(res.msg)
            }
            this.$message({
          message: this.$t('prompt.success'),
            type: 'success',
                    duration: 500,
                    onClose: () => {
              this.visible = false
              this.$emit('refreshDataList')
            }
          })
          }).catch(() => {})
        }).catch(() => {
          this.$message({
            type: 'info',
            message: '已取消'
          });
        });
      })
    }, 1000, { 'leading': true, 'trailing': false })
  }
}
</script>

<style scoped>
.input_width_onethird{
  width: 20%;
}
.input_width_half{
  width: 40%;
}
.required {
  color: red;
}
</style>
