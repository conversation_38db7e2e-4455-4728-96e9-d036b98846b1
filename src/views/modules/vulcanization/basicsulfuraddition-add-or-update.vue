<template>
  <el-dialog :visible.sync="visible" :title="!dataForm.id ? '新增加硫作业标准书' : '修改加硫作业标准书'"
             :close-on-click-modal="false" :inline="true" :close-on-press-escape="false" append-to-body>
    <el-form :model="dataForm" ref="dataForm" @keyup.enter.native="dataFormSubmitHandle()"
             :label-width="$i18n.locale === 'en-US' ? '120px' : '80px'">
      <el-descriptions class="margin-top" :column="column" :content-style="content_style" :label-style="label_style" border>
        <el-descriptions-item label="部品名称">
          <el-input v-model="dataForm.partName" placeholder="部品名称"></el-input>
        </el-descriptions-item>
        <el-descriptions-item label="材质">
          <el-input v-model="dataForm.material" placeholder="材质"></el-input>
        </el-descriptions-item>
        <el-descriptions-item>
          <template slot="label">
            <span class="required">*</span>
            部品番号
          </template>
          <part-number-component v-model="dataForm.partId" placeholder="部品番号"></part-number-component>
        </el-descriptions-item>
        <el-descriptions-item label="配方编号" prop="formulationId">
          <template slot="label">
            <span class="required">*</span>
            配方编号
          </template>
          <formulation-component v-model="dataForm.formulationId" placeholder="配方编号"></formulation-component>
        </el-descriptions-item>
        <el-descriptions-item label="版本">
          <el-input v-model="dataForm.version" placeholder="版本"></el-input>
        </el-descriptions-item>
        <el-descriptions-item label="文件编号">
          <el-input v-model="dataForm.standardBookId" placeholder="加硫作业标准书编号"></el-input>
        </el-descriptions-item>
        <el-descriptions-item label="成型机型(吨)">
          <el-input v-model="dataForm.formingMachine" placeholder="成型机型(吨)"></el-input>
        </el-descriptions-item>
        <el-descriptions-item label="机台编号">
          <machine-component v-model="dataForm.machineList" :multiple="true" placeholder="机台编号"></machine-component>
        </el-descriptions-item>
        <el-descriptions-item label="模具">
          <el-input v-model="dataForm.moldHole" placeholder="金型(X孔)" class="input_width_half"></el-input>
          [孔] *
          <el-input v-model="dataForm.moldSurface" placeholder="金型(X面)" class="input_width_half"></el-input>
          [模]
        </el-descriptions-item>
        <el-descriptions-item label="模具尺寸">
          <el-input v-model="dataForm.moldDimensionsOne" placeholder="金型寸法(X mm)" class="input_width_onethird"></el-input>
          X
          <el-input v-model="dataForm.moldDimensionsTwo" placeholder="金型寸法(X mm)" class="input_width_onethird"></el-input>
          X
          <el-input v-model="dataForm.moldDimensionsThree" placeholder="金型寸法(X mm)" class="input_width_onethird"></el-input>
          <span class="font_size">mm</span>
        </el-descriptions-item>
        <el-descriptions-item label="材料尺寸">
          <el-input v-model="dataForm.materialDimensionsLong" placeholder="材料寸法(长 mm)" class="input_width_half"></el-input>
          ±
          <el-input v-model="dataForm.moldDimensionsThreeLongMan" placeholder="材料寸法(长士 mm)" class="input_width_half"></el-input>
          <span class="font_size">mm</span><br/>
          <el-input v-model="dataForm.materialDimensionsThickness" placeholder="材料寸法(厚 mm)" class="input_width_half"></el-input>
          ±
          <el-input v-model="dataForm.materialDimensionsAtsushi" placeholder="材料寸法(厚士 mm)" class="input_width_half"></el-input>
          <span class="font_size">mm</span>
        </el-descriptions-item>
        <el-descriptions-item label="材料形状">
          <el-input v-model="dataForm.materialShape" placeholder="材料形状"></el-input>
        </el-descriptions-item>
        <el-descriptions-item label="材料重量">
          <el-input v-model="dataForm.materialWeightOne" placeholder="材料重量1" class="input_width_half"></el-input>
          -
          <el-input v-model="dataForm.materialWeightTwo" placeholder="材料重量2" class="input_width_half"></el-input>
          <span class="font_size">g/条</span>
        </el-descriptions-item>
        <el-descriptions-item label="共排">
          <el-input v-model="dataForm.rowTogether" placeholder="共排" class="input_width_half"></el-input>
          <span class="font_size">条</span>
        </el-descriptions-item>
        <el-descriptions-item label="模具实际温度">
          上
          <el-input v-model="dataForm.moldActualTemperatureSuperiorOne" placeholder="模具实际温度上1(℃)" class="input_width_half"></el-input>
          <el-input v-model="dataForm.moldActualTemperatureSuperiorTwo" placeholder="模具实际温度下2(℃)" class="input_width_half"></el-input>
          <span class="font_size">℃</span><br/>
          /
          <el-input v-model="dataForm.moldActualTemperaturePreviewOne" placeholder="模具实际温度预览1(℃)" class="input_width_half"></el-input>
          <el-input v-model="dataForm.moldActualTemperaturePreviewTwo" placeholder="模具实际温度预览2(℃)" class="input_width_half"></el-input>
          <span class="font_size">℃</span><br/>
          下
          <el-input v-model="dataForm.moldActualTemperatureDownOne" placeholder="模具实际温度上1(℃)" class="input_width_half"></el-input>
          <el-input v-model="dataForm.moldActualTemperatureDownTwo" placeholder="模具实际温度下2(℃)" class="input_width_half"></el-input>
          <span class="font_size">℃</span>
        </el-descriptions-item>
        <el-descriptions-item label="排气">
          <el-input v-model="dataForm.exhaust" placeholder="排气" class="input_width_half"></el-input>
          <span class="font_size">次</span>
        </el-descriptions-item>
        <el-descriptions-item label="成形压力">
          <el-input v-model="dataForm.formingPressureHead" class="input_width_half"></el-input>
          -
          <el-input v-model="dataForm.formingPressureEnd" class="input_width_half"></el-input>
          kgf/cm2
        </el-descriptions-item>
        <el-descriptions-item label="抽真空压力">
          <el-input v-model="dataForm.vacuumPressure" class="input_width_half"></el-input>
          cmHg
        </el-descriptions-item>
        <el-descriptions-item label="加热治具温度">
          设定
          <el-input v-model="dataForm.heatingFixtureTemperatureOne" class="input_width_half"></el-input>
          ±
          <el-input v-model="dataForm.heatingFixtureTemperatureTwo" class="input_width_half"></el-input>
          ℃
        </el-descriptions-item>
        <el-descriptions-item label="模具清洗">
          <el-input v-model="dataForm.moldCleaning" placeholder="模具清洗"></el-input>
        </el-descriptions-item>
        <el-descriptions-item label="清洗频率">
          <el-input v-model="dataForm.cleaningFrequency" placeholder="清洗频率"></el-input>
        </el-descriptions-item>
        <el-descriptions-item label="硫化时间">
          <el-input v-model="dataForm.vulcanizationTimeOne" placeholder="硫化时间1" class="input_width_half"></el-input>
          <el-input v-model="dataForm.vulcanizationTimeTwo" placeholder="硫化时间1" class="input_width_half"></el-input>
          秒
        </el-descriptions-item>
        <el-descriptions-item label="离型剂">
          <el-input v-model="dataForm.releaseAgent" placeholder="离型剂"></el-input>
        </el-descriptions-item>
        <el-descriptions-item label="冷模超时报警">
          <el-input v-model="dataForm.coldMoldTimeoutAlarmHead" placeholder="冷模超时报警1(秒)" class="input_width_half"></el-input>
          /
          <el-input v-model="dataForm.coldMoldTimeoutAlarmEnd" placeholder="冷模超时报警2(秒)" class="input_width_half"></el-input>
        </el-descriptions-item>
        <el-descriptions-item label="离型剂使用频率">
          <el-input v-model="dataForm.frequency" placeholder="频率"></el-input>
        </el-descriptions-item>
        <el-descriptions-item label="半成品装箱数">
          <el-input v-model="dataForm.semiFinishedProductNumberBoxes" placeholder="半成品装箱数(周转箱)"></el-input>
        </el-descriptions-item>
        <el-descriptions-item label="节拍时间">
          <el-input v-model="dataForm.taktTime" placeholder="节拍时间(秒)"></el-input>
        </el-descriptions-item>
        <el-descriptions-item label="产效目标">
          <el-input v-model="dataForm.takeEffect" placeholder="产效目标(模/小时)"></el-input>
        </el-descriptions-item>
        <el-descriptions-item label="不良率">
          <el-input v-model="dataForm.defectiveRate" placeholder="不良率(%)"></el-input>
        </el-descriptions-item>
        <el-descriptions-item label="单品重量">
          <el-input v-model="dataForm.singleProductWeight" placeholder="单品重量"></el-input>
        </el-descriptions-item>
        <el-descriptions-item label="注意事项" :span="2">
          <el-input
              type="textarea"
              :autosize="{ minRows: 4, maxRows: 6}"
              placeholder="请输入内容"
              v-model="dataForm.precautions">
          </el-input>
        </el-descriptions-item>
      </el-descriptions>
    </el-form>
    <template slot="footer">
      <el-button @click="visible = false">{{ $t('cancel') }}</el-button>
      <el-button type="primary" @click="dataFormSubmitHandle()">{{ $t('confirm') }}</el-button>
    </template>
  </el-dialog>
</template>

<script>
import debounce from 'lodash/debounce'
import uploadBase64 from "./upload-base64.vue";

export default {
  data() {
    return {
      uploadUrl: 'your-upload-url',
      fileList: [],
      showDialog: false,
      description: '',
      dialogImageUrl: '',
      dialogVisible: false,
      column: 2,
      visible: false,
      windowWidth: window.innerWidth,
      content_style: {
        'min-width': '200px',
        'word-break': 'break-all'
      },
      label_style: {
        'color': '#000',
        'text-align': 'center',
        'font-weight': '600',
        'height': '40px',
        'background-color': '#d6dbe1',
        'min-width': '100px',
        'word-break': 'keep-all'
      },
      requiredValidate:['partId','formulationId'],
      dataForm: {
        id: '',
        partId: '',
        partName: '',
        moldId: '',
        standardBookId: '',
        version: '',
        formulationId: '',
        material: '',
        formingMachine: '',
        machineList: '',
        singleProductWeight: '',
        taktTime: '',
        releaseAgentUsageFrequency: '',
        vacuumPressure: '',
        formingPressureEnd: '',
        formingPressureHead: '',
        coldMoldTimeoutAlarmEnd: '',
        coldMoldTimeoutAlarmHead: '',
        heatingFixtureTemperatureTwo: '',
        heatingFixtureTemperatureOne: '',
        moldActualTemperatureDownTwo: '',
        moldActualTemperatureDownOne: '',
        moldActualTemperaturePreviewTwo: '',
        moldActualTemperaturePreviewOne: '',
        moldActualTemperatureSuperiorTwo: '',
        moldActualTemperatureSuperiorOne: '',
        moldHole: '',
        moldSurface: '',
        moldDimensionsOne: '',
        moldDimensionsTwo: '',
        moldDimensionsThree: '',
        materialDimensionsLong: '',
        moldDimensionsThreeLongMan: '',
        materialDimensionsThickness: '',
        materialDimensionsAtsushi: '',
        materialWeightOne: '',
        materialWeightTwo: '',
        finishedProductWeight: '',
        materialShape: '',
        moldTemperatureTop: '',
        moldTemperatureBelow: '',
        vulcanizationTimeOne: '',
        vulcanizationTimeTwo: '',
        rowTogether: '',
        exhaust: '',
        machinePressure: '',
        moldCleaning: '',
        cleaningFrequency: '',
        releaseAgent: '',
        kind: '',
        frequency: '',
        plating: '',
        precautions: '',
        precautionsPictureOne: '',
        precautionsPictureTow: '',
        precautionsPictureThree: '',
        semiFinishedProductNumberBoxes: '',
        takeEffect: '',
        defectiveRate: '',
        remark: '',
        disabled: '',
        creator: '',
        createDate: '',
        updater: '',
        updateDate: ''
      },
    }
  },
  computed: {
    dataRule() {
      return {
        partId: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        formulationId: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
      }
    }
  },
  components:{
    uploadBase64
  },
  mounted() {
    window.addEventListener('resize', this.handleResize)
  },
  beforeDestroy() {
    window.removeEventListener('resize', this.handleResize)
  },
  methods: {
    handleResize() {
      this.windowWidth = window.innerWidth
      console.log(this.windowWidth)
      if (this.windowWidth < 1500){
        this.column = 1
      }else {
        this.column = 2
      }
      // 在这里更新组件状态或执行其他的逻辑
    },
    handleBeforeUpload(file) {
      if (file.raw.type !== 'image/jpeg' && file.raw.type !== 'image/png') {
        this.$message.error('只能上传jpg/png文件');
        return false;
      }
      if (file.size > 500 * 1024) {
        this.$message.error('文件大小不能超过500kb');
        return false;
      }
      if (this.fileList.length === 0) {
        this.fileList.push(file);
        return false;
      } else {
        this.showDialog = true;
        return false;
      }
    },
    handleSuccess(response, file) {
      console.log('上传成功', response, file);
    },
    handleError(error, file) {
      console.log('上传失败', error, file);
    },
    handleRemove(file, fileList) {
      console.log('移除文件', file, fileList);
    },
    handleBeforeClose(done) {
      if (this.description.trim() === '') {
        this.$message.error('请输入文字说明');
        done(false);
      } else {
        done();
      }
    },
    handleUpload() {
      // 执行上传操作，将 this.fileList 和 this.description 一并上传
      // 上传成功后关闭对话框，清空 this.fileList 和 this.description
      this.showDialog = false;
      this.fileList = [];
      this.description = '';
    },
    init() {
      this.visible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].resetFields()
        if (this.dataForm.id) {
          this.getInfo()
        }
      })
    },
    // 获取信息
    getInfo() {
      this.$http.get(`/vulcanization/basicsulfuraddition/${this.dataForm.id}`).then(({data: res}) => {
        if (res.code !== 0) {
          return this.$message.error(res.msg)
        }
        this.dataForm = {
          ...this.dataForm,
          ...res.data
        }
      }).catch(() => {
      })
    },
    // 表单提交
    dataFormSubmitHandle: debounce(function () {
      let vailDate = true
      for (const item of this.requiredValidate) {
        if(!this.dataForm[item]){
          vailDate = false
        }
      }
      if(vailDate){
        let message = '';
        if (this.dataForm.id) {
          message = '此操作将修改该数据'
        } else {
          message = '此操作将新增一条数据'
        }
        this.$confirm(message + ', 是否继续?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
          center: true
        }).then(() => {
          this.$http[!this.dataForm.id ? 'post' : 'put']('/vulcanization/basicsulfuraddition/', this.dataForm).then(({data: res}) => {
            if (res.code !== 0) {
              return this.$message.error(res.msg)
            }
            this.$message({
              message: this.$t('prompt.success'),
              type: 'success',
              duration: 500,
              onClose: () => {
                this.visible = false
                this.$emit('refreshDataList')
              }
            })
          }).catch(() => {
          })
        }).catch(() => {
          this.$message({
            type: 'info',
            message: '已取消'
          });
        });
      }else {
        this.$message({
          type: 'error',
          message: '您有必填项未填写'
        });
      }
    }, 1000, {'leading': true, 'trailing': false})
  }
}
</script>
<style scoped>
.container {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  align-items: center;
}
.input_width_half{
  width: 40%;
}
.required {
  color: red;
}
@media screen and (max-width: 767px) {
  .input_width_half {
    display: block; /* Change container display to block */
  }
  .box {
    width: 100%;
  }
}
.input_width_onethird{
  width: 20%;
}
@media screen and (max-width: 767px) {
  .input_width_onethird {
    display: block; /* Change container display to block */
  }
  .box {
    width: 100%;
  }
}

.box {
  width: calc(50% - 10px);
  margin: 5px;
}

@media screen and (max-width: 767px) {
  .container {
    display: block; /* Change container display to block */
  }

  .box {
    width: 100%;
  }
}

.div_border {
  border: #333333 1px solid;
}
</style>
