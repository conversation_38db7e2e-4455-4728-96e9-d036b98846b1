<template>
  <el-dialog :visible.sync="visible" :title="!dataForm.id ? $t('add') : $t('update')" :close-on-click-modal="false" :close-on-press-escape="false" append-to-body>
    <el-form :model="dataForm" :rules="dataRule" ref="dataForm" @keyup.enter.native="dataFormSubmitHandle()" :label-width="$i18n.locale === 'en-US' ? '120px' : '80px'">
      <div class="container">
                                                <el-form-item label="交件人id" prop="deliver">
              <el-input v-model="dataForm.deliver" placeholder="交件人id"></el-input>
            </el-form-item>
                                        <el-form-item label="交件人姓名" prop="deliverName">
              <el-input v-model="dataForm.deliverName" placeholder="交件人姓名"></el-input>
            </el-form-item>
                                        <el-form-item label="交件数量" prop="deliveryNumber">
              <el-input v-model="dataForm.deliveryNumber" placeholder="交件数量"></el-input>
            </el-form-item>
                                        <el-form-item label="交件时间" prop="deliveryTime">
              <el-input v-model="dataForm.deliveryTime" placeholder="交件时间"></el-input>
            </el-form-item>
                                        <el-form-item label="交件批号id" prop="deliverySubBatchId">
              <el-input v-model="dataForm.deliverySubBatchId" placeholder="交件批号id"></el-input>
            </el-form-item>
                                        <el-form-item label="交件批号" prop="subBatchNumber">
              <el-input v-model="dataForm.subBatchNumber" placeholder="交件批号"></el-input>
            </el-form-item>
                                        <el-form-item label="品番id" prop="partId">
              <el-input v-model="dataForm.partId" placeholder="品番id"></el-input>
            </el-form-item>
                                        <el-form-item label="品号" prop="designation">
              <el-input v-model="dataForm.designation" placeholder="品号"></el-input>
            </el-form-item>
                                        <el-form-item label="退回数量" prop="returnQuantity">
              <el-input v-model="dataForm.returnQuantity" placeholder="退回数量"></el-input>
            </el-form-item>
                                        <el-form-item label="不良数" prop="defectQuantity">
              <el-input v-model="dataForm.defectQuantity" placeholder="不良数"></el-input>
            </el-form-item>
                                        <el-form-item label="破损" prop="rupture">
              <el-input v-model="dataForm.rupture" placeholder="破损"></el-input>
            </el-form-item>
                                        <el-form-item label="死料" prop="deathPenalty">
              <el-input v-model="dataForm.deathPenalty" placeholder="死料"></el-input>
            </el-form-item>
                                        <el-form-item label="缺料" prop="lackOfMaterial">
              <el-input v-model="dataForm.lackOfMaterial" placeholder="缺料"></el-input>
            </el-form-item>
                                        <el-form-item label="变形" prop="deformation">
              <el-input v-model="dataForm.deformation" placeholder="变形"></el-input>
            </el-form-item>
                                        <el-form-item label="剪伤" prop="cut">
              <el-input v-model="dataForm.cut" placeholder="剪伤"></el-input>
            </el-form-item>
                                        <el-form-item label="其他" prop="other">
              <el-input v-model="dataForm.other" placeholder="其他"></el-input>
            </el-form-item>
                                        <el-form-item label="是否异常 0:是 1:否" prop="exceptionStatus">
              <el-input v-model="dataForm.exceptionStatus" placeholder="是否异常 0:是 1:否"></el-input>
            </el-form-item>
                                                                                                                  </div>
    </el-form>
    <template slot="footer">
      <el-button @click="visible = false">{{ $t('cancel') }}</el-button>
      <el-button type="primary" @click="dataFormSubmitHandle()">{{ $t('confirm') }}</el-button>
    </template>
  </el-dialog>
</template>

<script>
import debounce from 'lodash/debounce'
import mixinViewModule from '@/mixins/view-module'
export default {
  mixins:[mixinViewModule],
  data () {
    return {
      visible: false,
      //创建该页面时，不调用数据查询接口
      mixinViewModuleOptions: {
        createdIsNeed: false,
      },
      dataForm: {
        id: '',
        deliver: '',
        deliverName: '',
        deliveryNumber: '',
        deliveryTime: '',
        deliverySubBatchId: '',
        subBatchNumber: '',
        partId: '',
        designation: '',
        returnQuantity: '',
        defectQuantity: '',
        rupture: '',
        deathPenalty: '',
        lackOfMaterial: '',
        deformation: '',
        cut: '',
        other: '',
        exceptionStatus: '',
        disabled: '',
        creator: '',
        createDate: '',
        updater: '',
        updateDate: ''
      }
    }
  },
  computed: {
    dataRule () {
      return {
          id: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          deliver: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          deliverName: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          deliveryNumber: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          deliveryTime: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          deliverySubBatchId: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          subBatchNumber: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          partId: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          designation: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          returnQuantity: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          defectQuantity: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          rupture: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          deathPenalty: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          lackOfMaterial: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          deformation: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          cut: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          other: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          exceptionStatus: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
                }
    }
  },
  methods: {
    init () {
      this.visible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].resetFields()
        if (this.dataForm.id) {
          this.getInfo()
        }
      })
    },
    // 获取信息
    getInfo () {
      this.$http.get(`/repair/repairdeliveryrecord/${this.dataForm.id}`).then(({ data: res }) => {
        if (res.code !== 0) {
          return this.$message.error(res.msg)
        }
        this.dataForm = {
          ...this.dataForm,
          ...res.data
        }
      }).catch(() => {})
    },
    // 表单提交
    dataFormSubmitHandle: debounce(function () {
      this.$refs['dataForm'].validate((valid) => {
        if (!valid) {
          return false
        }
        let message = '';
        if(this.dataForm.id){
          message = '此操作将修改该数据'
        }else {
          message = '此操作将新增一条数据'
        }
        this.$confirm(message+', 是否继续?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
          center: true
        }).then(() => {
          this.$http[!this.dataForm.id ? 'post' : 'put']('/repair/repairdeliveryrecord/', this.dataForm).then(({ data: res }) => {
            if (res.code !== 0) {
            return this.$message.error(res.msg)
            }
            this.$message({
          message: this.$t('prompt.success'),
            type: 'success',
                    duration: 500,
                    onClose: () => {
              this.visible = false
              this.$emit('refreshDataList')
            }
          })
          }).catch(() => {})
        }).catch(() => {
          this.$message({
            type: 'info',
            message: '已取消'
          });
        });
      })
    }, 1000, { 'leading': true, 'trailing': false })
  }
}
</script>
