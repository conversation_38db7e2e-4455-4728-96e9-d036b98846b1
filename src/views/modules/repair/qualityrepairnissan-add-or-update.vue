<template>
  <el-dialog :visible.sync="visible" :title="!dataForm.id ? '新增品修记录' : '修改品修记录'" :close-on-click-modal="false" :close-on-press-escape="false">
    <el-form :model="dataForm" :rules="dataRule" ref="dataForm" @keyup.enter.native="dataFormSubmitHandle()" :label-width="$i18n.locale === 'en-US' ? '120px' : '80px'">
         <div class="container">
           <div class="item">
          <el-form-item label="品修日期" prop="repairDate">
            <el-date-picker
                class="datePicker"
                v-model="dataForm.repairDate"
                type="date"
                value-format="yyyy-MM-dd"
                placeholder="选择日期">
            </el-date-picker>
          </el-form-item>
           </div>
           <div class="item">
          <el-form-item label="次批号" prop="subBatchNumber">
            <el-autocomplete
                class="inline-input"
                v-model="dataForm.subBatchNumber"
                :fetch-suggestions="querySecondaryBatch"
                placement="bottom"
                placeholder="次批号"
                :trigger-on-focus="false"
                popper-class="my-popper"
                clearable
                @select="subBatchSelect">
            </el-autocomplete>
          </el-form-item>
           </div>
           <div class="item">
          <el-form-item label="品修员" prop="qualityRepairman">
            <employee-component v-model="dataForm.qualityRepairman" placeholder="品修员(工号)"></employee-component>
          </el-form-item>
           </div>
         </div>
      <div class="container">
        <div class="item">
          <el-form-item label="修毛边" prop="trimmingEdge">
            <el-input v-model="dataForm.trimmingEdge" placeholder="修毛边"></el-input>
          </el-form-item>
        </div>
        <div class="item">
          <el-form-item label="品修良品" prop="goodQualityRepair">
            <el-input v-model="goodQualityRepair" placeholder="品修良品"></el-input>
          </el-form-item>
        </div>
        <div class="item">
          <el-form-item label="不良数" prop="adverseNumber">
            <el-input v-model="adverseNumber" placeholder="不良数"></el-input>
          </el-form-item>
        </div>
        <div class="item">
          <el-form-item label="额外绩效">
            <el-input v-model="dataForm.extraPerformancePoint"></el-input>
          </el-form-item>
        </div>
      </div>
      <div class="container">
        <div class="four">
          <el-form-item label="破裂" prop="rupture">
            <el-input v-model="dataForm.rupture" placeholder="破裂"></el-input>
          </el-form-item>
        </div>
        <div class="four">
          <el-form-item label="包风" prop="bagWind">
            <el-input v-model="dataForm.bagWind" placeholder="包风"></el-input>
          </el-form-item>
        </div>
        <div class="four">
          <el-form-item label="压痕" prop="indentation">
            <el-input v-model="dataForm.indentation" placeholder="压痕"></el-input>
          </el-form-item>
        </div>
        <div class="four">
          <el-form-item label="死料" prop="deathPenalty">
            <el-input v-model="dataForm.deathPenalty" placeholder="死料"></el-input>
          </el-form-item>
        </div>
      </div>

      <div class="container">
        <div class="four">
          <el-form-item label="缺料" prop="lackOfMaterial">
            <el-input v-model="dataForm.lackOfMaterial" placeholder="缺料"></el-input>
          </el-form-item>
        </div>
        <div class="four">
          <el-form-item label="拉伤" prop="strain">
            <el-input v-model="dataForm.strain" placeholder="拉伤"></el-input>
          </el-form-item>
        </div>
        <div class="four">
          <el-form-item label="压毛边" prop="burr">
            <el-input v-model="dataForm.burr" placeholder="压毛边"></el-input>
          </el-form-item>
        </div>
        <div class="four">
          <el-form-item label="起泡" prop="bubbling">
            <el-input v-model="dataForm.bubbling" placeholder="起泡"></el-input>
          </el-form-item>
        </div>
        <div class="four">
          <el-form-item label="剪伤" prop="cut">
            <el-input v-model="dataForm.cut" placeholder="剪伤"></el-input>
          </el-form-item>
        </div>
        <div class="four">
          <el-form-item label="其他" prop="other">
            <el-input v-model="dataForm.other" placeholder="其他"></el-input>
          </el-form-item>
        </div>
      </div>
      <el-form-item label="备注" prop="remark">
        <el-input type="textarea" :rows="3" v-model="dataForm.remark" placeholder="备注"></el-input>
      </el-form-item>
      <el-button type="text" @click="showAll = !showAll">
        {{ showAll ? '收起' : '展开更多' }}
      </el-button>
      <div v-show="showAll">
        <div class="container">
            <el-form-item label="灌点凹" prop="concavePouringPoint">
              <el-input v-model="dataForm.concavePouringPoint" placeholder="灌点凹"></el-input>
            </el-form-item>
            <el-form-item label="料污" prop="materialPollution">
              <el-input v-model="dataForm.materialPollution" placeholder="料污"></el-input>
            </el-form-item>
            <el-form-item label="变形" prop="deformation">
              <el-input v-model="dataForm.deformation" placeholder="变形"></el-input>
            </el-form-item>
            <el-form-item label="不粘胶" prop="notStickyGlue">
              <el-input v-model="dataForm.notStickyGlue" placeholder="不粘胶"></el-input>
            </el-form-item>
            <el-form-item label="露铁" prop="exposedIron">
              <el-input v-model="dataForm.exposedIron" placeholder="露铁"></el-input>
            </el-form-item>
            <el-form-item label="毛边" prop="burrs">
              <el-input v-model="dataForm.burrs" placeholder="毛边"></el-input>
            </el-form-item>
            <el-form-item label="颗粒" prop="particles">
              <el-input v-model="dataForm.particles" placeholder="颗粒"></el-input>
            </el-form-item>
            <el-form-item label="断柱子" prop="brokenColumn">
              <el-input v-model="dataForm.brokenColumn" placeholder="断柱子"></el-input>
            </el-form-item>
            <el-form-item label="不熟" prop="undercooked">
              <el-input v-model="dataForm.undercooked" placeholder="不熟"></el-input>
            </el-form-item>
            <el-form-item label="爆边" prop="burstEdge">
              <el-input v-model="dataForm.burstEdge" placeholder="爆边"></el-input>
            </el-form-item>
            <el-form-item label="孔堵" prop="holePlug">
              <el-input v-model="dataForm.holePlug" placeholder="孔堵"></el-input>
            </el-form-item>
            <el-form-item label="模伤" prop="moldDamage">
              <el-input v-model="dataForm.moldDamage" placeholder="模伤"></el-input>
            </el-form-item>
            <el-form-item label="灌点破" prop="brokenPouringPoint">
              <el-input v-model="dataForm.brokenPouringPoint" placeholder="灌点破"></el-input>
            </el-form-item>
            <el-form-item label="五彩" prop="colorful">
              <el-input v-model="dataForm.colorful" placeholder="五彩"></el-input>
            </el-form-item>
            <el-form-item label="针孔" prop="needleHole">
              <el-input v-model="dataForm.needleHole" placeholder="针孔"></el-input>
            </el-form-item>
            <el-form-item label="脱胶" prop="degumming">
              <el-input v-model="dataForm.degumming" placeholder="脱胶"></el-input>
            </el-form-item>
            <el-form-item label="异品" prop="differentProducts">
              <el-input v-model="dataForm.differentProducts" placeholder="异品"></el-input>
            </el-form-item>
            <el-form-item label="异物" prop="foreignMatter">
              <el-input v-model="dataForm.foreignMatter" placeholder="异物"></el-input>
            </el-form-item>
            <el-form-item label="黑点" prop="blackSpot">
              <el-input v-model="dataForm.blackSpot" placeholder="黑点"></el-input>
            </el-form-item>
            <el-form-item label="分模线粗" prop="coarsePartingLine">
              <el-input v-model="dataForm.coarsePartingLine" placeholder="分模线粗"></el-input>
            </el-form-item>
            <el-form-item label="发亮" prop="shiny">
              <el-input v-model="dataForm.shiny" placeholder="发亮"></el-input>
            </el-form-item>
            <el-form-item label="铁件不良" prop="ironPartsDefects">
              <el-input v-model="dataForm.ironPartsDefects" placeholder="铁件不良"></el-input>
            </el-form-item>
            <el-form-item label="胶歪" prop="glueCrooked">
              <el-input v-model="dataForm.glueCrooked" placeholder="胶歪"></el-input>
            </el-form-item>
            <el-form-item label="灌点大" prop="largePouringPoint">
              <el-input v-model="dataForm.largePouringPoint" placeholder="灌点大"></el-input>
            </el-form-item>
          <el-form-item label="折痕" prop="crease">
            <el-input v-model="dataForm.crease" placeholder="折痕"></el-input>
          </el-form-item>
          <el-form-item label="穿孔" prop="perforation">
            <el-input v-model="dataForm.perforation" placeholder="穿孔"></el-input>
          </el-form-item>
          <el-form-item label="偏模" prop="deviantMode">
            <el-input v-model="dataForm.deviantMode" placeholder="偏模"></el-input>
          </el-form-item>
          <el-form-item label="柱裂" prop="columnarFissure">
            <el-input v-model="dataForm.columnarFissure" placeholder="柱裂"></el-input>
          </el-form-item>
          <el-form-item label="柱歪" prop="columnCrooked">
            <el-input v-model="dataForm.columnCrooked" placeholder="柱歪"></el-input>
          </el-form-item>
          <el-form-item label="粘胶" prop="viscose">
            <el-input v-model="dataForm.viscose" placeholder="粘胶"></el-input>
          </el-form-item>
          <el-form-item label="灌点不良" prop="poorFillingPoint">
            <el-input v-model="dataForm.poorFillingPoint" placeholder="灌点不良"></el-input>
          </el-form-item>
          <el-form-item label="夹破" prop="pinching">
            <el-input v-model="dataForm.pinching" placeholder="夹破"></el-input>
          </el-form-item>
          <el-form-item label="孔破" prop="holeBroken">
            <el-input v-model="dataForm.holeBroken" placeholder="孔破"></el-input>
          </el-form-item>
          <el-form-item label="生胶" prop="rawGum">
            <el-input v-model="dataForm.rawGum" placeholder="生胶"></el-input>
          </el-form-item>
          <el-form-item label="粘毛边" prop="stickyEdge">
            <el-input v-model="dataForm.stickyEdge" placeholder="粘毛边"></el-input>
          </el-form-item>
          <el-form-item label="端子接合开裂" prop="terminalJointCracking">
            <el-input v-model="dataForm.terminalJointCracking" placeholder="端子接合开裂"></el-input>
          </el-form-item>
          <el-form-item label="剥线尺寸" prop="strippingSize">
            <el-input v-model="dataForm.strippingSize" placeholder="剥线尺寸"></el-input>
          </el-form-item>
          <el-form-item label="接合部位" prop="jointArea">
            <el-input v-model="dataForm.jointArea" placeholder="接合部位"></el-input>
          </el-form-item>
          <el-form-item label="端子破裂" prop="terminalBreakage">
            <el-input v-model="dataForm.terminalBreakage" placeholder="端子破裂"></el-input>
          </el-form-item>
          <el-form-item label="起皮" prop="peeling">
            <el-input v-model="dataForm.peeling" placeholder="起皮"></el-input>
          </el-form-item>
          <el-form-item label="脱破" prop="detachment">
            <el-input v-model="dataForm.detachment" placeholder="脱破"></el-input>
          </el-form-item>
          <el-form-item label="缺铁" prop="ironDeficiency">
            <el-input v-model="dataForm.ironDeficiency" placeholder="缺铁"></el-input>
          </el-form-item>
          <el-form-item label="夹伤" prop="pinchInjury">
            <el-input v-model="dataForm.pinchInjury" placeholder="夹伤"></el-input>
          </el-form-item>
        </div>
      </div>
    </el-form>
    <template slot="footer">
      <el-button @click="visible = false">{{ $t('cancel') }}</el-button>
      <el-button type="primary" @click="dataFormSubmitHandle()">{{ $t('confirm') }}</el-button>
    </template>
  </el-dialog>
</template>

<script>
import debounce from 'lodash/debounce'
import mixinViewModule from '@/mixins/view-module'
import {getDictLabel} from '@/utils'
export default {
  mixins:[mixinViewModule],
 data () {
    return {
      visible: false,
      //创建该页面时，不调用数据查询接口
      mixinViewModuleOptions: {
        createdIsNeed: false,
      },
      showAll: false,
      dataForm: {
        id: '',
        subBatchId: '',
        subBatchNumber: this.subBatchNumber,
        qualityRepairman: '',
        outgoingName: '',
        startTime:'',
        endTime:'',
        trimmingEdge: 0,
        goodQualityRepair: 0,
        adverseNumber: 0,
        repairDate: this.formatDates(new Date()),
        rupture: 0,
        bagWind: 0,
        indentation: 0,
        deathPenalty: 0,
        lackOfMaterial: 0,
        strain: 0,
        burr: 0,
        bubbling: 0,
        impurities: 0,
        cut: 0,
        concavePouringPoint: 0,
        materialPollution: 0,
        deformation: 0,
        exposedIron: 0,
        burrs: 0,
        particles: 0,
        brokenColumn: 0,
        undercooked: 0,
        extraPerformancePoint:0,
        burstEdge: 0,
        holePlug: 0,
        moldDamage:0 ,
        brokenPouringPoint:0 ,
        colorful: 0,
        needleHole: 0,
        degumming: 0,
        differentProducts: 0,
        foreignMatter: 0,
        blackSpot: 0,
        other:0,
        coarsePartingLine: 0,
        shiny: 0,
        ironPartsDefects: 0,
        glueCrooked: 0,
        largePouringPoint: 0,
        notStickyGlue: 0,
        crease: 0,
        perforation: 0,
        deviantMode: 0,
        columnarFissure: 0,
        columnCrooked: 0,
        viscose: 0,
        poorFillingPoint: 0,
        pinching: 0,
        holeBroken: 0,
        rawGum: 0,
        stickyEdge: 0,
        terminalJointCracking: 0,
        strippingSize: 0,
        jointArea: 0,
        terminalBreakage: 0,
        peeling: 0,
        detachment: 0,
        ironDeficiency: 0,
        pinchInjury: 0,
        remark: '',
        disabled: '',
        creator: '',
        createDate: '',
        updater: '',
        updateDate: ''
      }
    }
  },
  computed: {
    dataRule () {
      return {
          id: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        /*  subBatchId: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],*/
          subBatchNumber: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
      }
    },
    goodQualityRepair(){
      this.dataForm.goodQualityRepair = this.dataForm.trimmingEdge - this.dataForm.adverseNumber
      return this.dataForm.goodQualityRepair
    }
  },
  methods: {
    subBatchSelect(item){
      this.dataForm.subBatchId = item.subBatchId
    },
    querySecondaryBatch(batchNumber,cb){
      this.$http.get(`/batch/batch/querySubBatchNumberList/` + batchNumber).then(({data: res}) => {
        if (res.code !== 0) {
          return this.$message.error(res.msg)
        }
        const list = res.data.map((obj) => {
          return {
            value: getDictLabel("size_category", obj.sizeCategory) + obj.batchNumber + (obj.subBatchNumber != null ? ('-'+obj.subBatchNumber) : '未建立次批'),
            subBatchNumber:getDictLabel("size_category", obj.sizeCategory) + obj.batchNumber + (obj.subBatchNumber != null ? ('-'+obj.subBatchNumber) : ''),
            subBatchId: obj.subBatchId,
          }
        })
        this.timeout = setTimeout(() => {
          cb(list);
        }, 300 * Math.random());
      })
    },
    init () {
      this.visible = true
      this.showAll = false
      this.$nextTick(() => {
        this.$refs['dataForm'].resetFields()
        if (this.dataForm.id) {
          this.getInfo()
        }
      })
    },
    // 获取信息
    getInfo () {
      this.$http.get(`/repair/qualityrepairnissan/${this.dataForm.id}`).then(({ data: res }) => {
        if (res.code !== 0) {
          return this.$message.error(res.msg)
        }
        this.dataForm = {
          ...this.dataForm,
          ...res.data
        }
      }).catch(() => {})
    },
    // 表单提交
    dataFormSubmitHandle: debounce(function () {
      this.$refs['dataForm'].validate((valid) => {
        if (!valid) {
          return false
        }
        let message = '';
        if(this.dataForm.id){
          message = '此操作将修改该数据'
        }else {
          message = '此操作将新增一条数据'
        }
        this.$confirm(message+', 是否继续?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
          center: true
        }).then(() => {
          this.$http[!this.dataForm.id ? 'post' : 'put']('/repair/qualityrepairnissan/', this.dataForm).then(({ data: res }) => {
          if (res.code !== 0) {
            return this.$message.error(res.msg)
          }
          this.$message({
            message: this.$t('prompt.success'),
            type: 'success',
            duration: 500,
            onClose: () => {
              this.visible = false
              this.$emit('refreshDataList')
            }
          })
        }).catch(() => {})
      }).catch(() => {
          this.$message({
            type: 'info',
            message: '已取消'
          });
        });
      })
    }, 1000, { 'leading': true, 'trailing': false })
  }
}
</script>

<style scoped>
form { background-color: #def5be; }
</style>
