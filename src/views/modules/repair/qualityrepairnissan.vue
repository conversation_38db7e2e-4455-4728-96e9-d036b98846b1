<template>
  <el-card shadow="never" class="aui-card--fill">
    <div class="mod-repair__qualityrepairnissan}">
      <el-form :inline="true" :model="dataForm" @keyup.enter.native="getDataList()">
        <el-form-item>
          <el-tooltip content="请输入查询关键字" placement="top">
            <el-input v-model="dataForm.paramStr" placeholder="查询关键字" @input="throttleFunction" clearable>
              <el-button slot="append" icon="el-icon-search" @click="getDataList()"></el-button>
            </el-input>
          </el-tooltip>
        </el-form-item>
        <el-form-item>
          <el-button v-if="$hasPermission('repair:qualityrepairnissan:save')" type="primary" @click="addOrUpdateHandle()">{{ $t('add') }}</el-button>
        </el-form-item>
        <el-dropdown  style="float:right;">
          <el-button type="primary">
            更多功能<i class="el-icon-arrow-down el-icon--right"></i>
          </el-button>
          <el-dropdown-menu slot="dropdown">
            <el-dropdown-item>
              <el-form-item>
                <el-button type="info" @click="exportHandle()">{{ $t('export') }}</el-button>
              </el-form-item>
            </el-dropdown-item>
            <el-dropdown-item>
              <el-form-item>
                <el-button v-if="$hasPermission('repair:qualityrepairnissan:export')" type="info" @click="exportTemplateHandle()">
                  {{ $t('exportTemplate') }}
                </el-button>
              </el-form-item>
            </el-dropdown-item>
            <el-dropdown-item>
              <el-form-item>
                <el-tooltip content="下载模板填入信息后导入" placement="top">
                  <el-upload
                      v-if="$hasPermission('repair:qualityrepairnissan:save')"
                      class="upload-demo"
                      :action="mixinViewModuleOptions.addBatchUrl"
                      :headers="headers"
                      :multiple="false"
                      :show-file-list="false"
                      :file-list="fileList"
                      :before-upload="beforeUpload"
                      :on-success="resultHandle"
                      :on-change="handleChange"
                      accept="'.xlsx','.xls'">
                    <el-button type="success">{{ $t('addBatch') }}</el-button>
                  </el-upload>
                </el-tooltip>
                <el-button type="text" v-show="unsuccessfulNameList" @click="dialogVisible = true">导入失败列表</el-button>
              </el-form-item>
            </el-dropdown-item>
            <el-dropdown-item>
              <el-form-item>
                <el-button v-if="$hasPermission('repair:qualityrepairnissan:delete')" type="danger" @click="deleteHandle()">{{ $t('deleteBatch') }}</el-button>
              </el-form-item>
            </el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown>
      </el-form>

      <el-table v-loading="dataListLoading" :data="dataList" height="700" border @selection-change="dataListSelectionChangeHandle" style="width: 100%;">
        <el-table-column type="selection" header-align="center" align="center" width="50"></el-table-column>
        <el-table-column prop="repairDate" label="品修日期" header-align="center" align="center" width="110"></el-table-column>
        <el-table-column prop="subBatchNumber" label="次批号" header-align="center" align="center" width="130"></el-table-column>
        <el-table-column prop="qualityRepairman" label="品修员" header-align="center" align="center" width="135">
          <template slot-scope="scope">
            <div>
              {{$getEmployeesList(scope.row.qualityRepairman)}}
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="trimmingEdge" label="修毛边" header-align="center" align="center"></el-table-column>
        <el-table-column prop="goodQualityRepair" label="品修良品" header-align="center" align="center"></el-table-column>
        <el-table-column  label="不良数" header-align="center" align="center">
          <template slot-scope="scope">
            <div @click="openExDialog(scope.row)">{{scope.row.adverseNumber}}</div>
          </template>
        </el-table-column>
        <el-table-column prop="yield" label="品修良率" header-align="center" align="center">
          <template slot-scope="scope">
            {{(scope.row.yield * 100).toFixed(2)}}%
          </template>
        </el-table-column>
        <el-table-column prop="performancePoint" label="绩效点" header-align="center" align="center"></el-table-column>
<!--        <el-table-column prop="startTime" label="开始时间" header-align="center" align="center"></el-table-column>-->
<!--        <el-table-column prop="endTime" label="结束时间" header-align="center" align="center"></el-table-column>-->
        <el-table-column :label="$t('handle')" fixed="right" header-align="center" align="center" width="150">
          <template slot-scope="scope">
            <el-dropdown>
              <el-button type="primary">
                功能<i class="el-icon-arrow-down el-icon--right"></i>
              </el-button>
              <el-dropdown-menu slot="dropdown">
                <el-dropdown-item>
                  <el-button v-if="$hasPermission('repair:qualityrepairnissan:update')" type="text" size="small" @click="addOrUpdateHandle(scope.row.id)">{{ $t('update') }}</el-button>
                </el-dropdown-item>
                <el-dropdown-item>
                  <el-button v-if="$hasPermission('repair:qualityrepairnissan:delete')" type="text" size="small" @click="deleteHandle(scope.row.id)">{{ $t('delete') }}</el-button>
                </el-dropdown-item>
              </el-dropdown-menu>
            </el-dropdown>
          </template>
        </el-table-column>
      </el-table>
      <el-pagination
        :current-page="page"
        :page-sizes="[10, 20, 50, 100]"
        :page-size="limit"
        :total="total"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="pageSizeChangeHandle"
        @current-change="pageCurrentChangeHandle">
      </el-pagination>
      <!-- 弹窗, 新增 / 修改 -->
      <add-or-update v-if="addOrUpdateVisible" ref="addOrUpdate" @refreshDataList="getDataList"></add-or-update>
    </div>
    <div>
      <el-dialog title="不良品详情" :visible.sync="dialogVisible" width="35%">
        <el-descriptions class="margin-top"  :column="3" border>
          <el-descriptions-item v-if="exData.rupture > 0" label="破裂">{{exData.rupture}}</el-descriptions-item>
          <el-descriptions-item v-if="exData.bagWind > 0" label="包风">{{exData.bagWind}}</el-descriptions-item>
          <el-descriptions-item v-if="exData.indentation > 0 " label="压痕">{{exData.indentation}}</el-descriptions-item>
          <el-descriptions-item v-if="exData.deathPenalty > 0" label="死料">{{ exData.deathPenalty }}</el-descriptions-item>
          <el-descriptions-item v-if="exData.lackOfMaterial > 0" label="缺料">{{ exData.lackOfMaterial }}</el-descriptions-item>
          <el-descriptions-item v-if="exData.strain > 0" label="拉伤">{{ exData.strain }}</el-descriptions-item>
          <el-descriptions-item v-if="exData.burr > 0" label="压毛边">{{ exData.burr }}</el-descriptions-item>
          <el-descriptions-item v-if="exData.bubbling > 0" label="起泡">{{ exData.bubbling }}</el-descriptions-item>
          <el-descriptions-item v-if="exData.impurities > 0" label="杂质">{{ exData.impurities }}</el-descriptions-item>
          <el-descriptions-item v-if="exData.cut > 0" label="剪伤">{{ exData.cut }}</el-descriptions-item>
          <el-descriptions-item v-if="exData.concavePouringPoint > 0" label="灌点凹">{{ exData.concavePouringPoint }}</el-descriptions-item>
          <el-descriptions-item v-if="exData.materialPollution > 0" label="料污">{{ exData.materialPollution }}</el-descriptions-item>
          <el-descriptions-item v-if="exData.deformation > 0" label="变形">{{ exData.deformation }}</el-descriptions-item>
          <el-descriptions-item v-if="exData.exposedIron > 0" label="露铁">{{ exData.exposedIron }}</el-descriptions-item>
          <el-descriptions-item v-if="exData.burrs > 0" label="毛边">{{ exData.burrs }}</el-descriptions-item>
          <el-descriptions-item v-if="exData.particles > 0" label="颗粒">{{ exData.particles }}</el-descriptions-item>
          <el-descriptions-item v-if="exData.brokenColumn > 0" label="断柱子">{{ exData.brokenColumn }}</el-descriptions-item>
          <el-descriptions-item v-if="exData.undercooked > 0" label="不熟">{{ exData.undercooked }}</el-descriptions-item>
          <el-descriptions-item v-if="exData.burstEdge > 0" label="爆边">{{ exData.burstEdge }}</el-descriptions-item>
          <el-descriptions-item v-if="exData.holePlug > 0" label="孔堵">{{ exData.holePlug }}</el-descriptions-item>
          <el-descriptions-item v-if="exData.moldDamage > 0" label="模伤">{{ exData.moldDamage }}</el-descriptions-item>
          <el-descriptions-item v-if="exData.brokenPouringPoint > 0" label="灌点破">{{ exData.brokenPouringPoint }}</el-descriptions-item>
          <el-descriptions-item v-if="exData.colorful > 0" label="五彩">{{ exData.colorful }}</el-descriptions-item>
          <el-descriptions-item v-if="exData.needleHole > 0" label="针孔">{{ exData.needleHole }}</el-descriptions-item>
          <el-descriptions-item v-if="exData.degumming > 0" label="脱胶">{{ exData.degumming }}</el-descriptions-item>
          <el-descriptions-item v-if="exData.differentProducts > 0" label="异品">{{ exData.differentProducts }}</el-descriptions-item>
          <el-descriptions-item v-if="exData.foreignMatter > 0" label="异物">{{ exData.foreignMatter }}</el-descriptions-item>
          <el-descriptions-item v-if="exData.blackSpot > 0" label="黑点">{{ exData.blackSpot }}</el-descriptions-item>
          <el-descriptions-item v-if="exData.coarsePartingLine > 0" label="分模线粗">{{ exData.coarsePartingLine }}</el-descriptions-item>
          <el-descriptions-item v-if="exData.shiny > 0" label="发亮">{{ exData.shiny }}</el-descriptions-item>
          <el-descriptions-item v-if="exData.ironPartsDefects > 0" label="铁件不良">{{ exData.ironPartsDefects }}</el-descriptions-item>
          <el-descriptions-item v-if="exData.glueCrooked > 0" label="胶歪">{{ exData.glueCrooked }}</el-descriptions-item>
          <el-descriptions-item v-if="exData.largePouringPoint > 0" label="灌点大">{{ exData.largePouringPoint }}</el-descriptions-item>
          <el-descriptions-item v-if="exData.notStickyGlue > 0" label="不粘胶">{{ exData.notStickyGlue }}</el-descriptions-item>
          <el-descriptions-item v-if="exData.crease > 0" label="折痕">{{ exData.crease }}</el-descriptions-item>
          <el-descriptions-item v-if="exData.perforation > 0" label="穿孔">{{ exData.perforation }}</el-descriptions-item>
          <el-descriptions-item v-if="exData.deviantMode > 0" label="偏模">{{ exData.deviantMode }}</el-descriptions-item>
          <el-descriptions-item v-if="exData.columnarFissure > 0" label="柱裂">{{ exData.columnarFissure }}</el-descriptions-item>
          <el-descriptions-item v-if="exData.columnCrooked > 0" label="柱歪">{{ exData.columnCrooked }}</el-descriptions-item>
          <el-descriptions-item v-if="exData.viscose > 0" label="粘胶">{{ exData.viscose }}</el-descriptions-item>
          <el-descriptions-item v-if="exData.poorFillingPoint > 0" label="灌点不良">{{ exData.poorFillingPoint }}</el-descriptions-item>
          <el-descriptions-item v-if="exData.pinching > 0" label="夹破">{{ exData.pinching }}</el-descriptions-item>
          <el-descriptions-item v-if="exData.holeBroken > 0" label="孔破">{{ exData.holeBroken }}</el-descriptions-item>
          <el-descriptions-item v-if="exData.rawGum > 0" label="生胶">{{ exData.rawGum }}</el-descriptions-item>
          <el-descriptions-item v-if="exData.stickyEdge > 0" label="粘毛边">{{ exData.stickyEdge }}</el-descriptions-item>
          <el-descriptions-item v-if="exData.terminalJointCracking > 0" label="端子接合开裂">{{ exData.terminalJointCracking }}</el-descriptions-item>
          <el-descriptions-item v-if="exData.strippingSize > 0" label="剥线尺寸">{{ exData.strippingSize }}</el-descriptions-item>
          <el-descriptions-item v-if="exData.jointArea > 0" label="接合部位">{{ exData.jointArea }}</el-descriptions-item>
          <el-descriptions-item v-if="exData.terminalBreakage > 0" label="端子破裂">{{ exData.terminalBreakage }}</el-descriptions-item>
          <el-descriptions-item v-if="exData.peeling > 0" label="起皮">{{ exData.peeling }}</el-descriptions-item>
          <el-descriptions-item v-if="exData.detachment > 0" label="脱破">{{ exData.detachment }}</el-descriptions-item>
          <el-descriptions-item v-if="exData.ironDeficiency > 0" label="缺铁">{{ exData.ironDeficiency }}</el-descriptions-item>
          <el-descriptions-item v-if="exData.pinchInjury > 0" label="夹伤">{{ exData.pinchInjury }}</el-descriptions-item>
        </el-descriptions>
      </el-dialog>
    </div>
  </el-card>
</template>

<script>
import mixinViewModule from '@/mixins/view-module'
import AddOrUpdate from './qualityrepairnissan-add-or-update'
import Cookies from "js-cookie";
export default {
  mixins: [mixinViewModule],
  data () {
    return {
      message:'',
      headers: {'token': Cookies.get('token')},
      fileList: [{name: '', url: ''}],
      unsuccessfulNameList: '',//  导入失败名称列表
      mixinViewModuleOptions: {
        getDataListURL: '/repair/qualityrepairnissan/page',
        getDataListIsPage: true,
        exportURL: '/repair/qualityrepairnissan/export',
        deleteURL: '/repair/qualityrepairnissan',
        deleteIsBatch: true,
        exportTemplateURL: '/repair/qualityrepairnissan/export/template',
        addBatchUrl: window.SITE_CONFIG['apiURL'] + '/repair/qualityrepairnissan/batchSave',
      },
      exData:{
        rupture:0,
        ironDeficiency: 0,
        pinchInjury: 0,
        indentation: 0,
        deathPenalty: 0,
        lackOfMaterial: 0,
        strain: 0,
        burr: 0,
        bubbling: 0,
        impurities: 0,
        cut: 0,
        concavePouringPoint: 0,
        materialPollution: 0,
        deformation: 0,
        exposedIron: 0,
        burrs: 0,
        particles: 0,
        brokenColumn: 0,
        undercooked: 0,
        burstEdge: 0,
        holePlug: 0,
        moldDamage: 0,
        brokenPouringPoint: 0,
        colorful: 0,
        needleHole: 0,
        degumming: 0,
        differentProducts: 0,
        foreignMatter: 0,
        blackSpot: 0,
        coarsePartingLine: 0,
        shiny: 0,
        ironPartsDefects: 0,
        glueCrooked: 0,
        largePouringPoint: 0,
        notStickyGlue: 0,
        crease: 0,
        perforation: 0,
        deviantMode: 0,
        columnarFissure: 0,
        columnCrooked: 0,
        viscose: 0,
        poorFillingPoint: 0,
        pinching: 0,
        holeBroken: 0,
        rawGum: 0,
        stickyEdge: 0,
        terminalJointCracking: 0,
        strippingSize: 0,
        jointArea: 0,
        terminalBreakage: 0,
        peeling: 0,
        detachment: 0,
        bagWind: 0,
      },
      dataForm: {
        id: '',
        paramStr: '',
      },
      // 判断是否还在继续输入
      timer: null,
      dialogVisible:false
    }
  },
  components: {
    AddOrUpdate
  },
  methods:{
    //节流函数
    throttleFunction() {
      // 如果timer不为空就表明在持续输入,每次改变都清除定时，这样就可以实现只输入最后一次结果(注意每次间隔如果超过500毫秒就会输出)
      if (this.timer !== null) clearTimeout(this.timer)
      this.timer = setTimeout(() => {
        this.getDataList()
      }, 500)
    },
    beforeUpload(file) {
      this.message = this.$message({
        message: '上传中，请勿操作',
        center: true,
        duration: 0,
      });
    },
    handleChange(file, fileList) {
      this.fileList = fileList.slice(-3);

    },
    openExDialog(data){
      this.exData = data;
      this.dialogVisible = true;
    },
    resultHandle(res) {
      if (res.code !== 0) {
      return this.$message.error(res.msg)
      }
      this.$message({
        message: this.$t('prompt.success'),
        type: 'success',
        duration: 500,
        onClose: () => {
          if (res.data) {
            this.unsuccessfulNameList = res.data
          }
          this.visible = false
          this.query()
        }
      })
    },
  }
}
</script>
