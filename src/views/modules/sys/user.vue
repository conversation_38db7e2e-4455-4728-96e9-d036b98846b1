<template>
  <el-card shadow="never" class="aui-card--fill">
    <div class="mod-sys__user">
      <el-form :inline="true" :model="dataForm" @keyup.enter.native="getDataList()">
        <el-tooltip content="请输入职工编号或者姓氏或名字" placement="top">
          <el-form-item>
            <el-input v-model="dataForm.paramStr" placeholder="关键字查询" @input="throttleFunction" clearable>
              <el-button slot="append" icon="el-icon-search" @click="getDataList()"></el-button>
            </el-input>
          </el-form-item>
        </el-tooltip>
        <el-form-item>
          <ren-dept-tree v-model="dataForm.deptId" :placeholder="$t('dept.title')"  :query="true"></ren-dept-tree>
        </el-form-item>
        <el-form-item>
          <el-select v-model="dataForm.status" placeholder="状态" @change="getDataList()" :clearable="true">
            <el-option label="正常" value="1" ></el-option>
            <el-option label="停用" value="0"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item>
          <ren-select v-model="dataForm.employmentStatus" placeholder="员工状态" @change="getDataList()" dict-type="employment_status" clearable></ren-select>
        </el-form-item>
        <el-form-item>
          <el-button v-if="$hasPermission('sys:user:save')" type="primary" @click="addOrUpdateHandle()">{{
              $t('add')
            }}
          </el-button>
        </el-form-item>




        <el-dropdown  style="float:right;">
          <el-button type="primary">
            更多功能<i class="el-icon-arrow-down el-icon--right"></i>
          </el-button>
          <el-dropdown-menu slot="dropdown">
            <el-dropdown-item>
              <el-form-item>
                <el-button v-if="$hasPermission('sys:user:export')" type="info" @click="exportHandle()">{{$t('export')}}</el-button>
              </el-form-item>
            </el-dropdown-item>
            <el-dropdown-item>
              <el-form-item>
                <el-button v-if="$hasPermission('sys:user:export')" type="info" @click="exportTemplateHandle()">
                  {{ $t('exportTemplate') }}
                </el-button>
              </el-form-item>
            </el-dropdown-item>
            <el-dropdown-item>
              <el-form-item>
                <el-tooltip content="下载模板填入用户信息后导入" placement="top">
                  <el-upload
                      v-if="$hasPermission('sys:user:save')"
                      class="upload-demo"
                      :action="mixinViewModuleOptions.addBatchUrl"
                      :headers="headers"
                      :multiple="false"
                      :show-file-list="false"
                      :file-list="fileList"
                      :before-upload="beforeUpload"
                      :on-success="resultHandle"
                      :on-change="handleChange"
                      accept="'.xlsx','.xls'">
                    <el-button type="success">{{ $t('addBatch') }}</el-button>
                  </el-upload>
                </el-tooltip>
                <el-button type="text" v-show="unsuccessfulNameList" @click="dialogVisible = true">导入失败列表</el-button>
              </el-form-item>
            </el-dropdown-item>
            <el-dropdown-item>
              <el-form-item>
                <el-button v-if="$hasPermission('sys:user:delete')" type="danger" @click="deleteHandle()">{{
                    $t('deleteBatch')
                  }}
                </el-button>
              </el-form-item>
            </el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown>
      </el-form>
      <el-table
          v-loading="dataListLoading"
          height="600px"
          :data="dataList"
          border
          @selection-change="dataListSelectionChangeHandle"
          @sort-change="dataListSortChangeHandle"
          style="width: 100%;">
        <el-table-column type="selection" header-align="center" align="center" width="50"></el-table-column>
        <el-table-column prop="employeeId" :label="$t('user.employeeId')" sortable="custom" header-align="center" align="center">
          <template slot-scope="scope">
            <div>{{scope.row.employeeId}}</div>
          </template>
        </el-table-column>

        <el-table-column prop="documentId" label="员工照片" header-align="center" align="center">
          <template slot-scope="scope">
            <div @click="openDialog(scope.row.documentId,scope.row.documentType)">
              <el-avatar shape="square" :size="40" v-if="scope.row.documentId">
                <img :src="$userFilePath+scope.row.documentId+scope.row.documentType"
                     style="width: 100%; height: 100%; object-fit: contain; max-width: 100%; max-height: 100%;" />
              </el-avatar>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="姓名" header-align="center" align="center">
          <template slot-scope="scope">
            <div>
              {{scope.row.surname+scope.row.name}}
            </div>
          </template>
        </el-table-column>
<!--        <el-table-column prop="surname" :label="$t('user.surname')" header-align="center" align="center"
                         width="50"></el-table-column>
        <el-table-column prop="name" :label="$t('user.name')" header-align="center"
                         align="center"></el-table-column>-->
        <el-table-column prop="deptName" :label="$t('user.deptName')" header-align="center"
                         align="center"></el-table-column>
        <el-table-column prop="jobTitle" label="职称" header-align="center" align="center">
          <template slot-scope="scope">
            <div v-if="scope.row.jobTitle">
              <span>
                {{$getDictLabel("company", scope.row.jobTitle.slice(0,2))}}
              </span>
              <span>
                {{$getDictLabel("department", scope.row.jobTitle.slice(2,4))}}
              </span>
              <span>
                {{$getDictLabel("scholarly", scope.row.jobTitle.slice(4,6))}}
              </span>
              <span>
                {{$getDictLabel("group_level", scope.row.jobTitle.slice(6,8))}}
              </span>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="rank" label="职等" header-align="center" align="center"></el-table-column>
        <el-table-column prop="mobile" :label="$t('user.mobile')" header-align="center"
                         align="center"></el-table-column>
        <el-table-column prop="gender" :label="$t('user.gender')" sortable="custom" header-align="center"
                         align="center">
          <template slot-scope="scope">
            {{ $getDictLabel("gender", scope.row.gender) }}
          </template>
        </el-table-column>
        <el-table-column prop="yearsOfService" label="工龄"  header-align="center" align="center"></el-table-column>
        <el-table-column prop="status" label="权限状态" sortable="custom" header-align="center" align="center">
          <template slot-scope="scope">
            <el-tag v-if="scope.row.status === 0" size="small" type="danger">{{ $t('user.status0') }}</el-tag>
            <el-tag v-else size="small" type="success">{{ $t('user.status1') }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="employmentStatus" label="员工状态" sortable="custom" header-align="center" align="center">
          <template slot-scope="scope">
            <div>
              {{$getDictLabel("employment_status",scope.row.employmentStatus)}}
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="hireDate" :label="$t('user.hireDate')" sortable="custom" header-align="center"
                         align="center" width="180"></el-table-column>
        <el-table-column :label="$t('handle')" fixed="right" header-align="center" align="center" width="150">
          <template slot-scope="scope">
            <el-dropdown>
              <el-button type="primary">
                功能<i class="el-icon-arrow-down el-icon--right"></i>
              </el-button>
              <el-dropdown-menu slot="dropdown">
                <el-dropdown-item>
                  <el-button v-if="$hasPermission('sys:user:update')" type="text" size="medium"
                             @click="addOrUpdateHandle(scope.row.id)">{{ $t('update') }}
                  </el-button>
                </el-dropdown-item>
                <el-dropdown-item>
<!--                  <el-button for="fileInput" type="text" v-if="$hasPermission('sys:user:save')" size="medium"
                             @click.native="chooseFile(scope.row.id,scope.row.documentId)">上传照片</el-button>
                  <input type="file" style="display: none" ref="fileInput" @change="handleFileChange">-->
                  <el-upload
                      v-if="$hasPermission('sys:user:save')"
                      :action="mixinViewModuleOptions.addImageUrl+scope.row.id"
                      class="upload-demo"
                      :show-file-list="false"
                      :headers="headers"
                      :multiple="false"
                      :on-success="handleUploadSuccess"
                      :before-upload="beforeUploadFile"
                      :accept="'image/*'">
                    <el-button
                        v-if="$hasPermission('sys:user:save')"
                        type="text"
                        size="small"
                        @click="chooseFile(scope.row.id,scope.row.documentId)"
                    >上传照片</el-button>
                  </el-upload>
                </el-dropdown-item>
                <el-dropdown-item>
                  <el-button v-if="$hasPermission('sys:user:delete')" type="text" size="medium"
                             @click="deleteHandle(scope.row.id)">{{ $t('delete') }}
                  </el-button>
                </el-dropdown-item>
              </el-dropdown-menu>
            </el-dropdown>

          </template>
        </el-table-column>
      </el-table>
      <el-pagination
          :current-page="page"
          :page-sizes="[10, 20, 50, 100]"
          :page-size="limit"
          :total="total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="pageSizeChangeHandle"
          @current-change="pageCurrentChangeHandle">
      </el-pagination>
      <!-- 弹窗, 新增 / 修改 -->
      <add-or-update v-if="addOrUpdateVisible" ref="addOrUpdate" @refreshDataList="getDataList"></add-or-update>

      <el-dialog title=图片显示 :visible.sync="imageDialogVisible" @closed="closeDialog" width="45vw">
        <el-image :src="$userFilePath+imagePath+documentType" fit="contain" alt="未上传图片" style="width: 40vw; height: 60vh"></el-image>
      </el-dialog>

      <el-dialog
          title="已存在用户"
          :visible.sync="dialogVisible"
          width="30%">
        <ul v-for="name of unsuccessfulNameList">
          <li>{{ name }}</li>
        </ul>
        <span slot="footer" class="dialog-footer">
            <el-button @click="dialogVisible = false">关 闭</el-button>
          </span>
      </el-dialog>
    </div>
  </el-card>
</template>

<script>
import Cookies from 'js-cookie'
import mixinViewModule from '@/mixins/view-module'
import AddOrUpdate from './user-add-or-update'

export default {
  mixins: [mixinViewModule],
  data() {
    return {
      imagePath: '',
      documentType: '',
      headers: {'token': Cookies.get('token')},
      fileList: [{name: '', url: ''}],
      unsuccessfulNameList: '',//  导入失败人员名称列表
      dialogVisible: false,
      imageDialogVisible: false,
      mixinViewModuleOptions: {
        getDataListURL: '/sys/user/page',
        getDataListIsPage: true,
        deleteURL: '/sys/user',
        deleteIsBatch: true,
        exportURL: '/sys/user/export',
        exportTemplateURL: '/sys/user/export/template',
        addBatchUrl: window.SITE_CONFIG['apiURL'] + '/sys/user/batchSave',
        addImageUrl: window.SITE_CONFIG['apiURL'] + '/sys/user/api/upload?id=',
      },
      dataForm: {
        employeeId: '',
        surname: '',
        documentId: '',
        employmentStatus: 0,
        name: '',
        deptId: '',
        gender: '',
        paramStr: this.$route.query.parameter != null ? this.$route.query.parameter:'',
        status: '1',
      },
      // 判断是否还在继续输入
      timer: null,
      message: '',
    }
  },
  components: {
    AddOrUpdate
  },
  created() {
    this.$deptSelect.$on('searchByDepartment', this.searchByDepartment);
  },
  beforeDestroy() {
    this.$deptSelect.$off('searchByDepartment', this.searchByDepartment);
  },
  methods: {
    //节流函数
    throttleFunction() {
      // 如果timer不为空就表明在持续输入,每次改变都清除定时，这样就可以实现只输入最后一次结果(注意每次间隔如果超过500毫秒就会输出)
      if (this.timer !== null) clearTimeout(this.timer)
      this.timer = setTimeout(() => {
        this.getDataList()
      }, 500)
    },
    searchByDepartment(){
      let deptId = JSON.parse(localStorage.getItem("deptId"));
      this.dataForm.deptId = deptId
      this.getDataList()
    },
    chooseFile(id, documentId) {
      this.currentRowId = documentId
    },
    beforeUploadFile(file) {
      if (!file.type.startsWith('image/')) {
        this.$message.error('只能上传图片文件')
        return false
      }
    },
    handleUploadSuccess(response) {
      console.log(response)
      if (response.code === 200) {
        if(this.currentRowId){
          this.$http.delete('/sys/user/deleteImage?id=' + this.currentRowId)
        }
        this.$message.success('上传成功')
      } else {
        this.$message.error('上传失败')
      }
      this.query()
    },
    employmentStatusFormatter(row,c){
      switch (row.employmentStatus) {
        case 0:
          return '在职'
        case 1:
          return '离职'
        case 2:
          return '自离'
        case 3:
          return '修养'
        case 4:
          return '退休'
        case 5:
          return '停职留薪'
        case 6:
          return '开除'
      }
    },
    /*chooseFile(id,documentId) {
      if(documentId != null){
        this.$confirm('已有图片，此操作将覆盖原有图片, 是否继续?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          this.$http.post('/sys/user/deleteImage?id='+id)
              .then(response => {
                this.currentRowId = id
                this.$refs.fileInput.click();
              })
              .catch(error => {
                /!* console.log(error);
                 alert('上传失败');*!/
              });
          this.$message({
            type: 'success',
            message: '覆盖成功!'
          });
          return;
        }).catch(() => {
          /!* this.$message({
             type: 'info',
             message: '已取消'
           });*!/

          return;
        });
      }else {
        this.currentRowId = id
        this.$refs.fileInput.click();
      }
    },
    // 文件选择后触发
    handleFileChange(event) {
      const file = event.target.files[0]

      const id = this.currentRowId
      // 上传文件
      this.uploadImage(id, file)
    },
    uploadImage: function (id,file) {
      if (file.type.startsWith('image/')) {
        const formData = new FormData();
        formData.append('file', file);
        this.$http.post('/sys/user/api/upload?id='+id, formData)
            .then(response => {
              console.log(response.data);
              alert('上传成功');
              this.query();
            })
            .catch(error => {
              console.log(error);
              alert('上传失败');
            });
      } else {
        alert('请选择一个图像文件');
      }
    },*/
    openDialog(documentId,documentType) { // 打开对话框
      this.imagePath = documentId
      this.documentType = documentType
      this.imageDialogVisible = true;
    },
    closeDialog() { // 关闭对话框
      this.imageDialogVisible = false;
    },
    beforeUpload(file) {
      this.message = this.$message({
        message: '上传中，请勿操作',
        center: true,
        duration: 0,
      });
    },
    handleChange(file, fileList) {
      this.fileList = fileList.slice(-3);
    },
    resultHandle(res) {
      if (res.code !== 0) {
        return this.$message.error(res.msg)
      }
      this.$message({
        message: this.$t('prompt.success'),
        type: 'success',
        duration: 500,
        onClose: () => {
          if (res.data) {
            this.unsuccessfulNameList = res.data
          }
          this.message.close();
          this.visible = false
          this.query()
        }
      })
    },
  },
}
</script>
