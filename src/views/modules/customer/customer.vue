<template>
  <el-card shadow="never" class="aui-card--fill">
    <div class="mod-customer__customer}">
      <el-form :inline="true" :model="dataForm" @keyup.enter.native="getDataList()">
        <el-form-item>
          <el-input v-model="dataForm.paramStr" placeholder="关键字查询" @input="throttleFunction" clearable @clear="clearValue">
            <el-button slot="append" icon="el-icon-search" @click="getDataList()"></el-button>
          </el-input>
        </el-form-item>
<!--        <el-form-item>
          <el-input v-model="dataForm.code" placeholder="代号" clearable></el-input>
        </el-form-item>
        <el-form-item>
          <el-input v-model="dataForm.chineseName" placeholder="中文名" clearable></el-input>
        </el-form-item>
        <el-form-item>
          <el-input v-model="dataForm.englishName" placeholder="英文名" clearable></el-input>
        </el-form-item>-->
        <el-form-item>
          <el-select v-model="dataForm.level" placeholder="客户等级" @change="getDataList()" :clearable="true">
            <el-option
                v-for="item in level"
                :key="item.value"
                :label="item.label"
                :value="item.value">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button @click="getDataList()">{{ $t('query') }}</el-button>
        </el-form-item>
        <el-form-item>
          <el-button type="info" @click="exportHandle()">{{ $t('export') }}</el-button>
        </el-form-item>
        <el-form-item>
          <el-button v-if="$hasPermission('customer:customer:export')" type="info" @click="exportTemplateHandle()">
            {{ $t('exportTemplate') }}
          </el-button>
        </el-form-item>
        <el-form-item>
          <el-tooltip content="下载模板填入信息后导入" placement="top">
            <el-upload
                v-if="$hasPermission('customer:customer:save')"
                class="upload-demo"
                :action="mixinViewModuleOptions.addBatchUrl"
                :headers="headers"
                :multiple="false"
                :show-file-list="false"
                :file-list="fileList"
                :on-success="resultHandle"
                :on-change="handleChange"
                accept="'.xlsx','.xls'">
              <el-button type="success">{{ $t('addBatch') }}</el-button>
            </el-upload>
          </el-tooltip>
          <el-button type="text" v-show="unsuccessfulNameList" @click="dialogVisible = true">导入失败列表</el-button>
        </el-form-item>
        <el-form-item>
          <el-button v-if="$hasPermission('customer:customer:save')" type="primary" @click="addOrUpdateHandle()">{{ $t('add') }}</el-button>
        </el-form-item>
        <el-form-item>
          <el-button v-if="$hasPermission('customer:customer:delete')" type="danger" @click="deleteHandle()">{{ $t('deleteBatch') }}</el-button>
        </el-form-item>
      </el-form>
      <el-table v-loading="dataListLoading" :data="dataList" border @selection-change="dataListSelectionChangeHandle" style="width: 100%;">
        <el-table-column type="selection" header-align="center" align="center" width="50"></el-table-column>
        <el-table-column prop="code" label="代号" header-align="center" align="center"></el-table-column>
        <el-table-column prop="chineseName" label="中文名称" header-align="center" align="center"></el-table-column>
        <el-table-column prop="englishName" label="英文名称" header-align="center" align="center"></el-table-column>
        <el-table-column prop="socialCode" label="社会码" header-align="center" align="center"></el-table-column>
<!--        <el-table-column prop="businessItems" label="营业项目" header-align="center" align="center"></el-table-column>-->
        <el-table-column prop="level" label="客户等级" :formatter="levelFormat" header-align="center" align="center"></el-table-column>
        <el-table-column prop="queryLevel" label="查询等级" header-align="center" align="center"></el-table-column>
        <el-table-column prop="leadTime" label="采购周期" header-align="center" align="center"></el-table-column>
        <el-table-column prop="reconciliationCycle" label="对账周期" header-align="center" align="center"></el-table-column>
        <el-table-column prop="paymentCycle" label="付款周期" header-align="center" align="center"></el-table-column>
        <el-table-column prop="nickname" label="昵称" header-align="center" align="center"></el-table-column>
        <el-table-column prop="remark" label="备注" header-align="center" align="center"></el-table-column>
        <el-table-column :label="$t('handle')" fixed="right" header-align="center" align="center" width="150">
          <template slot-scope="scope">
            <el-dropdown>
              <el-button type="primary">
                功能<i class="el-icon-arrow-down el-icon--right"></i>
              </el-button>
              <el-dropdown-menu slot="dropdown">
                <el-dropdown-item>
                  <el-button v-if="$hasPermission('customer:customer:update')" type="text" size="medium" @click="addOrUpdateHandle(scope.row.id)">{{ $t('update') }}</el-button>
                </el-dropdown-item>
                <el-dropdown-item>
                  <el-button v-if="$hasPermission('customer:customer:delete')" type="text" size="medium" @click="deleteHandle(scope.row.id)">{{ $t('delete') }}</el-button>
                </el-dropdown-item>
              </el-dropdown-menu>
            </el-dropdown>
          </template>
        </el-table-column>
      </el-table>
      <el-pagination
        :current-page="page"
        :page-sizes="[10, 20, 50, 100]"
        :page-size="limit"
        :total="total"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="pageSizeChangeHandle"
        @current-change="pageCurrentChangeHandle">
      </el-pagination>
      <!-- 弹窗, 新增 / 修改 -->
      <add-or-update v-if="addOrUpdateVisible" ref="addOrUpdate" @refreshDataList="getDataList"></add-or-update>
    </div>
  </el-card>
</template>

<script>
import mixinViewModule from '@/mixins/view-module'
import AddOrUpdate from './customer-add-or-update'
import Cookies from "js-cookie";
export default {
  mixins: [mixinViewModule],
  data () {
    return {
      headers: {'token': Cookies.get('token')},
      mixinViewModuleOptions: {
        getDataListURL: '/customer/customer/page',
        getDataListIsPage: true,
        exportURL: '/customer/customer/export',
        deleteURL: '/customer/customer',
        deleteIsBatch: true,
        exportTemplateURL: '/customer/customer/export/template',
        addBatchUrl: window.SITE_CONFIG['apiURL'] + '/customer/customer/batchSave',
      },
      dataForm: {
        id: '',
        code:'',
        level:'',
        paramStr: this.$route.query.parameter != null ? this.$route.query.parameter:'',
        chineseName:'',
        englishName:'',
      },
      // 判断是否还在继续输入
      timer: null,
      level: [{
        value: 1,
        label: '一般'
      }, {
        value: 2,
        label: '重要'
      }, {
        value: 3,
        label: 'VIP'
      }],
      message: '',
      fileList: [{name: '', url: ''}],

    }
  },
  components: {
    AddOrUpdate
  },
  methods: {
    //节流函数
    throttleFunction() {
      // 如果timer不为空就表明在持续输入,每次改变都清除定时，这样就可以实现只输入最后一次结果(注意每次间隔如果超过500毫秒就会输出)
      if (this.timer !== null) clearTimeout(this.timer)
      this.timer = setTimeout(() => {
        this.getDataList()
      }, 500)
    },
    clearValue(){
      this.dataForm.paramStr = '';
      //重新导航到当前路由，并将参数 parameter 设置为 null
      this.$router.replace({ query: { parameter: null } });
    },
    beforeUpload(file) {
      this.message = this.$message({
        message: '上传中，请勿操作',
        center: true,
        duration: 0,
      });
    },
    handleChange(file, fileList) {
      this.fileList = fileList.slice(-3);
    },
    resultHandle(res) {
      if (res.code !== 0) {
        return this.$message.error(res.msg)
      }
      this.$message({
        message: this.$t('prompt.success'),
        type: 'success',
        duration: 500,
        onClose: () => {
          this.message.close();
          this.visible = false
          this.query()
        }
      })
    },
    levelFormat(row, column){
      switch (row.level) {
        case 1:
          return '一般'
        case 2:
          return '重要'
        case 3:
          return 'VIP'
      }
    },
  }
}
</script>
