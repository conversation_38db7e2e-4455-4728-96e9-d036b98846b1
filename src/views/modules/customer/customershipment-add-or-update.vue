<template>
  <el-dialog :visible.sync="visible" :title="!dataForm.id ? $t('add') : $t('update')" :close-on-click-modal="false" :close-on-press-escape="false" append-to-body>
    <el-form :model="dataForm" :rules="dataRule" ref="dataForm" @keyup.enter.native="dataFormSubmitHandle()" :label-width="$i18n.locale === 'en-US' ? '120px' : '80px'">
<!--          <el-form-item label="客户id" prop="customerId">
          <el-input v-model="dataForm.customerId" placeholder="客户id"></el-input>
      </el-form-item>-->
      <el-form-item label="订单类型" prop="orderType">
        <el-select v-model="dataForm.orderType" placeholder="订单类型">
          <el-option
              v-for="item in orderTypes"
              :key="item.value"
              :label="item.label"
              :value="item.value">
          </el-option>
        </el-select>
      </el-form-item>
<!--      <el-form-item label="制交单号" prop="orderNumber" v-if="dataForm.orderType===0">-->
      <el-form-item label="制交单号" prop="trackingNumber">
        <el-autocomplete
            popper-class="my-class"
            style="width: 300px;"

            v-model="dataForm.trackingNumber"
            :fetch-suggestions="getLikeOrderNumber"
            placement="bottom"
            placeholder="请输入交制单号"
            :trigger-on-focus="false"
            @select="orderSelect"
        ></el-autocomplete>
      </el-form-item>
<!--          <el-form-item label="交货方式(指定或者未指定)" prop="deliveryMethod">
          <el-input v-model="dataForm.deliveryMethod" placeholder="交货方式(指定或者未指定)"></el-input>
      </el-form-item>-->
      <el-form-item label="订单号" prop="orderNumber">
        <el-input v-model="dataForm.orderNumber" placeholder="订单号"></el-input>
      </el-form-item>
      <el-form-item label="品号" prop="partCode">
        <el-input v-model="dataForm.partCode" placeholder="品号"></el-input>
      </el-form-item>
      <el-form-item label="出货数量" prop="shipmentQuantity">
        <el-input v-model="dataForm.shipmentQuantity" placeholder="出货数量"></el-input>
      </el-form-item>
      <el-form-item label="应收金额" prop="amountReceivable">
        <el-input v-model="dataForm.amountReceivable" placeholder="应收金额"></el-input>
      </el-form-item>
      <el-form-item label="币别" prop="currency">
        <el-select v-model="dataForm.currency" placeholder="币别">
          <el-option
              v-for="item in currencys"
              :key="item.value"
              :label="item.label"
              :value="item.value">
          </el-option>
        </el-select>
      </el-form-item>
          <el-form-item label="收货地点(国内或者国外)" prop="placeOfReceipt">
          <el-input v-model="dataForm.placeOfReceipt" placeholder="收货地点(国内或者国外)"></el-input>
      </el-form-item>
          <el-form-item label="收货人" prop="receiver">
          <el-input v-model="dataForm.receiver" placeholder="收货人"></el-input>
      </el-form-item>
          <el-form-item label="电话" prop="shipmentTelephone">
          <el-input v-model="dataForm.shipmentTelephone" placeholder="电话"></el-input>
      </el-form-item>
          <el-form-item label="电子邮箱" prop="email">
          <el-input v-model="dataForm.email" placeholder="电子邮箱"></el-input>
      </el-form-item>
          <el-form-item label="通讯软件" prop="shipmentCommunicationSoftware">
          <el-input v-model="dataForm.shipmentCommunicationSoftware" placeholder="通讯软件"></el-input>
      </el-form-item>
          <el-form-item label="包装方式" prop="packing">
          <el-input v-model="dataForm.packing" placeholder="包装方式"></el-input>
      </el-form-item>
          <el-form-item label="备注" prop="remark">
          <el-input v-model="dataForm.remark" placeholder="备注"></el-input>
      </el-form-item>
                </el-form>
    <template slot="footer">
      <el-button @click="visible = false">{{ $t('cancel') }}</el-button>
      <el-button type="primary" @click="dataFormSubmitHandle()">{{ $t('confirm') }}</el-button>
    </template>
  </el-dialog>
</template>

<script>
import debounce from 'lodash/debounce'
export default {
  data () {
    return {
      visible: false,
      dataForm: {
        id: '',
        orderType:'',
        orderNumber:'',
        customerCode:'',
        trackingNumber:'',
        contactPerson:'',
        shipmentQuantity:null,
        partCode:'',
        orderId:'',
        amountReceivable:'',
        unitPrice:'',
        exchangeRate:'',
        currency:'',
        customerId: '',
        deliveryMethod: '',
        placeOfReceipt: '',
        receiver: '',
        shipmentTelephone: '',
        email: '',
        shipmentCommunicationSoftware: '',
        packing: '',
        remark: '',
        disabled: '',
        creator: '',
        createDate: '',
        updater: '',
        updateDate: ''
      },
      orderTypes: [{
        value: 0,
        label: '生产订单'
      }, {
        value: 1,
        label: '预示订单'
      }],
      currencys: [{
        value: 0,
        label: '人民币'
      }, {
        value: 1,
        label: '欧元'
      }, {
        value: 2,
        label: '英镑'
      }, {
        value: 3,
        label: '日元'
      }, {
        value: 4,
        label: '澳元'
      }, {
        value: 5,
        label: '加元'
      }, {
        value: 6,
        label: '瑞士法郎'
      }, {
        value: 7,
        label: '美元'
      }, {
        value: 8,
        label: '印度卢比'
      }, {
        value: 9,
        label: '新加坡元'
      }, {
        value: 10,
        label: '港元'
      }],
      orderNumber: '',
    }
  },
  computed: {
    quantity() {
      console.log("----"+this.dataForm.quantity)
return this.dataForm.quantity==0?null:this.dataForm.quantity
    },
    dataRule () {
      return {
          id: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          customerId: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          deliveryMethod: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          placeOfReceipt: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          receiver: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          shipmentTelephone: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          email: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          /*shipmentCommunicationSoftware: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          packing: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],*/
          /*remark: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],*/
        orderType: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        trackingNumber: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        partCode: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        shipmentQuantity: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        amountReceivable: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        currency: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
                }
    }
  },
  watch:{
    /*"dataForm.unitPrice"(){
      this.dataForm.transactionAmount = this.dataForm.unitPrice * this.dataForm.quantity
    },
    "dataForm.orderType"(type){
      this.baseColorMatching(type)
    },*/
    "dataForm.shipmentQuantity"(){
      console.log("---unitPrice"+this.dataForm.unitPrice+"----"+this.dataForm.exchangeRate)
      this.dataForm.amountReceivable = this.dataForm.unitPrice * this.dataForm.shipmentQuantity*this.dataForm.exchangeRate
    },
    "dataForm.deliveryLocationCode"(){
      if(!this.dataForm.id){
        this.dataForm.tradingLocations = this.dataForm.deliveryLocationCode
      }
    },
  },
  methods: {
    init () {
      this.visible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].resetFields()
        if (this.dataForm.id) {
          this.getInfo()
        }
      })
    },
    // 获取信息
    getInfo () {
      this.$http.get(`/customer/customershipment/${this.dataForm.id}`).then(({ data: res }) => {
        if (res.code !== 0) {
          return this.$message.error(res.msg)
        }
        this.dataForm = {
          ...this.dataForm,
          ...res.data
        }
        console.log("\\\\\\"+res.data().orderTypes)
        this.dataForm.orderTypes=res.data().orderTypes
      }).catch(() => {})
    },
    // 表单提交
    dataFormSubmitHandle: debounce(function () {
      this.$refs['dataForm'].validate((valid) => {
        if (!valid) {
          return false
        }
        this.$http[!this.dataForm.id ? 'post' : 'put']('/customer/customershipment/', this.dataForm).then(({ data: res }) => {
          if (res.code !== 0) {
            return this.$message.error(res.msg)
          }
          this.$message({
            message: this.$t('prompt.success'),
            type: 'success',
            duration: 500,
            onClose: () => {
              this.visible = false
              this.$emit('refreshDataList')
            }
          })
        }).catch(() => {})
      })
    }, 1000, { 'leading': true, 'trailing': false }),
    // 获取订单列表
    getLikeOrderNumber(input,cb){
      this.$http.get(`orders/businessorder/getOrderListLikeInput?input=`+input+`&orderType=`+this.dataForm.orderType).then(({data: res}) => {
        console.log("res",res)
        if (res.code !== 0) {
          return this.$message.error(res.msg)
        }
        const list = res.data.map((obj) => {
          // console.log("obj",obj)
          return {
            value:obj.trackingNumber+'('+obj.partNameCode+')',
            orderNumber: obj.orderNumber,
            designation:obj.partDesignation,
            customerCode: obj.customerCode,
            partId: obj.partId,
            placeOfReceipt: obj.tradingLocations,
            receiver:obj.contactPerson,
            shipmentTelephone:obj.contactPhone,
            email: obj.contactEmail,
            shipmentQuantity:obj.quantity,
            partCode:obj.partNameCode,
            orderId:obj.id,
            amountReceivable:obj.amountReceivable,
            currency:obj.currency,
            exchangeRate:obj.exchangeRate,
            unitPrice: obj.unitPrice,
          }
        })
        this.timeout = setTimeout(() => {
          cb(list);
        }, 300 * Math.random());
      }).catch(() => {
      })
    },
    //  输出选择日志
    orderSelect(item) {
      this.dataForm.orderNumber=item.orderNumber
      this.dataForm.customerCode = item.customerCode
      this.dataForm.customerId = item.customerId
      this.dataForm.placeOfReceipt=item.placeOfReceipt
      this.dataForm.receiver=item.receiver
      this.dataForm.shipmentTelephone=item.shipmentTelephone
      if(item.email.includes('<')){
        this.dataForm.email=item.email.replace('<','"')
      }
      if(item.email.includes('>')){
        this.dataForm.email=this.dataForm.email.replace('>','"')
      }
      this.dataForm.shipmentQuantity=item.shipmentQuantity
      this.dataForm.partCode=item.partCode
      this.dataForm.orderId=item.orderId
      this.dataForm.amountReceivable=item.amountReceivable
      this.dataForm.currency=item.currency
      this.dataForm.unitPrice=item.unitPrice
      this.dataForm.exchangeRate=item.exchangeRate
    },
    trackChange() {
      console.log("==="+this.dataForm.quantity)
      this.dataForm.quantity = parseInt(this.dataForm.quantity) === 0  ? null : this.dataForm.quantity
    },
  }
}
</script>
<style>
.my-class {
  width: 27%!important;
}
.my-input {
  width: 27%!important;
}
</style>
