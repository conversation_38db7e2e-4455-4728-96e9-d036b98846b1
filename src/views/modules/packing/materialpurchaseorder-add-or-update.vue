<template>
  <el-dialog :visible.sync="visible" :title="!dataForm.id ? $t('add') : $t('update')" :close-on-click-modal="false" :close-on-press-escape="false" append-to-body>
    <el-form :model="dataForm" :rules="dataRule" ref="dataForm" @keyup.enter.native="dataFormSubmitHandle()" :label-width="$i18n.locale === 'en-US' ? '120px' : '80px'">
      <div class="container">
                                                <el-form-item label="采购单号" prop="purchaseOrderNumber">
              <el-input v-model="dataForm.purchaseOrderNumber" placeholder="采购单号"></el-input>
            </el-form-item>
                                        <el-form-item label="客户代码" prop="customerCode">
              <el-input v-model="dataForm.customerCode" placeholder="客户代码"></el-input>
            </el-form-item>
                                        <el-form-item label="采购产品代码" prop="purchaseProductCode">
              <el-input v-model="dataForm.purchaseProductCode" placeholder="采购产品代码"></el-input>
            </el-form-item>
                                        <el-form-item label="内容" prop="content">
              <el-input v-model="dataForm.content" placeholder="内容"></el-input>
            </el-form-item>
                                        <el-form-item label="数量" prop="quantity">
              <el-input v-model="dataForm.quantity" placeholder="数量"></el-input>
            </el-form-item>
                                        <el-form-item label="金额" prop="amountOfMoney">
              <el-input v-model="dataForm.amountOfMoney" placeholder="金额"></el-input>
            </el-form-item>
                                        <el-form-item label="交货日期" prop="deliveryDate">
                                          <el-date-picker
                                              v-model="dataForm.deliveryDate"
                                              type="date"
                                              placeholder="交货日期">
                                          </el-date-picker>
<!--              <el-input v-model="dataForm.deliveryDate" placeholder="交货日期"></el-input>-->
            </el-form-item>
                                        <el-form-item label="交货方式(指定及未指定)" prop="deliveryMethod">
              <el-input v-model="dataForm.deliveryMethod" placeholder="交货方式(指定及未指定)"></el-input>
            </el-form-item>
                                        <el-form-item label="收货地址(国内或国外)" prop="receivingLocation">
              <el-input v-model="dataForm.receivingLocation" placeholder="收货地址(国内或国外)"></el-input>
            </el-form-item>
                                        <el-form-item label="收货人" prop="consignee">
              <el-input v-model="dataForm.consignee" placeholder="收货人"></el-input>
            </el-form-item>
                                        <el-form-item label="电话" prop="phone">
              <el-input v-model="dataForm.phone" placeholder="电话"></el-input>
            </el-form-item>
                                        <el-form-item label="包装方式" prop="packagingMethod">
              <el-input v-model="dataForm.packagingMethod" placeholder="包装方式"></el-input>
            </el-form-item>
                                        <el-form-item label="进料检验编号" prop="incomingInspectionNumber">
              <el-input v-model="dataForm.incomingInspectionNumber" placeholder="进料检验编号"></el-input>
            </el-form-item>
                                        <el-form-item label="备注" prop="remark">
              <el-input v-model="dataForm.remark" placeholder="备注"></el-input>
            </el-form-item>
                                                                                                                  </div>
    </el-form>
    <template slot="footer">
      <el-button @click="visible = false">{{ $t('cancel') }}</el-button>
      <el-button type="primary" @click="dataFormSubmitHandle()">{{ $t('confirm') }}</el-button>
    </template>
  </el-dialog>
</template>

<script>
import debounce from 'lodash/debounce'
import mixinViewModule from '@/mixins/view-module'
export default {
  mixins:[mixinViewModule],
  data () {
    return {
      visible: false,
      //创建该页面时，不调用数据查询接口
      mixinViewModuleOptions: {
        createdIsNeed: false,
      },
      dataForm: {
        id: '',
        purchaseOrderNumber: '',
        customerCode: '',
        purchaseProductCode: '',
        content: '',
        quantity: '',
        amountOfMoney: '',
        deliveryDate: '',
        deliveryMethod: '',
        receivingLocation: '',
        consignee: '',
        phone: '',
        packagingMethod: '',
        incomingInspectionNumber: '',
        remark: '',
        disabled: '',
        creator: '',
        createDate: '',
        updater: '',
        updateDate: ''
      }
    }
  },
  computed: {
    dataRule () {
      return {
          id: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          purchaseOrderNumber: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          /*customerCode: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          purchaseProductCode: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          content: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          quantity: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          amountOfMoney: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          deliveryDate: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          deliveryMethod: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          receivingLocation: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          consignee: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          phone: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          packagingMethod: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          incomingInspectionNumber: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          remark: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],*/
                }
    }
  },
  methods: {
    init () {
      this.visible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].resetFields()
        if (this.dataForm.id) {
          this.getInfo()
        }
      })
    },
    // 获取信息
    getInfo () {
      this.$http.get(`/packing/materialpurchaseorder/${this.dataForm.id}`).then(({ data: res }) => {
        if (res.code !== 0) {
          return this.$message.error(res.msg)
        }
        this.dataForm = {
          ...this.dataForm,
          ...res.data
        }
      }).catch(() => {})
    },
    // 表单提交
    dataFormSubmitHandle: debounce(function () {
      this.$refs['dataForm'].validate((valid) => {
        if (!valid) {
          return false
        }
        let message = '';
        if(this.dataForm.id){
          message = '此操作将修改该数据'
        }else {
          message = '此操作将新增一条数据'
        }
        this.$confirm(message+', 是否继续?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
          center: true
        }).then(() => {
          this.$http[!this.dataForm.id ? 'post' : 'put']('/packing/materialpurchaseorder/', this.dataForm).then(({ data: res }) => {
            if (res.code !== 0) {
            return this.$message.error(res.msg)
            }
            this.$message({
          message: this.$t('prompt.success'),
            type: 'success',
                    duration: 500,
                    onClose: () => {
              this.visible = false
              this.$emit('refreshDataList')
            }
          })
          }).catch(() => {})
        }).catch(() => {
          this.$message({
            type: 'info',
            message: '已取消'
          });
        });
      })
    }, 1000, { 'leading': true, 'trailing': false })
  }
}
</script>
