<template>
  <div>
    <div>
  <el-card shadow="never" class="aui-card--fill">
    <div class="mod-packing__materialrawmaterialinventorysummary}">
      <el-form :inline="true" :model="dataForm" @keyup.enter.native="getDataList()">
        <el-form-item>
          <el-tooltip content="请输入查询关键字" placement="top">
            <el-input v-model="dataForm.paramStr" placeholder="查询关键字" @input="throttleFunction" clearable>
              <el-button slot="append" icon="el-icon-search" @click="getDataList()"></el-button>
            </el-input>
          </el-tooltip>
        </el-form-item>
        <el-form-item>
          <el-button v-if="$hasPermission('packing:materialrawmaterialinventorysummary:save')" type="primary" @click="addOrUpdateHandle()">{{ $t('add') }}</el-button>
        </el-form-item>
        <el-dropdown  style="float:right;">
          <el-button type="primary">
            更多功能<i class="el-icon-arrow-down el-icon--right"></i>
          </el-button>
          <el-dropdown-menu slot="dropdown">
            <el-dropdown-item>
              <el-form-item>
                <el-button type="info" @click="exportHandle()">{{ $t('export') }}</el-button>
              </el-form-item>
            </el-dropdown-item>
            <el-dropdown-item>
              <el-form-item>
                <el-button v-if="$hasPermission('packing:materialrawmaterialinventorysummary:export')" type="info" @click="exportTemplateHandle()">
                  {{ $t('exportTemplate') }}
                </el-button>
              </el-form-item>
            </el-dropdown-item>
            <el-dropdown-item>
              <el-form-item>
                <el-tooltip content="下载模板填入信息后导入" placement="top">
                  <el-upload
                          v-if="$hasPermission('packing:materialrawmaterialinventorysummary:save')"
                          class="upload-demo"
                          :action="mixinViewModuleOptions.addBatchUrl"
                          :headers="headers"
                          :multiple="false"
                          :show-file-list="false"
                          :file-list="fileList"
                          :before-upload="beforeUpload"
                          :on-success="resultHandle"
                          :on-change="handleChange"
                          accept="'.xlsx','.xls'">
                    <el-button type="success">{{ $t('addBatch') }}</el-button>
                  </el-upload>
                </el-tooltip>
                <el-button type="text" v-show="unsuccessfulNameList" @click="dialogVisible = true">导入失败列表</el-button>
              </el-form-item>
            </el-dropdown-item>
            <el-dropdown-item>
              <el-form-item>
                <el-button v-if="$hasPermission('packing:materialrawmaterialinventorysummary:delete')" type="danger" @click="deleteHandle()">{{ $t('deleteBatch') }}</el-button>
              </el-form-item>
            </el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown>
      </el-form>
      <el-table v-loading="dataListLoading" height="500px" :data="dataList" border @selection-change="dataListSelectionChangeHandle" style="width: 100%;">
        <el-table-column type="selection" header-align="center" align="center" width="50"></el-table-column>
        <el-table-column prop="materialName" label="材料名称" header-align="center" align="center"></el-table-column>
<!--        <el-table-column prop="correspondName" label="对应名称" header-align="center" align="center"></el-table-column>-->
        <el-table-column prop="rawMaterialCode" label="原物料代码" header-align="center" align="center"></el-table-column>
        <el-table-column prop="category" label="类别" header-align="center" align="center"></el-table-column>
        <el-table-column prop="unit" label="计算单位" header-align="center" align="center"></el-table-column>
        <el-table-column prop="inventoryQuantity" label="库存数量" header-align="center" align="center">
          <template slot-scope="scope">
              {{scope.row.inventoryQuantity}}{{scope.row.unit}}
          </template>
        </el-table-column>
        <el-table-column prop="securityInventoryQuantity" label="采购需求线" header-align="center" align="center">
          <template slot-scope="scope">
            {{scope.row.securityInventoryQuantity}}{{scope.row.unit}}
          </template>
        </el-table-column>
        <el-table-column prop="periodOfValidity" label="有效期" header-align="center" align="center"></el-table-column>
        <el-table-column prop="remark" label="备注" header-align="center" align="center"></el-table-column>
        <el-table-column :label="$t('handle')" fixed="right" header-align="center" align="center" width="150">
          <template slot-scope="scope">
            <el-dropdown>
              <el-button type="primary">
                功能<i class="el-icon-arrow-down el-icon--right"></i>
              </el-button>
              <el-dropdown-menu slot="dropdown">
                <el-dropdown-item>
                  <el-button v-if="$hasPermission('packing:materialrawmaterialinventorysummary:update')" type="text" size="small" @click="updateHandle(scope.row)">{{ $t('update') }}</el-button>
                </el-dropdown-item>
                <el-dropdown-item>
                  <el-button v-if="$hasPermission('packing:materialrawmaterialinventorysummary:delete')" type="text" size="small" @click="deleteHandle(scope.row.id)">{{ $t('delete') }}</el-button>
                </el-dropdown-item>
              </el-dropdown-menu>
            </el-dropdown>
          </template>
        </el-table-column>
      </el-table>
      <el-pagination
        :currenat-page="page"
        :page-sizes="[10, 20, 50, 100]"
        :page-size="limit"
        :total="total"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="pageSizeChangeHandle"
        @current-change="pageCurrentChangeHandle">
      </el-pagination>
      <!-- 弹窗, 新增 / 修改 -->
      <add-or-update v-if="addOrUpdateVisible" ref="addOrUpdate" @refreshDataList="getDataList"></add-or-update>
    </div>
  </el-card>
   </div>
    <div>
      <el-dialog :visible.sync="dialogVisible1"  title="资材库存调节">
        <el-form :model="inventoryForm" ref="inventoryForm"  :rules="inventoryRule"  label-width="7vw">
            <div style="display:flex">
              <el-form-item label="材料名称" prop="materialName" style="width: 33vw">
                <el-input v-model="inventoryForm.materialName" style="width: 12vw" disabled></el-input>
              </el-form-item>
              <el-form-item label="物料代码" prop="materialCode" style="width: 33vw">
                <el-input v-model="inventoryForm.materialCode" disabled></el-input>
              </el-form-item>
              <el-form-item label="类别" prop="category" style="width: 33vw">
                <el-input v-model="inventoryForm.category" placeholder="类别" disabled></el-input>
              </el-form-item>

              </div>
              <div style="display: flex">
              <el-form-item label="库存数量" style="width: 20vw">
                <el-input v-model="inventoryForm.inventoryQuantity" style="width: 10vw" placeholder="" disabled>
                  <template slot="append">{{this.inventoryForm.unit}}</template>
                </el-input>
              </el-form-item>
              <el-form-item label="调节后数量" prop="regulatedQuantity" style="width: 22vw">
                <el-input v-model="inventoryForm.regulatedQuantity" style="width: 11vw" placeholder="调节数量">
                  <template slot="append">{{this.inventoryForm.unit}}</template>
                </el-input>
              </el-form-item>
            </div>
            <div>
              <el-form-item label="调节原因" prop="regulatoryCause">
                <el-input type="textarea" :span:="3" v-model="inventoryForm.regulatoryCause" placeholder="调节原因"></el-input>
              </el-form-item>
            </div>
        </el-form>
        <template slot="footer">
          <el-button @click="closeDialog">{{ $t('cancel') }}</el-button>
          <el-button type="primary" @click="dataFormSubmitHandle()">{{ $t('confirm') }}</el-button>
        </template>
      </el-dialog>
    </div>
  </div>
</template>

<script>
import mixinViewModule from '@/mixins/view-module'
import AddOrUpdate from './materialrawmaterialinventorysummary-add-or-update'
import Cookies from "js-cookie";
import debounce from "lodash/debounce";
export default {
  mixins: [mixinViewModule],
  data () {
    return {
      message:'',
      headers: {'token': Cookies.get('token')},
      fileList: [{name: '', url: ''}],
      unsuccessfulNameList: '',//  导入失败名称列表
      mixinViewModuleOptions: {
        getDataListURL: '/packing/materialrawmaterialinventorysummary/page',
        getDataListIsPage: true,
        exportURL: '/packing/materialrawmaterialinventorysummary/export',
        deleteURL: '/packing/materialrawmaterialinventorysummary',
        deleteIsBatch: true,
        exportTemplateURL: '/packing/materialrawmaterialinventorysummary/export/template',
        addBatchUrl: window.SITE_CONFIG['apiURL'] + '/packing/materialrawmaterialinventorysummary/batchSave',
      },
      dataForm: {
        id: '',
        paramStr: '',
      },
      inventoryForm:{
        materialName:'',
        materialCode: '',
        category:'',
        inventoryQuantity:'',
        unit:'',
        regulatedQuantity: '',
        regulatoryCause: ''
      },
      // 判断是否还在继续输入
      timer: null,
      dialogVisible1:false
    }
  },
  computed: {
    inventoryRule(){
      return{
        regulatedQuantity: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        regulatoryCause: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ]
      }
    }
  },
  components: {
    AddOrUpdate

  },
  methods:{
    //节流函数
    throttleFunction() {
      // 如果timer不为空就表明在持续输入,每次改变都清除定时，这样就可以实现只输入最后一次结果(注意每次间隔如果超过500毫秒就会输出)
      if (this.timer !== null) clearTimeout(this.timer)
      this.timer = setTimeout(() => {
        this.getDataList()
      }, 500)
    },
    beforeUpload(file) {
      this.message = this.$message({
        message: '上传中，请勿操作',
        center: true,
        duration: 0,
      });
    },
    handleChange(file, fileList) {
      this.fileList = fileList.slice(-3);

    },
    updateHandle(data){
      this.dialogVisible1 = true
      this.inventoryForm.materialName = data.materialName
      this.inventoryForm.materialCode = data.rawMaterialCode
      this.inventoryForm.category = data.category
      this.inventoryForm.inventoryQuantity = data.inventoryQuantity
      this.inventoryForm.unit = data.unit
    },
    closeDialog(){
      this.dialogVisible1 = false
      this.inventoryForm ={
        materialName:'',
        materialCode: '',
        category:'',
        inventoryQuantity:'',
        unit:'',
        regulatedQuantity: '',
        regulatoryCause: ''
      }
    },
    // 表单提交
    dataFormSubmitHandle: debounce(function () {
      this.$refs['inventoryForm'].validate((valid) => {
        if (!valid) {
          return false
        }
        this.$confirm('此操作将修改该数据, 是否继续?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
          center: true
        }).then(() => {
          this.$http.post('/material/adjustmentofmaterialinventory/', this.inventoryForm).then(({ data: res }) => {
            if (res.code !== 0) {
              return this.$message.error(res.msg)
            }
            this.$message({
              message: this.$t('prompt.success'),
              type: 'success',
              duration: 500,
              onClose: () => {
                this.dialogVisible1 = false
                this.getDataList()
              }
            })
            this.inventoryForm = {
              materialName:'',
              materialCode: '',
              category:'',
              inventoryQuantity:'',
              unit:'',
              regulatedQuantity: '',
              regulatoryCause: ''
            }
          }).catch(() => {})
        }).catch(() => {
          this.$message({
            type: 'info',
            message: '已取消'
          });
        });
      })
    }, 1000, { 'leading': true, 'trailing': false }),

    resultHandle(res) {
      if (res.code !== 0) {
      return this.$message.error(res.msg)
      }
      this.$message({
        message: this.$t('prompt.success'),
        type: 'success',
        duration: 500,
        onClose: () => {
          if (res.data) {
            this.unsuccessfulNameList = res.data
          }
          this.visible = false
          this.query()
        }
      })
    },
  }
}
</script>
