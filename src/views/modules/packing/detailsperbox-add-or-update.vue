<template>
  <el-dialog :visible.sync="visible" :title="!dataForm.id ? $t('add') : $t('update')" :close-on-click-modal="false" :close-on-press-escape="false" append-to-body>
    <el-form :model="dataForm" :rules="dataRule" ref="dataForm" @keyup.enter.native="dataFormSubmitHandle()" :label-width="$i18n.locale === 'en-US' ? '120px' : '80px'">
          <el-form-item label="包装明细id" prop="wrapDetailsId">
          <el-input v-model="dataForm.wrapDetailsId" placeholder="包装明细id"></el-input>
      </el-form-item>
          <el-form-item label="次批号id" prop="subBatchId">
          <el-input v-model="dataForm.subBatchId" placeholder="次批号id"></el-input>
      </el-form-item>
          <el-form-item label="每袋数量" prop="quantityPerBag">
          <el-input v-model="dataForm.quantityPerBag" placeholder="每袋数量"></el-input>
      </el-form-item>
          <el-form-item label="总袋数" prop="numberOfBags">
          <el-input v-model="dataForm.numberOfBags" placeholder="总袋数"></el-input>
      </el-form-item>
          <el-form-item label="总数量" prop="totalQuantity">
          <el-input v-model="dataForm.totalQuantity" placeholder="总数量"></el-input>
      </el-form-item>
          <el-form-item label="实际数" prop="practicalNumber">
          <el-input v-model="dataForm.practicalNumber" placeholder="实际数"></el-input>
      </el-form-item>
          <el-form-item label="结余数" prop="residualNumber">
          <el-input v-model="dataForm.residualNumber" placeholder="结余数"></el-input>
      </el-form-item>
          <el-form-item label="备注" prop="remark">
          <el-input v-model="dataForm.remark" placeholder="备注"></el-input>
      </el-form-item>
                </el-form>
    <template slot="footer">
      <el-button @click="visible = false">{{ $t('cancel') }}</el-button>
      <el-button type="primary" @click="dataFormSubmitHandle()">{{ $t('confirm') }}</el-button>
    </template>
  </el-dialog>
</template>

<script>
import debounce from 'lodash/debounce'
export default {
  data () {
    return {
      visible: false,
      dataForm: {
        id: '',
        wrapDetailsId: '',
        subBatchId: '',
        quantityPerBag: '',
        numberOfBags: '',
        totalQuantity: '',
        practicalNumber: '',
        residualNumber: '',
        remark: '',
        disabled: '',
        creator: '',
        createDate: '',
        updater: '',
        updateDate: ''
      }
    }
  },
  computed: {
    dataRule () {
      return {
          id: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          wrapDetailsId: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          subBatchId: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          quantityPerBag: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          numberOfBags: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          totalQuantity: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          practicalNumber: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        residualNumber: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          remark: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
                }
    }
  },
  methods: {
    init () {
      this.visible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].resetFields()
        if (this.dataForm.id) {
          this.getInfo()
        }
      })
    },
    // 获取信息
    getInfo () {
      this.$http.get(`/packing/detailsperbox/${this.dataForm.id}`).then(({ data: res }) => {
        if (res.code !== 0) {
          return this.$message.error(res.msg)
        }
        this.dataForm = {
          ...this.dataForm,
          ...res.data
        }
      }).catch(() => {})
    },
    // 表单提交
    dataFormSubmitHandle: debounce(function () {
      this.$refs['dataForm'].validate((valid) => {
        if (!valid) {
          return false
        }
        this.$http[!this.dataForm.id ? 'post' : 'put']('/packing/detailsperbox/', this.dataForm).then(({ data: res }) => {
          if (res.code !== 0) {
            return this.$message.error(res.msg)
          }
          this.$message({
            message: this.$t('prompt.success'),
            type: 'success',
            duration: 500,
            onClose: () => {
              this.visible = false
              this.$emit('refreshDataList')
            }
          })
        }).catch(() => {})
      })
    }, 1000, { 'leading': true, 'trailing': false })
  }
}
</script>
