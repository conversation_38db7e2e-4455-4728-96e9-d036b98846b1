<template>
<!--  <el-card shadow="never" class="aui-card&#45;&#45;fill">
    <div class="mod-packing__wrapaddorupdate}" style="width: 100%">
    <el-form :model="dataForm" :rules="dataRule" ref="dataForm" @keyup.enter.native="dataFormSubmitHandle()" :label-width="$i18n.locale === 'en-US' ? '120px' : '80px'">
      <el-divider content-position="center">
        <span class="font_size" v-if="!dataForm.id">新增包装明细</span>
        <span class="font_size" v-else>新增包装明细</span>
      </el-divider>
      <div class="container">
        <div class="four">
          <el-form-item label="客户代码" prop="customerId">
            <customer-component v-model="dataForm.customerId" placeholder="客户代码"></customer-component>
          </el-form-item>
        </div>

        <div class="four">
          <el-form-item label="客户订单" prop="customerOrderId">
            <el-input v-model="dataForm.customerOrderId" placeholder="客户订单号"></el-input>
          </el-form-item>
        </div>

        <div class="four">
          <el-form-item label="订单日期" prop="orderDate">
            <el-date-picker
                v-model="dataForm.orderDate"
                type="date"
                value-format="yyyy-MM-dd"
                placeholder="订单日期">
            </el-date-picker>
          </el-form-item>
        </div>

        <div class="four">
          <el-form-item label="交货日期" prop="deliveryDate">
            <el-date-picker
                v-model="dataForm.deliveryDate"
                type="date"
                value-format="yyyy-MM-dd"
                placeholder="交货日期">
            </el-date-picker>
          </el-form-item>
        </div>
      </div>

      <div class="container">
        <div class="four">
          <el-form-item label="产品品名" prop="partId">
            <part-number-component v-model="dataForm.partId" placeholder="产品品名"></part-number-component>
          </el-form-item>
        </div>

        <div class="four">
          <el-form-item label="订单数量" prop="orderQuantity">
            <el-input v-model="dataForm.orderQuantity" placeholder="订单数量"></el-input>
          </el-form-item>
        </div>

        <div class="four">
          <el-form-item label="包装箱数" prop="numberOfBoxes">
            <el-input v-model="dataForm.numberOfBoxes" placeholder="包装箱数"></el-input>
          </el-form-item>
        </div>

        <div class="four">
          <el-form-item label="纸箱尺寸" prop="cartonId">
            <el-input v-model="dataForm.cartonId" placeholder="纸箱尺寸"></el-input>
          </el-form-item>
        </div>
      </div>

      <div class="container">
        <div class="four">
          <el-form-item label="单重" prop="singleWeight">
            <el-input v-model="dataForm.singleWeight" placeholder="单重"></el-input>
          </el-form-item>
        </div>

        <div class="four">
          <el-form-item label="取样数量" prop="numberOfSamples">
            <el-input v-model="dataForm.numberOfSamples" placeholder="取样数量"></el-input>
          </el-form-item>
        </div>

        <div class="four">
          <el-form-item label="进仓数量" prop="intoTheQuantity">
            <el-input v-model="dataForm.intoTheQuantity" placeholder="进仓数量"></el-input>
          </el-form-item>
        </div>

        <div class="four">
          <el-form-item label="纸箱外观" prop="cartonAppearance">
            <ren-radio-group  v-model="dataForm.cartonAppearance" dict-type="ok_ng"></ren-radio-group>
          </el-form-item>
        </div>
      </div>

      <div class="container">
        <div class="four">
          <el-form-item label="称量员" prop="weighers">
            <employee-component v-model="dataForm.weighers" placeholder="称量员"></employee-component>
          </el-form-item>
        </div>

        <div class="four">
          <el-form-item label="包装员" prop="packers">
            <employee-component v-model="dataForm.packers" placeholder="包装员"></employee-component>
          </el-form-item>
        </div>
        <el-form-item label="外发员工姓名" prop="outgoingName">
          <el-input v-model="dataForm.outgoingName" placeholder="外发员工姓名"></el-input>
        </el-form-item>

        <div class="four">
          <el-form-item label="确认人" prop="confirmPerson">
            <employee-component v-model="dataForm.confirmPerson" placeholder="确认人"></employee-component>
          </el-form-item>
        </div>

        <div class="four">
          <el-form-item label="包装日期" prop="packingDate">
            <el-date-picker
                v-model="dataForm.packingDate"
                type="date"
                value-format="yyyy-MM-dd"
                placeholder="包装日期">
            </el-date-picker>
          </el-form-item>
        </div>
      </div>
      <el-form-item label="备注" prop="remark">
        <el-input v-model="dataForm.remark" placeholder="备注"></el-input>
      </el-form-item>
      <Wrapdetails :label-display="false"></Wrapdetails>

    </el-form>
    <template slot="footer">
      <el-button @click="visible = false">{{ $t('cancel') }}</el-button>
      <el-button type="primary" @click="dataFormSubmitHandle()">{{ $t('confirm') }}</el-button>
    </template>
    </div>
  </el-card>-->
  <el-dialog :visible.sync="visible" :title="!dataForm.id ? '新增包装信息' : '修改包装信息'" :close-on-click-modal="false" :close-on-press-escape="false" append-to-body>
    <el-form :model="dataForm" :rules="dataRule" ref="dataForm" @keyup.enter.native="dataFormSubmitHandle()" :label-width="$i18n.locale === 'en-US' ? '120px' : '80px'">
      <div class="container">
        <div>
          <el-form-item label="包装日期" prop="packingDate">
            <el-date-picker
                v-model="dataForm.packingDate"
                type="date"
                value-format="yyyy-MM-dd"
                placeholder="包装日期">
            </el-date-picker>
          </el-form-item>
        </div>
        <div>
          <el-form-item label="包装员" prop="packers">
            <employee-component v-model="dataForm.packers" :departments="departments" placeholder="包装员"></employee-component>
          </el-form-item>
        </div>
<!--        <div>-->
<!--          <el-form-item label="外发员工姓名" prop="outgoingName">-->
<!--            <el-input v-model="dataForm.outgoingName"  placeholder="外发员工姓名"></el-input>-->
<!--          </el-form-item>-->
<!--        </div>-->
      </div>
      <div class="container">
        <div>
          <el-form-item label="次批号" prop="subBatchId">
            <batch-under-component v-model="dataForm.subBatchId" @input="value => getPackInfo(value)" @batchSubBatchData="data => truncateString(data,'subBatchNumber')"></batch-under-component>
          </el-form-item>
        </div>
        <div>
          <el-form-item label="次批号" prop="subBatchNumber">
            <span class="font_size">{{dataForm.subBatchNumber}}</span>
          </el-form-item>
        </div>
        <div style="margin-left: 13px">
          <el-form-item label="包装规格">
            <el-select v-model="dataForm.cartonId" placeholder="请选择" style="width: 350px">
              <el-option
                  v-for="item in packInfoList"
                  :key="item.id"
                  :label="'PE带: ' + item.peBagSize + '    纸箱: ' + item.cartonSize"
                  :value="item.id"
                  style="width: 350px">
              </el-option>
            </el-select>
          </el-form-item>
        </div>
      </div>
      <div class="container">

      </div>
      <div class="container">
        <div>
          <el-form-item label="称量员" prop="weighers">
            <employee-component v-model="dataForm.weighers" :departments="departments" placeholder="称量员"></employee-component>
          </el-form-item>
        </div>
        <div>
          <el-form-item label="封箱员">
            <employee-component v-model="dataForm.sealer" :departments="departments" placeholder="封箱员"></employee-component>
          </el-form-item>
        </div>
        <div>
          <el-form-item label="取样数量" prop="numberOfSamples">
            <el-input-number :controls="false" v-model="dataForm.numberOfSamples" placeholder="取样数量" style="width: 230px"></el-input-number>
          </el-form-item>
        </div>
      </div>
      <div class="container">
        <div>
          <el-form-item label="单重" prop="singleWeight">
            <el-input v-model="dataForm.singleWeight" placeholder="单重" style="width: 230px"></el-input>
          </el-form-item>
        </div>
        <div>
          <el-form-item label="数量" prop="quantity">
            <el-input-number :controls="false" v-model="dataForm.quantity" placeholder="数量" style="width: 230px"></el-input-number>
          </el-form-item>
        </div>
        <div>
          <el-form-item label="余数" prop="remainder">
            <el-input-number :controls="false" v-model="dataForm.remainder" placeholder="余数" style="width: 230px"></el-input-number>
          </el-form-item>
        </div>
      </div>
      <div class="container">
        <div>
          <el-form-item label="进仓数量" prop="intoTheQuantity">
            <el-input v-model="dataForm.intoTheQuantity" placeholder="进仓数量"></el-input>
          </el-form-item>
        </div>
        <div>
          <el-form-item label="纸箱外观" prop="cartonAppearance">
            <ren-radio-group  v-model="dataForm.cartonAppearance" dict-type="ok_ng"></ren-radio-group>
          </el-form-item>
        </div>
        <div>
          <el-form-item label="确认人" prop="confirmPerson">
            <employee-component v-model="dataForm.confirmPerson" :departments="departments" placeholder="确认人"></employee-component>
          </el-form-item>
        </div>
      </div>
      <el-form-item label="备注" prop="remark">
        <el-input v-model="dataForm.remark" placeholder="备注"></el-input>
      </el-form-item>
    </el-form>
    <template slot="footer">
      <el-button @click="closeDialog">{{ $t('cancel') }}</el-button>
      <el-button type="primary" @click="dataFormSubmitHandle()">{{ $t('confirm') }}</el-button>
    </template>
  </el-dialog>
</template>

<script>
import debounce from 'lodash/debounce'
import mixinViewModule from '@/mixins/view-module'
import Wrapdetails from "./wrapdetails.vue";
export default {
  mixins:[mixinViewModule],
 data () {
    return {
      visible: false,
      //创建该页面时，不调用数据查询接口
      mixinViewModuleOptions: {
        createdIsNeed: false,
      },
      //固定部门
      departments:['1641701552458743810'],
      dataForm: {
        id: '',
        customerId: '',
        partId: '',
        subBatchId:'',
        subBatchNumber:'',
        customerOrderId: '',
        orderDate: '',
        deliveryDate: '',
        weighers: '',
        orderQuantity: 0,
        numberOfBoxes: 0,
        cartonId: '',
        numberOfSamples: 0,
        singleWeight: '',
        packers: '',
        sealer:'',
        outgoingName: '',
        quantity: 0,
        remainder: 0,
        intoTheQuantity: 0,
        cartonAppearance: 0,
        confirmPerson: '',
        packingDate: this.formatTimes(new Date()),
        remark: '',
        disabled: '',
        creator: '',
        createDate: '',
        updater: '',
        updateDate: ''
      },
      packInfoList:[]
    }
  },
  components:{
    Wrapdetails
  },
  computed: {
    dataRule () {
      return {
        partId: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          customerOrderId: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        customerId: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        orderQuantity: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        singleWeight: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        subBatchId: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
      }
    }
  },
  methods: {
    truncateString(data,propertyName) {
      let label = data.label
      const index = label.indexOf('(');
      this.dataForm[propertyName] = label.substring(0, index);
    },
    init () {
      this.visible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].resetFields()
        if (this.dataForm.id) {
          this.getInfo()
        }
      })
    },
    closeDialog(){
      this.visible = false
      this.packInfoList = [];
    },
    // 获取信息
    getInfo () {
      this.$http.get(`/packing/wrap/${this.dataForm.id}`).then(({ data: res }) => {
        if (res.code !== 0) {
          return this.$message.error(res.msg)
        }
        this.dataForm = {
          ...this.dataForm,
          ...res.data
        }
      }).catch(() => {})
    },
    getPackInfo(value){
        console.log('value:' + value)
        this.$http.get(`/packing/packagingstandards/getInfoBySubBatchId?subBatchId=${value}`).then(({ data: res}) => {
          if (res.code !== 0) {
            return this.$message.error(res.msg)
          }
          this.packInfoList = res.data
        })
    },
    // 表单提交
    dataFormSubmitHandle: debounce(function () {
      this.$refs['dataForm'].validate((valid) => {
        if (!valid) {
          return false
        }
        this.$http[!this.dataForm.id ? 'post' : 'put']('/packing/wrap/', this.dataForm).then(({ data: res }) => {
          if (res.code !== 0) {
            return this.$message.error(res.msg)
          }
          this.$message({
            message: this.$t('prompt.success'),
            type: 'success',
            duration: 500,
            onClose: () => {
              this.visible = false
              this.$emit('refreshDataList')
            }
          })
        }).catch(() => {})
      })
    }, 1000, { 'leading': true, 'trailing': false })
  }
}
</script>
