<template>
  <el-card shadow="never" class="aui-card--fill">
    <div class="mod-packing__packagingguidance}">
      <el-form :inline="true" :model="dataForm" @keyup.enter.native="getDataList()">
        <el-form-item>
          <el-input v-model="dataForm.id" placeholder="id" clearable></el-input>
        </el-form-item>
        <el-form-item>
          <el-button @click="getDataList()">{{ $t('query') }}</el-button>
        </el-form-item>
        <el-form-item>
          <el-button type="info" @click="exportHandle()">{{ $t('export') }}</el-button>
        </el-form-item>
        <el-form-item>
          <el-button v-if="$hasPermission('packing:packagingguidance:export')" type="info" @click="exportTemplateHandle()">
            {{ $t('exportTemplate') }}
          </el-button>
        </el-form-item>
        <el-form-item>
          <el-tooltip content="下载模板填入信息后导入" placement="top">
            <el-upload
                    v-if="$hasPermission('packing:packagingguidance:save')"
                    class="upload-demo"
                    :action="mixinViewModuleOptions.addBatchUrl"
                    :headers="headers"
                    :multiple="false"
                    :show-file-list="false"
                    :file-list="fileList"
                    :before-upload="beforeUpload"
                    :on-success="resultHandle"
                    :on-change="handleChange"
                    accept="'.xlsx','.xls'">
              <el-button type="success">{{ $t('addBatch') }}</el-button>
            </el-upload>
          </el-tooltip>
          <el-button type="text" v-show="unsuccessfulNameList" @click="dialogVisible = true">导入失败列表</el-button>
        </el-form-item>
        <el-form-item>
          <el-button v-if="$hasPermission('packing:packagingguidance:save')" type="primary" @click="addOrUpdateHandle()">{{ $t('add') }}</el-button>
        </el-form-item>
        <el-form-item>
          <el-button v-if="$hasPermission('packing:packagingguidance:delete')" type="danger" @click="deleteHandle()">{{ $t('deleteBatch') }}</el-button>
        </el-form-item>
      </el-form>
      <el-table v-loading="dataListLoading" :data="dataList" border @selection-change="dataListSelectionChangeHandle" style="width: 100%;">
        <el-table-column type="selection" header-align="center" align="center" width="50"></el-table-column>
        <el-table-column prop="partId" label="部品id" header-align="center" align="center"></el-table-column>
        <el-table-column prop="componentName" label="部品名称" header-align="center" align="center"></el-table-column>
        <el-table-column prop="sysDeptId" label="制定部门" header-align="center" align="center"></el-table-column>
        <el-table-column prop="quantityPerBox" label="每箱数量" header-align="center" align="center"></el-table-column>
        <el-table-column prop="materialQuality" label="材质" header-align="center" align="center"></el-table-column>
        <el-table-column prop="boxRecycling" label="箱子回收" header-align="center" align="center"></el-table-column>
        <el-table-column prop="documentNumber" label="文件编号" header-align="center" align="center"></el-table-column>
        <el-table-column prop="cartonId" label="纸箱规格/材质" header-align="center" align="center"></el-table-column>
        <el-table-column prop="turnoverBoxId" label="周转箱规格/材质" header-align="center" align="center"></el-table-column>
        <el-table-column prop="partitionBoardId" label="隔板规格/材质" header-align="center" align="center"></el-table-column>
        <el-table-column prop="plasticBagId" label="PE袋规格/材质" header-align="center" align="center"></el-table-column>
        <el-table-column prop="other" label="其他规格/材质" header-align="center" align="center"></el-table-column>
        <el-table-column prop="innerBoxStyle" label="内箱式样" header-align="center" align="center"></el-table-column>
        <el-table-column prop="quantityPerBag" label="多少pcs/袋" header-align="center" align="center"></el-table-column>
        <el-table-column prop="productUnitWeight" label="产品单重(kg)" header-align="center" align="center"></el-table-column>
        <el-table-column prop="emptyContainerWeight" label="空箱重(kg)" header-align="center" align="center"></el-table-column>
        <el-table-column prop="drossWeight" label="毛重(kg)" header-align="center" align="center"></el-table-column>
        <el-table-column prop="fullContainerQuantity" label="整箱数量(PSC)" header-align="center" align="center"></el-table-column>
        <el-table-column prop="innerLabelNumber" label="内标签" header-align="center" align="center"></el-table-column>
        <el-table-column prop="outerLabelNumber" label="外标签" header-align="center" align="center"></el-table-column>
        <el-table-column prop="packagingProcess" label="包装过程" header-align="center" align="center"></el-table-column>
        <el-table-column prop="innerLabelId" label="内标签id" header-align="center" align="center"></el-table-column>
        <el-table-column prop="outerLabelId" label="外标签id" header-align="center" align="center"></el-table-column>
        <el-table-column prop="revision" label="版次" header-align="center" align="center"></el-table-column>
        <el-table-column prop="releaseTime" label="发布时间" header-align="center" align="center"></el-table-column>
        <el-table-column prop="revisedContent" label="改定内容" header-align="center" align="center"></el-table-column>
        <el-table-column prop="remark" label="备注" header-align="center" align="center"></el-table-column>
        <el-table-column :label="$t('handle')" fixed="right" header-align="center" align="center" width="150">
          <template slot-scope="scope">
            <el-dropdown>
              <el-button type="primary">
                功能<i class="el-icon-arrow-down el-icon--right"></i>
              </el-button>
              <el-dropdown-menu slot="dropdown">
                <el-dropdown-item>
                  <el-button v-if="$hasPermission('packing:packagingguidance:update')" type="text" size="small" @click="addOrUpdateHandle(scope.row.id)">{{ $t('update') }}</el-button>
                </el-dropdown-item>
                <el-dropdown-item>
                  <el-button v-if="$hasPermission('packing:packagingguidance:delete')" type="text" size="small" @click="deleteHandle(scope.row.id)">{{ $t('delete') }}</el-button>
                </el-dropdown-item>
              </el-dropdown-menu>
            </el-dropdown>
          </template>
        </el-table-column>
      </el-table>
      <el-pagination
        :current-page="page"
        :page-sizes="[10, 20, 50, 100]"
        :page-size="limit"
        :total="total"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="pageSizeChangeHandle"
        @current-change="pageCurrentChangeHandle">
      </el-pagination>
      <!-- 弹窗, 新增 / 修改 -->
      <add-or-update v-if="addOrUpdateVisible" ref="addOrUpdate" @refreshDataList="getDataList"></add-or-update>
    </div>
  </el-card>
</template>

<script>
import mixinViewModule from '@/mixins/view-module'
import AddOrUpdate from './packagingguidance-add-or-update'
import Cookies from "js-cookie";
export default {
  mixins: [mixinViewModule],
  data () {
    return {
      message:'',
      headers: {'token': Cookies.get('token')},
      fileList: [{name: '', url: ''}],
      unsuccessfulNameList: '',//  导入失败名称列表
      mixinViewModuleOptions: {
        getDataListURL: '/packing/packagingguidance/page',
        getDataListIsPage: true,
        exportURL: '/packing/packagingguidance/export',
        deleteURL: '/packing/packagingguidance',
        deleteIsBatch: true,
        exportTemplateURL: 'packing/packagingguidance/export/template',
        addBatchUrl: window.SITE_CONFIG['apiURL'] + 'packing/packagingguidance/batchSave',
      },
      dataForm: {
        id: '',
        paramStr: '',
      }
    }
  },
  components: {
    AddOrUpdate
  },
  methods:{
    beforeUpload(file) {
      this.message = this.$message({
        message: '上传中，请勿操作',
        center: true,
        duration: 0,
      });
    },
    handleChange(file, fileList) {
      this.fileList = fileList.slice(-3);

    },
    resultHandle(res) {
      if (res.code !== 0) {
      return this.$message.error(res.msg)
      }
      this.$message({
        message: this.$t('prompt.success'),
        type: 'success',
        duration: 500,
        onClose: () => {
          if (res.data) {
            this.unsuccessfulNameList = res.data
          }
          this.visible = false
          this.query()
        }
      })
    },
  }
}
</script>
