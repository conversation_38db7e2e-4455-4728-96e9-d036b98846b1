<template>
  <el-dialog :visible.sync="visible" :title="!dataForm.id ? $t('add') : $t('update')" :close-on-click-modal="false" :close-on-press-escape="false" append-to-body>
    <el-form :model="dataForm" :rules="dataRule" ref="dataForm" @keyup.enter.native="dataFormSubmitHandle()" :label-width="$i18n.locale === 'en-US' ? '120px' : '120px'">
      <div class="container">
                                                <el-form-item label="供应商" prop="supplierId">
              <el-input v-model="dataForm.supplierId" placeholder="供应商"></el-input>
            </el-form-item>
                                        <el-form-item label="部门" prop="dept">
              <el-input v-model="dataForm.dept" placeholder="部门"></el-input>
            </el-form-item>
                                        <el-form-item label="联络人姓名" prop="contactName">
              <el-input v-model="dataForm.contactName" placeholder="联络人姓名"></el-input>
            </el-form-item>
                                        <el-form-item label="电话" prop="phoneNumber">
              <el-input v-model="dataForm.phoneNumber" placeholder="电话"></el-input>
            </el-form-item>
                                        <el-form-item label="传真电话" prop="ototelephony">
              <el-input v-model="dataForm.ototelephony" placeholder="传真电话"></el-input>
            </el-form-item>
                                        <el-form-item label="邮箱" prop="email">
              <el-input v-model="dataForm.email" placeholder="邮箱"></el-input>
            </el-form-item>
                                        <el-form-item label="通讯软件" prop="bitcom">
              <el-input v-model="dataForm.bitcom" placeholder="通讯软件"></el-input>
            </el-form-item>
                                        <el-form-item label="所在地区" prop="area">
              <el-input v-model="dataForm.area" placeholder="所在地区"></el-input>
            </el-form-item>
                                        <el-form-item label="职务" prop="post">
              <el-input v-model="dataForm.post" placeholder="职务"></el-input>
            </el-form-item>
                                        <el-form-item label="在职状态" prop="currentState">
              <el-input v-model="dataForm.currentState" placeholder="在职状态"></el-input>
            </el-form-item>
                                        <el-form-item label="对应事项" prop="correspondence">
              <el-input v-model="dataForm.correspondence" placeholder="对应事项"></el-input>
            </el-form-item>
                                        <el-form-item label="联络周期" prop="contactCycle">
              <el-input v-model="dataForm.contactCycle" placeholder="联络周期"></el-input>
            </el-form-item>
                                        <el-form-item label="联络时段" prop="contactTime">
              <el-input v-model="dataForm.contactTime" placeholder="联络时段"></el-input>
            </el-form-item>
                                        <el-form-item label="个人喜好" prop="personalPreference">
              <el-input v-model="dataForm.personalPreference" placeholder="个人喜好"></el-input>
            </el-form-item>
                                        <el-form-item label="相片" prop="photo">
              <el-input v-model="dataForm.photo" placeholder="相片"></el-input>
            </el-form-item>
                                        <el-form-item label="备注" prop="remark">
              <el-input v-model="dataForm.remark" placeholder="备注"></el-input>
            </el-form-item>
                                                                                                                  </div>
    </el-form>
    <template slot="footer">
      <el-button @click="visible = false">{{ $t('cancel') }}</el-button>
      <el-button type="primary" @click="dataFormSubmitHandle()">{{ $t('confirm') }}</el-button>
    </template>
  </el-dialog>
</template>

<script>
import debounce from 'lodash/debounce'
import mixinViewModule from '@/mixins/view-module'
export default {
  mixins:[mixinViewModule],
  data () {
    return {
      visible: false,
      //创建该页面时，不调用数据查询接口
      mixinViewModuleOptions: {
        createdIsNeed: false,
      },
      dataForm: {
        id: '',
        supplierId: '',
        dept: '',
        contactName: '',
        phoneNumber: '',
        ototelephony: '',
        email: '',
        bitcom: '',
        area: '',
        post: '',
        currentState: '',
        correspondence: '',
        contactCycle: '',
        contactTime: '',
        personalPreference: '',
        photo: '',
        remark: '',
        disabled: '',
        creator: '',
        createDate: '',
        updater: '',
        updateDate: ''
      }
    }
  },
  computed: {
    dataRule () {
      return {
          id: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          supplierId: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          dept: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          contactName: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          phoneNumber: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          ototelephony: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          email: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          bitcom: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          area: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          post: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          currentState: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          correspondence: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          contactCycle: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          contactTime: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          personalPreference: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          photo: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          remark: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
                }
    }
  },
  methods: {
    init () {
      this.visible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].resetFields()
        if (this.dataForm.id) {
          this.getInfo()
        }
      })
    },
    // 获取信息
    getInfo () {
      this.$http.get(`/material/suppliercontactbase/${this.dataForm.id}`).then(({ data: res }) => {
        if (res.code !== 0) {
          return this.$message.error(res.msg)
        }
        this.dataForm = {
          ...this.dataForm,
          ...res.data
        }
      }).catch(() => {})
    },
    // 表单提交
    dataFormSubmitHandle: debounce(function () {
      this.$refs['dataForm'].validate((valid) => {
        if (!valid) {
          return false
        }
        let message = '';
        if(this.dataForm.id){
          message = '此操作将修改该数据'
        }else {
          message = '此操作将新增一条数据'
        }
        this.$confirm(message+', 是否继续?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
          center: true
        }).then(() => {
          this.$http[!this.dataForm.id ? 'post' : 'put']('/material/suppliercontactbase/', this.dataForm).then(({ data: res }) => {
            if (res.code !== 0) {
            return this.$message.error(res.msg)
            }
            this.$message({
          message: this.$t('prompt.success'),
            type: 'success',
                    duration: 500,
                    onClose: () => {
              this.visible = false
              this.$emit('refreshDataList')
            }
          })
          }).catch(() => {})
        }).catch(() => {
          this.$message({
            type: 'info',
            message: '已取消'
          });
        });
      })
    }, 1000, { 'leading': true, 'trailing': false })
  }
}
</script>
