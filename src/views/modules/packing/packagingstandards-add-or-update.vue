<template>
  <el-dialog :visible.sync="visible" :title="!dataForm.id ? $t('add') : $t('update')" :close-on-click-modal="false"
             :close-on-press-escape="false" append-to-body>
    <el-form :model="dataForm" :rules="dataRule" ref="dataForm" @keyup.enter.native="dataFormSubmitHandle()"
             :label-width="$i18n.locale === 'en-US' ? '120px' : '120px'">
      <div class="container">
        <el-form-item label="客户代码" prop="customerId">
          <el-select v-model="dataForm.customerId" filterable clearable>
            <el-option v-for="(item,index) in customerOption"
                       :key="index"
                       :label="item.label"
                       :value="item.value">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="品号" prop="partId">
          <el-select v-model="dataForm.partId" filterable clearable>
            <el-option v-for="(item,index) in partOption"
                       :key="index"
                       :label="item.label"
                       :value="item.value">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="单重" prop="single">
          <el-input v-model="dataForm.single">
            <template slot="append">g</template>
          </el-input>
        </el-form-item>
        <el-form-item label="每袋数量" prop="quantityPerBag">
          <el-input v-model="dataForm.quantityPerBag" placeholder="每袋数量">
            <template slot="append">PCS</template>
          </el-input>
        </el-form-item>
        <el-form-item label="每箱数量" prop="amountPerBox">
          <el-input v-model="dataForm.amountPerBox" placeholder="每箱数量">
            <template slot="append">PCS</template>
          </el-input>
        </el-form-item>
        <el-form-item label="每箱重量" prop="weightPerBox">
          <el-input v-model="dataForm.weightPerBox" placeholder="每箱重量">
            <template slot="append">KG</template>
          </el-input>
        </el-form-item>
        <el-form-item label="PE袋规格" prop="peBagSize">
          <el-input v-model="dataForm.peBagSize" placeholder="PE袋规格">
            <template slot="append">mm</template>
          </el-input>
        </el-form-item>
        <el-form-item label="纸箱规格" prop="cartonSize">
          <el-input v-model="dataForm.cartonSize" placeholder="纸箱规格">
            <template slot="append">mm</template>
          </el-input>
        </el-form-item>
        <el-form-item label="备注">
          <el-input v-model="dataForm.remark" placeholder="备注"></el-input>
        </el-form-item>
      </div>
    </el-form>
    <template slot="footer">
      <el-button @click="visible = false">{{ $t('cancel') }}</el-button>
      <el-button type="primary" @click="dataFormSubmitHandle()">{{ $t('confirm') }}</el-button>
    </template>
  </el-dialog>
</template>

<script>
import debounce from 'lodash/debounce'
import mixinViewModule from '@/mixins/view-module'

export default {
  mixins: [mixinViewModule],
  data() {
    return {
      visible: false,
      //创建该页面时，不调用数据查询接口
      mixinViewModuleOptions: {
        createdIsNeed: false,
      },
      dataForm: {
        id: '',
        customerId: '',
        partId: '',
        single: '',
        quantityPerBag: '',
        amountPerBox: '',
        weightPerBox: '',
        peBagSize: '',
        cartonSize: '',
        remark: '',
        disabled: '',
        creator: '',
        createDate: '',
        updater: '',
        updateDate: ''
      },
      customerOption: [],
      partOption: [],
    }
  },
  computed: {
    dataRule() {
      return {
        id: [
          {required: true, message: this.$t('validate.required'), trigger: 'blur'}
        ],
        customerId: [
          {required: true, message: this.$t('validate.required'), trigger: 'blur'}
        ],
        partId: [
          {required: true, message: this.$t('validate.required'), trigger: 'blur'}
        ],
        single: [
          {required: true, message: this.$t('validate.required'), trigger: 'blur'}
        ],
        quantityPerBag: [
          {required: true, message: this.$t('validate.required'), trigger: 'blur'}
        ],
        amountPerBox: [
          {required: true, message: this.$t('validate.required'), trigger: 'blur'}
        ],
        weightPerBox: [
          {required: true, message: this.$t('validate.required'), trigger: 'blur'}
        ],
        peBagSize: [
          {required: true, message: this.$t('validate.required'), trigger: 'blur'}
        ],
        cartonSize: [
          {required: true, message: this.$t('validate.required'), trigger: 'blur'}
        ],
        remark: [
          {required: true, message: this.$t('validate.required'), trigger: 'blur'}
        ],
      }
    }
  },
  methods: {
    init() {
      this.visible = true
      this.$nextTick(() => {
        this.getCustomerList()
        this.getPartOption()
        this.$refs['dataForm'].resetFields()
        if (this.dataForm.id) {
          this.getInfo()
        }
      })
    },
    //获取客户列表
    getCustomerList() {
      this.$http.get(`customer/customer/selectCode`).then(({data: res}) => {
        if (res.code !== 0) {
          return this.$message.error(res.msg)
        }
        this.customerOption = res.data.map((obj) => {
          return {
            label: obj.code,
            value: obj.id,
            data: obj
          }
        })
      })
    },
    //获取品号列表
    getPartOption() {
      this.$http.get(`fabricate/part/getPartListByDesignation`).then(({data: res}) => {
        if (res.code !== 0) {
          return this.$message.error(res.msg)
        }
        this.partOption = res.data.map((obj) => {
          return {
            label: obj.designation,
            value: obj.id,
            data: obj
          }
        })
      })
    },
    // 获取信息
    getInfo() {
      this.$http.get(`/packing/packagingstandards/${this.dataForm.id}`).then(({data: res}) => {
        if (res.code !== 0) {
          return this.$message.error(res.msg)
        }
        this.dataForm = {
          ...this.dataForm,
          ...res.data
        }
      }).catch(() => {
      })
    },
    // 表单提交
    dataFormSubmitHandle: debounce(function () {
      this.$refs['dataForm'].validate((valid) => {
        if (!valid) {
          return false
        }
        let message = '';
        if (this.dataForm.id) {
          message = '此操作将修改该数据'
        } else {
          message = '此操作将新增一条数据'
        }
        this.$confirm(message + ', 是否继续?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
          center: true
        }).then(() => {
          this.$http[!this.dataForm.id ? 'post' : 'put']('/packing/packagingstandards/', this.dataForm).then(({data: res}) => {
            if (res.code !== 0) {
              return this.$message.error(res.msg)
            }
            this.$message({
              message: this.$t('prompt.success'),
              type: 'success',
              duration: 500,
              onClose: () => {
                this.visible = false
                this.$emit('refreshDataList')
              }
            })
          }).catch(() => {
          })
        }).catch(() => {
          this.$message({
            type: 'info',
            message: '已取消'
          });
        });
      })
    }, 1000, {'leading': true, 'trailing': false})
  }
}
</script>
