<template>
  <el-dialog :visible.sync="visible" :title="!dataForm.id ? $t('add') : $t('update')" :close-on-click-modal="false" :close-on-press-escape="false" append-to-body>
    <el-form :model="dataForm" :rules="dataRule" ref="dataForm" @keyup.enter.native="dataFormSubmitHandle()" :label-width="$i18n.locale === 'en-US' ? '120px' : '80px'">
      <div class="container">
                                                <el-form-item label="假期类别" prop="vacationType">
                                                  <el-select v-model="dataForm.vacationType" placeholder="假期列表">
                                                    <el-option
                                                        v-for="item in vacationTypes"
                                                        :key="item.value"
                                                        :label="item.label"
                                                        :value="item.value">
                                                    </el-option>
                                                  </el-select>
<!--              <el-input v-model="dataForm.vacationType" placeholder="假期类别"></el-input>-->
            </el-form-item>
                                        <el-form-item label="部门" prop="department">
              <el-input v-model="dataForm.department" placeholder="部门" readonly></el-input>
            </el-form-item>
                                        <el-form-item label="职称" prop="title">
              <el-input v-model="dataForm.title" placeholder="职称" readonly></el-input>
            </el-form-item>
                                        <el-form-item label="工号" prop="workerNumber">
                                          <el-autocomplete
                                              popper-class="my-class"
                                              style="width: 200px;"

                                              v-model="dataForm.workerNumber"
                                              :fetch-suggestions="getLikeWorkerNumber"
                                              placement="bottom"
                                              placeholder="请输入工号"
                                              :trigger-on-focus="false"
                                              @select="orderSelect"
                                          ></el-autocomplete>
<!--              <el-input v-model="dataForm.workerNumber" placeholder="工号"></el-input>-->
            </el-form-item>
                                        <el-form-item label="姓" prop="surname">
              <el-input v-model="dataForm.surname" placeholder="姓" readonly></el-input>
            </el-form-item>
                                        <el-form-item label="名" prop="name">
              <el-input v-model="dataForm.name" placeholder="名" readonly></el-input>
            </el-form-item>
                                        <el-form-item label="请假日期" prop="leaveDate">
                                          <el-date-picker
                                              v-model="dataForm.leaveDate"
                                              type="date"
                                              format="yyyy 年 MM 月 dd 日"
                                              value-format="yyyy-MM-dd"
                                              placeholder="请假日期">
                                          </el-date-picker>
                                        </el-form-item>
        <el-form-item prop="leaveTime">
                                          <el-time-picker
                                              v-model="dataForm.leaveTime"
                                              :teleported="false"
                                              format="HH: mm"
                                              placeholder="请假时间"
                                              value-format="HH:mm"
                                              @change="selectEndTime"
                                              :disabled-minutes="disabledStartMinutes"
                                              :editable="false">
                                          </el-time-picker>
<!--              <el-input v-model="dataForm.leaveDate" placeholder="请假日期"></el-input>-->
            </el-form-item>
                                        <el-form-item label="请假开始日期" prop="startDate">
                                          <el-date-picker
                                              v-model="dataForm.startDate"
                                              type="date"
                                              format="yyyy 年 MM 月 dd 日"
                                              value-format="yyyy-MM-dd"
                                              placeholder="请假开始日期">
                                          </el-date-picker>
                                        </el-form-item>
        <el-form-item prop="startLeaveTime">
                                          <el-time-picker
                                              v-model="dataForm.startLeaveTime"
                                              :teleported="false"
                                              format="HH: mm"
                                              placeholder="请假开始时间"
                                              value-format="HH:mm"
                                              @change="selectEndTime"
                                              :disabled-minutes="disabledStartMinutes"
                                              :editable="false">
                                          </el-time-picker>
<!--              <el-input v-model="dataForm.startDate" placeholder="请假开始日期"></el-input>-->
            </el-form-item>
                                        <el-form-item label="请假结束日期" prop="endDate">
                                          <el-date-picker
                                              v-model="dataForm.endDate"
                                              type="date"
                                              format="yyyy 年 MM 月 dd 日"
                                              value-format="yyyy-MM-dd"
                                              placeholder="请假结束日期">
                                          </el-date-picker>
                                        </el-form-item>
        <el-form-item prop="endLeaveTime">
                                          <el-time-picker
                                              v-model="dataForm.endLeaveTime"
                                              :teleported="false"
                                              format="HH: mm"
                                              placeholder="请假结束时间"
                                              value-format="HH:mm"
                                              @change="selectEndTime"
                                              :disabled-minutes="disabledStartMinutes"
                                              :editable="false">
                                          </el-time-picker>
<!--              <el-input v-model="dataForm.endDate" placeholder="请假结束日期"></el-input>-->
            </el-form-item>

                                        <el-form-item label="总天数" prop="totalDays">
              <el-input v-model="dataForm.totalDays" placeholder="总天数"></el-input>
            </el-form-item>
        <el-form-item label="总小时数" prop="totalHours">
          <el-input v-model="dataForm.totalHours" placeholder="总小时数"></el-input>
        </el-form-item>
                                        <el-form-item label="代理人" prop="agentWorkerNumber">
                                          <el-autocomplete
                                              popper-class="my-class"
                                              style="width: 300px;"

                                              v-model="dataForm.agentWorkerNumber"
                                              :fetch-suggestions="getLikeWorkerNumber"
                                              placement="bottom"
                                              placeholder="请输入代理人工号"
                                              :trigger-on-focus="false"
                                              @select="agentSelect"
                                          ></el-autocomplete>
<!--              <el-input v-model="dataForm.agent" placeholder="代理人"></el-input>-->
            </el-form-item>
                                        <el-form-item label="备注" prop="remark">
              <el-input v-model="dataForm.remark" placeholder="备注"></el-input>
            </el-form-item>
<!--                                                                                                                                  <el-form-item label="部门ID" prop="deptId">
              <el-input v-model="dataForm.deptId" placeholder="部门ID"></el-input>
            </el-form-item>
                                        <el-form-item label="员工ID" prop="employeeId">
              <el-input v-model="dataForm.employeeId" placeholder="员工ID"></el-input>
            </el-form-item>
                                        <el-form-item label="用户ID" prop="userId">
              <el-input v-model="dataForm.userId" placeholder="用户ID"></el-input>
            </el-form-item>-->
                        </div>
    </el-form>
    <template slot="footer">
      <el-button @click="visible = false">{{ $t('cancel') }}</el-button>
      <el-button type="primary" @click="dataFormSubmitHandle()">{{ $t('confirm') }}</el-button>
    </template>
  </el-dialog>
</template>

<script>
import debounce from 'lodash/debounce'
import mixinViewModule from '@/mixins/view-module'
/*import VueCtkDateTimePicker from 'vue-ctk-date-time-picker';
import 'vue-ctk-date-time-picker/dist/vue-ctk-date-time-picker.css';*/
export default {
  mixins:[mixinViewModule],
  data () {
    return {
      visible: false,
      //创建该页面时，不调用数据查询接口
      mixinViewModuleOptions: {
        createdIsNeed: false,
      },
      dataForm: {
        leaveTime:'',
        startLeaveTime:'',
        endLeaveTime:'',
        agentWorkerNumber:'',
        // deptId:'',
        id: '',
        vacationType: '',
        department: '',
        title: '',
        workerNumber: '',
        surname: '',
        name: '',
        leaveDate: '',
        startDate: '',
        endDate: '',
        totalHours: '',
        totalDays: '',
        agent: '',
        remark: '',
        creator: '',
        createDate: '',
        updater: '',
        updateDate: '',
        disabled: '',
        deptId: '',
        employeeId: '',
        userId: ''
      },
      orderTypes: [{
        value: 0,
        label: '生产订单'
      }, {
        value: 1,
        label: '预示订单'
      }],
      vacationTypes: [{
        value: 0,
        label: '事假'
      }, {
        value: 1,
        label: '病假'
      }, {
        value: 2,
        label: '迟到/早退'
      }, {
        value: 3,
        label: '未打卡'
      }, {
        value: 4,
        label: '特休'
      }, {
        value: 5,
        label: '婚假'
      }, {
        value: 6,
        label: '产假'
      }, {
        value: 7,
        label: '丧假'
      }, {
        value: 8,
        label: '年假'
      }, {
        value: 9,
        label: '公假'
      }, {
        value: 10,
        label: '会议'
      }, {
        value: 11,
        label: '培训'
      }, {
        value: 12,
        label: '正常请假'
      }, {
        value: 13,
        label: '紧急请假'
      }],
    }
  },
  computed: {
    dataRule () {
      return {
          id: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          vacationType: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          /*department: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],*/
          /*title: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],*/
          workerNumber: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          /*surname: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          name: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],*/
          leaveDate: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        leaveTime: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          startDate: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        startLeaveTime: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          endDate: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        endLeaveTime: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          totalHours: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          totalDays: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        agentWorkerNumber: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          /*remark: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],*/
                    deptId: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          employeeId: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          userId: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ]
      }
    }
  },
  methods: {
    init () {
      this.visible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].resetFields()
        if (this.dataForm.id) {
          this.getInfo()
        }
      })
    },
    // 获取信息
    getInfo () {
      this.$http.get(`/packing/hrvacation/${this.dataForm.id}`).then(({ data: res }) => {
        if (res.code !== 0) {
          return this.$message.error(res.msg)
        }
        this.dataForm = {
          ...this.dataForm,
          ...res.data
        }
      }).catch(() => {})
    },
    //  输出选择日志
    orderSelect(item) {
      this.dataForm.workerNumber=item.workerNumber
      this.dataForm.department=item.department
      this.dataForm.title=item.position
      this.dataForm.surname=item.surname
      this.dataForm.name=item.name
      this.dataForm.deptId=item.deptId

    },
    agentSelect(item){
      this.dataForm.agent=item.agentId
      this.dataForm.agentWorkerNumber=item.agentNumber
    },
    // 获取订单列表
    getLikeWorkerNumber(employeeId,cb){
      this.$http.get(`sys/user/selectEmployeeIdLikeWorkerNumber/`+ employeeId).then(({data: res}) => {
        console.log("res",res)
        if (res.code !== 0) {
          return this.$message.error(res.msg)
        }
        const list = res.data.map((obj) => {
          // console.log("obj",obj)
          return {
            // value:obj.trackingNumber+'('+obj.partNameCode+')',
            value:obj.username,
            workerNumber: obj.username,
            agentNumber: obj.username,
            department: obj.deptName,
            position:obj.position,
            surname: obj.surname,
            name:obj.name,
            deptId:obj.deptId,
            agentId:obj.id,
          }
        })
        this.timeout = setTimeout(() => {
          cb(list);
        }, 300 * Math.random());
      }).catch(() => {
      })
    },
    // 表单提交
    dataFormSubmitHandle: debounce(function () {
      this.$refs['dataForm'].validate((valid) => {
        if (!valid) {
          return false
        }
        let message = '';
        if(this.dataForm.id){
          message = '此操作将修改该数据'
        }else {
          message = '此操作将新增一条数据'
        }
        this.$confirm(message+', 是否继续?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
          center: true
        }).then(() => {
          this.$http[!this.dataForm.id ? 'post' : 'put']('/packing/hrvacation/', this.dataForm).then(({ data: res }) => {
            if (res.code !== 0) {
            return this.$message.error(res.msg)
            }
            this.$message({
          message: this.$t('prompt.success'),
            type: 'success',
                    duration: 500,
                    onClose: () => {
              this.visible = false
              this.$emit('refreshDataList')
            }
          })
          }).catch(() => {})
        }).catch(() => {
          this.$message({
            type: 'info',
            message: '已取消'
          });
        });
      })
    }, 1000, { 'leading': true, 'trailing': false })
  }
}
</script>
