<template>
  <el-dialog :visible.sync="visible" :title="!dataForm.id ? '新增资材供应商信息' : '修改资材供应商信息'" :close-on-click-modal="false" :close-on-press-escape="false" append-to-body>
    <el-form :model="dataForm" :rules="dataRule" ref="dataForm" @keyup.enter.native="dataFormSubmitHandle()" :label-width="$i18n.locale === 'en-US' ? '120px' : '120px'">
      <div class="container">
                                                <el-form-item label="供应商名称" prop="supplierName">
              <el-input v-model="dataForm.supplierName" placeholder="供应商名称"></el-input>
            </el-form-item>
                                        <el-form-item label="供应商代码" prop="supplierCode">
              <el-input v-model="dataForm.supplierCode" placeholder="供应商代码"></el-input>
            </el-form-item>
                                        <el-form-item label="中文名称" prop="chineseName">
              <el-input v-model="dataForm.chineseName" placeholder="中文名称"></el-input>
            </el-form-item>
                                        <el-form-item label="英文名称" prop="englishName">
              <el-input v-model="dataForm.englishName" placeholder="英文名称"></el-input>
            </el-form-item>
                                        <el-form-item label="供应商地址" prop="supplierAddress">
              <el-input v-model="dataForm.supplierAddress" placeholder="供应商地址"></el-input>
            </el-form-item>
                                        <el-form-item label="地图坐标" prop="mapCoordinates">
              <el-input v-model="dataForm.mapCoordinates" placeholder="地图坐标"></el-input>
            </el-form-item>
                                        <el-form-item label="与公司的距离" prop="distance">
              <el-input v-model="dataForm.distance" placeholder="与公司的距离"></el-input>
            </el-form-item>
                                        <el-form-item label="车程时间" prop="travelTime">
              <el-input v-model="dataForm.travelTime" placeholder="车程时间"></el-input>
            </el-form-item>
                                        <el-form-item label="电话" prop="phone">
              <el-input v-model="dataForm.phone" placeholder="电话"></el-input>
            </el-form-item>
                                        <el-form-item label="公司网址" prop="website">
              <el-input v-model="dataForm.website" placeholder="公司网址"></el-input>
            </el-form-item>
                                        <el-form-item label="公司营业执照上传" prop="companyLicense">
              <el-input v-model="dataForm.companyLicense" placeholder="公司营业执照上传"></el-input>
            </el-form-item>
                                        <el-form-item label="社会码" prop="socialCode">
              <el-input v-model="dataForm.socialCode" placeholder="社会码"></el-input>
            </el-form-item>
                                        <el-form-item label="公司营业项目" prop="companyBusinessProjects">
              <el-input v-model="dataForm.companyBusinessProjects" placeholder="公司营业项目"></el-input>
            </el-form-item>
                                        <el-form-item label="与本公司主要往来项目" prop="companyTransactions">
              <el-input v-model="dataForm.companyTransactions" placeholder="与本公司主要往来项目"></el-input>
            </el-form-item>
                                        <el-form-item label="未来发展预估" prop="futureDevelopmentEstimate">
              <el-input v-model="dataForm.futureDevelopmentEstimate" placeholder="未来发展预估"></el-input>
            </el-form-item>
                                        <el-form-item label="主要往来公司名称" prop="mainTradingCompany">
              <el-input v-model="dataForm.mainTradingCompany" placeholder="主要往来公司名称"></el-input>
            </el-form-item>
                                        <el-form-item label="供应商等级" prop="supplierLevel">
              <el-input v-model="dataForm.supplierLevel" placeholder="供应商等级"></el-input>
            </el-form-item>
                                        <el-form-item label="评定时间" prop="ratingTime">
              <el-input v-model="dataForm.ratingTime" placeholder="评定时间"></el-input>
            </el-form-item>
                                        <el-form-item label="采购需求及注意事项" prop="procurementRequirements">
              <el-input v-model="dataForm.procurementRequirements" placeholder="采购需求及注意事项"></el-input>
            </el-form-item>
                                        <el-form-item label="采购周期" prop="procurementCycle">
              <el-input v-model="dataForm.procurementCycle" placeholder="采购周期"></el-input>
            </el-form-item>
                                        <el-form-item label="对账周期" prop="reconciliationCycle">
              <el-input v-model="dataForm.reconciliationCycle" placeholder="对账周期"></el-input>
            </el-form-item>
                                        <el-form-item label="付款周期" prop="paymentCycle">
              <el-input v-model="dataForm.paymentCycle" placeholder="付款周期"></el-input>
            </el-form-item>
                                        <el-form-item label="主要货币" prop="mainCurrency">
              <el-input v-model="dataForm.mainCurrency" placeholder="主要货币"></el-input>
            </el-form-item>
                                        <el-form-item label="次要货币" prop="secondaryCurrency">
              <el-input v-model="dataForm.secondaryCurrency" placeholder="次要货币"></el-input>
            </el-form-item>
                                        <el-form-item label="登入时间" prop="logonTime">
              <el-input v-model="dataForm.logonTime" placeholder="登入时间"></el-input>
            </el-form-item>
                                        <el-form-item label="修改时间" prop="modificationTime">
              <el-input v-model="dataForm.modificationTime" placeholder="修改时间"></el-input>
            </el-form-item>
                                        <el-form-item label="作成" prop="sucess">
              <el-input v-model="dataForm.sucess" placeholder="作成"></el-input>
            </el-form-item>
                                        <el-form-item label="确认" prop="confirm">
              <el-input v-model="dataForm.confirm" placeholder="确认"></el-input>
            </el-form-item>
                                        <el-form-item label="备注" prop="notes">
              <el-input v-model="dataForm.notes" placeholder="备注"></el-input>
            </el-form-item>
                                                                                                                  </div>
    </el-form>
    <template slot="footer">
      <el-button @click="visible = false">{{ $t('cancel') }}</el-button>
      <el-button type="primary" @click="dataFormSubmitHandle()">{{ $t('confirm') }}</el-button>
    </template>
  </el-dialog>
</template>

<script>
import debounce from 'lodash/debounce'
import mixinViewModule from '@/mixins/view-module'
export default {
  mixins:[mixinViewModule],
  data () {
    return {
      visible: false,
      //创建该页面时，不调用数据查询接口
      mixinViewModuleOptions: {
        createdIsNeed: false,
      },
      dataForm: {
        id: '',
        supplierName: '',
        supplierCode: '',
        chineseName: '',
        englishName: '',
        supplierAddress: '',
        mapCoordinates: '',
        distance: '',
        travelTime: '',
        phone: '',
        website: '',
        companyLicense: '',
        socialCode: '',
        companyBusinessProjects: '',
        companyTransactions: '',
        futureDevelopmentEstimate: '',
        mainTradingCompany: '',
        supplierLevel: '',
        ratingTime: '',
        procurementRequirements: '',
        procurementCycle: '',
        reconciliationCycle: '',
        paymentCycle: '',
        mainCurrency: '',
        secondaryCurrency: '',
        logonTime: '',
        modificationTime: '',
        sucess: '',
        confirm: '',
        notes: '',
        disabled: '',
        creator: '',
        createDate: '',
        updater: '',
        updateDate: ''
      }
    }
  },
  computed: {
    dataRule () {
      return {
          id: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          supplierName: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          supplierCode: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          /*chineseName: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          englishName: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          supplierAddress: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          mapCoordinates: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          distance: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          travelTime: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          phone: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          website: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          companyLicense: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          socialCode: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          companyBusinessProjects: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          companyTransactions: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          futureDevelopmentEstimate: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          mainTradingCompany: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          supplierLevel: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          ratingTime: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          procurementRequirements: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          procurementCycle: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          reconciliationCycle: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          paymentCycle: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          mainCurrency: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          secondaryCurrency: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          logonTime: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          modificationTime: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          sucess: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          confirm: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          notes: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],*/
                }
    }
  },
  methods: {
    init () {
      this.visible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].resetFields()
        if (this.dataForm.id) {
          this.getInfo()
        }
      })
    },
    // 获取信息
    getInfo () {
      this.$http.get(`/packing/materialsupplierbase/${this.dataForm.id}`).then(({ data: res }) => {
        if (res.code !== 0) {
          return this.$message.error(res.msg)
        }
        this.dataForm = {
          ...this.dataForm,
          ...res.data
        }
      }).catch(() => {})
    },
    // 表单提交
    dataFormSubmitHandle: debounce(function () {
      this.$refs['dataForm'].validate((valid) => {
        if (!valid) {
          return false
        }
        let message = '';
        if(this.dataForm.id){
          message = '此操作将修改该数据'
        }else {
          message = '此操作将新增一条数据'
        }
        this.$confirm(message+', 是否继续?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
          center: true
        }).then(() => {
          this.$http[!this.dataForm.id ? 'post' : 'put']('/packing/materialsupplierbase/', this.dataForm).then(({ data: res }) => {
            if (res.code !== 0) {
            return this.$message.error(res.msg)
            }
            this.$message({
          message: this.$t('prompt.success'),
            type: 'success',
                    duration: 500,
                    onClose: () => {
              this.visible = false
              this.$emit('refreshDataList')
            }
          })
          }).catch(() => {})
        }).catch(() => {
          this.$message({
            type: 'info',
            message: '已取消'
          });
        });
      })
    }, 1000, { 'leading': true, 'trailing': false })
  }
}
</script>
