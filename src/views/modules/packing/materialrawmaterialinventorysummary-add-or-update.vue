<template>
  <el-dialog :visible.sync="visible" :title="!dataForm.id ? '新增资材库存信息' : '修改资材库存信息'" :close-on-click-modal="false" :close-on-press-escape="false" append-to-body>
    <el-form :model="dataForm" :rules="dataRule" ref="dataForm" @keyup.enter.native="dataFormSubmitHandle()" :label-width="$i18n.locale === 'en-US' ? '120px' : '130px'">
      <div style="display: flex">
            <el-form-item label="原物料代码" prop="rawMaterialCode" style="width: 33%">
              <el-select v-model="dataForm.rawMaterialCode"
                         placeholder="请选择"
                         filterable
                         @change="changeMaterialInfo"
              >
                <el-option
                    v-for="item in materialList"
                    :key="item.id"
                    :label="item.materialsName + '(' + item.materialsCode + ')'"
                    :value="item.materialsCode">
                </el-option>
              </el-select>
            </el-form-item>
        <el-form-item label="类别" prop="category" style="width: 33%">
          <el-input v-model="dataForm.category" placeholder="类别" disabled></el-input>
        </el-form-item>
        <el-form-item label="材料名称" prop="materialName" style="width: 33%">
          <el-input v-model="dataForm.materialName" placeholder="材料名称" disabled></el-input>
        </el-form-item>
      </div>
      <div style="display: flex">
<!--                                        <el-form-item label="对应名称" prop="correspondName">-->
<!--              <el-input v-model="dataForm.correspondName" placeholder="对应名称"></el-input>-->
<!--            </el-form-item>-->
                                        <el-form-item label="总量单位" prop="unitOfAccount" style="width: 33%">
               <el-select v-model="dataForm.unitOfAccount"
                          placeholder="请选择库存单位"
                          @input="">
                 <el-option
                     v-for="item in measuringUnitList"
                     :key="item.id"
                     :label="item.unit"
                     :value="item.id">
                 </el-option>
               </el-select>
            </el-form-item>

               <el-form-item label="库存数量" prop="inventoryQuantity" style="width: 33%">
              <el-input v-model="dataForm.inventoryQuantity" placeholder="库存数量"></el-input>
            </el-form-item>
                                        <el-form-item label="采购需求线" prop="securityInventoryQuantity" style="width: 33%">
              <el-input v-model="dataForm.securityInventoryQuantity" placeholder="采购线"></el-input>
            </el-form-item>
      </div>
      <div style="display: flex">
        <el-form-item label="尾数" prop="materialMantissa" style="width: 33%">
          <el-input v-model="dataForm.materialMantissa" placeholder="请输入尾数"></el-input>
        </el-form-item>
                        <el-form-item label="有效期" prop="periodOfValidity" style="width:33%">
                      <el-input v-model="dataForm.periodOfValidity" placeholder="有效期"></el-input>
                    </el-form-item>
            <el-form-item style="width: 33%">
            </el-form-item>
                        </div>
      <el-form-item label="备注" prop="remark">
        <el-input v-model="dataForm.remark" type="textarea" :rows="3" placeholder="备注"></el-input>
      </el-form-item>
    </el-form>
    <template slot="footer">
      <el-button @click="visible = false">{{ $t('cancel') }}</el-button>
      <el-button type="primary" @click="dataFormSubmitHandle()">{{ $t('confirm') }}</el-button>
    </template>
  </el-dialog>
</template>

<script>
import debounce from 'lodash/debounce'
import mixinViewModule from '@/mixins/view-module'
export default {
  mixins:[mixinViewModule],
  data () {
    return {
      visible: false,
      //创建该页面时，不调用数据查询接口
      mixinViewModuleOptions: {
        createdIsNeed: false,
      },
      dataForm: {
        id: '',
        rawMaterialCode: '',
        category: '',
        materialName: '',
        correspondName: '',
        unitOfAccount: '',
        periodOfValidity: '',
        inventoryQuantity: '',
        securityInventoryQuantity: '',
        materialMantissa:'',
        remark: ''
      },
      materialList: [],
      measuringUnitList:[]
    }
  },
  computed: {
    dataRule () {
      return {
          id: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          rawMaterialCode: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        //   category: [
        //   { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        // ],
        //   materialName: [
        //   { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        // ],
        //   correspondName: [
        //   { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        // ],
          unitOfAccount: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        // materialMantissa: [
        //   { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        // ],
        //   periodOfValidity: [
        //   { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        // ],
          inventoryQuantity: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        //   securityInventoryQuantity: [
        //   { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        // ],
        //   remark: [
        //   { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        // ]
      }
    }
  },
  created() {
    this.getMaterialList()
    this.getMeasuringUnitList()
  },
  methods: {
    init () {
      this.visible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].resetFields()
        if (this.dataForm.id) {
          this.getInfo()
        }
      })
    },
    changeMaterialInfo(){
      let material = this.materialList.filter(item => item.materialsCode === this.dataForm.rawMaterialCode);
      console.log(material)
      this.dataForm.category = material[0].category;
      this.dataForm.materialName = material[0].materialsName;
    },
    //获取物料代码列表
    getMaterialList(){
      this.$http.get(`/packing/materialbase/getMaterialList`).then(({ data: res }) => {
        if (res.code !== 0) {
          return this.$message.error(res.msg)
        }
        this.materialList = res.data
      }).catch(() => {})
    },
    //获取总量单位列表
    getMeasuringUnitList(){
      this.$http.get(`/material/measuringunit/infoList`).then(({ data: res }) => {
        if (res.code !== 0) {
          return this.$message.error(res.msg)
        }
        this.measuringUnitList = res.data
      }).catch(() => {

      })
    },
    // 获取信息
    getInfo () {
      this.$http.get(`/packing/materialrawmaterialinventorysummary/${this.dataForm.id}`).then(({ data: res }) => {
        if (res.code !== 0) {
          return this.$message.error(res.msg)
        }
        this.dataForm = {
          ...this.dataForm,
          ...res.data
        }
      }).catch(() => {})
    },
    // 表单提交
    dataFormSubmitHandle: debounce(function () {
      this.$refs['dataForm'].validate((valid) => {
        if (!valid) {
          return false
        }
        let message = '';
        if(this.dataForm.id){
          message = '此操作将修改该数据'
        }else {
          message = '此操作将新增一条数据'
        }
        this.$confirm(message+', 是否继续?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
          center: true
        }).then(() => {
          this.$http[!this.dataForm.id ? 'post' : 'put']('/packing/materialrawmaterialinventorysummary/', this.dataForm).then(({ data: res }) => {
            if (res.code !== 0) {
            return this.$message.error(res.msg)
            }
            this.$message({
          message: this.$t('prompt.success'),
            type: 'success',
                    duration: 500,
                    onClose: () => {
              this.visible = false
              this.$emit('refreshDataList')
            }
          })
          }).catch(() => {})
        }).catch(() => {
          this.$message({
            type: 'info',
            message: '已取消'
          });
        });
      })
    }, 1000, { 'leading': true, 'trailing': false })
  }
}
</script>
