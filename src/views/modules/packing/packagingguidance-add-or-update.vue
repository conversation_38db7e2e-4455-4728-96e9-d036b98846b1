<template>
  <el-dialog :visible.sync="visible" :title="!dataForm.id ? $t('add') : $t('update')" :close-on-click-modal="false" :close-on-press-escape="false" append-to-body>
    <el-form :model="dataForm" :rules="dataRule" ref="dataForm" @keyup.enter.native="dataFormSubmitHandle()" :label-width="$i18n.locale === 'en-US' ? '120px' : '80px'">
      <div class="container">
        <div class="item">
          <el-form-item label="部品id" prop="partId">
            <el-input v-model="dataForm.partId" placeholder="部品id"></el-input>
          </el-form-item>
        </div>

        <div class="item">
          <el-form-item label="部品名称" prop="componentName">
            <el-input v-model="dataForm.componentName" placeholder="部品名称"></el-input>
          </el-form-item>
        </div>

        <div class="item">
          <el-form-item label="制定部门" prop="sysDeptId">
            <ren-dept-tree v-model="dataForm.sysDeptId" :placeholder="$t('dept.title')"></ren-dept-tree>
          </el-form-item>
        </div>
      </div>

      <div class="container">
        <div class="item">
          <el-form-item label="文件编号" prop="documentNumber">
            <el-input v-model="dataForm.documentNumber" placeholder="文件编号"></el-input>
          </el-form-item>
        </div>

        <div class="item">
          <el-form-item label="材质" prop="materialQuality">
            <el-input v-model="dataForm.materialQuality" placeholder="材质"></el-input>
          </el-form-item>
        </div>

        <div class="item">
          <el-form-item label="箱子回收" prop="boxRecycling">
            <ren-radio-group v-model="dataForm.boxRecycling" dict-type="yes_no"></ren-radio-group>
          </el-form-item>
        </div>
      </div>

      <div class="container">
        <div class="item">
          <el-form-item label="纸箱" prop="cartonId">
            <el-input v-model="dataForm.cartonId" placeholder="纸箱规格/材质"></el-input>
          </el-form-item>
          <el-form-item label="周转箱" prop="turnoverBoxId">
            <el-input v-model="dataForm.turnoverBoxId" placeholder="周转箱规格/材质"></el-input>
          </el-form-item>
          <el-form-item label="隔板" prop="partitionBoardId">
            <el-input v-model="dataForm.partitionBoardId" placeholder="隔板规格/材质"></el-input>
          </el-form-item>
          <el-form-item label="PE袋" prop="plasticBagId">
            <el-input v-model="dataForm.plasticBagId" placeholder="PE袋规格/材质"></el-input>
          </el-form-item>
          <el-form-item label="其他" prop="other">
            <el-input v-model="dataForm.other" placeholder="其他规格/材质"></el-input>
          </el-form-item>
        </div>

        <div class="item">
          <el-form-item label="内箱式样" prop="innerBoxStyle">
            <ren-radio-group v-model="dataForm.innerBoxStyle" dict-type="yes_no"></ren-radio-group>
          </el-form-item>
          <el-form-item label="产品单重" prop="productUnitWeight">
            <el-input v-model="dataForm.productUnitWeight" placeholder="产品单重(kg)"></el-input>
          </el-form-item>
          <el-form-item label="空箱重" prop="emptyContainerWeight">
            <el-input v-model="dataForm.emptyContainerWeight" placeholder="空箱重(kg)"></el-input>
          </el-form-item>
          <el-form-item label="毛重" prop="drossWeight">
            <el-input v-model="dataForm.drossWeight" placeholder="毛重(kg)"></el-input>
          </el-form-item>
        </div>

        <div class="item">
          <el-form-item label="pcs/袋" prop="quantityPerBag">
            <el-input v-model="dataForm.quantityPerBag" placeholder="多少pcs/袋"></el-input>
          </el-form-item>
          <el-form-item label="整箱数量" prop="fullContainerQuantity">
            <el-input v-model="dataForm.fullContainerQuantity" placeholder="整箱数量(PSC)"></el-input>
          </el-form-item>
          <el-form-item label="内标签" prop="innerLabelNumber">
            <el-input v-model="dataForm.innerLabelNumber" placeholder="内标签"></el-input>
          </el-form-item>
          <el-form-item label="外标签" prop="outerLabelNumber">
            <el-input v-model="dataForm.outerLabelNumber" placeholder="外标签"></el-input>
          </el-form-item>
        </div>
      </div>

      <el-form-item label="包装过程" prop="packagingProcess">

        <image-upload-component v-model="dataForm.packagingProcess" ref="upload" :action="imagePath" @pictureInformation="pictureInformation" :limit="20"></image-upload-component>

      </el-form-item>

      <div class="container">
        <div class="box">
          <el-form-item label="内标签id" prop="innerLabelId">
            <el-input v-model="dataForm.innerLabelId" placeholder="内标签id"></el-input>
          </el-form-item>
        </div>

        <div class="box">
          <el-form-item label="外标签id" prop="outerLabelId">
            <el-input v-model="dataForm.outerLabelId" placeholder="外标签id"></el-input>
          </el-form-item>
        </div>
      </div>

      <div class="container">
        <div class="item">
          <el-form-item label="版次" prop="revision">
            <el-input v-model="dataForm.revision" placeholder="版次"></el-input>
          </el-form-item>
        </div>

        <div class="item">
          <el-form-item label="发布时间" prop="releaseTime">
            <el-date-picker
                class="datePicker"
                v-model="dataForm.releaseTime"
                type="datetime"
                value-format="yyyy-MM-dd"
                placeholder="发布时间">
            </el-date-picker>
          </el-form-item>
        </div>

        <div class="item">
          <el-form-item label="改定内容" prop="revisedContent">
            <el-input v-model="dataForm.revisedContent" placeholder="改定内容"></el-input>
          </el-form-item>
        </div>
      </div>
      <el-form-item label="备注" prop="remark">
          <el-input v-model="dataForm.remark" placeholder="备注"></el-input>
      </el-form-item>
    </el-form>
    <template slot="footer">
      <el-button @click="visible = false">{{ $t('cancel') }}</el-button>
      <el-button type="primary" @click="dataFormSubmitHandle()">{{ $t('confirm') }}</el-button>
    </template>
  </el-dialog>
</template>

<script>
import debounce from 'lodash/debounce'
export default {
  data () {
    return {
      visible: false,
      imagePath:window.SITE_CONFIG['apiURL']+'/packing/packagingguidance/api/upload',
      dataForm: {
        id: '',
        partId: '',
        componentName: '',
        sysDeptId: '',
        quantityPerBox: '',
        materialQuality: '',
        boxRecycling: 1,
        documentNumber: '',
        cartonId: '',
        turnoverBoxId: '',
        partitionBoardId: '',
        plasticBagId: '',
        other: '',
        innerBoxStyle: 1,
        quantityPerBag: '',
        productUnitWeight: '',
        emptyContainerWeight: '',
        drossWeight: '',
        fullContainerQuantity: '',
        innerLabelNumber: '',
        outerLabelNumber: '',
        packagingProcess: [],
        pictureCollection:[],
        innerLabelId: '',
        outerLabelId: '',
        revision: '',
        releaseTime: '',
        revisedContent: '',
        remark: '',
        disabled: '',
        creator: '',
        createDate: '',
        updater: '',
        updateDate: ''
      },

    }
  },
  computed: {
    dataRule () {
      return {}
    }
  },
  watch:{
    "dataFrom.packagingProcess"(value){
      console.log(value,'图片对象')
    }
  },
  methods: {
    pictureInformation(data){
      console.log(this.dataForm.packagingProcess,'图片得')
      console.log(data,"askdjflkjskdlf=========++++++++++++++++++++++")
      this.dataForm.pictureCollection.push(data)
    },
    init () {
      this.visible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].resetFields()
        if (this.dataForm.id) {
          this.getInfo()
        }
      })
    },
    // 获取信息
    getInfo () {
      this.$http.get(window.SITE_CONFIG['apiURL']+`/packing/packagingguidance/${this.dataForm.id}`).then(({ data: res }) => {
        if (res.code !== 0) {
          return this.$message.error(res.msg)
        }
        this.dataForm = {
          ...this.dataForm,
          ...res.data
        }
      }).catch(() => {})
    },
    // 表单提交
    dataFormSubmitHandle: debounce(function () {
      this.$refs.upload.submitUpload()
      this.visible = false
      this.$emit('refreshDataList')
      /*this.$refs['dataForm'].validate((valid) => {
        if (!valid) {
          return false
        }
        this.$http[!this.dataForm.id ? 'post' : 'put']('/packing/packagingguidance/', this.dataForm).then(({ data: res }) => {
          if (res.code !== 0) {
            return this.$message.error(res.msg)
          }
          this.$message({
            message: this.$t('prompt.success'),
            type: 'success',
            duration: 500,
            onClose: () => {
              this.visible = false
              this.$emit('refreshDataList')
            }
          })
        }).catch(() => {})
      })*/
    }, 1000, { 'leading': true, 'trailing': false })
  }
}
</script>
