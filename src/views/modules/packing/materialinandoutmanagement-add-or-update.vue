<template>
  <el-dialog :visible.sync="visible" :title="!dataForm.id ? '新增进料记录' : $t('update')" :close-on-click-modal="false" width="68vw" :close-on-press-escape="false" append-to-body>
    <el-form :model="dataForm" :rules="dataRule" ref="dataForm" @keyup.enter.native="dataFormSubmitHandle()" :label-width="$i18n.locale === 'en-US' ? '120px' : '9vw'">
      <div style="display: flex">
      <el-form-item label="物料代码" prop="materialCode">
              <el-select style="width: 13vw" v-model="dataForm.materialCode"
                         placeholder="请选择"
                         filterable
                         @change="changeMaterialCode"
                          >
                <el-option
                    v-for="item in materialList"
                    :key="item.id"
                    :label="item.materialsName + '(' + item.materialsCode + ')'"
                    :value="item.materialsCode">
                </el-option>
              </el-select>
            </el-form-item>
                                        <el-form-item label="类别" prop="category" >
              <el-input v-model="dataForm.category" style="width: 13vw" placeholder="类别" disabled></el-input>
            </el-form-item>
                                        <el-form-item label="材料名称" prop="materialName">
              <el-input v-model="dataForm.materialName" style="width: 13vw" placeholder="材料名称" disabled></el-input>
            </el-form-item>
      </div>
<!--      <div style="display: flex">-->
<!--        <el-form-item label="采购/生产单号" prop="purchaseOrderNumber" style="width: 334px">-->
<!--          <el-input v-model="dataForm.purchaseOrderNumber" placeholder="采购单号/生产工单号"></el-input>-->
<!--        </el-form-item>-->

<!--                                        <el-form-item label="资材进料批号" prop="inAndOutOfMaterialsNumber" style="width: 334px">-->
<!--              <el-input v-model="dataForm.inAndOutOfMaterialsNumber" placeholder="资材进料批号"></el-input>-->
<!--            </el-form-item>-->
<!--                                        <el-form-item label="进/出料批次" prop="inAndOutOfMaterialsBatch" style="width: 334px">-->
<!--              <el-input v-model="dataForm.inAndOutOfMaterialsBatch" placeholder="进/出料批次"></el-input>-->
<!--            </el-form-item>-->
<!--      </div>-->
      <div style="display: flex">
                                        <el-form-item label="进/出厂日期" prop="inAndOutOfMaterialsDate" >
                                          <el-date-picker
                                              v-model="dataForm.inAndOutOfMaterialsDate"
                                              type="date"
                                              value-format="yyyy-MM-dd"
                                              placeholder="选择日期"
                                              style="width: 13vw">
                                          </el-date-picker>
            </el-form-item>
                                        <el-form-item label="进/出仓库日期" prop="inAndOutOfMaterialsWarehouseDate" >
                                          <el-date-picker
                                              v-model="dataForm.inAndOutOfMaterialsWarehouseDate"
                                              type="date"
                                              value-format="yyyy-MM-dd"
                                              placeholder="选择日期"
                                              style="width: 13vw">
                                          </el-date-picker>
            </el-form-item>
                                        <el-form-item label="本批生产日期" prop="produceDate" >
                                          <el-date-picker
                                              v-model="dataForm.produceDate"
                                              type="date"
                                              value-format="yyyy-MM-dd"
                                              placeholder="选择日期"
                                              style="width: 13vw">
                                          </el-date-picker>
            </el-form-item>
      </div>
<!--                                        <el-form-item label="毛重" prop="roughWeight">-->
<!--              <el-input v-model="dataForm.roughWeight" placeholder="毛重"></el-input>-->
<!--            </el-form-item>-->
<!--                                        <el-form-item label="净重" prop="suttle">-->
<!--              <el-input v-model="dataForm.suttle" placeholder="净重"></el-input>-->
<!--            </el-form-item>-->
<!--      <div style="display: flex">-->

<!--&lt;!&ndash;                                        <el-form-item label="累计结余数量" prop="accumulatedSurplusQuantity" style="width: 334px;">&ndash;&gt;-->
<!--&lt;!&ndash;              <el-input v-model="dataForm.accumulatedSurplusQuantity" placeholder="累计结余数量"></el-input>&ndash;&gt;-->
<!--&lt;!&ndash;            </el-form-item>&ndash;&gt;-->
<!--                                       -->
<!--      </div>-->
      <div style="display: flex">
        <el-form-item label="供应商代码" prop="supplierCode">
          <el-select style="width: 13vw" v-model="dataForm.supplierCode"
                     placeholder="请选择"
                     filterable
          >
            <el-option
                v-for="item in supplierList"
                :key="item.id"
                :label="item.supplierName"
                :value="item.supplierCode">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="材料到期日" prop="materialExpirationDate">
          <el-date-picker
              v-model="dataForm.materialExpirationDate"
              type="date"
              value-format="yyyy-MM-dd"
              placeholder="选择日期"
              style="width: 13vw">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="成作" prop="success" >
          <el-input v-model="dataForm.success" style="width: 13vw" placeholder="成作"></el-input>
        </el-form-item>

      </div>
      <div style="display: flex">
        <el-form-item label="单位数量" prop="category">
          <el-input v-model="dataForm.amount" style="width: 13vw" placeholder="数量"></el-input>
        </el-form-item>
        <el-form-item label="单位规格" prop="quantity" style="width: 33vw">
          <el-input v-model="dataForm.quantity" placeholder="" style="width: 4vw;"></el-input>
          <el-select v-model="dataForm.unitOne"
                     placeholder=""
                     @input=""
                     style="width: 6vw"
          >
            <el-option
                v-for="item in measuringUnitList"
                :key="item.id"
                :label="item.unit"
                :value="item.id"
            >
            </el-option>
          </el-select>
          <span style="margin: 2px">每</span>
          <el-select v-model="dataForm.unitTwo"
                     placeholder=""
                     @input=""
                     style="width: 5vw"
          >
            <el-option
                v-for="item in materialPackingUnit"
                :key="item.id"
                :label="item.unit"
                :value="item.id">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="尾数" prop="materialMantissa">
          <el-input v-model="dataForm.materialMantissa" placeholder="尾数"></el-input>
        </el-form-item>
        <!--                                        <el-form-item label="总数量" prop="materialSum" style="width: 334px;">-->
        <!--              <el-input v-model="dataForm.materialSum" placeholder="总数量"></el-input>-->
        <!--            </el-form-item>-->
      </div>
<!--                                        <el-form-item label="二维码" prop="qrCode">-->
<!--              <el-input v-model="dataForm.qrCode" placeholder="二维码"></el-input>-->
<!--            </el-form-item>-->
<!--                                        <el-form-item label="条码" prop="barCode">-->
<!--              <el-input v-model="dataForm.barCode" placeholder="条码"></el-input>-->
<!--            </el-form-item>-->
<div>
                                        <el-form-item label="备注" prop="remark">
              <el-input v-model="dataForm.remark" type="textarea" :span="6" placeholder="备注"></el-input>
            </el-form-item>
                                                                                                                  </div>
    </el-form>
    <template slot="footer">
      <el-button @click="visible = false">{{ $t('cancel') }}</el-button>
      <el-button type="primary" @click="dataFormSubmitHandle()">{{ $t('confirm') }}</el-button>
    </template>
  </el-dialog>
</template>

<script>
import debounce from 'lodash/debounce'
import mixinViewModule from '@/mixins/view-module'
export default {
  mixins:[mixinViewModule],
  data () {
    return {
      visible: false,
      //创建该页面时，不调用数据查询接口
      mixinViewModuleOptions: {
        createdIsNeed: false,
      },
      dataForm: {
        id: '',
        materialCode: '',
        category: '',
        materialName: '',
        inAndOutOfMaterials: 0,
        inAndOutOfMaterialsNumber: '',
        inAndOutOfMaterialsBatch: '',
        inAndOutOfMaterialsDate: '',
        inAndOutOfMaterialsWarehouseDate: '',
        produceDate: '',
        quantity: 0,
        unitOne: '',
        amount:'',
        unitTwo: '',
        materialSum: '',
        roughWeight: '',
        suttle: '',
        materialMantissa: 0,
        accumulatedSurplusQuantity: '',
        materialExpirationDate: '',
        purchaseOrderNumber: '',
        supplierCode: '',
        qrCode: '',
        barCode: '',
        success: '',
        remark: '',
        disabled: '',
        creator: '',
        createDate: '',
        updater: '',
        updateDate: ''
      },
      materialList:[],
      measuringUnitList:[],
      materialPackingUnit:[],
      supplierList:[]
    }
  },
  created() {
    this.getMaterialList();
    this.getSupplierList();
    this.getMeasuringUnitList();
    this.getMaterialPackingUnitList();
  },
  computed: {
    dataRule () {
      return {
          materialCode: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        //   category: [
        //   { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        // ],
        //   materialName: [
        //   { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        // ],
        //   inAndOutOfMaterials: [
        //   { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        // ],
        //   inAndOutOfMaterialsNumber: [
        //   { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        // ],
        //   inAndOutOfMaterialsBatch: [
        //   { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        // ],
        //   inAndOutOfMaterialsDate: [
        //   { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        // ],
        //   inAndOutOfMaterialsWarehouseDate: [
        //   { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        // ],
        //   produceDate: [
        //   { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        // ],
        //   quantity: [
        //   { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        // ],
        //   unitOne: [
        //   { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        // ],
        //   unitTwo: [
        //   { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        // ],
        //   materialSum: [
        //   { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        // ],
        //   roughWeight: [
        //   { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        // ],
        //   suttle: [
        //   { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        // ],
        //   materialMantissa: [
        //   { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        // ],
        //   accumulatedSurplusQuantity: [
        //   { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        // ],
        //   materialExpirationDate: [
        //   { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        // ],
        //   purchaseOrderNumber: [
        //   { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        // ],
          supplierCode: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        //   qrCode: [
        //   { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        // ],
        //   barCode: [
        //   { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        // ],
        //   success: [
        //   { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        // ],
        //   remark: [
        //   { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        // ],
                }
    }
  },
  methods: {
    init () {
      this.visible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].resetFields()
        if (this.dataForm.id) {
          this.getInfo()
        }
      })
    },
    changeMaterialCode(val) {
      let material = this.materialList.filter(item => item.materialsCode === this.dataForm.materialCode);
      this.dataForm.category = material[0].category;
      this.dataForm.materialName = material[0].materialsName;
    },
    //获取物料代码列表
    getMaterialList(){
      this.$http.get(`/packing/materialbase/getMaterialList`).then(({ data: res }) => {
        if (res.code !== 0) {
          return this.$message.error(res.msg)
        }
        this.materialList = res.data
      }).catch(() => {

      })
    },
    //获取总量单位列表
    getMeasuringUnitList(){
      this.$http.get(`/material/measuringunit/infoList`).then(({ data: res }) => {
        if (res.code !== 0) {
          return this.$message.error(res.msg)
        }
        this.measuringUnitList = res.data
      }).catch(() => {

      })
    },
    getMaterialPackingUnitList(){
      this.$http.get(`/material/materialpackingunit/getInfoList`).then(({ data: res }) => {
        if (res.code !== 0) {
          return this.$message.error(res.msg)
        }
        this.materialPackingUnit = res.data
      }).catch(() => {

      })
    },
    //获取供应商代码列表
    getSupplierList(){
      this.$http.get(`/packing/materialsupplierbase/getInfoList`).then(({ data: res }) => {
        if (res.code !== 0) {
          return this.$message.error(res.msg)
        }
        this.supplierList = res.data
      }).catch(() => {})
    },
    // 获取信息
    getInfo () {
      this.$http.get(`/material/materialinandoutmanagement/${this.dataForm.id}`).then(({ data: res }) => {
        if (res.code !== 0) {
          return this.$message.error(res.msg)
        }
        this.dataForm = {
          ...this.dataForm,
          ...res.data
        }
      }).catch(() => {})
    },
    // 表单提交
    dataFormSubmitHandle: debounce(function () {
      this.$refs['dataForm'].validate((valid) => {
        if (!valid) {
          return false
        }
        let message = '';
        if(this.dataForm.id){
          message = '此操作将修改该数据'
        }else {
          message = '此操作将新增一条数据'
        }
        this.$confirm(message+', 是否继续?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
          center: true
        }).then(() => {
          this.$http[!this.dataForm.id ? 'post' : 'put']('/material/materialinandoutmanagement/', this.dataForm).then(({ data: res }) => {
            if (res.code !== 0) {
            return this.$message.error(res.msg)
            }
            this.$message({
          message: this.$t('prompt.success'),
            type: 'success',
                    duration: 500,
                    onClose: () => {
              this.visible = false
              this.$emit('refreshDataList')
            }
          })
          }).catch(() => {})
        }).catch(() => {
          this.$message({
            type: 'info',
            message: '已取消'
          });
        });
      })
    }, 1000, { 'leading': true, 'trailing': false })
  }
}
</script>
<style>

</style>
