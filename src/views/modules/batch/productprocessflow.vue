<template>
  <div>
    <div>
  <el-card shadow="never" class="aui-card--fill">
    <div class="mod-batch__productprocessflow">
      <el-form :inline="true" :model="dataForm" @keyup.enter.native="getDataList()">
        <el-form-item>
          <el-input v-model="dataForm.paramStr" placeholder="批号" clearable></el-input>
        </el-form-item>
        <el-form-item>
          <el-button @click="getDataList()">{{ $t('query') }}</el-button>
        </el-form-item>
        <el-form-item>
          <el-button type="info" @click="exportHandle()">{{ $t('export') }}</el-button>
        </el-form-item>
        <el-form-item>
          <el-button v-if="$hasPermission('batch:productprocessflow:save')" type="primary" @click="addOrUpdateHandle()">{{ $t('add') }}</el-button>
        </el-form-item>
        <el-form-item>
          <el-button v-if="$hasPermission('batch:productprocessflow:save')" type="primary" @click="addBatchHandle">批量流转</el-button>
        </el-form-item>
        <el-form-item>
          <el-button v-if="$hasPermission('batch:productprocessflow:delete')" type="danger" @click="deleteHandle()">{{ $t('deleteBatch') }}</el-button>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="showDataList">数据查看</el-button>
        </el-form-item>
      </el-form>
      <el-table v-loading="dataListLoading" :data="dataList" border @selection-change="dataListSelectionChangeHandle" style="width: 100%;" :row-class-name="rowClassName">
        <el-table-column type="selection" header-align="center" align="center" width="50"></el-table-column>
        <el-table-column prop="designation" label="品号" header-align="center" align="center"></el-table-column>
        <el-table-column label="批号" header-align="center" align="center">
          <template slot-scope="scope">
            <div>
              {{$getDictLabel("size_category",scope.row.sizeCategory) + scope.row.batchNumber}}
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="subBatchNumber" label="次批号" header-align="center" align="center"></el-table-column>
        <el-table-column prop="itemCategory" label="项目类别" header-align="center" align="center">
          <template slot-scope="scope">
            {{ $getDictLabel("item_category", scope.row.itemCategory) }}
          </template>
        </el-table-column>
        <el-table-column prop="transferUnit" label="转出单位" header-align="center" align="center">
          <template slot-scope="scope">
            {{ $getDictLabel("product_category", scope.row.transferUnit) }}
          </template>
        </el-table-column>
        <el-table-column prop="transferOutQuantity" label="转出数量" header-align="center" align="center"></el-table-column>
        <el-table-column prop="transferee" label="转出者(工号)" header-align="center" align="center" width="150">
          <template slot-scope="scope">
            {{ $getEmployeesList(scope.row.transferee)}}
          </template>
        </el-table-column>
        <el-table-column prop="receivingDepartment" label="接收单位" header-align="center" align="center">
          <template slot-scope="scope">
            {{ $getDictLabel("product_category", scope.row.receivingDepartment) }}
          </template>
        </el-table-column>
        <el-table-column prop="recipient" label="接收者(工号)" header-align="center" align="center" width="150">
          <template slot-scope="scope">
            {{ $getEmployeesList(scope.row.recipient)}}
          </template>
        </el-table-column>
        <el-table-column prop="receiveQuantity" label="接收数量" header-align="center" align="center"></el-table-column>
        <el-table-column prop="receivingTime" label="接收时间" header-align="center" align="center" width="180"></el-table-column>
        <el-table-column prop="storageLocation" label="存放位置" header-align="center" align="center"></el-table-column>
        <el-table-column prop="remark" label="备注" header-align="center" align="center"></el-table-column>
        <el-table-column :label="$t('handle')" fixed="right" header-align="center" align="center" width="150">
          <template slot-scope="scope">
<!--            <el-button v-if="$hasPermission('batch:productprocessflow:update')" type="text" size="small" @click="addOrUpdateHandle(scope.row.id)">{{ $t('update') }}</el-button>-->
            <el-button v-if="$hasPermission('batch:productprocessflow:delete')" type="text" size="small" @click="deleteHandle(scope.row.id)">{{ $t('delete') }}</el-button>
          </template>
        </el-table-column>
      </el-table>
      <el-pagination
        :current-page="page"
        :page-sizes="[10, 20, 50, 100]"
        :page-size="limit"
        :total="total"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="pageSizeChangeHandle"
        @current-change="pageCurrentChangeHandle">
      </el-pagination>
      <!-- 弹窗, 新增 / 修改 -->
      <add-or-update v-if="addOrUpdateVisible" ref="addOrUpdate" @refreshDataList="getDataList"></add-or-update>
    </div>
  </el-card>
    </div>
<!--    批量整批流转的窗口-->
    <div>
      <el-dialog :visible.sync="dialogVisible1"  title="批量流转">
        <el-form :model="batchFlowForm" :rules="batchFlowRules" ref="batchFlowForm" label-width="130px">
          <div style="display: flex">
            <div class="box">
              <el-form-item label="批号查询" prop="subBatchId">
                <el-select
                    v-model="subBatchIdList"
                    multiple
                    filterable
                    style="width: 300px"
                    placeholder="请选择">
                  <el-option
                      v-for="item in batchOptions"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value">
                  </el-option>
                </el-select>
              </el-form-item>
            </div>
            <div class="box">
              <el-form-item label="项目类别" prop="itemCategory">
                <ren-select-item-category v-model="batchFlowForm.itemCategory" placeholder="请选择项目类别"
                                          dict-type="item_category">
                </ren-select-item-category>
              </el-form-item>
            </div>
          </div>
            <div style="display: flex">
            <div class="container">
              <div>
                <el-form-item label="转出单位" prop="transferUnit">
                  <ren-select-product-category  v-model="batchFlowForm.transferUnit"
                                               placeholder="转出单位"
                                               dict-type="product_category"></ren-select-product-category>
                </el-form-item>
              </div>
              <div>
                <el-form-item label="转出者" prop="transferee">
                  <employee-component v-model="batchFlowForm.transferee" placeholder="工号"></employee-component>
                </el-form-item>
              </div>
            </div>
            </div>
          <div style="display: flex">
            <div class="container">
              <div>
                <el-form-item label="接收单位" prop="receivingDepartment">
                  <ren-select-product-category v-model="batchFlowForm.receivingDepartment" placeholder="接收单位"
                                               dict-type="product_category"></ren-select-product-category>
                </el-form-item>
              </div>
              <div>
                <el-form-item label="接收者" prop="recipient">
                  <employee-component v-model="batchFlowForm.recipient" placeholder="工号"></employee-component>
                </el-form-item>
              </div>
            </div>
          </div>
          <div style="display: flex">
            <div class="container">
              <div class="box">
                <el-form-item label="接收时间" prop="receivingTime">
                  <el-date-picker
                      style="width: 200px"
                      v-model="batchFlowForm.receivingTime"
                      type="datetime"
                      value-format="yyyy-MM-dd HH:mm:ss"
                      placeholder="请选择接收时间(含时分)">
                  </el-date-picker>
                </el-form-item>
              </div>
              <div class="box">
                <el-form-item label="备注">
                  <el-input type="textarea" :span="3" v-model="batchFlowForm.remark" placeholder="备注" style="width: 300px;"></el-input>
                </el-form-item>
              </div>
          </div>
          </div>
        </el-form>
        <template slot="footer">
          <el-button @click="dialogVisible1 = false">{{ $t('cancel') }}</el-button>
          <el-button type="primary" @click="dataFormSubmitHandle">{{ $t('confirm') }}</el-button>
        </template>
      </el-dialog>
    </div>
    <div>
      <el-dialog :visible.sync="dialogVisible2" title="成形入库数据">
        <div style="display: flex">
          <el-date-picker
              v-model="dateValue"
              type="daterange"
              @change="fillDate"
              value-format="yyyy-MM-dd"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期">
          </el-date-picker>
            <el-select v-model="partId" filterable clearable placeholder="请选择品号">
              <el-option v-for="(item,index) in partOption"
                         :key="index"
                         :label="item.label"
                         :value="item.value">
              </el-option>
            </el-select>
          <el-button type="primary" @click="queryStatistics">查询</el-button>
        </div>
        <el-table v-loading="loading1" :data="statisticsData" style="margin-top: 3vh">
          <el-table-column prop="designation" label="品号" header-align="center" align="center" width="300"></el-table-column>
          <el-table-column prop="receiveQuantity" label="接收总数" header-align="center" align="center" width="300"></el-table-column>
        </el-table>
      </el-dialog>
    </div>
  </div>
</template>

<script>
import mixinViewModule from '@/mixins/view-module'
import publicFunctions from '@/mixins/public-functions'
import AddOrUpdate from './productprocessflow-add-or-update'
import {mapGetters} from "vuex";
import {getDictLabel, getEmployeesList} from "../../../utils";
import th from "element-ui/src/locale/lang/th";
import debounce from "lodash/debounce";
export default {
  //导航守卫,当路由发生变化时调用函数
  beforeRouteEnter(to, from, next) {
    next(vm => {
      // 调用要运行的函数
      vm.getDataList();
    });
  },
  mixins: [mixinViewModule,publicFunctions],
  data () {
    return {
      mixinViewModuleOptions: {
        getDataListURL: '/batch/productprocessflow/page',
        getDataListIsPage: true,
        exportURL: '/batch/productprocessflow/export',
        deleteURL: '/batch/productprocessflow',
        deleteIsBatch: true
      },
      dataForm: {
        id: '',
        paramStr:'',
      },
      dateValue:'',
      startDate:'',
      endDate:'',
      partId:'',
      subBatchIdList:[],
      batchFlowForm:{
        subBatchId: '',
        partId:'',
        locking: 0,
        itemCategory: 0,
        transferUnit: "2",
        transferUnitName: '',
        deptId: '',
        transferDeptName: '',
        transferOutQuantity: 0,
        transferee: '',
        receivingDepartment: "1",
        receivingDeptName: '',
        recipient: '',
        receiveQuantity: 0,
        numberOfLosses: 0,
        receivingTime: this.formatTimes(new Date),
        storageLocation: '',
        remark: '',
      },
      dialogVisible1:false,
      dialogVisible2:false,
      batchOptions:[],
      partOption:[],
      statisticsData:[],
      loading1:false,
    }
  },
  computed:{
    batchFlowRules(){
      return{
        subBatchIdList: [
          {required: true, message: this.$t('validate.required'), trigger: 'blur'}
        ],
        itemCategory: [
          {required: true, message: this.$t('validate.required'), trigger: 'blur'}
        ],
        transferUnit: [
          {required: true, message: this.$t('validate.required'), trigger: 'blur'}
        ],
        transferee: [
          {required: true, message: this.$t('validate.required'), trigger: 'blur'}
        ],
        receivingDepartment: [
          {required: true, message: this.$t('validate.required'), trigger: 'blur'}
        ],
        recipient: [
          {required: true, message: this.$t('validate.required'), trigger: 'blur'}
        ],
        receivingTime: [
          {required: true, message: this.$t('validate.required'), trigger: 'blur'}
        ],
      }
    }
  },
  components: {
    AddOrUpdate
  },
  // created() {
  //   window.addEventListener('beforeunload', this.handleBeforeUnload)
  // },
  // //用户关闭标签、刷新页面或导航离开页面时触发
  // beforeDestroy() {
  //   window.removeEventListener('beforeunload', this.handleBeforeUnload)
  // },
  methods:{
    handleBeforeUnload(e){
      console.log('准备关闭页面')
      this.$router.push('productprocessflow-h5');
      return '关闭当前页面';
    },
    addOrUpdateHandle(id){
      // if(this.equipment()){
        this.addOrUpdateVisible = true
        this.$nextTick(() => {
          this.$refs.addOrUpdate.dataForm.id = id
          this.$refs.addOrUpdate.init()
        })
      // }else {
      //   this.$router.push({name:'productprocessflow-h5',query:{id:id}})
      // }
    },
    showDataList(){
      this.startDate = ''
      this.endDate = ''
      this.dateValue = ''
      this.partId = ''
      this.statisticsData = []
      this.getPartList()
      this.dialogVisible2 = true
    },
    queryStatistics(){
      if(this.startDate === '' || this.endDate === ''){
        return this.$message.warning("请选择日期")
      }
      this.loading1 = true
      console.log('查询')
      this.$http.get(`batch/productprocessflow/getStatisticsData?partId=${this.partId}&startTime=${this.startDate}&endTime=${this.endDate}`).then(({data:res})=>{
        if (res.code !== 0) {
          return this.$message.error(res.msg)
        }
        console.log(res)
        this.statisticsData = res.data
        this.loading1 = false
      })
    },
    //获取品号列表
    getPartList() {
      this.$http.get('fabricate/part/getPartListByDesignation').then(({data: res}) => {
        if (res.code !== 0) {
          return this.$message.error(res.msg)
        }
        this.partOption = res.data.map((obj) => {
          return {
            label: obj.designation,
            value: obj.id,
            data: obj
          }
        })
      })
    },
    fillDate() {
      if (this.dateValue) {
        // 复制开始和结束日期以避免直接修改原始值
        this.startDate = new Date(this.dateValue[0]);
        let endDate = new Date(this.dateValue[1]);
        // 结束日期加一天
        endDate.setDate(endDate.getDate() + 1);
        // 确保日期格式为 "yyyy-MM-dd"
        const formatDate = (date) => {
          const y = date.getFullYear();
          let m = date.getMonth() + 1;
          m = m < 10 ? '0' + m : m;
          let d = date.getDate();
          d = d < 10 ? '0' + d : d;
          return `${y}-${m}-${d}`;
        };
        this.startDate = formatDate(this.startDate);
        this.endDate = formatDate(endDate);
      } else {
        this.startDate = '';
        this.endDate = '';
      }
    },
    //
    addBatchHandle(){
      this.dialogVisible1 = true;
      this.getSubBatchList();
    },
    // 表单提交
    dataFormSubmitHandle: debounce(function () {
      this.$refs['batchFlowForm'].validate((valid) => {
        if (!valid) {
          return false
        }
        if(this.batchFlowForm.transferUnit === this.batchFlowForm.receivingDepartment){
          return this.$message({
            message:'转出单位与接收单位不得相同',
            type:"warning",
            center:true
          })
        }
        let length = this.subBatchIdList.length;
        this.$confirm('此操作将新增'  + length + '条流转记录, 是否继续?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
          center: true
        }).then(() => {
          let dataFormList = []
          for (let i = 0; i < length; i++) {
            this.batchFlowForm.subBatchId = this.subBatchIdList[i]
            let item = this.batchOptions.find(item => item.value === this.batchFlowForm.subBatchId);
            //console.log('option=================' + item.data.partId)
            this.batchFlowForm.partId = item.data.partId
            let newForm = {...this.batchFlowForm}
            dataFormList.push(newForm)
          }
          console.log(dataFormList)
          this.$http.post('/batch/productprocessflow/batchSave', dataFormList).then(({data: res}) => {
            if (res.code !== 0) {
              return this.$message.error(res.msg)
            }
            this.$message({
              message: this.$t('prompt.success'),
              type: 'success',
              duration: 500,
              onClose: () => {
                this.dialogVisible1 = false
                this.subBatchIdList = []
                this.getDataList()
              }
            })
          }).catch(() => {
          })
        })
      })
    }, 1000, {'leading': true, 'trailing': false}),
    //获取批次信息列表
    getSubBatchList(){
      this.$http.get(`/batch/batch/queryRecentBatch`).then(({data: res}) => {
        if (res.code !== 0) {
          return this.$message.error(res.msg)
        }
        this.batchOptions = res.data.map((obj) => {
          return {
            label: getDictLabel("size_category",obj.sizeCategory)+obj.batchNumber+
                (obj.subBatchNumber != null ? ('-'+obj.subBatchNumber) : '(未建立次批)')+'('+obj.designation+')',
            value: obj.subBatchId,
            data:obj
          }
        })
      }).catch(() => {
      })
    },
    // 获取系统当前日期及时间
    formatTimes(date) {
      let year = date.getFullYear();
      let month = date.getMonth() + 1;
      let day = date.getDate();
      let hours = date.getHours();
      let minutes = date.getMinutes();
      let seconds = date.getSeconds();

      month = month < 10 ? "0" + month : month;
      day = day < 10 ? "0" + day : day;
      hours = hours < 10 ? "0" + hours : hours;
      minutes = minutes < 10 ? "0" + minutes : minutes;
      seconds = seconds < 10 ? "0" + seconds : seconds;

      return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
    },
    rowClassName(row,rowIndex) {
      if (row.row.locks == 1) {
        return 'warning-row';
      }
    },
    /*formatTransfereeLabel(row) {
      const selectedItem = this.employees.find(item => item.value === row.transferee);
      return selectedItem ? selectedItem.label : '';
    },
    formatRecipientLabel(row) {
      const selectedItem = this.employees.find(item => item.value === row.recipient);
      return selectedItem ? selectedItem.label : '';
    }*/
  }
}
</script>

<style scoped>
  ::v-deep .warning-row {
    background-color: #e3b673 !important;
  }
</style>
