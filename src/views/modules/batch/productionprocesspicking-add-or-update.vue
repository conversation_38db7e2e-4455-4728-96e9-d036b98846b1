<template>
  <el-dialog :visible.sync="visible" :title="!dataForm.id ? $t('add') : $t('update')" :close-on-click-modal="false" :close-on-press-escape="false" append-to-body>
    <el-form :model="dataForm" :rules="dataRule" ref="dataForm" @keyup.enter.native="dataFormSubmitHandle()" :label-width="$i18n.locale === 'en-US' ? '120px' : '80px'">
          <el-form-item label="次批号id" prop="subBatchId">
          <el-input v-model="dataForm.subBatchId" placeholder="次批号id"></el-input>
      </el-form-item>
          <el-form-item label="量产前三批 0:是;1:否" prop="massProduction">
          <el-input v-model="dataForm.massProduction" placeholder="量产前三批 0:是;1:否"></el-input>
      </el-form-item>
          <el-form-item label="领料日期" prop="requisitionDate">
          <el-input v-model="dataForm.requisitionDate" placeholder="领料日期"></el-input>
      </el-form-item>
          <el-form-item label="领料" prop="requisition">
          <el-input v-model="dataForm.requisition" placeholder="领料"></el-input>
      </el-form-item>
          <el-form-item label="回头料" prop="backToMaterial">
          <el-input v-model="dataForm.backToMaterial" placeholder="回头料"></el-input>
      </el-form-item>
          <el-form-item label="净重" prop="netWeight">
          <el-input v-model="dataForm.netWeight" placeholder="净重"></el-input>
      </el-form-item>
          <el-form-item label="留料" prop="distillate">
          <el-input v-model="dataForm.distillate" placeholder="留料"></el-input>
      </el-form-item>
          <el-form-item label="备注" prop="remark">
          <el-input v-model="dataForm.remark" placeholder="备注"></el-input>
      </el-form-item>
                </el-form>
    <template slot="footer">
      <el-button @click="visible = false">{{ $t('cancel') }}</el-button>
      <el-button type="primary" @click="dataFormSubmitHandle()">{{ $t('confirm') }}</el-button>
    </template>
  </el-dialog>
</template>

<script>
import debounce from 'lodash/debounce'
export default {
  data () {
    return {
      visible: false,
      dataForm: {
        id: '',
        subBatchId: '',
        massProduction: '',
        requisitionDate: '',
        requisition: '',
        backToMaterial: '',
        netWeight: '',
        distillate: '',
        remark: '',
        disabled: '',
        creator: '',
        createDate: '',
        updater: '',
        updateDate: ''
      }
    }
  },
  computed: {
    dataRule () {
      return {
          id: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          subBatchId: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          massProduction: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          requisitionDate: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          requisition: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          backToMaterial: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          netWeight: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          distillate: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          remark: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
                }
    }
  },
  methods: {
    init () {
      this.visible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].resetFields()
        if (this.dataForm.id) {
          this.getInfo()
        }
      })
    },
    // 获取信息
    getInfo () {
      this.$http.get(`/batch/productionprocesspicking/${this.dataForm.id}`).then(({ data: res }) => {
        if (res.code !== 0) {
          return this.$message.error(res.msg)
        }
        this.dataForm = {
          ...this.dataForm,
          ...res.data
        }
      }).catch(() => {})
    },
    // 表单提交
    dataFormSubmitHandle: debounce(function () {
      this.$refs['dataForm'].validate((valid) => {
        if (!valid) {
          return false
        }
        this.$http[!this.dataForm.id ? 'post' : 'put']('/batch/productionprocesspicking/', this.dataForm).then(({ data: res }) => {
          if (res.code !== 0) {
            return this.$message.error(res.msg)
          }
          this.$message({
            message: this.$t('prompt.success'),
            type: 'success',
            duration: 500,
            onClose: () => {
              this.visible = false
              this.$emit('refreshDataList')
            }
          })
        }).catch(() => {})
      })
    }, 1000, { 'leading': true, 'trailing': false })
  }
}
</script>
