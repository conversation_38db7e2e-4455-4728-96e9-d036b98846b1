<template>
  <el-dialog :visible.sync="visible" :title="!dataForm.id ? $t('add') : $t('update')" :close-on-click-modal="false" :close-on-press-escape="false" append-to-body>
    <el-form :model="dataForm" :rules="dataRule" ref="dataForm" @keyup.enter.native="dataFormSubmitHandle()" :label-width="$i18n.locale === 'en-US' ? '120px' : '80px'">
          <el-form-item label="部品番号" prop="partId">
          <el-input v-model="dataForm.designation" placeholder="部品编号"></el-input>
      </el-form-item>
          <el-form-item label="制造指令" prop="manufacturingOrderId">
          <el-input v-model="dataForm.manufacturingInstructions" placeholder="制造指令编号"></el-input>
      </el-form-item>
          <el-form-item label="制造指令日期" prop="manufacturingOrderDate">
          <el-input v-model="dataForm.manufacturingOrderDate" placeholder="制造指令日期"></el-input>
      </el-form-item>
          <el-form-item label="品检id" prop="qualityInspectionId">
          <el-input v-model="dataForm.qualityInspectionId" placeholder="品检id"></el-input>
      </el-form-item>
          <el-form-item label="是否放行" prop="letGo">
            <el-select v-model="dataForm.letGo" placeholder="是否放行">
              <el-option
                  v-for="item in letGo"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value">
              </el-option>
            </el-select>
          <el-input v-model="dataForm.letGo" placeholder="是否放行"></el-input>
      </el-form-item>
          <el-form-item label="硬度标准" prop="hardnessStandard">
          <el-input v-model="dataForm.hardnessStandard" placeholder="硬度标准"></el-input>
      </el-form-item>
          <el-form-item label="实测硬度" prop="measuredHardness">
          <el-input v-model="dataForm.measuredHardness" placeholder="实测硬度"></el-input>
      </el-form-item>
          <el-form-item label="检验员" prop="inspectors">
          <el-input v-model="dataForm.inspectors" placeholder="检验员"></el-input>
      </el-form-item>
          <el-form-item label="品检判定 1:OK 2:NG 3:特采" prop="qualityInspection">
            <el-select v-model="dataForm.qualityInspection" placeholder="品检判定">
              <el-option
                  v-for="item in qualityInspection"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value">
              </el-option>
            </el-select>
      </el-form-item>
          <el-form-item label="备注" prop="remark">
          <el-input v-model="dataForm.remark" placeholder="备注"></el-input>
      </el-form-item>
                </el-form>
    <template slot="footer">
      <el-button @click="visible = false">{{ $t('cancel') }}</el-button>
      <el-button type="primary" @click="dataFormSubmitHandle()">{{ $t('confirm') }}</el-button>
    </template>
  </el-dialog>
</template>

<script>
import debounce from 'lodash/debounce'
export default {
  data () {
    return {
      visible: false,
      dataForm: {
        id: '',
        partId: '',
        manufacturingOrderId: '',
        manufacturingOrderDate: '',
        manufacturingInstructions: '',
        designation:'',
        qualityInspectionId: '',
        letGo: '',
        hardnessStandard: '',
        measuredHardness: '',
        inspectors: '',
        qualityInspection: '',
        remark: '',
        disabled: '',
        creator: '',
        createDate: '',
        updater: '',
        updateDate: ''
      },
      letGo:[{
        value: 0,
        label: 'T'
      }, {
        value: 1,
        label: 'F'
      }],
      qualityInspection:[{
        value: 1,
        label: 'OK'
      }, {
        value: 2,
        label: 'NG'
      }, {
        value: 3,
        label: '特采'
      }],
    }
  },
  computed: {
    dataRule () {
      return {
          id: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          partId: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          manufacturingOrderId: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          manufacturingOrderDate: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          qualityInspectionId: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          letGo: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          hardnessStandard: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          measuredHardness: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          inspectors: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          qualityInspection: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          remark: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
                }
    }
  },
  methods: {
    init () {
      this.visible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].resetFields()
        if (this.dataForm.id) {
          this.getInfo()
        }
      })
    },
    // 获取信息
    getInfo () {
      this.$http.get(`/batch/batchnumberinspection/${this.dataForm.id}`).then(({ data: res }) => {
        if (res.code !== 0) {
          return this.$message.error(res.msg)
        }
        this.dataForm = {
          ...this.dataForm,
          ...res.data
        }
      }).catch(() => {})
    },
    // 表单提交
    dataFormSubmitHandle: debounce(function () {
      this.$refs['dataForm'].validate((valid) => {
        if (!valid) {
          return false
        }
        this.$http[!this.dataForm.id ? 'post' : 'put']('/batch/batchnumberinspection/', this.dataForm).then(({ data: res }) => {
          if (res.code !== 0) {
            return this.$message.error(res.msg)
          }
          this.$message({
            message: this.$t('prompt.success'),
            type: 'success',
            duration: 500,
            onClose: () => {
              this.visible = false
              this.$emit('refreshDataList')
            }
          })
        }).catch(() => {})
      })
    }, 1000, { 'leading': true, 'trailing': false })
  }
}
</script>
