<template>
  <el-drawer
      class="drawer_b"
      :visible.sync="drawer"
      direction="ttb" size="95vh"
      :title="drawerTitle">
    <div>
      <div style="position: fixed; top: 8vh; background-color: white">
      <div>
        <div style="margin-left: 1vw; margin-bottom: 2vh">
          <span style="font-size: large;">部门选择</span>
          <el-select  @change="changeDept" v-model="dept" clearable>
          <el-option v-for="item in deptOptions"
          :key="item.value"
          :label="item.label"
          :value="item.value"></el-option>
        </el-select>
        </div>
      </div>
    <div style="display: flex;justify-content: center;align-items: center">
      <div style="width: 15vw; display: flex;justify-content: center">
        <el-button class="custom-button" @click="toNowDate" type="primary">返回当天</el-button>
        <el-button class="custom-button" @click="toOneWeekAgo" type="primary">上一周</el-button>
      </div>
    <div style="width: 70vw;display: flex;height: 5vh; justify-content: center;border: 1px solid #35bb87;">
      <div v-for="item in a" style="width: 5vw;">
        <div :style="returnDateStyle(item)">
          <span style="font-size: x-large; font-weight: bold">{{item.getDate()}}</span>
          <span style="font-size: large">{{getDayOfWeek(item)}}</span>
        </div>
      </div>
    </div>
      <div style="width: 15vw; display: flex;justify-content: center"><el-button class="custom-button" @click="toOneWeekLate" type="primary">下一周</el-button> </div>
    </div>
      </div>
      <div style="margin-top: 10vh;margin-right: calc(100% - 100vw);">
        <div v-for="(item,index1) in machineProductPlanData" style="display: flex;align-items: center;">
          <div style="width: 15vw;height: 4vh; font-size: x-large; font-weight: bold;align-items: center; display: flex;justify-content: end">{{item.machineCode}}:</div>
          <div v-for="(date,index2) in a">
            <div :style="returnStyleByDate(date,item,index2)"></div>
          </div>
          <div style="width: 15vw;height: 4vh; font-size: x-large; font-weight: bold; display: flex;justify-content: end">
          </div>
        </div>
      </div>
    </div>
  </el-drawer>
</template>

<script>

export default {
  name: "machine-product-drawer",
  data(){
    return{
      drawer: false,
      a:[],
      beginDate:'',
      drawerTitle:'',
      nowDate:new Date(new Date().getFullYear(), new Date().getMonth(), new Date().getDate()),
      machineProductPlanData:[],
      dept:'',
      deptOptions:[
        {
          label:'制一部',
          value:'1641701467113046018'
        },
        {
          label:'制三部',
          value: '1641701649229725698'
        }
      ]
    }
  },
  methods:{
    //初始化
    init(){
      this.showCalendar()
    },
    //变更部门时触发
    changeDept(){
      const firstDay = this.a[0];
      const endDay = this.a[13];
      this.getMachineProductPlan(firstDay,endDay,this.dept);
    },
    //获取机台排产计划
    getMachineProductPlan(startDate,endDate,deptId){
      this.$http.get('batch/formingproductplan/getMachineProductPlanForOverview',{
        params:{
          startDate: startDate,
          endDate: endDate,
          deptId: deptId
        }
      }).then(({data: res}) =>{
        if(res.code !== 0){
          return this.$message.error(res.msg)
        }
        this.machineProductPlanData = res.data
      })
    },
    //查看排产计划
    showCalendar(){
      this.a = this.getDaysOfWeek(this.nowDate)
      const firstDay = this.a[0];
      const endDay = this.a[13];
      this.drawerTitle = firstDay.getFullYear() + '年' + (firstDay.getMonth() + 1) + '月' + firstDay.getDate() +
          '日~' + endDay.getFullYear() + '年' + (endDay.getMonth() + 1) + '月' + endDay.getDate() + '日排产计划'
      this.getMachineProductPlan(firstDay,endDay,this.dept);
      console.log("触发")
      this.drawer = true;
    },
    //上一周
    toOneWeekAgo(){
      this.a[0].setDate(this.a[0].getDate() - 1)
      this.a = this.getDaysOfWeek(this.a[0])
      const firstDay = this.a[0];
      const endDay = this.a[13];
      this.drawerTitle = firstDay.getFullYear() + '年' + (firstDay.getMonth() + 1) + '月' + firstDay.getDate() +
          '日~' + endDay.getFullYear() + '年' + (endDay.getMonth() + 1) + '月' + endDay.getDate() + '日排产计划'
      this.getMachineProductPlan(firstDay,endDay,this.dept);
    },
    //下一周
    toOneWeekLate(){
      this.a[13].setDate(this.a[13].getDate() + 1)
      this.a = this.getDaysOfWeek(this.a[13])
      const firstDay = this.a[0];
      const endDay = this.a[13];
      this.drawerTitle = firstDay.getFullYear() + '年' + (firstDay.getMonth() + 1) + '月' + firstDay.getDate() +
          '日~' + endDay.getFullYear() + '年' + (endDay.getMonth() + 1) + '月' + endDay.getDate() + '日排产计划'
      this.getMachineProductPlan(firstDay,endDay,this.dept);
    },
    //返回当天
    toNowDate(){
      this.a = this.getDaysOfWeek(this.nowDate)
      const firstDay = this.a[0];
      const endDay = this.a[13];
      this.drawerTitle = firstDay.getFullYear() + '年' + (firstDay.getMonth() + 1) + '月' + firstDay.getDate() +
          '日~' + endDay.getFullYear() + '年' + (endDay.getMonth() + 1) + '月' + endDay.getDate() + '日排产计划'
      this.getMachineProductPlan(firstDay,endDay,this.dept);
    },
    //获取传入日期当周1至下周日的日期
    getDaysOfWeek(currentDate) {
      const dateArray = [];
      // 获取传入日期是本周的第几天，0表示星期日，1表示星期一，以此类推
      const dayOfWeek = currentDate.getDay();
      // 计算本周一的日期
      const monday = new Date(currentDate);
      monday.setDate(currentDate.getDate() - dayOfWeek + (dayOfWeek === 0 ? -6 : 1));
      // 将本周一至下周日的日期添加到数组中
      for (let i = 0; i < 14; i++) { // 14天保证了当前周和下一周的日期都被包括
        const nextDay = new Date(monday);
        nextDay.setDate(monday.getDate() + i);
        dateArray.push(nextDay);
      }
      return dateArray;
    },
    //返回日期的星期数
    getDayOfWeek(date) {
      let days = ['日', '一', '二', '三', '四', '五', '六'];
      return days[date.getDay()];
    },
    //根据日期返回样式
    returnDateStyle(date){
      let style = {
        display: "flex",
        justifyContent: "center",
        alignItems: "center",
        height: "100%",
        border: "1px solid #219f88",
        background: ''
      }
      const dayOfWeek = date.getDay();
      let nowDate = new Date(new Date().getFullYear(), new Date().getMonth(), new Date().getDate());
      let backgroundColor;
      if (dayOfWeek === 0) {
        // 周末
        backgroundColor = '#6c6ce8'; // 蓝色
      } else if(nowDate.getTime() === date.getTime()){
        // 当天
        backgroundColor = '#e1f811'; // 蓝色
      }else {
        // 周一到周六
        backgroundColor = '#43deb5'; // 绿色
      }
      style.background = backgroundColor
      return style
    },
    //根据给定的值返回对应样式
    returnStyleByDate(dateData,item,index){
      let style = {
        width: '5vw',
        height: '5vh',
        border: '1px solid rgba(8,46,63,0.48)',
      }
      //判断该天是否有数据
      let filter = item.data.filter(item => new Date(item.date).getTime() === dateData.getTime());
      if(filter.length !== 0){
        console.log(filter[0].status)
        // style.background = filter[0].status === 0 ? '#4741ea' : '#0fa270'
        style.background = '#097c55'
      }
      return style
    },
  },
}
</script>

<style scoped>
.drawer_b ::v-deep .el-drawer__header{
  text-align: center;
  font-size: xx-large;
  color: #1e62e5;
  font-weight: bold;
}

.custom-button {
  width: 6vw;
  height: 5vh;
}

</style>