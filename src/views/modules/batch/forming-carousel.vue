<template>
<div>
  <div>
    <el-drawer
        :visible.sync="drawer"
        :with-header="false"
        direction="ttb"
        size="100vh"
        @close="clearIntervalTask"
    >
      <div style="font-size: xx-large;font-weight: bold;text-align: center; background-color: #43deb5">制一部</div>
      <el-carousel height="45vh" :interval="15000" indicator-position="none">
        <el-carousel-item v-for="(item,index1) in productDetail1" :key="index1" style="background-color: #43deb5">
          <div style="display: flex; flex-wrap: wrap">
            <div v-for="(data,index2) in item" :key="index2" style="display: flex; width: 33.33vw; height: 22vh; justify-content: center; align-items: center">
              <el-card style="width: 31vw; height: 22vh;" :style="{backgroundColor: data.machineStatus === 0 ? 'whitesmoke' : '#ffd311'}">
                <div style="font-size: x-large;font-weight: bold;text-align: center;color: slateblue">{{data.machineCode}}</div>
                <el-empty v-if="data.machineStatus === 1" :image-size="80" description="暂无数据"></el-empty>
                <el-descriptions v-if="data.machineStatus === 0"  :column="2"  border >
                  <el-descriptions-item>
                    <template slot="label">
                      <span style="font-weight: bold;font-size: large">品名</span>
                    </template>
                    <span style="font-weight: bold;font-size: large">{{data.designation}}</span>
                  </el-descriptions-item>
                  <el-descriptions-item>
                    <template slot="label">
                      <span style="font-weight: bold;font-size: large">孔数</span>
                    </template>
                    <span style="font-weight: bold;font-size: large">{{data.actualProductionHoles}}</span>
                  </el-descriptions-item>
                  <el-descriptions-item>
                    <template slot="label">
                      <span style="font-weight: bold;font-size: large">品号代码</span>
                    </template>
                    <span style="font-weight: bold;font-size: large">{{data.partCode}}</span>
                  </el-descriptions-item>
                  <el-descriptions-item>
                    <template slot="label">
                      <span style="font-weight: bold;font-size: large">生产模数</span>
                    </template>
                    <span style="font-weight: bold;font-size: large">{{data.accumulate}}</span>
                  </el-descriptions-item>
                  <el-descriptions-item>
                    <template slot="label">
                      <span style=" font-weight: bold;font-size: large">批号</span>
                    </template>
                    <span style="font-weight: bold;font-size: large">{{data.subBatchNumber}}</span>
                  </el-descriptions-item>
                  <el-descriptions-item>
                    <template slot="label">
                      <span style="font-weight: bold;font-size: large">生产总数</span>
                    </template>
                    <span style="font-weight: bold;font-size: large">{{data.actualCapacity}}</span>
                  </el-descriptions-item>
                  <el-descriptions-item>
                    <template slot="label">
                      <span style="font-weight: bold;font-size: large">作业员</span>
                    </template>
                    <span style="font-weight: bold;font-size: large">{{$getEmployeesList(data.operator)}}</span>
                  </el-descriptions-item>
                  <el-descriptions-item>
                    <template slot="label">
                      <span style="font-weight: bold;font-size: large">不良数</span>
                    </template>
                    <span style="font-weight: bold;font-size: large">{{data.undesirable}}</span>
                  </el-descriptions-item>
                </el-descriptions>
              </el-card>
            </div>
          </div>
        </el-carousel-item>
      </el-carousel>
      <div style="font-size: xx-large;font-weight: bold; text-align: center; background-color: #43deb5">制三部</div>
      <el-carousel height="45vh" :interval="15000" indicator-position="none">
        <el-carousel-item v-for="(item,index1) in productDetail2" :key="index1" style="background-color: #43deb5">
          <div style="display: flex; flex-wrap: wrap">
            <div v-for="(data,index2) in item" :key="index2" style="display: flex; width: 33.33vw; height: 22vh; justify-content: center; align-items: center">
              <el-card style="width: 31vw; height: 22vh;" :style="{backgroundColor: data.machineStatus === 0 ? 'whitesmoke' : '#ffd311'}">
                <div style="font-size: x-large;font-weight: bold;text-align: center;color: slateblue">{{data.machineCode}}</div>
                <el-empty v-if="data.machineStatus === 1" :image-size="80" description="暂无数据"></el-empty>
                <el-descriptions v-if="data.machineStatus === 0"  :column="2"  border >
                  <el-descriptions-item>
                    <template slot="label">
                      <span style="font-weight: bold;font-size: large">品名</span>
                    </template>
                    <span style="font-weight: bold;font-size: large">{{data.designation}}</span>
                  </el-descriptions-item>
                  <el-descriptions-item>
                    <template slot="label">
                      <span style="font-weight: bold;font-size: large">孔数</span>
                    </template>
                    <span style="font-weight: bold;font-size: large">{{data.actualProductionHoles}}</span>
                  </el-descriptions-item>
                  <el-descriptions-item>
                    <template slot="label">
                      <span style="font-weight: bold;font-size: large">品号代码</span>
                    </template>
                    <span style="font-weight: bold;font-size: large"></span>
                  </el-descriptions-item>
                  <el-descriptions-item>
                    <template slot="label">
                      <span style="font-weight: bold;font-size: large">生产模数</span>
                    </template>
                    <span style="font-weight: bold;font-size: large">{{data.accumulate}}</span>
                  </el-descriptions-item>
                  <el-descriptions-item>
                    <template slot="label">
                      <span style=" font-weight: bold;font-size: large">批号</span>
                    </template>
                    <span style="font-weight: bold;font-size: large">{{data.subBatchNumber}}</span>
                  </el-descriptions-item>
                  <el-descriptions-item>
                    <template slot="label">
                      <span style="font-weight: bold;font-size: large">生产总数</span>
                    </template>
                    <span style="font-weight: bold;font-size: large">{{data.actualCapacity}}</span>
                  </el-descriptions-item>
                  <el-descriptions-item>
                    <template slot="label">
                      <span style="font-weight: bold;font-size: large">作业员</span>
                    </template>
                    <span style="font-weight: bold;font-size: large">{{$getEmployeesList(data.operator)}}</span>
                  </el-descriptions-item>
                  <el-descriptions-item>
                    <template slot="label">
                      <span style="font-weight: bold;font-size: large">不良数</span>
                    </template>
                    <span style="font-weight: bold;font-size: large">{{data.undesirable}}</span>
                  </el-descriptions-item>
                </el-descriptions>
              </el-card>
            </div>
          </div>
        </el-carousel-item>
      </el-carousel>
    </el-drawer>

  </div>
</div>
</template>

<script>


export default {
  name: "forming-carousel",
  data(){
    return{
      drawer:false,
      productDetail1:[],
      productDetail2:[],
    }
  },
  methods:{
      init(){
        this.getProductDetail()
        this.startCounting();
        this.drawer = true
      },
    getProductDetail(){
        console.log("查询")
        this.$http.get('batch/formingmachinevariation/productDetail').then(({data: res}) =>{
          if (res.code !== 0) {
            return this.$message.error(res.msg)
          }
          this.productDetail1 = res.data["1"]
          this.productDetail2 = res.data["2"]
        })
    },
    startCounting() {
      this.intervalId = setInterval(() => {
        this.getProductDetail();
      }, 30000); // 30秒
    },
    clearIntervalTask() {
      console.log("触发关闭")
      if (this.intervalId) {
        clearInterval(this.intervalId);
        this.intervalId = null;
      }
    },
  }
}
</script>


<style scoped>

</style>