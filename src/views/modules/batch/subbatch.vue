<template>
  <el-card shadow="never" class="aui-card--fill">
    <div class="mod-batch__subbatch}">
      <el-form :inline="true" :model="dataForm" @keyup.enter.native="getDataList()">
        <el-form-item label="批号：">
          <batch-under-component v-model="dataForm.batchId" :display-or-not="false"></batch-under-component>
        </el-form-item>
<!--        <br/>-->
        <el-form-item>
          <el-input v-model="dataForm.batchId" placeholder="查询关键字" @input="throttleFunction" clearable @clear="clearValue">
            <el-button slot="append" icon="el-icon-search" @click="getDataList()"></el-button>
          </el-input>
        </el-form-item>
        <el-form-item>
          <el-button type="info" @click="exportHandle()">{{ $t('export') }}</el-button>
        </el-form-item>
        <el-form-item>
          <el-button v-if="$hasPermission('batch:subbatch:save')" type="primary" @click="addOrUpdateHandle()">{{ $t('add') }}</el-button>
        </el-form-item>
        <el-form-item>
          <el-button v-if="$hasPermission('batch:subbatch:delete')" type="danger" @click="deleteHandle()">{{ $t('deleteBatch') }}</el-button>
        </el-form-item>
      </el-form>
      <el-table v-loading="dataListLoading" :data="dataList" border height="750" @selection-change="dataListSelectionChangeHandle" style="width: 100%;">
        <el-table-column type="selection" header-align="center" align="center" width="50"></el-table-column>
        <el-table-column prop="batchNumber" label="批号" header-align="center" align="center" width="130">
          <template slot-scope="scope">
            <div>
              {{$getDictLabel('size_category',scope.row.sizeCategory) + scope.row.batchNumber}}
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="subBatchNumber" label="次批号" header-align="center" align="center"></el-table-column>
        <el-table-column prop="machineId" label="机台号" header-align="center" align="center" width="115">
          <template slot-scope="scope">
        {{$getMachineList(scope.row.machineId)}}
          </template>
        </el-table-column>
        <el-table-column prop="manufacturingTime" label="制造时间" header-align="center" align="center" width="153"></el-table-column>
        <el-table-column prop="productionQuantity" label="生产总数" header-align="center" align="center"></el-table-column>
        <el-table-column prop="technician" label="技术员" header-align="center" align="center">
          <template slot-scope="scope">
            {{ $getEmployeesList(scope.row.technician) }}
          </template>
        </el-table-column>
        <el-table-column prop="formingNumber" label="成形数" header-align="center" align="center">
          <template slot-scope="scope">
            <el-tooltip placement="top">
              <div slot="content">
                次批建立时间：{{scope.row.createDate}}
                <br/>
                次批最近修改时间：{{scope.row.updateDate}}
              </div>
              <div>
                {{scope.row.formingNumber}}
              </div>
            </el-tooltip>
          </template>
        </el-table-column>
        <el-table-column prop="productRepairNumber" label="品修数" header-align="center" align="center"></el-table-column>
        <el-table-column prop="qualityInspectionNumber" label="品检数" header-align="center" align="center"></el-table-column>
        <el-table-column prop="toBePacked" label="待包装数" header-align="center" align="center"></el-table-column>
        <el-table-column prop="toBeShipped" label="待出货数" header-align="center" align="center"></el-table-column>
        <el-table-column prop="barcode" label="条码" header-align="center" align="center" width="160">
          <template slot-scope="scope">
            <div @click="enlargeDisplay(true,$getDictLabel('size_category',scope.row.sizeCategory) + scope.row.batchNumber+'-'+scope.row.subBatchNumber)">
              <barcode-component :value="$getDictLabel('size_category',scope.row.sizeCategory) + scope.row.batchNumber+'-'+scope.row.subBatchNumber" :display-value="true"></barcode-component>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="orCode" label="二维码" header-align="center" align="center">
          <template slot-scope="scope">
            <div @click="enlargeDisplay(false,path+scope.row.id)">
              <qrcode-component :content="path+scope.row.id"></qrcode-component>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="productStatus" label="产品状态" header-align="center" align="center">
          <template slot-scope="scope">
            {{$getDictLabel("management_category",scope.row.productStatus)}}
          </template>
        </el-table-column>
        <el-table-column prop="boxNumber" label="包装箱号" header-align="center" align="center"></el-table-column>
        <el-table-column prop="cardNumber" label="卡板编号" header-align="center" align="center"></el-table-column>
        <el-table-column prop="shippingDate" label="出货日期" header-align="center" align="center"></el-table-column>
        <el-table-column prop="productionLoss" label="生产损耗量" header-align="center" align="center" width="100"></el-table-column>
        <el-table-column prop="materialWeight" label="料重(kg)" header-align="center" align="center"></el-table-column>
        <el-table-column prop="productionModulus" label="生产模数" header-align="center" align="center"></el-table-column>
        <el-table-column prop="numberOfHoles" label="孔数" header-align="center" align="center"></el-table-column>
        <el-table-column prop="remark" label="备注" header-align="center" align="center"></el-table-column>
        <el-table-column :label="$t('handle')" fixed="right" header-align="center" align="center" width="150">
          <template slot-scope="scope">
            <el-dropdown>
              <el-button type="primary">
                功能<i class="el-icon-arrow-down el-icon--right"></i>
              </el-button>
              <el-dropdown-menu slot="dropdown">
                <el-dropdown-item>
                  <el-button v-if="$hasPermission('batch:subbatch:update')" type="text" size="small" @click="subBatchUpdateHandle(scope.row.id)">{{ $t('update') }}</el-button>
                </el-dropdown-item>
                <el-dropdown-item>
                  <el-button v-if="$hasPermission('batch:subbatch:delete')" type="text" size="small" @click="deleteHandle(scope.row.id)">{{ $t('delete') }}</el-button>
                </el-dropdown-item>
              </el-dropdown-menu>
            </el-dropdown>
          </template>
        </el-table-column>
      </el-table>
      <el-pagination
        :current-page="page"
        :page-sizes="[10, 20, 50, 100]"
        :page-size="limit"
        :total="total"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="pageSizeChangeHandle"
        @current-change="pageCurrentChangeHandle">
      </el-pagination>
      <!-- 弹窗, 新增 / 修改 -->
      <add-or-update v-if="addOrUpdateVisible" ref="addOrUpdate" @refreshDataList="getDataList"></add-or-update>
      <production-process-add-or-update  @dialog-closed="handleDialogClosed" v-if="subAddOrUpdateVisible" ref="subAddOrUpdate" @refreshDataList="getDataList"></production-process-add-or-update>
      <el-dialog :visible.sync="enlargeDisplayVisible" @closed="closeDialog">
        <barcode-component v-if="target" :value="convertData" :display-value="true" :width="2" :height="20"></barcode-component>
        <qrcode-component v-else :content="convertData" :width="400" :height="400"></qrcode-component>
      </el-dialog>
    </div>
  </el-card>
</template>

<script>
import mixinViewModule from '@/mixins/view-module'
import AddOrUpdate from './subbatch-add-or-update'
import ProductionProcessAddOrUpdate from "./production-process-add-or-update.vue";
import {getDictLabel} from "../../../utils";
import QrcodeComponent from "../../../components/shared-search/qrcode-component/src/qrcode-component.vue";
export default {
  mixins: [mixinViewModule],
  data () {
    return {
      queryBatchId:this.$route.query.batchId,
      mixinViewModuleOptions: {
        getDataListURL: '/batch/subbatch/page',
        getDataListIsPage: true,
        exportURL: '/batch/subbatch/export',
        deleteURL: '/batch/subbatch',
        deleteIsBatch: true
      },
      path:`http://**************/yuqun-admin//batch/batch/`,
      dataForm: {
        id: '',
        paramStr: '',
        batchId:this.$route.query.batchId,
        subBatchNumber:this.$route.query.subBatchId,
        batchNumber:'',
      },
      enlargeDisplayVisible:false,
      subAddOrUpdateVisible:false,
      target:'',
      convertData:'',
    }
  },
  components: {
    QrcodeComponent,
    AddOrUpdate,
    ProductionProcessAddOrUpdate
  },
  watch:{
    queryBatchId(newValue,oldValue){
      this.getBatchNumbers(newValue)
    },
    'dataForm.batchId'(){
      this.getDataList()
    },
  },
  created() {
    this.dataForm.batchId = this.$route.query.batchId
    this.getBatchNumbers(this.dataForm.batchId)
  },
  methods:{
    handleDialogClosed() {
      // 调用子组件中的 myFunction 方法
     /* this.$refs.subAddOrUpdate.myFunction();*/
    },
    addOrUpdateHandle(){
      this.subAddOrUpdateVisible = true
      this.$nextTick(() => {
          this.$refs.subAddOrUpdate.informationVisible = false
          this.$refs.subAddOrUpdate.displayButton = true
        this.$refs.subAddOrUpdate.init()
      })
    },
    subBatchUpdateHandle(id){
      this.addOrUpdateVisible = true
      this.$nextTick(() => {
        this.$refs.addOrUpdate.dataForm.id = id
        this.$refs.addOrUpdate.init()
      })
    },
    enlargeDisplay(target,data) { // 打开对话框
      this.target = target
      this.convertData = data
      this.enlargeDisplayVisible = true;
    },
    closeDialog() { // 关闭对话框
      this.enlargeDisplayVisible = false;
    },
    //节流函数
    throttleFunction() {
      // 如果timer不为空就表明在持续输入,每次改变都清除定时，这样就可以实现只输入最后一次结果(注意每次间隔如果超过500毫秒就会输出)
      if (this.timer !== null) clearTimeout(this.timer)
      this.timer = setTimeout(() => {
        this.getDataList()
      }, 500)
    },
    clearValue(){
      this.dataForm.paramStr = '';
      //重新导航到当前路由，并将参数 parameter 设置为 null
      this.$router.replace({ query: {subBatchId:null} });
    },
    rowClassName(row,rowIndex) {
      if (row.row.locking == 1) {
        return 'warning-row';
      }
    },
    //清空后重新查询
    onClear(){
      this.query()
    },
    getBatchNumbers(batchId) {
      if(batchId != undefined){
        this.$http.get(`/batch/batch/${batchId}`).then(({ data: res }) => {
          if (res.code !== 0) {
            return this.$message.error(res.msg)
          }
          this.dataForm.batchNumber = res.data.batchNumber
        }).catch(() => {})
      }

    }
  }


}
</script>


<style scoped>
::v-deep .warning-row {
  background-color: #e3b673 !important;
}
</style>

