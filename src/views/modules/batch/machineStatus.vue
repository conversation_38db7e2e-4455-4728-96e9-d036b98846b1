<template>
  <div>
  <el-drawer
      class="drawer_c"
      title="机台生产现况"
      :visible.sync="drawer"
      direction="ttb"
      size="100vh">
<div>
  <div style="display: flex;">
    <div style="width: 20vw;height: 10vh; margin-left: 3vw">
    <el-select v-model="dept"
               @change="filterByDept"
        class="custom-select4">
      <el-option
          class="custom-option4"
          v-for="item in deptOption"
          :key="item.value"
          :label="item.label"
          :value="item.value"
      ></el-option>
    </el-select>
    </div>
    <div>
      <button type="button" @click="getMachineList" style="width: 15vw; height: 10vh; font-size: xxx-large; margin-top: 0.5vh;font-weight: bold; background-color: #39b8ce">刷新</button>
    </div>
  </div>
  <div style="width: 100vw; display: flex; flex-wrap: wrap">
    <div v-for="item in showMachineList">
      <el-card  style="width: 25vw; height: 16vh;text-align: center;" :style="{backgroundColor: item.machineStatus === 0 ? '#4CAF50' : '#b91414'}">
        <div  @click="viewDetails(item)" style="font-size: 35px; color: #33333b;">{{item.machineName}}</div>
        <div v-if="item.machineStatus === 0" style="font-size: 30px">品名:{{item.designation}}</div>
        <div v-if="item.machineStatus === 0" style="font-size: 30px">生产数量:{{item.moldProductionQuantity}}</div>
      </el-card>
    </div>
  </div>
</div>
  </el-drawer>
  <div>
    <el-dialog :visible.sync="visible" title="机台详细信息" width="45vw" top="1vh" class="d-dialog" center append-to-body>
      <div style="text-align: left">
        <div><span class="b-text-info">机台编号:</span><span style="font-size: 40px">{{machineForm.machineName}}</span></div>
        <div class="a-text-info"><span class="b-text-info">客户品名:</span><span style="font-size: 40px">{{machineForm.designation}}</span></div>
        <div class="a-text-info"><span class="b-text-info">当前批号:</span><span style="font-size: 40px">{{machineForm.subBatchNumber}}</span></div>
        <div class="a-text-info"><span class="b-text-info">作业员工:</span><span style="font-size: 40px">{{$getEmployeesList(machineForm.operatorOne)}}</span></div>
        <div class="a-text-info"><span class="b-text-info">当前模数:</span><span style="font-size: 40px">{{machineForm.accumulate}}</span></div>
        <div class="a-text-info"><span class="b-text-info">生产数量:</span><span style="font-size: 40px">{{machineForm.moldProductionQuantity}}</span></div>
        <div class="a-text-info"><span class="b-text-info">不良数:</span><span style="font-size: 40px">{{machineForm.undesirable}}</span></div>
      </div>
    </el-dialog>
  </div>
  </div>
</template>

<script>
 export default {
   name: "machine-status",
   data(){
     return{
       visible:false,
       userName:'',
       dept:'',
       deptOption:[
         {
           label:'制一部',
           value:1641701467113046018,
         },
         {
           label:'制三部',
           value:1641701649229725698,
         }
       ],
       drawer:false,
       machineForm:{},
       machineList:[],
       showMachineList:[],
     }
   },
   methods:{
     init(){
       this.getMachineList()
       this.drawer = true
     },
     //过滤机台部门
     filterByDept(){
        if(this.dept){
          this.showMachineList = this.machineList.filter(item => item.deptId == this.dept)
        }
     },
     //查看机台详细信息
     viewDetails(item){
       this.visible = true
       this.machineForm = item
     },
     //获取机台列表
     getMachineList(){
       this.$http.get(`batch/formingmachinevariation/getMachineListData`).then(({data: res}) => {
         if (res.code !== 0) {
           return this.$message.error(res.msg)
         }
         this.machineList = res.data;
         if(this.dept) {
         this.showMachineList = this.machineList.filter(item => item.deptId == this.dept)
         }else {
         this.showMachineList = this.machineList;
         }
       })
     },
   }
 }
</script>

<style scoped>
.custom-select4 ::v-deep .el-input__inner {
  margin-top: 0.5vh;
  width: 16vw;
  height: 10vh; /* 调整选择框的高度 */
  font-size: xx-large; /* 调整选择框的字体大小 */
  text-align: center; /* 调整选择框文本的对齐 */
}
.custom-option4 {
  font-size: xx-large; /* 调整选项的字体大小 */
  width: 16vw;
  height: 60px; /* 调整选项的高度 */
  line-height: 60px; /* 调整选项的行高以垂直居中 */
}
.a-text-info{
  margin-top: 1vh;
}
.b-text-info{
  font-size: 40px;
  font-weight: bold;
  margin-right: 1vw;
}
.d-dialog ::v-deep .el-dialog__title{
  font-size: xx-large;
  font-weight: bold;
  color: #3a8ee6;
}
.drawer_c ::v-deep .el-drawer {
  background-color: #d7aeae; /* 灰色背景 */
}
.drawer_c ::v-deep .el-drawer__header{
  text-align: center;
  font-size: xxx-large;
  font-weight: bold;
}
</style>