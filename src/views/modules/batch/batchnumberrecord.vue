<template>
  <el-card shadow="never" class="aui-card--fill">
    <div class="mod-batch__batchnumberrecord}">
      <el-form :inline="true" :model="dataForm" @keyup.enter.native="getDataList()">
        <el-form-item>
          <el-input v-model="dataForm.id" placeholder="id" clearable></el-input>
        </el-form-item>
        <el-form-item>
          <el-button @click="getDataList()">{{ $t('query') }}</el-button>
        </el-form-item>
        <el-form-item>
          <el-button type="info" @click="exportHandle()">{{ $t('export') }}</el-button>
        </el-form-item>
        <el-form-item>
          <el-button v-if="$hasPermission('batch:batchnumberrecord:save')" type="primary" @click="addOrUpdateHandle()">{{ $t('add') }}</el-button>
        </el-form-item>
        <el-form-item>
          <el-button v-if="$hasPermission('batch:batchnumberrecord:delete')" type="danger" @click="deleteHandle()">{{ $t('deleteBatch') }}</el-button>
        </el-form-item>
      </el-form>
      <el-table v-loading="dataListLoading" :data="dataList" border @selection-change="dataListSelectionChangeHandle" style="width: 100%;">
        <el-table-column type="selection" header-align="center" align="center" width="50"></el-table-column>
        <el-table-column prop="id" label="id" header-align="center" align="center"></el-table-column>
        <el-table-column prop="productInventoryId" label="库存总表id" header-align="center" align="center"></el-table-column>
        <el-table-column prop="batchId" label="批号id" header-align="center" align="center"></el-table-column>
        <el-table-column prop="subBatchId" label="次批号id" header-align="center" align="center"></el-table-column>
        <el-table-column prop="batchNumberBarcode" label="批号条码" header-align="center" align="center"></el-table-column>
        <el-table-column prop="batchNumberQrCode" label="批号二维码" header-align="center" align="center"></el-table-column>
        <el-table-column prop="materialWeight" label="料重(kg)" header-align="center" align="center"></el-table-column>
        <el-table-column prop="productionModulus" label="生产模数" header-align="center" align="center"></el-table-column>
        <el-table-column prop="numberOfHoles" label="孔数" header-align="center" align="center"></el-table-column>
        <el-table-column prop="manufacturingTime" label="制造时间" header-align="center" align="center"></el-table-column>
        <el-table-column prop="productionQuantity" label="生产数量" header-align="center" align="center"></el-table-column>
        <el-table-column prop="productionLoss" label="生产损耗量" header-align="center" align="center"></el-table-column>
        <el-table-column prop="technician" label="技术员" header-align="center" align="center"></el-table-column>
        <el-table-column prop="productRepairNumber" label="品修数" header-align="center" align="center"></el-table-column>
        <el-table-column prop="qualityInspectionNumber" label="品检数" header-align="center" align="center"></el-table-column>
        <el-table-column prop="toBePacked" label="待包装数" header-align="center" align="center"></el-table-column>
        <el-table-column prop="toBeShipped" label="待出货数" header-align="center" align="center"></el-table-column>
        <el-table-column prop="productStatus" label="产品状态 0:正常; 1:重点; 2:出货; 3:锁定; 4:隔离; 5:返工; 6:返检; 7:报废" header-align="center" align="center"></el-table-column>
        <el-table-column prop="boxNumber" label="包装箱号" header-align="center" align="center"></el-table-column>
        <el-table-column prop="cardNumber" label="卡板编号" header-align="center" align="center"></el-table-column>
        <el-table-column prop="shippingDate" label="出货日期" header-align="center" align="center"></el-table-column>
        <el-table-column prop="remark" label="备注" header-align="center" align="center"></el-table-column>
        <el-table-column :label="$t('handle')" fixed="right" header-align="center" align="center" width="150">
          <template slot-scope="scope">
            <el-button v-if="$hasPermission('batch:batchnumberrecord:update')" type="text" size="small" @click="addOrUpdateHandle(scope.row.id)">{{ $t('update') }}</el-button>
            <el-button v-if="$hasPermission('batch:batchnumberrecord:delete')" type="text" size="small" @click="deleteHandle(scope.row.id)">{{ $t('delete') }}</el-button>
          </template>
        </el-table-column>
      </el-table>
      <el-pagination
        :current-page="page"
        :page-sizes="[10, 20, 50, 100]"
        :page-size="limit"
        :total="total"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="pageSizeChangeHandle"
        @current-change="pageCurrentChangeHandle">
      </el-pagination>
      <!-- 弹窗, 新增 / 修改 -->
      <add-or-update v-if="addOrUpdateVisible" ref="addOrUpdate" @refreshDataList="getDataList"></add-or-update>
    </div>
  </el-card>
</template>

<script>
import mixinViewModule from '@/mixins/view-module'
import AddOrUpdate from './batchnumberrecord-add-or-update'
export default {
  mixins: [mixinViewModule],
  data () {
    return {
      mixinViewModuleOptions: {
        getDataListURL: '/batch/batchnumberrecord/page',
        getDataListIsPage: true,
        exportURL: '/batch/batchnumberrecord/export',
        deleteURL: '/batch/batchnumberrecord',
        deleteIsBatch: true
      },
      dataForm: {
        id: ''
      }
    }
  },
  components: {
    AddOrUpdate
  }
}
</script>
