<template>
  <el-dialog :visible.sync="visible" title="新增转入批号" :close-on-click-modal="false" :close-on-press-escape="false"
             append-to-body>
    <el-form :model="dataForm" :rules="dataRule" ref="dataForm" @keyup.enter.native="dataFormSubmitHandle()"
             :label-width="$i18n.locale === 'en-US' ? '120px' : '80px'">
      <div>
        <div class="container">
          <el-form-item label="胶料别" prop="sizeCategory">
            <div>
              <ren-radio-tick v-model="dataForm.sizeCategory" dict-type="size_category"></ren-radio-tick>
            </div>
          </el-form-item>
          <el-form-item label="生产批号" prop="batchNumber">
            <el-input v-model="dataForm.batchNumber" placeholder="生产批号"></el-input>
          </el-form-item>
          <el-form-item label="次批号" prop="subBatchNumber">
            <el-input v-model="dataForm.subBatchNumber" placeholder="次批号"></el-input>
          </el-form-item>
          <el-form-item label="材料重量" prop="weight">
            <el-input v-model="dataForm.weight" placeholder="材料重量"></el-input>
          </el-form-item>
        </div>
        <div class="container">
          <el-form-item label="部品品名" prop="itemCategory">
            <el-autocomplete
                class="inline-input"
                v-model="dataForm.selectDesignation"
                :fetch-suggestions="getPartList"
                placement="bottom"
                placeholder="部品品名模糊查询"
                :trigger-on-focus="false"
                @select="partSelect"
                clearable
            ></el-autocomplete>
          </el-form-item>
          <el-form-item label="部品品名" prop="designation">
            <span class="font_size">{{ dataForm.designation }}</span>
          </el-form-item>
          <el-form-item label="查询客户" prop="customerCode">
            <span class="font_size">{{ dataForm.customerCode }}</span>
          </el-form-item>
        </div>
        <div class="container">
          <div class="three">
            <el-form-item label="孔数" prop="numberOfHoles">
              <el-input type="number" v-model.number="dataForm.numberOfHoles" placeholder="孔数"></el-input>
            </el-form-item>
          </div>
          <div class="three">
            <el-form-item label="模数" prop="productionModulus">
              <el-input type="number" v-model.number="dataForm.productionModulus" placeholder="模数"></el-input>
            </el-form-item>
          </div>
          <div class="three">
            <el-form-item label="实际数" prop="productionQuantity">
              <el-input v-model="productionQuantity" placeholder="实际数"></el-input>
            </el-form-item>
          </div>
          <div class="three">
            <el-form-item label="成形日期" prop="transferPerson">
              <el-date-picker
                  v-model="dataForm.manufacturingTime"
                  type="date"
                  value-format="yyyy-MM-dd"
                  placeholder="成形日期">
              </el-date-picker>
            </el-form-item>
          </div>
        </div>
        <div class="container">
          <div>
            <el-form-item label="部门" prop="receivingDepartment">
              <ren-select-product-category v-model="dataForm.transferUnit" :placeholder="$t('dept.title')"
                                           dict-type="product_category"></ren-select-product-category>
            </el-form-item>
          </div>
          <div>
            <el-form-item label="建立人" prop="transferee">
              <employee-component v-model="dataForm.transferee" :departments="$formingDefault" placeholder="工号"
                                  @employeeData="data => extractContentAndAssign(data,'transferPerson')"></employee-component>
            </el-form-item>
          </div>
          <div>
            <el-form-item label="建立人姓名" prop="transferPerson">
              <el-input v-model="dataForm.transferPerson" placeholder="建立人姓名"></el-input>
            </el-form-item>
          </div>
          <div>
            <el-form-item label="技术员" prop="technician">
              <employee-component-list v-model="dataForm.technician" :departments="$formingDefault" placeholder="工号"
                                       @employeeData="employeeData"></employee-component-list>
            </el-form-item>
          </div>
          <div>
            <el-form-item label="技术员姓名" prop="technicianName">
              <el-input v-model="dataForm.technicianName" placeholder="技术员姓名"></el-input>
            </el-form-item>
          </div>
        </div>
        <div class="container">
          <div class="item">
            <el-form-item label="建立时间" prop="receivingTime">
              <el-date-picker
                  v-model="dataForm.createDate"
                  type="datetime"
                  value-format="yyyy-MM-dd HH:mm:ss"
                  placeholder="请选择接收时间(含时分)">
              </el-date-picker>
            </el-form-item>
          </div>
        </div>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="dataForm.remark" placeholder="备注"></el-input>
        </el-form-item>
      </div>
    </el-form>
    <template slot="footer">
      <el-button @click="visible = false">{{ $t('cancel') }}</el-button>
      <el-button type="primary" @click="dataFormSubmitHandle()">{{ $t('confirm') }}</el-button>
    </template>
  </el-dialog>
</template>

<script>
import debounce from 'lodash/debounce'
import mixinViewModule from '@/mixins/view-module'
import PublicFunctions from '@/mixins/public-functions'
import {getDictLabel} from '@/utils'

export default {
  mixins: [mixinViewModule, PublicFunctions],
  data() {
    return {
      batchNumberId: '',
      visible: false,
      //创建该页面时，不调用数据查询接口
      mixinViewModuleOptions: {
        createdIsNeed: false,
      },
      alternateDisplay: false,
      dataForm: {
        id: '',
        batchId: '',
        customerId: '',
        customerCode: '',
        productInventoryId: '',
        materialWeight: '',
        transferUnit: 0,
        itemCategory: 0,
        transferee: '',
        transferPerson: '',
        technician: [],
        technicianList: [],
        technicianName: '',
        outgoingName: '',
        productionModulus: 0,
        selectDesignation: '',
        numberOfHoles: 0,
        manufacturingTime: this.formatTimes(new Date()),
        productionQuantity: '',
        productionLoss: '',
        productRepairNumber: '',
        qualityInspectionNumber: '',
        toBePacked: '',
        toBeShipped: '',
        productStatus: '',
        boxNumber: '',
        cardNumber: '',
        shippingDate: '',
        subBatchNumber: 1,
        barcode: '',
        sizeCategory: '',
        batchNumber: '',
        orCode: '',
        remark: '',
        disabled: '',
        creator: '',
        createDate: this.formatTimes(new Date()),
        updater: '',
        updateDate: ''
      },
      subBatchId: '',

    }
  },
  computed: {
    buttonColor() {
      return this.alternateDisplay ? 'blue' : 'yellow';
    },
    dataRule() {
      return {
        batchNumber: [
          {required: true, message: this.$t('validate.required'), trigger: 'blur'}
        ],
        subBatchNumber: [
          {required: true, message: this.$t('validate.required'), trigger: 'blur'}
        ],
        technician: [
          {required: true, message: this.$t('validate.required'), trigger: 'blur'}
        ],
        numberOfHoles: [
          {required: true, message: this.$t('validate.required'), trigger: 'blur'}
        ],
        productionModulus: [
          {required: true, message: this.$t('validate.required'), trigger: 'blur'}
        ],
      }
    },
    productionQuantity: function () {
      this.dataForm.productionQuantity = this.dataForm.numberOfHoles * this.dataForm.productionModulus
      return this.dataForm.productionQuantity;
    },
    subBatchNumber: function () {
      let par = ''
      if (this.dataForm.subBatchNumber && this.dataForm.batchNumber && this.dataForm.sizeCategory) {
        par = getDictLabel("size_category", this.dataForm.sizeCategory) + this.dataForm.batchNumber + '-' + this.dataForm.subBatchNumber
      }
      return par;
    }
  },
  methods: {
    employeeData(data) {
      console.log(data, 'data')
      this.dataForm.technicianList = []
      this.dataForm.technicianName = ''
      for (let i = 0; i < data.length; i++) {
        this.dataForm.technicianList.push({technician: data[i].value, technicianName: this.labelName(data[i].label)})
        console.log(i === data.length, "i=",i,'data.length',data.length)
        this.dataForm.technicianName += this.labelName(data[i].label) + (data.length - i === 1 ? "" : ",")
      }
      console.log(this.dataForm.technicianList)
    },
    toggleColor() {
      this.alternateDisplay = !this.alternateDisplay;
    },
    assignments(data, propertyName) {
      this.oldBatchNumber[propertyName] = data
    },
    assignmentsList(data, propertyName) {
      console.log(data, propertyName)
      this.oldBatchNumber[propertyName].push(data)
    },
    duplicateItem() {
      // 复制最后一个项并添加到列表
      const lastItem = this.dataFormList[this.dataFormList.length - 1];
      this.dataFormList.push(lastItem);
    },

    extractContentAndAssign(data, propertyName) {
      const regex = /\(([^)]+)\)/; // 匹配括号中的内容
      const matches = data.label.match(regex); // 使用正则表达式匹配
      if (matches && matches.length > 1) {
        const content = matches[1]; // 获取匹配到的内容
        this.dataForm[propertyName] = content; // 将内容赋值给指定的属性
      }
    },
    //  获取部品列表
    getPartList(queryString, cb) {
      this.$http.get(`/fabricate/part/getPartDesignation/` + queryString).then(({data: res}) => {
        if (res.code !== 0) {
          return this.$message.error(res.msg)
        }
        const list = res.data.map((obj) => {
          return {
            customerCode: obj.customerCode,
            value: obj.designation + '(' + obj.customerCode + ')',
            partId: obj.id,
            designation: obj.designation,
            customerId: obj.customerId
          }
        })
        this.timeout = setTimeout(() => {
          cb(list);
        }, 300 * Math.random());
      }).catch(() => {
      })
    },
    //  输出选择日志
    partSelect(item) {
      this.dataForm.designation = item.designation
      this.dataForm.customerCode = item.customerCode
      this.dataForm.partId = item.partId
      this.dataForm.customerId = item.customerId
    },
    init() {
      this.visible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].resetFields()
        if (this.dataForm.id) {
          this.getInfo()
        }
      })
    },
    // 获取信息
    getInfo() {
      this.$http.get(`/batch/subbatch/${this.dataForm.id}`).then(({data: res}) => {
        if (res.code !== 0) {
          return this.$message.error(res.msg)
        }
        this.dataForm = {
          ...this.dataForm,
          ...res.data
        }
      }).catch(() => {
      })
    },
    // 表单提交
    dataFormSubmitHandle: debounce(function () {
      this.$refs['dataForm'].validate((valid) => {
        if (!valid) {
          return false
        }
        let message = '';
        if (this.dataForm.id) {
          message = '此操作将修改该数据'
        } else {
          message = '此操作将新增一条数据'
        }
        this.$confirm(message + ', 是否继续?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
          center: true
        }).then(() => {
          this.$http['post']('/batch/subbatch/saveOldSubBatch', this.dataForm).then(({data: res}) => {
            if (res.code !== 0) {
              return this.$message.error(res.msg)
            }
            localStorage.setItem("data", JSON.stringify(res.data));
            this.$bus.$emit('startTimer');
            this.$message({
              message: this.$t('prompt.success'),
              type: 'success',
              duration: 500,
              onClose: () => {
                this.visible = false
                this.$emit('refreshDataList')
              }
            })
          }).catch(() => {
          })
        }).catch(() => {
          this.$message({
            type: 'info',
            message: '已取消'
          });
        });
      })
    }, 1000, {'leading': true, 'trailing': false})
  }
}
</script>


