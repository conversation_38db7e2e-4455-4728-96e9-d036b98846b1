<template>
  <el-dialog :visible.sync="visible" :title="!dataForm.id ? $t('add') : $t('update')" :close-on-click-modal="false" :close-on-press-escape="false" append-to-body>
    <el-form :model="dataForm" :rules="dataRule" ref="dataForm" @keyup.enter.native="dataFormSubmitHandle()" :label-width="$i18n.locale === 'en-US' ? '120px' : '80px'">
      <div class="container">
        <el-form-item label="次批号编号" prop="subBatchId">
          <batch-under-component v-model="dataForm.subBatchId"  placeholder="次批号编号"></batch-under-component>
        </el-form-item>
        <el-form-item label="班别" prop="formingClass">
          <el-input v-model="dataForm.formingClass" placeholder="班别"></el-input>
        </el-form-item>
        <el-form-item label="机台号" prop="machineNumber">
          <el-input v-model="dataForm.machineNumber" placeholder="机台号"></el-input>
        </el-form-item>
        <el-form-item label="模具孔数" prop="dieHoleNumber">
          <el-input v-model="dataForm.dieHoleNumber" placeholder="模具孔数"></el-input>
        </el-form-item>
        <el-form-item label="生产模数" prop="productionModulus">
          <el-input v-model="dataForm.productionModulus" placeholder="生产模数"></el-input>
        </el-form-item>
        <el-form-item label="不良率" prop="defectiveRate">
          <el-input v-model="dataForm.defectiveRate" placeholder="不良率"></el-input>
        </el-form-item>
        <el-form-item label="实际产能" prop="actualCapacity">
          <el-input v-model="dataForm.actualCapacity" placeholder="实际产能"></el-input>
        </el-form-item>
        <el-form-item label="标准产能" prop="standardCapacity">
          <el-input v-model="dataForm.standardCapacity" placeholder="标准产能"></el-input>
        </el-form-item>
        <el-form-item label="第几模开始" prop="whichModeToStart">
          <el-input v-model="dataForm.whichModeToStart" placeholder="第几模开始"></el-input>
        </el-form-item>
        <el-form-item label="实际生产孔数" prop="actualProductionHoles">
          <el-input v-model="dataForm.actualProductionHoles" placeholder="实际生产孔数"></el-input>
        </el-form-item>
        <el-form-item label="技术员" prop="technician">
          <el-input v-model="dataForm.technician" placeholder="技术员"></el-input>
        </el-form-item>
        <el-form-item label="成型日期" prop="moldingDate">
          <el-input v-model="dataForm.moldingDate" placeholder="成型日期"></el-input>
        </el-form-item>
      </div>
      <el-form-item label="备注" prop="remark">
        <el-input v-model="dataForm.remark" placeholder="备注"></el-input>
      </el-form-item>
      </el-form>
    <template slot="footer">
      <el-button @click="visible = false">{{ $t('cancel') }}</el-button>
      <el-button type="primary" @click="dataFormSubmitHandle()">{{ $t('confirm') }}</el-button>
    </template>
  </el-dialog>
</template>

<script>
import debounce from 'lodash/debounce'
export default {
  data () {
    return {
      visible: false,
      dataForm: {
        id: '',
        subBatchId: '',
        insertNumber: '',
        insertName: '',
        sprayDate: '',
        pickingConfirmation: '',
        pickingDate: '',
        formingClass: '',
        machineNumber: '',
        vulcanizationDate: '',
        sulfurizationTemperature: '',
        machinePressure: '',
        dieHoleNumber: '',
        productionModulus: '',
        defectiveRate: '',
        actualCapacity: '',
        standardCapacity: '',
        whichModeToStart: '',
        actualProductionHoles: '',
        technician: '',
        moldingDate: '',
        remark: '',
        disabled: '',
        creator: '',
        createDate: '',
        updater: '',
        updateDate: ''
      }
    }
  },
  computed: {
    dataRule () {
      return {
        insertNumber: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        insertName: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        sprayDate: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
      }
    }
  },
  methods: {
    init () {
      this.visible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].resetFields()
        if (this.dataForm.id) {
          this.getInfo()
        }
      })
    },
    // 获取信息
    getInfo () {
      this.$http.get(`/batch/forming/${this.dataForm.id}`).then(({ data: res }) => {
        if (res.code !== 0) {
          return this.$message.error(res.msg)
        }
        this.dataForm = {
          ...this.dataForm,
          ...res.data
        }
      }).catch(() => {})
    },
    // 表单提交
    dataFormSubmitHandle: debounce(function () {
      this.$refs['dataForm'].validate((valid) => {
        if (!valid) {
          return false
        }
        this.$http[!this.dataForm.id ? 'post' : 'put']('/batch/forming/', this.dataForm).then(({ data: res }) => {
          if (res.code !== 0) {
            return this.$message.error(res.msg)
          }
          this.$message({
            message: this.$t('prompt.success'),
            type: 'success',
            duration: 500,
            onClose: () => {
              this.visible = false
              this.$emit('refreshDataList')
            }
          })
        }).catch(() => {})
      })
    }, 1000, { 'leading': true, 'trailing': false })
  }
}
</script>
