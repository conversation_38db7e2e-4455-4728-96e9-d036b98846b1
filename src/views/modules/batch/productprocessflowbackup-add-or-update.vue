<template>
  <el-dialog :visible.sync="visible" :title="!dataForm.id ? $t('add') : $t('update')" :close-on-click-modal="false" :close-on-press-escape="false" append-to-body>
    <el-form :model="dataForm" :rules="dataRule" ref="dataForm" @keyup.enter.native="dataFormSubmitHandle()" :label-width="$i18n.locale === 'en-US' ? '120px' : '80px'">
          <el-form-item label="库存总表id" prop="productInventoryId">
          <el-input v-model="dataForm.productInventoryId" placeholder="库存总表id"></el-input>
      </el-form-item>
          <el-form-item label="批号id" prop="batchId">
          <el-input v-model="dataForm.batchId" placeholder="批号id"></el-input>
      </el-form-item>
          <el-form-item label="次批号id" prop="subBatchId">
          <el-input v-model="dataForm.subBatchId" placeholder="次批号id"></el-input>
      </el-form-item>
          <el-form-item label="项目类别  0:场内(默认);1:转入;2:外加工;3:退货" prop="itemCategory">
          <el-input v-model="dataForm.itemCategory" placeholder="项目类别  0:场内(默认);1:转入;2:外加工;3:退货"></el-input>
      </el-form-item>
          <el-form-item label="转出单位" prop="transferUnit">
          <el-input v-model="dataForm.transferUnit" placeholder="转出单位"></el-input>
      </el-form-item>
          <el-form-item label="转出数量" prop="transferOutQuantity">
          <el-input v-model="dataForm.transferOutQuantity" placeholder="转出数量"></el-input>
      </el-form-item>
          <el-form-item label="转出承人" prop="transferee">
          <el-input v-model="dataForm.transferee" placeholder="转出承人"></el-input>
      </el-form-item>
          <el-form-item label="接收部门" prop="receivingDepartment">
          <el-input v-model="dataForm.receivingDepartment" placeholder="接收部门"></el-input>
      </el-form-item>
          <el-form-item label="接收者(工号)" prop="recipient">
          <el-input v-model="dataForm.recipient" placeholder="接收者(工号)"></el-input>
      </el-form-item>
          <el-form-item label="接收数量" prop="receiveQuantity">
          <el-input v-model="dataForm.receiveQuantity" placeholder="接收数量"></el-input>
      </el-form-item>
          <el-form-item label="接收时间(含时分)" prop="receivingTime">
          <el-input v-model="dataForm.receivingTime" placeholder="接收时间(含时分)"></el-input>
      </el-form-item>
          <el-form-item label="存放位置" prop="storageLocation">
          <el-input v-model="dataForm.storageLocation" placeholder="存放位置"></el-input>
      </el-form-item>
          <el-form-item label="产品货别 0:品制; 1:品检; 2:品修; 3:包装; 4:出货" prop="productCategory">
          <el-input v-model="dataForm.productCategory" placeholder="产品货别 0:品制; 1:品检; 2:品修; 3:包装; 4:出货"></el-input>
      </el-form-item>
          <el-form-item label="备注" prop="remark">
          <el-input v-model="dataForm.remark" placeholder="备注"></el-input>
      </el-form-item>
                </el-form>
    <template slot="footer">
      <el-button @click="visible = false">{{ $t('cancel') }}</el-button>
      <el-button type="primary" @click="dataFormSubmitHandle()">{{ $t('confirm') }}</el-button>
    </template>
  </el-dialog>
</template>

<script>
import debounce from 'lodash/debounce'
export default {
  data () {
    return {
      visible: false,
      dataForm: {
        id: '',
        productInventoryId: '',
        batchId: '',
        subBatchId: '',
        itemCategory: '',
        transferUnit: '',
        transferOutQuantity: '',
        transferee: '',
        receivingDepartment: '',
        recipient: '',
        receiveQuantity: '',
        receivingTime: '',
        storageLocation: '',
        productCategory: '',
        remark: '',
        disabled: '',
        creator: '',
        createDate: '',
        updater: '',
        updateDate: ''
      }
    }
  },
  computed: {
    dataRule () {
      return {
          id: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          productInventoryId: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          batchId: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          subBatchId: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          itemCategory: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          transferUnit: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          transferOutQuantity: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          transferee: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          receivingDepartment: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          recipient: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          receiveQuantity: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          receivingTime: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          storageLocation: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          productCategory: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          remark: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
                }
    }
  },
  methods: {
    init () {
      this.visible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].resetFields()
        if (this.dataForm.id) {
          this.getInfo()
        }
      })
    },
    // 获取信息
    getInfo () {
      this.$http.get(`/batch/productprocessflowbackup/${this.dataForm.id}`).then(({ data: res }) => {
        if (res.code !== 0) {
          return this.$message.error(res.msg)
        }
        this.dataForm = {
          ...this.dataForm,
          ...res.data
        }
      }).catch(() => {})
    },
    // 表单提交
    dataFormSubmitHandle: debounce(function () {
      this.$refs['dataForm'].validate((valid) => {
        if (!valid) {
          return false
        }
        this.$http[!this.dataForm.id ? 'post' : 'put']('/batch/productprocessflowbackup/', this.dataForm).then(({ data: res }) => {
          if (res.code !== 0) {
            return this.$message.error(res.msg)
          }
          this.$message({
            message: this.$t('prompt.success'),
            type: 'success',
            duration: 500,
            onClose: () => {
              this.visible = false
              this.$emit('refreshDataList')
            }
          })
        }).catch(() => {})
      })
    }, 1000, { 'leading': true, 'trailing': false })
  }
}
</script>
