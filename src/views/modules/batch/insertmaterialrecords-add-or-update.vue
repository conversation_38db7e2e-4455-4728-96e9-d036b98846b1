<template>
  <el-dialog :visible.sync="visible" :title="!dataForm.id ? $t('add') : $t('update')" :close-on-click-modal="false" :close-on-press-escape="false" append-to-body>
    <el-form :model="dataForm" :rules="dataRule" ref="dataForm" @keyup.enter.native="dataFormSubmitHandle()" :label-width="$i18n.locale === 'en-US' ? '120px' : '80px'">
      <div class="container">
        <el-form-item label="镶件批号" prop="insertNumber">
          <el-input v-model="dataForm.insertNumber" placeholder="镶件批号"></el-input>
        </el-form-item>
        <el-form-item label="镶件名称" prop="insertName">
          <el-input v-model="dataForm.insertName" placeholder="镶件名称"></el-input>
        </el-form-item>
        <el-form-item label="喷涂日期" prop="sprayDate">
          <el-input v-model="dataForm.sprayDate" placeholder="喷涂日期"></el-input>
        </el-form-item>
        <el-form-item label="领料确认者" prop="pickingConfirmation">
          <el-input v-model="dataForm.pickingConfirmation" placeholder="领料确认者"></el-input>
        </el-form-item>
        <el-form-item label="领料数量" prop="pickingQuantity">
          <el-input v-model="dataForm.pickingQuantity" placeholder="领料数量"></el-input>
        </el-form-item>
        <el-form-item label="领料日期" prop="pickingDate">
          <el-input v-model="dataForm.pickingDate" placeholder="领料日期"></el-input>
        </el-form-item>
      </div>
      <el-form-item label="备注" prop="remark">
        <el-input v-model="dataForm.remark" placeholder="备注"></el-input>
      </el-form-item>
    </el-form>
    <template slot="footer">
      <el-button @click="visible = false">{{ $t('cancel') }}</el-button>
      <el-button type="primary" @click="dataFormSubmitHandle()">{{ $t('confirm') }}</el-button>
    </template>
  </el-dialog>
</template>

<script>
import debounce from 'lodash/debounce'
export default {
  data () {
    return {
      visible: false,
      dataForm: {
        id: '',
        insertNumber: '',
        insertName: '',
        sprayDate: '',
        pickingConfirmation: '',
        pickingQuantity: '',
        pickingDate: '',
        remark: '',
        disabled: '',
        creator: '',
        createDate: '',
        updater: '',
        updateDate: ''
      }
    }
  },
  computed: {
    dataRule () {
      return {
          id: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          insertNumber: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          insertName: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          sprayDate: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          pickingConfirmation: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          pickingQuantity: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          pickingDate: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          remark: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
                }
    }
  },
  methods: {
    init () {
      this.visible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].resetFields()
        if (this.dataForm.id) {
          this.getInfo()
        }
      })
    },
    // 获取信息
    getInfo () {
      this.$http.get(`/batch/insertmaterialrecords/${this.dataForm.id}`).then(({ data: res }) => {
        if (res.code !== 0) {
          return this.$message.error(res.msg)
        }
        this.dataForm = {
          ...this.dataForm,
          ...res.data
        }
      }).catch(() => {})
    },
    // 表单提交
    dataFormSubmitHandle: debounce(function () {
      this.$refs['dataForm'].validate((valid) => {
        if (!valid) {
          return false
        }
        this.$http[!this.dataForm.id ? 'post' : 'put']('/batch/insertmaterialrecords/', this.dataForm).then(({ data: res }) => {
          if (res.code !== 0) {
            return this.$message.error(res.msg)
          }
          this.$message({
            message: this.$t('prompt.success'),
            type: 'success',
            duration: 500,
            onClose: () => {
              this.visible = false
              this.$emit('refreshDataList')
            }
          })
        }).catch(() => {})
      })
    }, 1000, { 'leading': true, 'trailing': false })
  }
}
</script>
