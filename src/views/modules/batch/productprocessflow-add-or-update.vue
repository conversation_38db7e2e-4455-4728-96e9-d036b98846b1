<template>
  <div>
    <div>
  <el-dialog :visible.sync="visible" :title="!dataForm.id ? '新增工序流转记录' : '修改工序流转记录'"
             :close-on-click-modal="false" :close-on-press-escape="false" append-to-body>
    <el-form :model="dataForm" :rules="dataRule" ref="dataForm"
             :label-width="$i18n.locale === 'en-US' ? '120px' : '80px'">
      <div class="container">
        <div class="box">
          <el-form-item label="部品品号" prop="partId">
            <part-number-component v-model="dataForm.partId" @partData="partSubBatchData"
                                   placeholder="请输入部品品号"></part-number-component>
          </el-form-item>
        </div>
        <div class="box">
          <el-form-item label="批号查询" prop="subBatchId">
            <batch-under-component ref="batchUpdate" remote v-model="dataForm.subBatchId"
                                   @batchSubBatchData="batchSubBatchData"
                                   placeholder="请输入批号"></batch-under-component>
          </el-form-item>
          <div style="margin-left: 80px">
            <el-button type="primary" @click="visible1 = true">新增批次号</el-button>
          </div>
        </div>
      </div>
      <div class="container">
        <div class="box">
          <el-form-item label="批号" prop="temporaryBatchNumber">
            <span class="font_size">{{ dataForm.temporaryBatchNumber }}</span>
          </el-form-item>
        </div>
        <div class="box">
          <el-form-item label="项目类别" prop="itemCategory">
            <ren-select-item-category v-model="dataForm.itemCategory" placeholder="请选择项目类别"
                                      dict-type="item_category"></ren-select-item-category>
          </el-form-item>
        </div>
      </div>
      <div class="container">
        <div class="item">
          <el-form-item label="转出单位" prop="productCategory">
            <ren-select-product-category @change="dropdownSelectionBox" v-model="dataForm.transferUnit"
                                         placeholder="转出单位"
                                         dict-type="product_category"></ren-select-product-category>
          </el-form-item>
        </div>
        <div class="item">
          <el-form-item label="转出者" prop="transferee">
            <employee-component v-model="dataForm.transferee" placeholder="工号"></employee-component>
          </el-form-item>
        </div>
      </div>
      <div class="container">
        <div class="box">
          <el-form-item label="转出数量" prop="transferOutQuantity">
            <el-input v-model.number="dataForm.transferOutQuantity" type="number" class="input-green"
                      placeholder="转出数量" style="width: 30%;"></el-input>
          </el-form-item>
        </div>
        <div style="width: 10vw" v-if="currentQuantity">
          当前单位数量：<span class="font_size">{{ currentQuantity }}</span>
        </div>
        <div v-if="currentQuantity">
          成形不良：<span class="font_size">{{ formingDefectQuantity }}</span>
        </div>
      </div>

      <div class="container">
        <div class="box">
          <el-form-item label="损耗数量">
            <el-input v-model.number="dataForm.numberOfLosses" type="number" placeholder="损耗数" style="width: 15vw;"
                      class="birthday"></el-input>
            <p style="display: none">{{ numberOfLosses }}</p>
          </el-form-item>
        </div>
        <div v-if="dataForm.subBatchId" style="width: 10vw">
          成形孔数:<span class="font_size">{{numberOfHoles}}</span>
        </div>
        <div v-if="currentQuantity">
          品修不良：<span class="font_size">{{ repairDefectQuantity }}</span>
        </div>
      </div>

      <div class="container">
        <div class="box">
          <el-form-item label="接收数量" prop="receiveQuantity">
            <el-input v-model.number="dataForm.receiveQuantity" type="number" placeholder="接收数量"
                      style="width: 15vw;" class="birthday"></el-input>
          </el-form-item>
        </div>
        <div v-if="dataForm.subBatchId" style="width: 10vw">
          成形模数:<span class="font_size">{{productionModulus}}</span>
        </div>
        <div v-if="currentQuantity">
          品检不良：<span class="font_size">{{ inspectDefectQuantity }}</span>
        </div>
      </div>
      <div class="container">
        <div class="item">
          <el-form-item label="接收单位" prop="receivingDepartment">
            <ren-select-product-category v-model="dataForm.receivingDepartment" placeholder="接收单位"
                                         dict-type="product_category"></ren-select-product-category>
          </el-form-item>
        </div>
        <div class="item">
          <el-form-item label="接收者" prop="recipient">
            <employee-component v-model="dataForm.recipient" placeholder="工号"></employee-component>
          </el-form-item>
        </div>
      </div>
      <div class="container">
        <div class="box">
          <el-form-item label="接收时间" prop="receivingTime">
            <el-date-picker
                style="width: 200px"
                v-model="dataForm.receivingTime"
                type="datetime"
                value-format="yyyy-MM-dd HH:mm:ss"
                placeholder="请选择接收时间(含时分)">
            </el-date-picker>
          </el-form-item>
        </div>
        <div class="box">
          <el-form-item label="存放位置" prop="storageLocation">
            <el-input v-model="dataForm.storageLocation" placeholder="存放位置"></el-input>
          </el-form-item>
        </div>
      </div>
      <el-form-item label="备注" prop="remark">
        <el-input v-model="dataForm.remark" placeholder="备注"></el-input>
      </el-form-item>
    </el-form>
    <template slot="footer">
<!--      <el-button type="primary" @click="oldBatchAddOrUpdateHandle">新增旧批号</el-button>-->
      <el-button @click="visible = false">{{ $t('cancel') }}</el-button>
      <el-button type="primary" @click="dataFormSubmitHandle()">{{ $t('confirm') }}</el-button>
    </template>
    <div>
      <old-batch-add v-if="oldBatchAddVisible" ref="oldBatchAddParameter"></old-batch-add>
      <batch-add-or-update v-if="batchAddOrUpdateVisible" ref="batchAddOrUpdate"
                           @callbackFlow="callbackFlowDataFrom($event)"></batch-add-or-update>
      <sub-batch-add-or-update v-if="subAddOrUpdateVisible" ref="subAddOrUpdate"
                               @callbackFlow="callbackFlowDataFrom($event)"></sub-batch-add-or-update>
      <!--   外转入批号   -->
      <transfer-batch-number v-if="transferBatchNumberVisible" ref="transferUpdate"></transfer-batch-number>
    </div>
  </el-dialog>
    </div>

    <div>
      <el-dialog :visible.sync="visible1" title="新增批次号" >
        <el-form :model="batchForm" :rules="batchRules" ref="batchForm" label-width="80px">
          <div style="display: flex">
            <el-form-item label="品名" prop="batchNumber" style="width: 30%">
              <el-select v-model="batchForm.partId"  filterable  @visible-change="partHandleVisibleChange" placeholder="请选择"  style="width: 227px">
                <el-option
                    v-for="(item,index) in partOptions"
                    :key="index"
                    :label="item.label"
                    :value="item.value">
                </el-option>
              </el-select>
            </el-form-item>
<!--            <el-form-item label="胶料类别" prop="itemCategory" style="width: 334px">-->
<!--              <el-radio-group v-model="batchForm.sizeCategory">-->
<!--                <el-radio-button label="1">C</el-radio-button>-->
<!--                <el-radio-button label="2">F</el-radio-button>-->
<!--                <el-radio-button label="3">T</el-radio-button>-->
<!--                <el-radio-button label="4">R</el-radio-button>-->
<!--              </el-radio-group>-->
<!--            </el-form-item>-->
            <el-form-item label="批号" prop="batchNumber" style="width: 38%">
              <el-input v-model="batchForm.batchNumber" placeholder="请输入批号" style="width: 65%" @blur="getNextSubBatch"></el-input>
              -
              <el-input v-model="batchForm.subBatchNumber" style="width: 28%" disabled></el-input>
            </el-form-item>
            <el-form-item label="当前工序" prop="currentProcess"  style="width: 28%">
              <el-select v-model="batchForm.currentProcess" placeholder="请选择" style="width: 99%">
                <el-option
                    v-for="(item,index) in processOptions"
                    :key="index"
                    :label="item.label"
                    :value="item.value">
                </el-option>
              </el-select>
            </el-form-item>
          </div>
          <div style="display: flex">
            <el-form-item label="数量" prop="productionQuantity" style="width: 28%">
              <el-input v-model="batchForm.productionQuantity" placeholder="请输入数量"></el-input>
            </el-form-item>
            <el-form-item label="转入类型" prop="isInventory"  style="width: 28%">
              <el-select v-model="batchForm.isInventory" placeholder="请选择" style="width: 99%">
                <el-option
                    v-for="(item,index) in isInventoryOptions"
                    :key="index"
                    :label="item.label"
                    :value="item.value">
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="备注" style="width: 40%">
              <el-input v-model="batchForm.remark" type="textarea" :rows="4" style="90%"></el-input>
            </el-form-item>
          </div>
        </el-form>
          <template slot="footer">
        <el-button @click="closeBatchFormSubmitHandle">{{ $t('cancel') }}</el-button>
        <el-button type="primary" @click="batchFormSubmitHandle()">{{ $t('confirm') }}</el-button>
          </template>
      </el-dialog>
    </div>
  </div>

</template>

<script>
import debounce from 'lodash/debounce'
import SubBatchAddOrUpdate from "./subbatch-add-or-update.vue";
import oldBatchAdd from "./old-batch-add.vue";
import {MessageBox} from "element-ui";
import BatchAddOrUpdate from "./batch-add-or-update.vue";
import {getDictLabel} from "@/utils/index"
import TransferBatchNumber from "./transfer-batch-number.vue";

export default {
  data() {
    return {
      currentQuantity: 0,
      //成形不良
      formingDefectQuantity: 0,
      //品修不良
      repairDefectQuantity:0,
      //品检不良
      inspectDefectQuantity:0,
      promptStatement: '',
      subAddOrUpdateVisible: false,
      batchAddOrUpdateVisible: false,
      transferBatchNumberVisible: false,
      newOldVisible: false,
      oldBatchAddVisible: false,
      visible: false,
      visible1:false,
      showOldBatchNumber: false,
      batchForm:{
          partId:'',
          sizeCategory:'',
          currentProcess:'',
          productionQuantity:'',
          batchNumber:'',
          isInventory:'',
          subBatchNumber:'',
          remark:''
      },
      partOptions:[],
      productionModulus:0,
      numberOfHoles:0,
      // practicalForming:0,
      dataForm: {
        temporaryBatchNumber: '',
        id: '',
        productInventoryId: '',
        batchId: '',
        sizeCategory: '',
        batchNumber: '',
        selectBatchNumber: '',
        partId: '',
        designation: '',
        subBatchId: '',
        subBatchNumber: '',
        locking: 0,
        itemCategory: 0,
        transferUnit: 0,
        transferUnitName: '',
        deptId: '',
        transferDeptName: '',
        transferOutQuantity: 100,
        transferee: '',
        receivingDepartment: 4,
        receivingDeptName: '',
        recipient: '',
        receiveQuantity: 100,
        numberOfLosses: 0,
        receivingTime: this.formatTimes(new Date),
        storageLocation: '',
        remark: '',
        disabled: '',
        creator: '',
        createDate: '',
        updater: '',
        updateDate: ''
      },
      initialization: {
        temporaryBatchNumber: '',
        id: '',
        productInventoryId: '',
        batchId: '',
        sizeCategory: '',
        batchNumber: '',
        selectBatchNumber: '',
        partId: '',
        designation: '',
        subBatchId: '',
        subBatchNumber: '',
        locking: 0,
        itemCategory: 0,
        transferUnit: 0,
        transferUnitName: '',
        deptId: '',
        transferDeptName: '',
        transferOutQuantity: 100,
        transferee: '',
        receivingDepartment: 4,
        receivingDeptName: '',
        recipient: '',
        receiveQuantity: 100,
        receivingTime: this.formatTimes(new Date),
        storageLocation: '',
        remark: '',
        disabled: '',
        creator: '',
        createDate: '',
        updater: '',
        updateDate: ''
      },
      processOptions:[
        {
          label:'成形',
          value:1
        },
        {
          label:'品修',
          value:2
        },
        {
          label:'品检',
          value:3
        },
      ],
      isInventoryOptions: [
        {
          label: '库存内',
          value: 1
        },
        {
          label: '库存外',
          value: 0
        }
        ]
    }
  },
  components: {
    TransferBatchNumber,
    oldBatchAdd,
    BatchAddOrUpdate,
    SubBatchAddOrUpdate,
  },
  computed: {
    batchRules() {
      return {
        batchNumber: [
          {required: true, message: this.$t('validate.required'), trigger: 'blur'},
          {
            pattern: /^[A-Z0-9]{10}$/,
            message: '批次号格式错误',
            trigger: 'blur'
          }
        ],
        subBatchNUmber: [
          {required: true, message: this.$t('validate.required'), trigger: 'blur'}
        ],
        partId: [
          {required: true, message: this.$t('validate.required'), trigger: 'blur'}
        ],
        currentProcess: [
          {required: true, message: this.$t('validate.required'), trigger: 'blur'}
        ],
        sizeCategory: [
          {required: true, message: this.$t('validate.required'), trigger: 'blur'}
        ],
        productionQuantity: [
          {required: true, message: this.$t('validate.required'), trigger: 'blur'}
        ],
        isInventory: [
          {required: true, message: this.$t('validate.required'), trigger: 'blur'}
        ],
      }
    },
    dataRule() {
      return {
        transferOutQuantity: [
          {validator: this.checkTransferOutQuantity, trigger: 'blur'},
          {required: false, message: this.promptStatement, trigger: 'blur'}
        ],
        batchNumber: [
          {required: true, message: this.$t('validate.required'), trigger: 'blur'}
        ],
      }
    },
    numberOfLosses: function () {
      let number = this.dataForm.transferOutQuantity - this.dataForm.numberOfLosses;
      console.log(this.dataForm.transferOutQuantity + '-' + this.dataForm.numberOfLosses + '=' + number)
      this.dataForm.receiveQuantity = number
      return number
    },
  },
  watch: {
    'dataForm.subBatchId': function (newValue, oldValue) {
      this.dropdownSelectionBox()
      this.getPracticalHolesAndModulus()
      this.getDefectQuantity()
    },
    'dataForm.partId': function (newValue, oldValue) {
      this.dropdownSelectionBox()
    },
    // "dataForm.itemCategory"() {
    //   this.addTransferHandle()
    // }
  },
  created() {
    this.initializations()
    this.getPartList()
    this.$bus.$on('startTimer', this.startTimer);
    this.$retures.$on('param', this.param);
  },
  beforeDestroy() {
    this.$bus.$off('startTimer', this.startTimer);
    this.$retures.$off('param', this.param);
  },
  methods: {
    // 新增转入批号
    addTransferHandle(id) {
      this.transferBatchNumberVisible = true
      this.$nextTick(() => {
        this.$refs.transferUpdate.dataForm.id = id
        this.$refs.transferUpdate.dataForm.itemCategory = this.dataForm.itemCategory
        this.$refs.transferUpdate.init()
      })
    },
    closeBatchFormSubmitHandle() {
      this.visible1 = false
      this.batchForm = {
        partId:'',
        sizeCategory:'',
        productionQuantity:'',
        batchNumber:'',
        isInventory:'',
        subBatchNumber:'',
        remark:''
      }
    },
    // 表单提交
    batchFormSubmitHandle: debounce(function () {
      this.$refs['batchForm'].validate((valid) => {
        if (!valid) {
          return false
        }
        this.$confirm('此操作将新增一条数据, 是否继续?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
          center: true
        }).then(() => {
          this.$http.post('/batch/subbatch/saveNewBatch', this.batchForm).then(({ data: res }) => {
            if (res.code !== 0) {
              return this.$message.error(res.msg)
            }
            this.$message({
              message: this.$t('prompt.success'),
              type: 'success',
              duration: 500,
              onClose: () => {
                this.visible1 = false
                this.dataForm.partId = this.batchForm.partId
                this.partSubBatchData(this.batchForm);
                this.batchForm = {
                  partId:'',
                  sizeCategory:'',
                  productionQuantity:'',
                  isInventory:'',
                  batchNumber:'',
                  subBatchNumber:'',
                  remark:''
                }
              }
            })
          }).catch(() => {})
        }).catch(() => {
          this.$message({
            type: 'info',
            message: '已取消'
          });
        });
      })
    }, 1000, { 'leading': true, 'trailing': false }),
    partHandleVisibleChange(){

    },
    //当批次号符合验证规则后失去焦点触发，自动获取下一个批次
    getNextSubBatch(){
      if(/^[A-Z0-9]{10}$/.test(this.batchForm.batchNumber)){
        this.$http.get(`/batch/subbatch/getNextBatchNumber/${this.batchForm.batchNumber}`).then(({data:res}) => {
          if(res.code !== 0){
            return this.$message.error(res.msg)
          }
          this.batchForm.subBatchNumber = res.data
        })
      }
    },

    //查询匹配品番列表
    getPartList() {
      // 根据参数是否存在来确定发送的请求
      const url = '/fabricate/part/getPartListByDesignation';
      console.log('请求参数:' + url)
      this.$http.get(url).then(({data: res}) => {
        if (res.code !== 0) {
          return this.$message.error(res.msg);
        }
        this.partOptions = res.data.map((obj) => {
          return {
            label: obj.designation,
            value: obj.id,
            data: obj
          };
        });
      }).catch(() => {
        // 错误处理
      });
    },
    batchSubBatchData(data) {
      this.dataForm.partId = data.data.partId
      this.dataForm.temporaryBatchNumber = data.label.substring(0, data.label.indexOf('('));
    },
    partSubBatchData(data) {
      console.log(data.partId, '部品id')
      this.dataForm.partId = data.partId
      this.dataForm.subBatchId = ''
      this.dataForm.temporaryBatchNumber = ''
      this.$nextTick(() => {
        this.$refs.batchUpdate.selectButchNumberInfo(this.dataForm.partId)
      })
    },
    checkTransferOutQuantity(rule, value, callback) {
      if (value > this.currentQuantity) {
        this.locking = 1
        callback(new Error('数量不符，请慎重填写'));
      } else {
        this.locking = 0
        callback();
      }
    },
    dropdownSelectionBox(value) {
      let operation = 0
      console.log("转出单位======" + this.dataForm.transferUnit)
      if (this.dataForm.transferUnit) {
        operation = this.dataForm.transferUnit
      }
      this.$http.get(`/batch/subbatch/queryTheCurrentUnitQuantity?subBatchId=` + this.dataForm.subBatchId + `&productCategoryTransferOut=
      ` + operation).then(({data: res}) => {
        if (res.code !== 0) {
          return this.$message.error(res.msg)
        }
        this.currentQuantity = res.data
        this.dataForm.transferOutQuantity = res.data
        this.dataForm.receiveQuantity = res.data
      }).catch(() => {
      })
    },
    //获取该次批实际成形孔数与模数
    getPracticalHolesAndModulus(){
      if(this.dataForm.subBatchId !== undefined && this.dataForm.subBatchId !== ''){
        console.log("subBatchId========" + this.dataForm.subBatchId)
        this.$http.get(`/batch/subbatch/getPracticalHolesAndModulus?subBatchId=` + this.dataForm.subBatchId).then(({data: res}) =>{
          if(res.code !== 0){
            return this.$message.error(res.msg)
          }
          console.log("res.data==================" + res.data)
          this.productionModulus = res.data.productionModulus;
          this.numberOfHoles = res.data.numberOfHoles;
          console.log("this.productionModulus===================" + this.productionModulus)
          console.log("this.numberOfHoles===================" + this.numberOfHoles)
        })
      }
    },
    //获取各工序不良数
    getDefectQuantity(){
      if(this.dataForm.subBatchId !== undefined && this.dataForm.subBatchId !== ''){
        this.$http.get(`batch/productprocessflow/getDefectQuantity?subBatchId=` + this.dataForm.subBatchId).then(({data: res}) => {
          if(res.code !== 0){
            return this.$message.error(res.msg)
          }
          this.formingDefectQuantity = res.data.formingNum
          this.repairDefectQuantity = res.data.repairNum
          this.inspectDefectQuantity = res.data.inspectNum
        })
      }
    },
    // 获取系统当前日期及时间
    formatTimes(date) {
      let year = date.getFullYear();
      let month = date.getMonth() + 1;
      let day = date.getDate();
      let hours = date.getHours();
      let minutes = date.getMinutes();
      let seconds = date.getSeconds();

      month = month < 10 ? "0" + month : month;
      day = day < 10 ? "0" + day : day;
      hours = hours < 10 ? "0" + hours : hours;
      minutes = minutes < 10 ? "0" + minutes : minutes;
      seconds = seconds < 10 ? "0" + seconds : seconds;

      return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
    },

    //初始化
    initializations() {
      this.dataForm = {
        ...this.dataForm,
        ...this.initialization
      }
    },
    //胶料类别获取
    sizeCategoryConvert(values) {
      switch (values) {
        case 1:
          return 'C'
        case 2:
          return 'F'
        case 3:
          return 'T'
        case 4:
          return "R"
      }
    },
    callbackFlowDataFrom(value) {
      this.dataForm = {
        ...this.dataForm,
        ...value
      }
      let sizeCategory = this.getDictLabel("size_category", value.sizeCategory)
      this.dataForm.temporaryBatchNumber = sizeCategory + value.batchNumber + '-' + value.subBatchNumber
      this.$nextTick(() => {
        this.$refs.batchUpdate.selectButchNumberInfo()
      })
    },
    showMessage(message, click, batchId) {
      MessageBox.confirm(message, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        if (click) {
          this.insertBatchOrSubBatch("批号", batchId)
        } else {
          this.insertBatchOrSubBatch("次批号", batchId)
        }
      }).catch(() => {
        this.initializations()
        // 点击取消按钮后的回调函数
        console.log('点击了取消按钮')
        this.dataForm.paramStr = ''
      })
    },
    //新增批号或者次批号
    insertBatchOrSubBatch(logo, id) {
      if (logo === '批号') {
        this.batchAddOrUpdateVisible = true
        this.$nextTick(() => {
          this.$refs.batchAddOrUpdate.dataForm.id = id
          this.$refs.batchAddOrUpdate.init()
        })
      } else {
        this.subAddOrUpdateVisible = true
        this.$nextTick(() => {
          this.$refs.subAddOrUpdate.batchNumberId = id
          this.$refs.subAddOrUpdate.init()
        })
      }
    },
    getPlaceholder(judge) {
      var placeholder
      if (judge === 'inputOne') {
        placeholder = this.$refs.inputBatch.$attrs.placeholder;
        console.log(placeholder); // 输出 placeholder 值
      } else if (judge === 'inputTow') {
        placeholder = this.$refs.inputDesignation.$attrs.placeholder;
        console.log(placeholder); // 输出 placeholder 值
      }
      this.$router.push({name: 'batch-insertbatchsubbatch', query: {param: placeholder}})
    },
    param: function () {
      let adadf = setInterval(() => {
        let paramData = JSON.parse(localStorage.getItem("paramData"));
        if (paramData != null) {
          console.log(paramData, "回调参数")
          clearInterval(adadf)
        }
      }, 1000)
      console.log("来了")
    },
    //获取旧批号数据
    startTimer() {
      var productProcessFlowData = setInterval(() => {
        let data = JSON.parse(localStorage.getItem("data"));
        if (data != null) {
          this.dataForm = {
            ...this.dataForm,
            ...data
          }
          localStorage.removeItem("data");
          clearInterval(productProcessFlowData)
        }
      }, 1000)
    },
    oldBatchAddOrUpdateHandle() {
      if (this.dataForm.itemCategory == 1) {
        this.addTransferHandle()
      } else {
        this.oldBatchAddVisible = true
        this.$nextTick(() => {
          this.$refs.oldBatchAddParameter.init()
        })
      }

    },
    //模糊查询批号
    selectButchNumberInfo(batchNumber, cb) {
      this.$http.get(`/batch/batch/selectButchNumberInfo/` + batchNumber).then(({data: res}) => {
        if (res.code !== 0) {
          return this.$message.error(res.msg)
        }
        const list = res.data.map((obj) => {
          return {
            batchId: obj.id,
            batchNumber: obj.batchNumber,
            value: obj.sizeCategory + obj.batchNumber +
                (obj.subBatchNumber != null ? ('-' + obj.subBatchNumber) : '') +
                '(' + obj.designation + ')',
            designation: obj.designation,
            selectBatchNumber: obj.sizeCategory + obj.batchNumber + '-' + obj.subBatchNumber,
            temporaryBatchNumber: obj.sizeCategory + obj.batchNumber + '-' + obj.subBatchNumber,
            subBatchNumber: obj.subBatchNumber,
            subBatchId: obj.subBatchId,
            partId: obj.partId
          }
        })
        this.timeout = setTimeout(() => {
          cb(list);
        }, 3000 * Math.random());

        if (res.data === null || res.data.length === 0) {
          MessageBox.confirm('查无此批号，是否进行生成？', '提示', {
            confirmButtonText: '生成批号',
            cancelButtonText: '取消',
            type: 'warning'
          }).then(() => {
            this.oldBatchAddOrUpdateHandle()
            return;
          }).catch(() => {
            this.initializations()
          })
        }

      }).catch(() => {
        this.initializations()
      })
    },
    //  输出选择日志
    handleSelect(item) {
      this.dataForm.partId = item.partId
      this.dataForm.batchId = item.batchId
      if (item.batchNumber != null && item.batchNumber != '') {
        MessageBox.confirm('查无此批号，是否进行生成？', '提示', {
          confirmButtonText: '生成批号',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          this.oldBatchAddOrUpdateHandle()
          return;
        }).catch(() => {
          this.initializations()
        })
        this.dataForm.batchNumber = item.batchNumber
      }
      this.dataForm.designation = item.designation
      this.dataForm.selectBatchNumber = item.selectBatchNumber
      this.dataForm.temporaryBatchNumber = item.temporaryBatchNumber
      if (item.subBatchNumber != null && item.subBatchNumber != '') {
        MessageBox.confirm('该批号已有批次，是使用现有批次还是生成新批次使用？', '提示', {
          confirmButtonText: '生成新批次',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          this.insertBatchOrSubBatch("次批号", item.batchId)
          return;
        }).catch(() => {

        })
        this.dataForm.subBatchNumber = item.subBatchNumber
      } else {
        this.showMessage("该批号还未生成批次是否进行生成次批次？", false, item.batchId)
        return;
      }
      this.dataForm.subBatchId = item.subBatchId

    },
    init() {
      this.visible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].resetFields()
        if (this.dataForm.id) {
          this.getInfo()
        }
        this.numberOfHoles = 0
        this.productionModulus = 0
      })
    },
    // 获取信息
    getInfo() {
      this.$http.get(`/batch/productprocessflow/${this.dataForm.id}`).then(({data: res}) => {
        if (res.code !== 0) {
          return this.$message.error(res.msg)
        }
        this.dataForm = {
          ...this.dataForm,
          ...res.data
        }
      }).catch(() => {
      })
    },
    // 表单提交
    dataFormSubmitHandle: debounce(function () {
      this.$refs['dataForm'].validate((valid) => {
        if (!valid) {
          return false
        }
        let message = '';
        if (this.dataForm.id) {
          message = '此操作将修改该数据'
        } else {
          message = '此操作将新增一条数据'
        }
        this.$confirm(message + ', 是否继续?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
          center: true
        }).then(() => {
          this.$http[!this.dataForm.id ? 'post' : 'put']('/batch/productprocessflow/', this.dataForm).then(({data: res}) => {
            if (res.code !== 0) {
              return this.$message.error(res.msg)
            }
            this.$message({
              message: this.$t('prompt.success'),
              type: 'success',
              duration: 500,
              onClose: () => {
                this.visible = false
                this.$emit('refreshDataList')
              }
            })
          }).catch(() => {
          })
        }).catch(() => {
          this.$message({
            type: 'info',
            message: '已取消'
          });
        });
      })
    }, 1000, {'leading': true, 'trailing': false})
  }
}
</script>
<style lang="scss" scoped>
::v-deep .birthday .el-input__inner {
  background-color: #e8e889 !important;
  color: black !important;
}

::v-deep .input-green .el-input__inner {
  width: 10vw;
  background-color: #88e388 !important; /* 设置输入框的背景色为绿色 */
  color: black !important;
}
</style>

<style scoped>
.jobNumber {
  min-width: 200px;
}

.container {
  display: flex;
  flex-wrap: wrap;
}

.item {
  width: calc(33.33% - 10px);
  margin: 5px;
}

.three {
  width: calc(20% - 10px);
  margin: 5px;
}

@media screen and (max-width: 768px) {
  .container {
    display: block; /* Change container display to block */
  }

  .three {
    width: 100%; /* Change item width to 100% */
  }
}

@media screen and (max-width: 768px) {
  .container {
    display: block; /* Change container display to block */
  }

  .item {
    width: 100%; /* Change item width to 100% */
  }
}

@media (min-width: 768px) and (max-width: 1023px) {
  .container {
    display: block; /* Change container display to block */
  }

  .item {
    width: calc(50% - 10px);
  }
}

@media (min-width: 1024px) {
  .item {
    width: calc(33.33% - 10px);
  }
}

.box {
  width: calc(50% - 10px);
  margin: 5px;
}

@media screen and (max-width: 767px) {
  .container {
    display: block; /* Change container display to block */
  }

  .box {
    width: 100%;
  }
}

</style>
