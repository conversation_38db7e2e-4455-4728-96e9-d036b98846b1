<template>
  <div>
    <el-dialog :visible.sync="visible" title="次批号讯息" :close-on-click-modal="false" :close-on-press-escape="false" append-to-body>
      <el-table :data="dataLists" border>
        <el-table-column prop="batchNumber" label="次批号" header-align="center" align="center">
          <template slot-scope="scope">
            <div>
              {{$getDictLabel("size_category", scope.row.sizeCategory)+ scope.row.batchNumber+'-'+scope.row.subBatchNumber}}
            </div>
          </template>
        </el-table-column>
        <el-table-column  prop="moldNumber" label="模具"  header-align="center" align="center"></el-table-column>
        <el-table-column label="技术员" header-align="center" align="center">
          <template slot-scope="scope">
            <div v-for="(item,index) in scope.row.technicianList" :key="index">
              {{$getEmployeesList(item.technician)}}
            </div>
          </template>
        </el-table-column>
        <el-table-column :label="$t('handle')" fixed="right" header-align="center" align="center" width="150">
          <template slot-scope="scope">
            <el-button type="text" @click="clickToQueryDetails(scope.row.formingId)">点击查询明细</el-button>
          </template>
        </el-table-column>
      </el-table>

    </el-dialog>
    <production-process-add-or-update v-if="subAddOrUpdateVisible" ref="subAddOrUpdate" :input-data-from="batchData" :permission-hiding="permissionHiding"></production-process-add-or-update>
  </div>
</template>

<script>
import debounce from 'lodash/debounce'
import mixinViewModule from '@/mixins/view-module'
import productionProcessAddOrUpdate from "./production-process-add-or-update.vue";
export default {
  mixins: [mixinViewModule],
  props:{
    batchData:{
      type:Object,
      require:true
    },
    permissionHiding:{
      type:Boolean,
      default:true
    }
  },
  data () {
    return {
      visible: false,
      subAddOrUpdateVisible: false,
      dataLists: [],
      dataFormList:[],
      batchId:'',
      dataForm:{}
    }
  },
  components:{
    productionProcessAddOrUpdate
  },
  methods: {
    clickToQueryDetails(formingId){
      this.subAddOrUpdateVisible = true
      this.$nextTick(() => {
        this.$refs.subAddOrUpdate.dataForm.id = formingId
        this.$refs.subAddOrUpdate.informationVisible = true
        this.$refs.subAddOrUpdate.displayButton = false
        this.$refs.subAddOrUpdate.init()
      })
    },
    init () {
      this.visible = true
      this.$nextTick(() => {
        this.getInfo()
      })
    },
    // 获取信息
    getInfo () {
      this.$http.get(`/batch/subbatch/querySubBatchDetails/${this.batchId}`).then(({ data: res }) => {
        if (res.code !== 0) {
          return this.$message.error(res.msg)
        }
        this.dataLists = res.data
      }).catch(() => {})
    },
  }
}
</script>
