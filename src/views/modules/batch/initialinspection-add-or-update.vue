<template>
  <el-dialog :visible.sync="visible" :title="!dataForm.id ? '新增始业点检确认表' : '修改始业点检确认表'" :close-on-click-modal="false" :close-on-press-escape="false" append-to-body>
    <el-form :model="dataForm" :rules="dataRule" ref="dataForm" @keyup.enter.native="dataFormSubmitHandle()" :label-width="$i18n.locale === 'en-US' ? '120px' : '80px'">
      <el-divider content-position="left">
        <span style="font-size: 20px; font-weight: bold;">始业点检确认</span>
      </el-divider>
      <el-form-item label="成形条件" prop="formingConditions">
        <ren-radio-group v-model="dataForm.formingConditions" dict-type="yes_no"></ren-radio-group>
      </el-form-item>
      <el-form-item label="材料" prop="material">
        <ren-radio-group v-model="dataForm.material" dict-type="prenatal_remnants"></ren-radio-group>
      </el-form-item>
      <el-form-item label="毛边" prop="roughEdge">
        <ren-radio-group v-model="dataForm.roughEdge" dict-type="prenatal_remnants"></ren-radio-group>
      </el-form-item>
      <el-form-item label="不良品" prop="defectiveProducts">
        <ren-radio-group v-model="dataForm.defectiveProducts" dict-type="prenatal_remnants"></ren-radio-group>
      </el-form-item>
      <el-form-item label="辅助工具是否完好" prop="auxiliaryTools"  label-width="160px">
        <ren-radio-group v-model="dataForm.auxiliaryTools" dict-type="prenatal_remnants"></ren-radio-group>
      </el-form-item>
    </el-form>
    <template slot="footer">
      <el-button @click="visible = false">{{ $t('cancel') }}</el-button>
      <el-button type="primary" @click="dataFormSubmitHandle()">{{ $t('confirm') }}</el-button>
    </template>
  </el-dialog>
</template>

<script>
import debounce from 'lodash/debounce'
export default {
  data () {
    return {
      visible: false,
      dataForm: {
        id: '',
        subBatchId: '',
        formingConditions: '',
        material: '',
        roughEdge: '',
        defectiveProducts: '',
        auxiliaryTools: '',
        remark: '',
        disabled: '',
        creator: '',
        createDate: '',
        updater: '',
        updateDate: ''
      }
    }
  },
  computed: {
    dataRule () {
      return {
          id: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          subBatchId: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          formingConditions: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          material: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          roughEdge: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          defectiveProducts: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          auxiliaryTools: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
      }
    }
  },
  methods: {
    init () {
      this.visible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].resetFields()
        if (this.dataForm.id) {
          this.getInfo()
        }
      })
    },
    // 获取信息
    getInfo () {
      this.$http.get(`/batch/initialinspection/${this.dataForm.id}`).then(({ data: res }) => {
        if (res.code !== 0) {
          return this.$message.error(res.msg)
        }
        this.dataForm = {
          ...this.dataForm,
          ...res.data
        }
      }).catch(() => {})
    },
    // 表单提交
    dataFormSubmitHandle: debounce(function () {
      this.$refs['dataForm'].validate((valid) => {
        if (!valid) {
          return false
        }
        this.$http[!this.dataForm.id ? 'post' : 'put']('/batch/initialinspection/', this.dataForm).then(({ data: res }) => {
          if (res.code !== 0) {
            return this.$message.error(res.msg)
          }
          this.$message({
            message: this.$t('prompt.success'),
            type: 'success',
            duration: 500,
            onClose: () => {
              this.visible = false
              this.$emit('refreshDataList')
            }
          })
        }).catch(() => {})
      })
    }, 1000, { 'leading': true, 'trailing': false })
  }
}
</script>
