<template>
  <el-dialog :visible.sync="visible" :title="!dataForm.id ? $t('add') : $t('update')" :close-on-click-modal="false" :close-on-press-escape="false" append-to-body>
    <el-form :model="dataForm" :rules="dataRule" ref="dataForm" @keyup.enter.native="dataFormSubmitHandle()" :label-width="$i18n.locale === 'en-US' ? '120px' : '80px'">
      <div class="container">
        <el-form-item label="次批id" prop="subBatchId">
          <el-input v-model="dataForm.subBatchId" placeholder="次批id"></el-input>
        </el-form-item>
        <el-form-item label="成形" prop="forming">
          <el-input v-model="dataForm.forming" placeholder="成形"></el-input>
        </el-form-item>
        <el-form-item label="品检" prop="qualityInspection">
          <el-input v-model="dataForm.qualityInspection" placeholder="品检"></el-input>
        </el-form-item>
        <el-form-item label="品修" prop="productRepair">
          <el-input v-model="dataForm.productRepair" placeholder="品修"></el-input>
        </el-form-item>
        <el-form-item label="待包装" prop="toBePacked">
          <el-input v-model="dataForm.toBePacked" placeholder="待包装"></el-input>
        </el-form-item>
        <el-form-item label="待出货" prop="toBeShipped">
          <el-input v-model="dataForm.toBeShipped" placeholder="待出货"></el-input>
        </el-form-item>
      </div>
    </el-form>
    <template slot="footer">
      <el-button @click="visible = false">{{ $t('cancel') }}</el-button>
      <el-button type="primary" @click="dataFormSubmitHandle()">{{ $t('confirm') }}</el-button>
    </template>
  </el-dialog>
</template>

<script>
import debounce from 'lodash/debounce'
import mixinViewModule from '@/mixins/view-module'
export default {
  mixins:[mixinViewModule],
  data () {
    return {
      visible: false,
      //创建该页面时，不调用数据查询接口
      mixinViewModuleOptions: {
        createdIsNeed: false,
      },
      dataForm: {
        id: '',
        subBatchId: '',
        forming: '',
        qualityInspection: '',
        productRepair: '',
        toBePacked: '',
        toBeShipped: '',
        disabled: '',
        creator: '',
        createDate: '',
        updater: '',
        updateDate: ''
      }
    }
  },
  computed: {
    dataRule () {
      return {
          id: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          subBatchId: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          forming: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          qualityInspection: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          productRepair: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          toBePacked: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          toBeShipped: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
                }
    }
  },
  methods: {
    init () {
      this.visible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].resetFields()
        if (this.dataForm.id) {
          this.getInfo()
        }
      })
    },
    // 获取信息
    getInfo () {
      this.$http.get(`/batch/productcirculationlist/${this.dataForm.id}`).then(({ data: res }) => {
        if (res.code !== 0) {
          return this.$message.error(res.msg)
        }
        this.dataForm = {
          ...this.dataForm,
          ...res.data
        }
      }).catch(() => {})
    },
    // 表单提交
    dataFormSubmitHandle: debounce(function () {
      this.$refs['dataForm'].validate((valid) => {
        if (!valid) {
          return false
        }
        let message = '';
        if(this.dataForm.id){
          message = '此操作将修改该数据'
        }else {
          message = '此操作将新增一条数据'
        }
        this.$confirm(message+', 是否继续?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
          center: true
        }).then(() => {
          this.$http[!this.dataForm.id ? 'post' : 'put']('/batch/productcirculationlist/', this.dataForm).then(({ data: res }) => {
            if (res.code !== 0) {
            return this.$message.error(res.msg)
            }
            this.$message({
          message: this.$t('prompt.success'),
            type: 'success',
                    duration: 500,
                    onClose: () => {
              this.visible = false
              this.$emit('refreshDataList')
            }
          })
          }).catch(() => {})
        }).catch(() => {
          this.$message({
            type: 'info',
            message: '已取消'
          });
        });
      })
    }, 1000, { 'leading': true, 'trailing': false })
  }
}
</script>
