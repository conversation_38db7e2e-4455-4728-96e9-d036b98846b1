<template>
  <el-dialog :visible.sync="visible" :title="$t('add')" :close-on-click-modal="false" :close-on-press-escape="false"
             append-to-body>
    <el-form :model="dataForm" :rules="dataRule" ref="dataForm" @keyup.enter.native="dataFormSubmitHandle()"
             :label-width="$i18n.locale === 'en-US' ? '120px' : '80px'">
      <div class="container">
        <el-form-item label="员工班别" prop="employeeFormingClass">
          <el-select v-model="dataForm.employeeFormingClass" style="width: 150px">
            <el-option v-for="(item,index) in groupOption"
                       :key="index"
                       :label="item.label"
                       :value="item.value"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="员工列表">
          <!--          <el-input v-model="dataForm.employeeIdList" placeholder="员工id"></el-input>-->
          <el-select v-model="dataForm.employeeIdList" filterable multiple style="width: 300px">
            <el-option v-for="(item,index) in employeeList"
                       :key="index"
                       :label="item.label"
                       :value="item.value">
            </el-option>
          </el-select>
        </el-form-item>
      </div>
    </el-form>
    <template slot="footer">
      <el-button @click="visible = false">{{ $t('cancel') }}</el-button>
      <el-button type="primary" @click="dataFormSubmitHandle()">{{ $t('confirm') }}</el-button>
    </template>
  </el-dialog>
</template>

<script>
import debounce from 'lodash/debounce'
import mixinViewModule from '@/mixins/view-module'
import th from "element-ui/src/locale/lang/th";

export default {
  mixins: [mixinViewModule],
  data() {
    return {
      visible: false,
      //创建该页面时，不调用数据查询接口
      mixinViewModuleOptions: {
        createdIsNeed: false,
      },
      dataForm: {
        id: '',
        employeeFormingClass: '',
        employeeIdList: [],
      },
      groupOption: [
        {
          label: 'A班',
          value: 0
        },
        {
          label: 'B班',
          value: 1
        }
      ],
      employeeList: [],
      deptId: ['1641701467113046018', '1641701649229725698'],
    }
  },
  computed: {
    dataRule() {
      return {
        id: [
          {required: true, message: this.$t('validate.required'), trigger: 'blur'}
        ],
        employeeId: [
          {required: true, message: this.$t('validate.required'), trigger: 'blur'}
        ],
        employeeFormingClass: [
          {required: true, message: this.$t('validate.required'), trigger: 'blur'}
        ],
        remark: [
          {required: true, message: this.$t('validate.required'), trigger: 'blur'}
        ],
      }
    }
  },
  methods: {
    init() {
      this.visible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].resetFields()
        this.dataForm.employeeIdList = []
        this.getEmployeeList()
      })
    },
    //获取制一与制三的人员
    getEmployeeList() {
      this.$http.post('/sys/user/getUserListByDeptId', this.deptId).then(({data: res}) => {
        if (res.code !== 0) {
          return this.$message.error(res.msg)
        }
        this.employeeList = res.data.map((obj) => {
          return {
            label: obj.userCode + '(' + obj.username + ')',
            value: obj.id,
            data: obj
          }
        })
      })
    },
    // 获取信息
    getInfo() {
      this.$http.get(`/batch/formingclass/${this.dataForm.id}`).then(({data: res}) => {
        if (res.code !== 0) {
          return this.$message.error(res.msg)
        }
        this.dataForm = {
          ...this.dataForm,
          ...res.data
        }
      }).catch(() => {
      })
    },
    // 表单提交
    dataFormSubmitHandle: debounce(function () {
      this.$refs['dataForm'].validate((valid) => {
        if (!valid) {
          return false
        }
        let message = '';
        if (this.dataForm.id) {
          message = '此操作将修改该数据'
        } else {
          message = '此操作将新增一条数据'
        }
        this.$confirm(message + ', 是否继续?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
          center: true
        }).then(() => {
          this.$http[!this.dataForm.id ? 'post' : 'put']('/batch/formingclass/', this.dataForm).then(({data: res}) => {
            if (res.code !== 0) {
              return this.$message.error(res.msg)
            }
            this.$message({
              message: this.$t('prompt.success'),
              type: 'success',
              duration: 500,
              onClose: () => {
                this.visible = false
                this.$emit('refreshDataList')
              }
            })
          }).catch(() => {
          })
        }).catch(() => {
          this.$message({
            type: 'info',
            message: '已取消'
          });
        });
      })
    }, 1000, {'leading': true, 'trailing': false})
  }
}
</script>
