 <template>
  <el-dialog :visible.sync="visible" :title="!dataForm.id ? $t('add') : $t('update')" :close-on-click-modal="false" :close-on-press-escape="false" append-to-body>
    <el-form :model="dataForm" :rules="dataRule" ref="dataForm" @keyup.enter.native="dataFormSubmitHandle()" :label-width="$i18n.locale === 'en-US' ? '120px' : '80px'">
          <el-form-item label="库存总表id" prop="productInventoryId">
          <el-input v-model="dataForm.productInventoryId" placeholder="库存总表id"></el-input>
      </el-form-item>
          <el-form-item label="批号id" prop="batchId">
          <el-input v-model="dataForm.batchId" placeholder="批号id"></el-input>
      </el-form-item>
          <el-form-item label="次批号id" prop="subBatchId">
          <el-input v-model="dataForm.subBatchId" placeholder="次批号id"></el-input>
      </el-form-item>
          <el-form-item label="批号条码" prop="batchNumberBarcode">
          <el-input v-model="dataForm.batchNumberBarcode" placeholder="批号条码"></el-input>
      </el-form-item>
          <el-form-item label="批号二维码" prop="batchNumberQrCode">
          <el-input v-model="dataForm.batchNumberQrCode" placeholder="批号二维码"></el-input>
      </el-form-item>
          <el-form-item label="料重(kg)" prop="materialWeight">
          <el-input v-model="dataForm.materialWeight" placeholder="料重(kg)"></el-input>
      </el-form-item>
          <el-form-item label="生产模数" prop="productionModulus">
          <el-input v-model="dataForm.productionModulus" placeholder="生产模数"></el-input>
      </el-form-item>
          <el-form-item label="孔数" prop="numberOfHoles">
          <el-input v-model="dataForm.numberOfHoles" placeholder="孔数"></el-input>
      </el-form-item>
          <el-form-item label="制造时间" prop="manufacturingTime">
          <el-input v-model="dataForm.manufacturingTime" placeholder="制造时间"></el-input>
      </el-form-item>
          <el-form-item label="生产数量" prop="productionQuantity">
          <el-input v-model="dataForm.productionQuantity" placeholder="生产数量"></el-input>
      </el-form-item>
          <el-form-item label="生产损耗量" prop="productionLoss">
          <el-input v-model="dataForm.productionLoss" placeholder="生产损耗量"></el-input>
      </el-form-item>
          <el-form-item label="技术员" prop="technician">
          <el-input v-model="dataForm.technician" placeholder="技术员"></el-input>
      </el-form-item>
          <el-form-item label="品修数" prop="productRepairNumber">
          <el-input v-model="dataForm.productRepairNumber" placeholder="品修数"></el-input>
      </el-form-item>
          <el-form-item label="品检数" prop="qualityInspectionNumber">
          <el-input v-model="dataForm.qualityInspectionNumber" placeholder="品检数"></el-input>
      </el-form-item>
          <el-form-item label="待包装数" prop="toBePacked">
          <el-input v-model="dataForm.toBePacked" placeholder="待包装数"></el-input>
      </el-form-item>
          <el-form-item label="待出货数" prop="toBeShipped">
          <el-input v-model="dataForm.toBeShipped" placeholder="待出货数"></el-input>
      </el-form-item>
          <el-form-item label="产品状态 0:正常; 1:重点; 2:出货; 3:锁定; 4:隔离; 5:返工; 6:返检; 7:报废" prop="productStatus">
          <el-input v-model="dataForm.productStatus" placeholder="产品状态 0:正常; 1:重点; 2:出货; 3:锁定; 4:隔离; 5:返工; 6:返检; 7:报废"></el-input>
      </el-form-item>
          <el-form-item label="包装箱号" prop="boxNumber">
          <el-input v-model="dataForm.boxNumber" placeholder="包装箱号"></el-input>
      </el-form-item>
          <el-form-item label="卡板编号" prop="cardNumber">
          <el-input v-model="dataForm.cardNumber" placeholder="卡板编号"></el-input>
      </el-form-item>
          <el-form-item label="出货日期" prop="shippingDate">
          <el-input v-model="dataForm.shippingDate" placeholder="出货日期"></el-input>
      </el-form-item>
          <el-form-item label="备注" prop="remark">
          <el-input v-model="dataForm.remark" placeholder="备注"></el-input>
      </el-form-item>
                </el-form>
    <template slot="footer">
      <el-button @click="visible = false">{{ $t('cancel') }}</el-button>
      <el-button type="primary" @click="dataFormSubmitHandle()">{{ $t('confirm') }}</el-button>
    </template>
  </el-dialog>
</template>

<script>
import debounce from 'lodash/debounce'
export default {
  data () {
    return {
      visible: false,
      dataForm: {
        id: '',
        productInventoryId: '',
        batchId: '',
        subBatchId: '',
        batchNumberBarcode: '',
        batchNumberQrCode: '',
        materialWeight: '',
        productionModulus: '',
        numberOfHoles: '',
        manufacturingTime: '',
        productionQuantity: '',
        productionLoss: '',
        technician: '',
        productRepairNumber: '',
        qualityInspectionNumber: '',
        toBePacked: '',
        toBeShipped: '',
        productStatus: '',
        boxNumber: '',
        cardNumber: '',
        shippingDate: '',
        remark: '',
        disabled: '',
        creator: '',
        createDate: '',
        updater: '',
        updateDate: ''
      }
    }
  },
  computed: {
    dataRule () {
      return {
          id: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          productInventoryId: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          batchId: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          subBatchId: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          batchNumberBarcode: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          batchNumberQrCode: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          materialWeight: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          productionModulus: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          numberOfHoles: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          manufacturingTime: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          productionQuantity: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          productionLoss: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          technician: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          productRepairNumber: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          qualityInspectionNumber: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          toBePacked: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          toBeShipped: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          productStatus: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          boxNumber: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          cardNumber: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          shippingDate: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          remark: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
                }
    }
  },
  methods: {
    init () {
      this.visible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].resetFields()
        if (this.dataForm.id) {
          this.getInfo()
        }
      })
    },
    // 获取信息
    getInfo () {
      this.$http.get(`/batch/batchnumberrecord/${this.dataForm.id}`).then(({ data: res }) => {
        if (res.code !== 0) {
          return this.$message.error(res.msg)
        }
        this.dataForm = {
          ...this.dataForm,
          ...res.data
        }
      }).catch(() => {})
    },
    // 表单提交
    dataFormSubmitHandle: debounce(function () {
      this.$refs['dataForm'].validate((valid) => {
        if (!valid) {
          return false
        }
        this.$http[!this.dataForm.id ? 'post' : 'put']('/batch/batchnumberrecord/', this.dataForm).then(({ data: res }) => {
          if (res.code !== 0) {
            return this.$message.error(res.msg)
          }
          this.$message({
            message: this.$t('prompt.success'),
            type: 'success',
            duration: 500,
            onClose: () => {
              this.visible = false
              this.$emit('refreshDataList')
            }
          })
        }).catch(() => {})
      })
    }, 1000, { 'leading': true, 'trailing': false })
  }
}
</script>
