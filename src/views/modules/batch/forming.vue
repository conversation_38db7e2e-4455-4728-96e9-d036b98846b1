<template>
  <el-card shadow="never" class="aui-card--fill">
    <div class="mod-batch__forming}">
      <el-form :inline="true" :model="dataForm" @keyup.enter.native="getDataList()">
        <el-form-item>
          <el-input v-model="dataForm.batchNumber" placeholder="批号" clearable @clear="onClear">
          </el-input>
        </el-form-item>
        <el-form-item>
          <el-select v-model="dataForm.employeeId" filterable clearable placeholder="请选择生产人员">
            <el-option v-for="(item,index) in employeeOption"
                       :key="index"
                       :label="item.label"
                       :value="item.value">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-date-picker
              @change="changeDate"
              v-model="queryDate"
              type="daterange"
              value-format="yyyy-MM-dd"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期">
          </el-date-picker>
        </el-form-item>
       <el-form-item>
          <el-button type="primary" @click="getDataList()">{{ $t('query') }}</el-button>
        </el-form-item>
        <el-form-item>
          <el-button type="info" @click="exportHandle()">{{ $t('export') }}</el-button>
        </el-form-item>
<!--        <el-form-item>-->
<!--          <el-button v-if="$hasPermission('batch:forming:save')" type="primary" @click="addOrUpdateHandle()">{{ $t('add') }}</el-button>-->
<!--        </el-form-item>-->
        <el-form-item>
          <el-button v-if="$hasPermission('batch:forming:delete')" type="danger" @click="deleteHandle()">{{ $t('deleteBatch') }}</el-button>
        </el-form-item>
      </el-form>
      <el-table v-loading="dataListLoading" height="600px" :data="dataList" border @selection-change="dataListSelectionChangeHandle" style="width: 100%;">
        <el-table-column type="selection" header-align="center" align="center" width="50"></el-table-column>
        <el-table-column prop="partCode" label="品号" header-align="center" align="center" width="180">
          <template slot-scope="scope">
            {{ scope.row.designation}}
          </template>
        </el-table-column>
        <el-table-column prop="subBatchNumber" label="次批号" header-align="center" align="center" width="130">
          <template slot-scope="scope">
            <el-link type="info" @click="showDetail(scope.row.subBatchId)">{{scope.row.subBatchNumber}}</el-link>
          </template>
        </el-table-column>
        <el-table-column prop="machineCode" label="机台" header-align="center" align="center" width="150"></el-table-column>
        <el-table-column prop="moldNumber" label="模具" header-align="center" align="center" width="150"></el-table-column>
        <el-table-column prop="technician" label="技术员" header-align="center" align="center" width="166">
          <template slot-scope="scope">
            <div v-for="(item,index) in scope.row.technicianList" :key="index">
              {{$getEmployeesList(item.technician)}}
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="formingClass" label="班别" header-align="center" align="center"></el-table-column>
        <el-table-column prop="performance" label="时效" header-align="center" align="center"></el-table-column>
        <el-table-column prop="standardCapacity" label="标准时效" header-align="center" align="center"></el-table-column>
<!--        <el-table-column prop="insertNumber" label="镶件批号" header-align="center" align="center"></el-table-column>
        <el-table-column prop="insertName" label="镶件名称" header-align="center" align="center"></el-table-column>
        <el-table-column prop="sprayDate" label="喷涂日期" header-align="center" align="center"></el-table-column>
        <el-table-column prop="pickingConfirmation" label="领料确认者" header-align="center" align="center"></el-table-column>
        <el-table-column prop="pickingDate" label="领料日期" header-align="center" align="center"></el-table-column>
        <el-table-column prop="machineNumber" label="机台号" header-align="center" align="center"></el-table-column>
        <el-table-column prop="vulcanizationDate" label="加硫时间" header-align="center" align="center"></el-table-column>
        <el-table-column prop="sulfurizationTemperature" label="加硫温度" header-align="center" align="center"></el-table-column>
        <el-table-column prop="machinePressure" label="机台压力" header-align="center" align="center"></el-table-column>-->
        <el-table-column prop="dieHoleNumber" label="模具孔数" header-align="center" align="center"></el-table-column>
        <el-table-column prop="actualProductionHoles" label="实际生产孔数" header-align="center" align="center" width="130"></el-table-column>
        <el-table-column prop="productionModulus" label="生产模数" header-align="center" align="center"></el-table-column>
        <el-table-column prop="actualCapacity" label="生产总数" header-align="center" align="center"></el-table-column>
        <el-table-column prop="defectiveRate" label="不良率(%)" header-align="center" align="center" width="100"></el-table-column>
        <el-table-column prop="whichModeToStart" label="第几模开始" header-align="center" align="center" width="100"></el-table-column>
        <el-table-column prop="moldingDate" label="成型日期" header-align="center" align="center" width="120"></el-table-column>
        <el-table-column prop="remark" label="备注" header-align="center" align="center"></el-table-column>
        <el-table-column :label="$t('handle')" fixed="right" header-align="center" align="center" width="150">
          <template slot-scope="scope">
            <el-dropdown>
              <el-button type="primary">
                功能<i class="el-icon-arrow-down el-icon--right"></i>
              </el-button>
              <el-dropdown-menu slot="dropdown">
                <el-dropdown-item>
                  <el-button v-if="$hasPermission('batch:forming:update')" type="text" size="small" @click="addOrUpdateHandle(scope.row.id)">{{ $t('update') }}</el-button>
                </el-dropdown-item>
                <el-dropdown-item>
                  <el-button v-if="$hasPermission('batch:forming:delete')" type="text" size="small" @click="deleteHandle(scope.row.id)">{{ $t('delete') }}</el-button>
                </el-dropdown-item>
              </el-dropdown-menu>
            </el-dropdown>
          </template>
        </el-table-column>
      </el-table>
      <el-pagination
        :current-page="page"
        :page-sizes="[10, 20, 50, 100]"
        :page-size="limit"
        :total="total"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="pageSizeChangeHandle"
        @current-change="pageCurrentChangeHandle">
      </el-pagination>
      <!-- 弹窗, 新增 / 修改 -->
      <add-or-update v-if="addOrUpdateVisible" ref="addOrUpdate" @refreshDataList="getDataList"></add-or-update>
    </div>
    <div>
      <el-dialog width="25vw" top="2vh" :visible.sync="visible" append-to-body title="成型记录" @close="onClose" center>
        <el-table
          v-loading="loading"
          :data="detailTable"
          :cell-style="{ textAlign: 'center' }"
          :header-cell-style="{ textAlign: 'center'}">
          <el-table-column
              prop="cumulativeModulus"
              label="模数"
              width="120">
          </el-table-column>
          <el-table-column
              prop="createDate"
              label="时间">
          </el-table-column>
        </el-table>
      </el-dialog>
    </div>
  </el-card>
</template>

<script>
import mixinViewModule from '@/mixins/view-module'
import AddOrUpdate from './forming-add-or-update'
import fa from "element-ui/src/locale/lang/fa";
export default {
  mixins: [mixinViewModule],
  data () {
    return {
      mixinViewModuleOptions: {
        getDataListURL: '/batch/forming/page',
        getDataListIsPage: true,
        exportURL: '/batch/forming/export',
        deleteURL: '/batch/forming',
        deleteIsBatch: true
      },
      visible:false,
      queryDate:'',
      loading:true,
      detailTable:[],
      dataForm: {
        id: '',
        batchNumber:'',
        employeeId:'',
        startDate:'',
        endDate:''
      },
      employeeOption:[],
      deptIdList:['1641701467113046018','1641701649229725698'],
    }
  },
  components: {
    AddOrUpdate
  },
  created() {
    this.getProductEmployee()
  },
  methods:{
    //清空后重新查询
    onClear(){
      this.query()
    },
    onClose(){
      this.detailTable = []
      this.loading = true
    },
    //获取生产人员列表
    getProductEmployee(){
      this.$http.post('/sys/user/getUserListByDeptId',this.deptIdList).then(({data: res}) => {
        if(res.code !== 0){
          return this.$message.error(res.msg)
        }
        this.employeeOption = res.data.map((obj) => {
          return{
            value: obj.id,
            label: obj.userCode + '(' + obj.username + ')',
            data:obj
          }
        })
      })
    },
    //查看成形每一笔记录
    showDetail(subBatchId){
      this.visible = true
      this.$http.get(`batch/inspectioninstructions/getInfoBySubBatch?subBatchId=${subBatchId}`).then(({data: res}) =>{
        if(res.code !== 0){
          return this.$message.error(res.msg)
        }
        this.detailTable = res.data
        this.loading = false
      })
    },
    changeDate(){
      if(this.queryDate){
        this.dataForm.startDate = this.queryDate[0]
        this.dataForm.endDate = this.queryDate[1]
      }else {
        this.dataForm.startDate = ''
        this.dataForm.endDate = ''
      }
    }
  }
}
</script>
