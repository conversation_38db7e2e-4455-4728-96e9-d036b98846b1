<template>
  <el-dialog :visible.sync="visible" title="新增旧批号" :close-on-click-modal="false" :close-on-press-escape="false" append-to-body>
    <el-form :model="dataForm" :rules="dataRule" ref="dataForm" @keyup.enter.native="dataFormSubmitHandle()" :label-width="$i18n.locale === 'en-US' ? '120px' : '80px'">
      <div>
        <div class="container">
          <el-form-item label="胶料别" prop="sizeCategory">
            <div>
              <ren-radio-tick v-model="dataForm.sizeCategory" dict-type="size_category"></ren-radio-tick>
            </div>
          </el-form-item>
          <el-form-item label="生产批号" prop="batchNumber">
            <el-input v-model="dataForm.batchNumber"  placeholder="生产批号"></el-input>
          </el-form-item>
          <el-form-item label="次批号" prop="subBatchNumber">
            <el-input v-model="dataForm.subBatchNumber" placeholder="次批号"></el-input>
          </el-form-item>
          <el-form-item label="材料重量" prop="weight">
            <el-input v-model="dataForm.weight" placeholder="材料重量"></el-input>
          </el-form-item>
        </div>
        <div class="container">
          <el-form-item label="部品品名" prop="itemCategory">
            <el-autocomplete
                class="inline-input"
                v-model="dataForm.selectDesignation"
                :fetch-suggestions="getPartList"
                placement="bottom"
                placeholder="部品品名模糊查询"
                :trigger-on-focus="false"
                @select="partSelect"
                clearable
            ></el-autocomplete>
          </el-form-item>
          <el-form-item label="部品品名" prop="designation">
            <span class="font_size">{{dataForm.designation}}</span>
          </el-form-item>
          <el-form-item label="查询客户" prop="customerCode">
            <span class="font_size">{{dataForm.customerCode}}</span>
          </el-form-item>
        </div>
        <div class="container">
          <div class="three">
            <el-form-item label="孔数" prop="numberOfHoles">
              <el-input type="number" v-model.number="dataForm.numberOfHoles" placeholder="孔数"></el-input>
            </el-form-item>
          </div>
          <div class="three">
            <el-form-item label="模数" prop="productionModulus">
              <el-input type="number" v-model.number="dataForm.productionModulus"  placeholder="模数"></el-input>
            </el-form-item>
          </div>
          <div class="three">
            <el-form-item label="实际数" prop="productionQuantity">
              <el-input v-model="productionQuantity" placeholder="实际数"></el-input>
            </el-form-item>
          </div>
          <div class="three">
            <el-form-item label="成形日期" prop="transferPerson">
              <el-date-picker
                  v-model="dataForm.manufacturingTime"
                  type="date"
                  value-format="yyyy-MM-dd"
                  placeholder="成形日期">
              </el-date-picker>
            </el-form-item>
          </div>
        </div>
        <div class="container">
          <div>
            <el-form-item label="部门" prop="receivingDepartment">
              <ren-select-product-category v-model="dataForm.transferUnit" :placeholder="$t('dept.title')" dict-type="product_category"></ren-select-product-category>
            </el-form-item>
          </div>
          <div>
            <el-form-item label="建立人" prop="transferee">
              <employee-component v-model="dataForm.transferee" :departments="$formingDefault" placeholder="工号" @employeeData="data => extractContentAndAssign(data,'transferPerson')"></employee-component>
            </el-form-item>
          </div>
          <div>
            <el-form-item label="建立人姓名" prop="transferPerson">
              <el-input v-model="dataForm.transferPerson" placeholder="建立人姓名"></el-input>
            </el-form-item>
          </div>
          <div>

            <el-form-item label="技术员" prop="technician">
              <employee-component-list v-model="dataForm.technician" :departments="$formingDefault"
                                       @employeeData="data => employeeData(data)"
                                       placeholder="工号"></employee-component-list>
            </el-form-item>
          </div>
          <div>
            <el-form-item label="技术员姓名" prop="technicianName">
              <el-input v-model="dataForm.technicianName" placeholder="技术员姓名"></el-input>
            </el-form-item>
          </div>
          <div>
            <el-form-item label="外发员工姓名" prop="outgoingName">
              <el-input v-model="dataForm.outgoingName" placeholder="外发员工姓名"></el-input>
            </el-form-item>
          </div>
        </div>
        <div class="container">
          <div class="item">
            <el-form-item label="建立时间" prop="receivingTime">
              <el-date-picker
                  v-model="dataForm.createDate"
                  type="datetime"
                  value-format="yyyy-MM-dd HH:mm:ss"
                  placeholder="请选择接收时间(含时分)">
              </el-date-picker>
            </el-form-item>
          </div>
        </div>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="dataForm.remark" placeholder="备注"></el-input>
        </el-form-item>
      </div>
      <el-divider content-position="center">
        <span class="font_size">品修</span>
      </el-divider>
      <div>
        <qualityrepairnissan-visible ref="qualityrepairnissan" :sub-batch-number="subBatchNumber"
                                     @qualityrepairnissan="data => assignments(data,'qualityRepairNissanDTO')"></qualityrepairnissan-visible>
      </div>
      <el-divider content-position="center">
        <span class="font_size">品检</span>
      </el-divider>
      <qualityinspectionnissan-visible
          ref="qualityinspectionnissan"
          :sub-batch-number="subBatchNumber"
          @qualityinspectionnissanrecord="data => assignments(data, 'qualityInspectionNissanRecordDTO')"
      >
      </qualityinspectionnissan-visible>
<!--      <el-button type="primary" @click="alternateDisplay = false" round>1</el-button>
      <el-button type="primary" @click="alternateDisplay = true" round>2</el-button>
      <div v-show="!alternateDisplay">
        <qualityinspectionnissan-visible ref="qualityinspectionnissanone"
                                         :sub-batch-number="subBatchNumber"
                                         @qualityinspectionnissanrecord="data => assignmentsList(data,'qualityInspectionNissanRecordDTO')">
        </qualityinspectionnissan-visible>
      </div>
      <div v-show="alternateDisplay">
        <qualityinspectionnissan-visible ref="qualityinspectionnissantwo"
                                         :sub-batch-number="subBatchNumber"
                                         @qualityinspectionnissanrecord="data => assignmentsList(data,'qualityInspectionNissanRecordDTO')">
        </qualityinspectionnissan-visible>
      </div>-->
      <el-divider content-position="center">
        <span class="font_size">品保</span>
      </el-divider>
      <div>
        <qualityassurance-visible ref="qualityassurance" :sub-batch-number="subBatchNumber" @qualityassurance="data => assignments(data,'qualityAssuranceDTO')"></qualityassurance-visible>
      </div>
      <el-divider content-position="center">
        <span class="font_size">包装</span>
      </el-divider>
      <div>
        <wrap-visible ref="warp" :sub-batch-number="subBatchNumber" @wrap="data => assignments(data,'wrapDTO')"></wrap-visible>
      </div>
    </el-form>
    <template slot="footer">
      <el-button @click="visible = false">{{ $t('cancel') }}</el-button>
      <el-button type="primary" @click="dataFormSubmitHandle()">{{ $t('confirm') }}</el-button>
    </template>
  </el-dialog>
</template>

<script>
import debounce from 'lodash/debounce'
import mixinViewModule from '@/mixins/view-module'
import publicFunctions from '@/mixins/public-functions'
import QualityinspectionnissanVisible from "../inspect/qualityinspectionnissan-visible.vue";
import QualityrepairnissanVisible from "../repair/qualityrepairnissan-visible.vue";
import {getDictLabel} from '@/utils'
import WrapVisible from "../packing/wrap-visible.vue";
import QualityassuranceVisible from "../inspect/qualityassurance-visible.vue";
export default {
  components: {QualityassuranceVisible, WrapVisible, QualityrepairnissanVisible, QualityinspectionnissanVisible},
  mixins:[mixinViewModule,publicFunctions],
  // name:'subBatchAddOrUpdate',
  data () {
    return {
      batchNumberId:'',
      visible: false,
      //创建该页面时，不调用数据查询接口
      mixinViewModuleOptions: {
        createdIsNeed: false,
      },
      alternateDisplay: false,
      dataFormList:[],
      oldBatchNumber:{
        batchDTO: {},
        subBatchDTO: {},
        formingDTO: {},
        qualityRepairNissanDTO: {},
        qualityInspectionNissanRecordDTO:[{}],
        qualityAssuranceDTO: {},
        wrapDTO: {},
      },
      dataForm: {
        id: '',
        batchId:'',
        customerId:'',
        customerCode:'',
        productInventoryId: '',
        materialWeight: '',
        transferUnit: 0,
        transferee: '',
        transferPerson: '',
        technician: [[]],
        technicianList: [[]],
        technicianName: '',
        outgoingName: '',
        productionModulus: 0,
        selectDesignation: '',
        numberOfHoles: 0,
        manufacturingTime: this.formatTimes(new Date()),
        productionQuantity: '',
        productionLoss: '',
        productRepairNumber: '',
        qualityInspectionNumber: '',
        toBePacked: '',
        toBeShipped: '',
        productStatus: '',
        boxNumber: '',
        cardNumber: '',
        shippingDate: '',
        subBatchNumber:'',
        barcode: '',
        sizeCategory: '',
        batchNumber: '',
        orCode: '',
        remark: '',
        disabled: '',
        creator: '',
        createDate: this.formatTimes(new Date()),
        updater: '',
        updateDate: ''
      },
      subBatchId:'',

    }
  },
  computed: {
    buttonColor() {
      return this.alternateDisplay ? 'blue' : 'yellow';
    },
    dataRule () {
      return {
        batchNumber: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        subBatchNumber: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        technician: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        numberOfHoles: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        productionModulus: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
      }
    },
    productionQuantity: function () {
      this.dataForm.productionQuantity = this.dataForm.numberOfHoles * this.dataForm.productionModulus
      return this.dataForm.productionQuantity;
    },
    subBatchNumber: function () {
      let par = ''
      if(this.dataForm.subBatchNumber && this.dataForm.batchNumber && this.dataForm.sizeCategory){
        par = getDictLabel("size_category",this.dataForm.sizeCategory)+this.dataForm.batchNumber+'-'+this.dataForm.subBatchNumber
      }
      return par;
    }
  },
  methods: {
    toggleColor() {
      this.alternateDisplay = !this.alternateDisplay;
    },
    assignments(data, propertyName){
      this.oldBatchNumber[propertyName] = data
    },
    assignmentsList(data, propertyName){
      console.log(data,propertyName)
      this.oldBatchNumber[propertyName].push(data)
    },
    duplicateItem() {
      // 复制最后一个项并添加到列表
      const lastItem = this.dataFormList[this.dataFormList.length - 1];
      this.dataFormList.push(lastItem);
    },
    confirmSubmission(subBatchNumber){
      this.$nextTick(()=>{
        /*this.$refs.qualityrepairnissan.dataForm.subBatchId = subBatchId
        this.$refs.qualityinspectionnissanone.dataForm.subBatchId = subBatchId
        this.$refs.qualityinspectionnissantwo.dataForm.subBatchId = subBatchId
        this.$refs.qualityassurance.dataForm.subBatchId = subBatchId
        this.$refs.warp.dataForm.subBatchId = subBatchId*/

        this.$refs.qualityinspectionnissan.dataForm.subBatchNumber = subBatchNumber
        this.$refs.qualityinspectionnissan.dataFormSubmitHandle()

        this.$refs.qualityrepairnissan.dataForm.subBatchNumber = subBatchNumber
/*        this.$refs.qualityinspectionnissanone.dataForm.subBatchNumber = subBatchNumber
        this.$refs.qualityinspectionnissantwo.dataForm.subBatchNumber = subBatchNumber*/
        this.$refs.qualityassurance.dataForm.subBatchNumber = subBatchNumber
        this.$refs.warp.dataForm.subBatchNumber = subBatchNumber

        this.$refs.qualityrepairnissan.dataFormSubmitHandle()
/*this.$refs.qualityinspectionnissanone.dataFormSubmitHandle()
        this.$refs.qualityinspectionnissantwo.dataFormSubmitHandle()*/
        this.$refs.warp.dataFormSubmitHandle()
        this.$refs.qualityassurance.dataFormSubmitHandle()
      })
    },
    employeeData(data){
      this.dataForm.technicianList = []
      this.dataForm.technicianList = []
      this.dataForm.technicianName = ''
      for (let i = 0; i < data.length; i++) {
        this.dataForm.technicianList.push({technician: data[i].value, technicianName: this.labelName(data[i].label)})
        console.log(i === data.length, "i=",i,'data.length',data.length)
        this.dataForm.technicianName += this.labelName(data[i].label) + (data.length - i === 1 ? "" : ",")
      }
    },
    extractContentAndAssign(data, propertyName) {
      const regex = /\(([^)]+)\)/; // 匹配括号中的内容
      const matches = data.label.match(regex); // 使用正则表达式匹配
      if (matches && matches.length > 1) {
        const content = matches[1]; // 获取匹配到的内容
        this.dataForm[propertyName] = content; // 将内容赋值给指定的属性
      }
    },
    //  获取部品列表
    getPartList(queryString, cb) {
      this.$http.get(`/fabricate/part/getPartDesignation/` + queryString).then(({data: res}) => {
        if (res.code !== 0) {
          return this.$message.error(res.msg)
        }
        const list = res.data.map((obj) => {
          return {
            customerCode: obj.customerCode,
            value: obj.designation + '(' + obj.customerCode + ')',
            partId: obj.id,
            designation: obj.designation,
            customerId: obj.customerId
          }
        })
        this.timeout = setTimeout(() => {
          cb(list);
        }, 300 * Math.random());
      }).catch(() => {
      })
    },
    //  输出选择日志
    partSelect(item) {
      this.dataForm.designation = item.designation
      this.dataForm.customerCode = item.customerCode
      this.dataForm.partId = item.partId
      this.dataForm.customerId = item.customerId
    },
    init () {
      this.visible = true
      this.$refs.qualityrepairnissan.init()
      this.$refs.qualityinspectionnissanone.init()
      this.$refs.qualityinspectionnissantwo.init()
      this.$refs.warp.init()
      this.$refs.qualityassurance.init()
      this.$nextTick(() => {
        this.$refs['dataForm'].resetFields()
        if (this.dataForm.id) {
          this.getInfo()
        }
      })
    },
    // 获取信息
    getInfo () {
      this.$http.get(`/batch/subbatch/${this.dataForm.id}`).then(({ data: res }) => {
        if (res.code !== 0) {
          return this.$message.error(res.msg)
        }
        this.dataForm = {
          ...this.dataForm,
          ...res.data
        }
      }).catch(() => {})
    },
    // 表单提交
    dataFormSubmitHandle: debounce(function () {
      this.$refs['dataForm'].validate((valid) => {
        if (!valid) {
          return false
        }
        let message = '';
        if(this.dataForm.id){
          message = '此操作将修改该数据'
        }else {
          message = '此操作将新增一条数据'
        }
        this.$confirm(message+', 是否继续?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
          center: true
        }).then(() => {
          this.oldBatchNumber.subBatchDTO = this.dataForm
          this.confirmSubmission(this.subBatchNumber)
          this.$http['post']('/batch/subbatch/insertOldBatchNumber', this.oldBatchNumber).then(({ data: res }) => {
            if (res.code !== 0) {
              return this.$message.error(res.msg)
            }
            localStorage.setItem("data", JSON.stringify(res.data));
            this.$bus.$emit('startTimer');
            this.$message({
              message: this.$t('prompt.success'),
              type: 'success',
              duration: 500,
              onClose: () => {
                this.visible = false
                this.$emit('refreshDataList')
                this.$emit("objectReturn",res.data)
              }
            })
          }).catch(() => {})
        }).catch(() => {
          this.$message({
            type: 'info',
            message: '已取消'
          });
        });
      })
    }, 1000, { 'leading': true, 'trailing': false })
  }
}
</script>


