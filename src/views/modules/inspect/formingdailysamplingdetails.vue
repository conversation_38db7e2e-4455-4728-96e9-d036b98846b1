<template>
  <el-card shadow="never" class="aui-card--fill">
    <div class="mod-inspect__formingdailysamplingdetails}">
      <el-form :inline="true" :model="dataForm" @keyup.enter.native="getDataList()">
        <el-form-item>
          <el-tooltip content="请输入查询关键字" placement="top">
            <el-input v-model="dataForm.id" placeholder="查询关键字" @input="throttleFunction" clearable>
              <el-button slot="append" icon="el-icon-search" @click="getDataList()"></el-button>
            </el-input>
          </el-tooltip>
        </el-form-item>
        <el-form-item>
          <el-button v-if="$hasPermission('inspect:formingdailysamplingdetails:save')" type="primary" @click="addOrUpdateHandle()">{{ $t('add') }}</el-button>
        </el-form-item>
        <el-dropdown  style="float:right;">
          <el-button type="primary">
            更多功能<i class="el-icon-arrow-down el-icon--right"></i>
          </el-button>
          <el-dropdown-menu slot="dropdown">
            <el-dropdown-item>
              <el-form-item>
                <el-button type="info" @click="exportHandle()">{{ $t('export') }}</el-button>
              </el-form-item>
            </el-dropdown-item>
            <el-dropdown-item>
              <el-form-item>
                <el-button v-if="$hasPermission('inspect:formingdailysamplingdetails:export')" type="info" @click="exportTemplateHandle()">
                  {{ $t('exportTemplate') }}
                </el-button>
              </el-form-item>
            </el-dropdown-item>
            <el-dropdown-item>
              <el-form-item>
                <el-tooltip content="下载模板填入信息后导入" placement="top">
                  <el-upload
                          v-if="$hasPermission('inspect:formingdailysamplingdetails:save')"
                          class="upload-demo"
                          :action="mixinViewModuleOptions.addBatchUrl"
                          :headers="headers"
                          :multiple="false"
                          :show-file-list="false"
                          :file-list="fileList"
                          :before-upload="beforeUpload"
                          :on-success="resultHandle"
                          :on-change="handleChange"
                          accept="'.xlsx','.xls'">
                    <el-button type="success">{{ $t('addBatch') }}</el-button>
                  </el-upload>
                </el-tooltip>
                <el-button type="text" v-show="unsuccessfulNameList" @click="dialogVisible = true">导入失败列表</el-button>
              </el-form-item>
            </el-dropdown-item>
            <el-dropdown-item>
              <el-form-item>
                <el-button v-if="$hasPermission('inspect:formingdailysamplingdetails:delete')" type="danger" @click="deleteHandle()">{{ $t('deleteBatch') }}</el-button>
              </el-form-item>
            </el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown>
      </el-form>
      <el-table v-loading="dataListLoading" height="600px" :data="dataList" border @selection-change="dataListSelectionChangeHandle" style="width: 100%;">
        <el-table-column type="selection" header-align="center" align="center" width="50"></el-table-column>
        <el-table-column prop="id" label="id" header-align="center" align="center"></el-table-column>
        <el-table-column prop="formingDailySamplingId" label="日抽检id" header-align="center" align="center"></el-table-column>
        <el-table-column prop="subBatchId" label="次批id" header-align="center" align="center"></el-table-column>
        <el-table-column prop="subBatchNumber" label="生产次批" header-align="center" align="center"></el-table-column>
        <el-table-column prop="hardness" label="硬度" header-align="center" align="center"></el-table-column>
        <el-table-column prop="firstPiece" label="首件" header-align="center" align="center"></el-table-column>
        <el-table-column prop="sampleVerification" label="样品核对" header-align="center" align="center"></el-table-column>
        <el-table-column prop="inspection" label="巡检" header-align="center" align="center"></el-table-column>
        <el-table-column prop="lastPiece" label="末件" header-align="center" align="center"></el-table-column>
        <el-table-column prop="inspectionTime" label="检查时间" header-align="center" align="center"></el-table-column>
        <el-table-column prop="change" label="4M变更" header-align="center" align="center"></el-table-column>
        <el-table-column prop="samplingNumber" label="取样数量" header-align="center" align="center"></el-table-column>
        <el-table-column prop="rupture" label="破裂" header-align="center" align="center"></el-table-column>
        <el-table-column prop="bagWind" label="包风" header-align="center" align="center"></el-table-column>
        <el-table-column prop="indentation" label="压痕" header-align="center" align="center"></el-table-column>
        <el-table-column prop="deathPenalty" label="死料" header-align="center" align="center"></el-table-column>
        <el-table-column prop="lackOfMaterial" label="缺料" header-align="center" align="center"></el-table-column>
        <el-table-column prop="strain" label="拉伤" header-align="center" align="center"></el-table-column>
        <el-table-column prop="burr" label="压毛边" header-align="center" align="center"></el-table-column>
        <el-table-column prop="bubbling" label="起泡" header-align="center" align="center"></el-table-column>
        <el-table-column prop="impurities" label="杂质" header-align="center" align="center"></el-table-column>
        <el-table-column prop="cut" label="剪伤" header-align="center" align="center"></el-table-column>
        <el-table-column prop="concavePouringPoint" label="灌点凹" header-align="center" align="center"></el-table-column>
        <el-table-column prop="materialPollution" label="料污" header-align="center" align="center"></el-table-column>
        <el-table-column prop="deformation" label="变形" header-align="center" align="center"></el-table-column>
        <el-table-column prop="exposedIron" label="露铁" header-align="center" align="center"></el-table-column>
        <el-table-column prop="burrs" label="毛边" header-align="center" align="center"></el-table-column>
        <el-table-column prop="particles" label="颗粒" header-align="center" align="center"></el-table-column>
        <el-table-column prop="brokenColumn" label="断柱子" header-align="center" align="center"></el-table-column>
        <el-table-column prop="undercooked" label="不熟" header-align="center" align="center"></el-table-column>
        <el-table-column prop="burstEdge" label="爆边" header-align="center" align="center"></el-table-column>
        <el-table-column prop="holePlug" label="孔堵" header-align="center" align="center"></el-table-column>
        <el-table-column prop="moldDamage" label="模伤" header-align="center" align="center"></el-table-column>
        <el-table-column prop="brokenPouringPoint" label="灌点破" header-align="center" align="center"></el-table-column>
        <el-table-column prop="colorful" label="五彩" header-align="center" align="center"></el-table-column>
        <el-table-column prop="needleHole" label="针孔" header-align="center" align="center"></el-table-column>
        <el-table-column prop="degumming" label="脱胶" header-align="center" align="center"></el-table-column>
        <el-table-column prop="differentProducts" label="异品" header-align="center" align="center"></el-table-column>
        <el-table-column prop="foreignMatter" label="异物" header-align="center" align="center"></el-table-column>
        <el-table-column prop="blackSpot" label="黑点" header-align="center" align="center"></el-table-column>
        <el-table-column prop="coarsePartingLine" label="分模线粗" header-align="center" align="center"></el-table-column>
        <el-table-column prop="shiny" label="发亮" header-align="center" align="center"></el-table-column>
        <el-table-column prop="ironPartsDefects" label="铁件不良" header-align="center" align="center"></el-table-column>
        <el-table-column prop="glueCrooked" label="胶歪" header-align="center" align="center"></el-table-column>
        <el-table-column prop="largePouringPoint" label="灌点大" header-align="center" align="center"></el-table-column>
        <el-table-column prop="notStickyGlue" label="不粘胶" header-align="center" align="center"></el-table-column>
        <el-table-column prop="engraving" label="刻印" header-align="center" align="center"></el-table-column>
        <el-table-column prop="roughEdge" label="毛边" header-align="center" align="center"></el-table-column>
        <el-table-column prop="otherProjects" label="其他项目检查记录" header-align="center" align="center"></el-table-column>
        <el-table-column prop="defectiveRate" label="不良率" header-align="center" align="center"></el-table-column>
        <el-table-column prop="remark" label="备注" header-align="center" align="center"></el-table-column>
        <el-table-column :label="$t('handle')" fixed="right" header-align="center" align="center" width="150">
          <template slot-scope="scope">
            <el-dropdown>
              <el-button type="primary">
                功能<i class="el-icon-arrow-down el-icon--right"></i>
              </el-button>
              <el-dropdown-menu slot="dropdown">
                <el-dropdown-item>
                  <el-button v-if="$hasPermission('inspect:formingdailysamplingdetails:update')" type="text" size="small" @click="addOrUpdateHandle(scope.row.id)">{{ $t('update') }}</el-button>
                </el-dropdown-item>
                <el-dropdown-item>
                  <el-button v-if="$hasPermission('inspect:formingdailysamplingdetails:delete')" type="text" size="small" @click="deleteHandle(scope.row.id)">{{ $t('delete') }}</el-button>
                </el-dropdown-item>
              </el-dropdown-menu>
            </el-dropdown>
          </template>
        </el-table-column>
      </el-table>
      <el-pagination
        :current-page="page"
        :page-sizes="[10, 20, 50, 100]"
        :page-size="limit"
        :total="total"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="pageSizeChangeHandle"
        @current-change="pageCurrentChangeHandle">
      </el-pagination>
      <!-- 弹窗, 新增 / 修改 -->
      <add-or-update v-if="addOrUpdateVisible" ref="addOrUpdate" @refreshDataList="getDataList"></add-or-update>
    </div>
  </el-card>
</template>

<script>
import mixinViewModule from '@/mixins/view-module'
import AddOrUpdate from './formingdailysamplingdetails-add-or-update'
import Cookies from "js-cookie";
export default {
  mixins: [mixinViewModule],
  data () {
    return {
      message:'',
      headers: {'token': Cookies.get('token')},
      fileList: [{name: '', url: ''}],
      unsuccessfulNameList: '',//  导入失败名称列表
      mixinViewModuleOptions: {
        getDataListURL: '/inspect/formingdailysamplingdetails/page',
        getDataListIsPage: true,
        exportURL: '/inspect/formingdailysamplingdetails/export',
        deleteURL: '/inspect/formingdailysamplingdetails',
        deleteIsBatch: true,
        exportTemplateURL: '/inspect/formingdailysamplingdetails/export/template',
        addBatchUrl: window.SITE_CONFIG['apiURL'] + '/inspect/formingdailysamplingdetails/batchSave',
      },
      dataForm: {
        id: '',
        paramStr: '',
      },
      // 判断是否还在继续输入
      timer: null,
    }
  },
  components: {
    AddOrUpdate
  },
  methods:{
    //节流函数
    throttleFunction() {
      // 如果timer不为空就表明在持续输入,每次改变都清除定时，这样就可以实现只输入最后一次结果(注意每次间隔如果超过500毫秒就会输出)
      if (this.timer !== null) clearTimeout(this.timer)
      this.timer = setTimeout(() => {
        this.getDataList()
      }, 500)
    },
    beforeUpload(file) {
      this.message = this.$message({
        message: '上传中，请勿操作',
        center: true,
        duration: 0,
      });
    },
    handleChange(file, fileList) {
      this.fileList = fileList.slice(-3);

    },
    resultHandle(res) {
      if (res.code !== 0) {
      return this.$message.error(res.msg)
      }
      this.$message({
        message: this.$t('prompt.success'),
        type: 'success',
        duration: 500,
        onClose: () => {
          if (res.data) {
            this.unsuccessfulNameList = res.data
          }
          this.visible = false
          this.query()
        }
      })
    },
  }
}
</script>
