<template>
  <el-dialog :visible.sync="visible" :title="!dataForm.id ? $t('add') : $t('update')" :close-on-click-modal="false"
             :close-on-press-escape="false" append-to-body>
    <el-form :model="dataForm" :rules="dataRule" ref="dataForm" @keyup.enter.native="dataFormSubmitHandle()"
             :label-width="$i18n.locale === 'en-US' ? '120px' : '120px'">
      <div class="container">
        <el-form-item label="品号" prop="partId">
          <el-select v-model="dataForm.partId" disabled>
            <el-option v-for="(item,index) in partOption"
                       :key="index"
                       :label="item.label"
                       :value="item.value">
            </el-option>
          </el-select>
        </el-form-item>
<!--        <el-form-item label="次批id" prop="subBatchId">-->
<!--          <el-input v-model="dataForm.subBatchId" placeholder="次批id"></el-input>-->
<!--        </el-form-item>-->
        <el-form-item label="次批号" prop="subBatchNumber">
          <el-input v-model="dataForm.subBatchNumber" disabled placeholder="次批号"></el-input>
        </el-form-item>
        <el-form-item label="检查方式" prop="inspectionType">
          <el-select v-model="dataForm.inspectionType">
            <el-option v-for="(item,index) in inspectionTypeOption"
                       :key="index"
                       :label="item.label"
                       :value="item.value">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="检查日期" prop="inspectionTime">
          <el-date-picker
              v-model="dataForm.inspectionTime"
              type="datetime"
              default-time="15:00:00"
              value-format="yyyy-MM-dd HH:mm:ss">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="检查员" prop="inspector">
          <el-select v-model="dataForm.inspector" @change="selectInspectorName()" filterable clearable>
            <el-option v-for="(item,index) in inspectorOption"
                       :key="index"
                       :label="item.label"
                       :value="item.value">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="判定结果" prop="judgementResult">
          <el-radio-group v-model="dataForm.judgementResult">
            <el-radio :label="0">OK</el-radio>
            <el-radio :label="1">NG</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="不良项">
          <el-input v-model="dataForm.adverseItem" placeholder="不良项"></el-input>
        </el-form-item>
        <el-form-item label="处理方式">
            <el-autocomplete
                v-model="dataForm.processMode"
                :fetch-suggestions="querySearch"
            ></el-autocomplete>
        </el-form-item>
        <el-form-item label="时效">
          <el-input v-model="dataForm.performance" placeholder="时效"></el-input>
        </el-form-item>
        <el-form-item label="时效点">
          <el-input v-model="dataForm.performancePoint" placeholder="时效点"></el-input>
        </el-form-item>
        <el-form-item label="金额" >
          <el-input v-model="dataForm.amount" placeholder="金额"></el-input>
        </el-form-item>
        <el-form-item label="备注" >
          <el-input v-model="dataForm.remark" type="textarea" placeholder="备注"></el-input>
        </el-form-item>
      </div>
    </el-form>
    <template slot="footer">
      <el-button @click="visible = false">{{ $t('cancel') }}</el-button>
      <el-button type="primary" @click="dataFormSubmitHandle()">{{ $t('confirm') }}</el-button>
    </template>
  </el-dialog>
</template>

<script>
import debounce from 'lodash/debounce'
import mixinViewModule from '@/mixins/view-module'
import th from "element-ui/src/locale/lang/th";

export default {
  mixins: [mixinViewModule],
  data() {
    return {
      visible: false,
      //创建该页面时，不调用数据查询接口
      mixinViewModuleOptions: {
        createdIsNeed: false,
      },
      dataForm: {
        id: '',
        partId: '',
        subBatchId: '',
        subBatchNumber: '',
        inspectionType: '',
        inspectionTime: '',
        inspector: '',
        inspectorName: '',
        judgementResult: '',
        adverseItem: '',
        processMode: '',
        performance: '',
        performancePoint: '',
        amount: '',
        remark: '',
        disabled: '',
        creator: '',
        createDate: '',
        updater: '',
        updateDate: ''
      },
      partOption: [],
      inspectorOption:[],
      deptId:['1641701892159619073'],
      processModeType:[
        {
          value:'退货'
        },
        {
          value:'厂内二检'
        }
      ],
      inspectionTypeOption: [
        {
          label: '抽检',
          value: 0
        },
        {
          label: '全检',
          value: 1
        },
      ],
    }
  },
  computed: {
    dataRule() {
      return {
        id: [
          {required: true, message: this.$t('validate.required'), trigger: 'blur'}
        ],
        partId: [
          {required: true, message: this.$t('validate.required'), trigger: 'blur'}
        ],
        subBatchId: [
          {required: true, message: this.$t('validate.required'), trigger: 'blur'}
        ],
        subBatchNumber: [
          {required: true, message: this.$t('validate.required'), trigger: 'blur'}
        ],
        inspectionType: [
          {required: true, message: this.$t('validate.required'), trigger: 'blur'}
        ],
        inspectionTime: [
          {required: true, message: this.$t('validate.required'), trigger: 'blur'}
        ],
        inspector: [
          {required: true, message: this.$t('validate.required'), trigger: 'blur'}
        ],
        inspectorName: [
          {required: true, message: this.$t('validate.required'), trigger: 'blur'}
        ],
        judgementResult: [
          {required: true, message: this.$t('validate.required'), trigger: 'blur'}
        ],
        adverseItem: [
          {required: true, message: this.$t('validate.required'), trigger: 'blur'}
        ],
        processMode: [
          {required: true, message: this.$t('validate.required'), trigger: 'blur'}
        ],
        performance: [
          {required: true, message: this.$t('validate.required'), trigger: 'blur'}
        ],
        performancePoint: [
          {required: true, message: this.$t('validate.required'), trigger: 'blur'}
        ],
        amount: [
          {required: true, message: this.$t('validate.required'), trigger: 'blur'}
        ],
        remark: [
          {required: true, message: this.$t('validate.required'), trigger: 'blur'}
        ],
      }
    }
  },
  methods: {
    init() {
      this.visible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].resetFields()
        this.getInspectorOption()
        this.getPartList()
        if (this.dataForm.id) {
          this.getInfo()
        }
      })
    },
    querySearch(queryString, cb) {
      // 调用 callback 返回建议列表的数据
      cb(this.processModeType);
    },
    //获取品管人员列表
    getInspectorOption(){
      this.$http.post('/sys/user/getUserListByDeptId',this.deptId).then(({data: res}) =>{
        if(res.code !== 0){
          return this.$message.error(res.msg)
        }
        this.inspectorOption = res.data.map((obj) => {
          return{
            label:obj.userCode + '(' + obj.username + ')',
            value:obj.id,
            data:obj
          }
        })
      })
    },
    //选取检查人员做的动作
    selectInspectorName(){
      const filter = this.inspectorOption.filter(item => this.dataForm.inspector === item.value);
      if(filter.length === 0){
        this.dataForm.inspectorName = ''
      }else {
        this.dataForm.inspectorName = filter[0].label
      }
    },
    //获取品号列表
    getPartList() {
      this.$http.get('fabricate/part/getPartListByDesignation').then(({data: res}) => {
        if (res.code !== 0) {
          return this.$message.error(res.msg)
        }
        this.partOption = res.data.map((obj) => {
          return {
            label: obj.designation,
            value: obj.id,
            data: obj
          }
        })
      })
    },
    // 获取信息
    getInfo() {
      this.$http.get(`/inspect/outinspectionrecords/${this.dataForm.id}`).then(({data: res}) => {
        if (res.code !== 0) {
          return this.$message.error(res.msg)
        }
        this.dataForm = {
          ...this.dataForm,
          ...res.data
        }
      }).catch(() => {
      })
    },
    // 表单提交
    dataFormSubmitHandle: debounce(function () {
      this.$refs['dataForm'].validate((valid) => {
        if (!valid) {
          return false
        }
        let message = '';
        if (this.dataForm.id) {
          message = '此操作将修改该数据'
        } else {
          message = '此操作将新增一条数据'
        }
        this.$confirm(message + ', 是否继续?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
          center: true
        }).then(() => {
          this.$http[!this.dataForm.id ? 'post' : 'put']('/inspect/outinspectionrecords/', this.dataForm).then(({data: res}) => {
            if (res.code !== 0) {
              return this.$message.error(res.msg)
            }
            this.$message({
              message: this.$t('prompt.success'),
              type: 'success',
              duration: 500,
              onClose: () => {
                this.visible = false
                this.$emit('refreshDataList')
              }
            })
          }).catch(() => {
          })
        }).catch(() => {
          this.$message({
            type: 'info',
            message: '已取消'
          });
        });
      })
    }, 1000, {'leading': true, 'trailing': false})
  }
}
</script>
