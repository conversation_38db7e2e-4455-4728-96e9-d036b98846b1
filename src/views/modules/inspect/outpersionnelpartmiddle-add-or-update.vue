<template>
  <el-dialog :visible.sync="visible" :title="!dataForm.id ? $t('add') : $t('update')" :close-on-click-modal="false"
             :close-on-press-escape="false" append-to-body>
    <el-form :model="dataForm" :rules="dataRule" ref="dataForm" @keyup.enter.native="dataFormSubmitHandle()"
             :label-width="$i18n.locale === 'en-US' ? '120px' : '80px'">
      <div class="container">
        <el-form-item label="品号" prop="stadardsId">
          <el-select v-model="dataForm.stadardsId" clearable filterable>
            <el-option v-for="(item,index) in standardsOption"
                       :key="index"
                       :label="item.designation"
                       :value="item.id">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="外发人员" prop="personnelId">
          <el-select v-model="dataForm.personnelId" clearable filterable>
          <el-option v-for="(item,index) in personnelOption"
                     :key="index"
                     :label="item.personnelCode"
                     :value="item.id">
          </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="合格次数" prop="qualificationNumber">
          <el-input v-model="dataForm.qualificationNumber" placeholder="合格次数"></el-input>
        </el-form-item>
        <el-form-item label="备注">
          <el-input v-model="dataForm.remark" placeholder="备注"></el-input>
        </el-form-item>
      </div>
    </el-form>
    <template slot="footer">
      <el-button @click="visible = false">{{ $t('cancel') }}</el-button>
      <el-button type="primary" @click="dataFormSubmitHandle()">{{ $t('confirm') }}</el-button>
    </template>
  </el-dialog>
</template>

<script>
import debounce from 'lodash/debounce'
import mixinViewModule from '@/mixins/view-module'

export default {
  mixins: [mixinViewModule],
  data() {
    return {
      visible: false,
      //创建该页面时，不调用数据查询接口
      mixinViewModuleOptions: {
        createdIsNeed: false,
      },
      dataForm: {
        id: '',
        stadardsId: '',
        personnelId: '',
        qualificationNumber: '',
        remark: '',
        // disabled: '',
        // creator: '',
        // createDate: '',
        // updater: '',
        // updateDate: ''
      },
      standardsOption: [],
      personnelOption: [],
    }
  },
  computed: {
    dataRule() {
      return {
        id: [
          {required: true, message: this.$t('validate.required'), trigger: 'blur'}
        ],
        stadardsId: [
          {required: true, message: this.$t('validate.required'), trigger: 'blur'}
        ],
        personnelId: [
          {required: true, message: this.$t('validate.required'), trigger: 'blur'}
        ],
        qualificationNumber: [
          {required: true, message: this.$t('validate.required'), trigger: 'blur'}
        ],
        remark: [
          {required: true, message: this.$t('validate.required'), trigger: 'blur'}
        ],
      }
    }
  },
  methods: {
    init() {
      this.visible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].resetFields()
        this.getOutPersonnelList()
        this.getStandardsList()
        if (this.dataForm.id) {
          this.getInfo()
        }
      })
    },
    // 获取信息
    getInfo() {
      this.$http.get(`/inspect/outpersionnelpartmiddle/${this.dataForm.id}`).then(({data: res}) => {
        if (res.code !== 0) {
          return this.$message.error(res.msg)
        }
        this.dataForm = {
          ...this.dataForm,
          ...res.data
        }
      }).catch(() => {
      })
    },
    //获取外发人员列表
    getOutPersonnelList(){
      this.$http.get('inspect/outpersonnelrecord/getOutPersonnelList').then(({data:res}) => {
        if(res.code !== 0){
          return this.$message.error(res.msg)
        }
        this.personnelOption = res.data
      })
    },
    //获取外发部品标准列表
    getStandardsList(){
      this.$http.get('inspect/partoutstadards/getStandardsList').then(({data:res}) =>{
        if(res.code !== 0){
          return this.$message.error(res.msg)
        }
        this.standardsOption = res.data
      })
    },
    // 表单提交
    dataFormSubmitHandle: debounce(function () {
      this.$refs['dataForm'].validate((valid) => {
        if (!valid) {
          return false
        }
        let message = '';
        if (this.dataForm.id) {
          message = '此操作将修改该数据'
        } else {
          message = '此操作将新增一条数据'
        }
        this.$confirm(message + ', 是否继续?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
          center: true
        }).then(() => {
          this.$http[!this.dataForm.id ? 'post' : 'put']('/inspect/outpersionnelpartmiddle/', this.dataForm).then(({data: res}) => {
            if (res.code !== 0) {
              return this.$message.error(res.msg)
            }
            this.$message({
              message: this.$t('prompt.success'),
              type: 'success',
              duration: 500,
              onClose: () => {
                this.visible = false
                this.$emit('refreshDataList')
              }
            })
          }).catch(() => {
          })
        }).catch(() => {
          this.$message({
            type: 'info',
            message: '已取消'
          });
        });
      })
    }, 1000, {'leading': true, 'trailing': false})
  }
}
</script>
