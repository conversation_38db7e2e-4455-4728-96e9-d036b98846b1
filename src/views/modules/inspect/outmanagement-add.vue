<template>
  <el-dialog width="70vw" :visible.sync="visible" title="新增外发记录"
             :close-on-click-modal="false" :close-on-press-escape="false" append-to-body>
    <el-form :model="dataForm" :rules="dataRule" ref="dataForm" @keyup.enter.native="dataFormSubmitHandle()"
             :label-width="$i18n.locale === 'en-US' ? '120px' : '120px'">
      <div style="display: flex">
        <el-form-item label="工序" prop="process">
          <el-select v-model="dataForm.process">
            <el-option v-for="(item,index) in processOption"
                       :key="index"
                       :label="item.label"
                       :value="item.value">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="品号" prop="partId">
          <el-select v-model="dataForm.partId" @change="changePartAfter" filterable clearable>
            <el-option v-for="(item,index) in partOption"
                       :key="index"
                       :label="item.label"
                       :value="item.value">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="选取次批">
          <el-select v-model="subBatchIdList" @change="fillSubBatchList" filterable multiple>
            <el-option v-for="(item,index) in subBatchOption"
                       :key="index"
                       :label="item.subBatchNumber"
                       :value="item.subBatchId">
            </el-option>
          </el-select>
        </el-form-item>
        <div v-if="subBatchIdList.length !== 0" style="margin-left: 1vw">
          <el-button type="info" @click="openFill">填写数量</el-button>
        </div>
      </div>
      <!--                                        <el-form-item label="次批号" prop="subBatchNumber">-->
      <!--              <el-input v-model="dataForm.subBatchNumber" placeholder="次批号"></el-input>-->
      <!--            </el-form-item>-->
      <!--            <el-form-item label="发货数量" prop="quantity">-->
      <!--              <el-input v-model="dataForm.quantity" placeholder="发货数量"></el-input>-->
      <!--            </el-form-item>-->
      <div style="display: flex">
        <el-form-item label="发货日期" prop="consignmentTime">
          <el-date-picker
              v-model="dataForm.consignmentTime"
              type="datetime"
              placeholder="选择日期时间"
              default-time="18:00:00"
              value-format="yyyy-MM-dd HH:mm:ss"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item label="发货类型" prop="consignmentStatus">
          <el-select v-model="dataForm.consignmentStatus">
            <el-option v-for="(item,index) in consignmentStatusOption"
                       :key="index"
                       :label="item.label"
                       :value="item.value">
            </el-option>
          </el-select>
        </el-form-item>
      </div>
      <!--             <el-form-item label="当前位置" prop="storagePlace">-->
      <!--              <el-input v-model="dataForm.storagePlace" placeholder="当前位置"></el-input>-->
      <!--            </el-form-item>-->
      <el-form-item label="备注">
        <el-input type="textarea" v-model="dataForm.remark" placeholder="备注" style="width: 30vw"></el-input>
      </el-form-item>
    </el-form>
    <template slot="footer">
      <el-button @click="visible = false">{{ $t('cancel') }}</el-button>
      <el-button type="primary" @click="dataFormSubmitHandle()">{{ $t('confirm') }}</el-button>
    </template>
    <el-dialog width="30vw" :visible.sync="visible1" title="填写数量" append-to-body center>
      <div v-for="(item, index) in dataForm.subBatchList" :key="index" style="display: flex; margin-bottom: 1vh">
        <div style="flex: 1; display: flex; justify-content: center; align-items: center;">
          <span style="font-size: large;">次批号:{{ item.subBatchNumber }}</span>
        </div>
        <div style="flex: 1; display: flex; justify-content: center; align-items: center;">
      <span style="font-size: large; display: flex; align-items: center;">
        数量:<el-input v-model="item.quantity" style="margin-left: 8px; width: 160px;"></el-input>
      </span>
        </div>
      </div>
    </el-dialog>
  </el-dialog>
</template>

<script>
import debounce from 'lodash/debounce'
import mixinViewModule from '@/mixins/view-module'
import {isEmail} from "@/utils/validate";
import th from "element-ui/src/locale/lang/th";

export default {
  mixins: [mixinViewModule],
  data() {
    return {
      visible: false,
      visible1: false,
      //创建该页面时，不调用数据查询接口
      mixinViewModuleOptions: {
        createdIsNeed: false,
      },
      dataForm: {
        id: '',
        process: 1,
        partId: '',
        // subBatchId: '',
        // subBatchNumber: '',
        // quantity: '',
        subBatchList: [],
        consignmentTime: '',
        consignmentStatus: 0,
        remark: '',
      },
      subBatchIdList: [],
      processOption: [
        {
          label: '品修',
          value: 0,
        },
        {
          label: '品检',
          value: 1,
        },
        {
          label: '品管',
          value: 2,
        }
      ],
      consignmentStatusOption: [
        {
          label: '正常',
          value: 0
        },
        {
          label: '退货',
          value: 1
        }
      ],
      partOption: [],
      subBatchOption: [],
    }
  },
  computed: {
    dataRule() {
      return {
        id: [
          {required: true, message: this.$t('validate.required'), trigger: 'blur'}
        ],
        process: [
          {required: true, message: this.$t('validate.required'), trigger: 'blur'}
        ],
        partId: [
          {required: true, message: this.$t('validate.required'), trigger: 'blur'}
        ],
        subBatchIdList: [
          {required: true, message: this.$t('validate.required'), trigger: 'blur'}
        ],
        subBatchNumber: [
          {required: true, message: this.$t('validate.required'), trigger: 'blur'}
        ],
        quantity: [
          {required: true, message: this.$t('validate.required'), trigger: 'blur'}
        ],
        consignmentTime: [
          {required: true, message: this.$t('validate.required'), trigger: 'blur'}
        ],
        consignmentStatus: [
          {required: true, message: this.$t('validate.required'), trigger: 'blur'}
        ],
        storagePlace: [
          {required: true, message: this.$t('validate.required'), trigger: 'blur'}
        ],
        remark: [
          {required: true, message: this.$t('validate.required'), trigger: 'blur'}
        ],
      }
    }
  },
  methods: {
    isEmail,
    init() {
      this.visible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].resetFields()
        this.subBatchIdList = []
        this.subBatchOption = []
        this.getPartList()
      })
    },
    //自定义查询方法
    customFilter(val) {
      const filtered = this.subBatchOption.filter((item) => {
        return item.label.includes(val)
      });
      // 限制结果数量
      this.subBatchTempOption = filtered.length > 200 ? filtered.slice(0, 200) : filtered;
    },
    //获取品号列表
    getPartList() {
      this.$http.get('fabricate/part/getPartListByDesignation').then(({data: res}) => {
        if (res.code !== 0) {
          return this.$message.error(res.msg)
        }
        this.partOption = res.data.map((obj) => {
          return {
            label: obj.designation,
            value: obj.id,
            data: obj
          }
        })
      })
    },
    //打开填写数字窗口
    openFill() {
      this.visible1 = true
    },
    //选取品号后查询次批
    changePartAfter() {
      if (this.dataForm.partId) {
        this.getSubBatchList(this.dataForm.partId)
      } else {
        this.subBatchIdList = []
        this.subBatchOption = []
      }
    },
    //选取次批ID后填充次批
    fillSubBatchList() {
      // 创建一个映射对象，以便快速查找subBatchOption中的项目
      const subBatchOptionMap = new Map(this.subBatchOption.map(item => [item.subBatchId, item]));
      // 使用subBatchIdList的顺序创建一个新的数组，只包含存在于subBatchOption中的项目
      this.dataForm.subBatchList = this.subBatchIdList
          .filter(id => subBatchOptionMap.has(id)) // 确保subBatchOption中有对应的id
          .map(id => ({...subBatchOptionMap.get(id), 'quantity': ''}));
      // 如果需要保留原始的this.dataForm.subBatchList（即不覆盖），可以使用concat或者push等方法添加新元素
    },
    //获取次批列表
    getSubBatchList(partId) {
      this.$http.get(`batch/subbatch/getOutSubBatchList?partId=${partId}`).then(({data: res}) => {
        if (res.code !== 0) {
          return this.$message.error(res.msg)
        }
        this.subBatchOption = res.data
      })
    },
    // 表单提交
    dataFormSubmitHandle: debounce(function () {
      this.$refs['dataForm'].validate((valid) => {
        if (!valid) {
          return false
        }
        if (this.subBatchIdList.length === 0) {
          return this.$message.error("请选取次批")
        }
        const hasEmptyQuantity = this.dataForm.subBatchList.some(item =>
            item.quantity === null || item.quantity === undefined || item.quantity === ''
        );
        if (hasEmptyQuantity) {
          return this.$message.error("请填写所有数量");
        }
        let message = '';
        if (this.dataForm.id) {
          message = '此操作将修改该数据'
        } else {
          message = '此操作将新增一条数据'
        }
        this.$confirm(message + ', 是否继续?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
          center: true
        }).then(() => {
          this.$http[!this.dataForm.id ? 'post' : 'put']('/inspect/outmanagement/', this.dataForm).then(({data: res}) => {
            if (res.code !== 0) {
              return this.$message.error(res.msg)
            }
            this.$message({
              message: this.$t('prompt.success'),
              type: 'success',
              duration: 500,
              onClose: () => {
                this.visible = false
                this.$emit('refreshDataList')
              }
            })
          }).catch(() => {
          })
        }).catch(() => {
          this.$message({
            type: 'info',
            message: '已取消'
          });
        });
      })
    }, 1000, {'leading': true, 'trailing': false})
  }
}
</script>
