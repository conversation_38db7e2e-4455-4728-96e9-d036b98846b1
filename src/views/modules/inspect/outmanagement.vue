<template>
  <el-card shadow="never" class="aui-card--fill">
    <div class="mod-inspect__outmanagement}">
      <el-form :inline="true" :model="dataForm" @keyup.enter.native="getDataList()">
        <el-form-item>
          <el-tooltip content="请输入查询关键字" placement="top">
            <el-input v-model="dataForm.paramStr" placeholder="查询关键字" @input="throttleFunction" clearable>
              <el-button slot="append" icon="el-icon-search" @click="getDataList()"></el-button>
            </el-input>
          </el-tooltip>
        </el-form-item>
        <el-form-item>
          <el-select v-model="dataForm.type" style="width: 100px">
            <el-option v-for="(item,index) in typeOption"
                       :key="index"
                       :label="item.label"
                       :value="item.value">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="工序:">
          <el-select v-model="dataForm.processType" clearable style="width: 80px" placeholder="">
            <el-option v-for="(item,index) in processOption"
                       :key="index"
                       :label="item.label"
                       :value="item.value">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="发货类型:">
          <el-select v-model="dataForm.consignmentStatus" clearable style="width: 100px">
            <el-option v-for="(item,index) in consignmentStatusOption"
                       :key="index"
                       :label="item.label"
                       :value="item.value">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="当前位置:">
          <el-select v-model="dataForm.storagePlace" clearable style="width: 100px">
            <el-option v-for="(item,index) in storagePlaceOption"
                       :key="index"
                       :label="item.label"
                       :value="item.value">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="发货日期:">
          <el-date-picker
              style="width: 180px"
              v-model="dataForm.consignmentTime"
              type="date"
              value-format="yyyy-MM-dd"
              placeholder="选择日期">
          </el-date-picker>
        </el-form-item>
        <el-form-item>
          <el-button v-if="$hasPermission('inspect:outmanagement:save')" type="primary" @click="addOrUpdateHandle()">{{ $t('新增外发记录') }}</el-button>
        </el-form-item>
        <el-dropdown  style="float:right;">
          <el-button type="primary">
            更多功能<i class="el-icon-arrow-down el-icon--right"></i>
          </el-button>
          <el-dropdown-menu slot="dropdown">
<!--            <el-dropdown-item>-->
<!--              <el-form-item>-->
<!--                <el-button type="info" @click="exportHandle()">{{ $t('export') }}</el-button>-->
<!--              </el-form-item>-->
<!--            </el-dropdown-item>-->
<!--            <el-dropdown-item>-->
<!--              <el-form-item>-->
<!--                <el-button v-if="$hasPermission('inspect:outmanagement:export')" type="info" @click="exportTemplateHandle()">-->
<!--                  {{ $t('exportTemplate') }}-->
<!--                </el-button>-->
<!--              </el-form-item>-->
<!--            </el-dropdown-item>-->
<!--            <el-dropdown-item>-->
<!--              <el-form-item>-->
<!--                <el-tooltip content="下载模板填入信息后导入" placement="top">-->
<!--                  <el-upload-->
<!--                          v-if="$hasPermission('inspect:outmanagement:save')"-->
<!--                          class="upload-demo"-->
<!--                          :action="mixinViewModuleOptions.addBatchUrl"-->
<!--                          :headers="headers"-->
<!--                          :multiple="false"-->
<!--                          :show-file-list="false"-->
<!--                          :file-list="fileList"-->
<!--                          :before-upload="beforeUpload"-->
<!--                          :on-success="resultHandle"-->
<!--                          :on-change="handleChange"-->
<!--                          accept="'.xlsx','.xls'">-->
<!--                    <el-button type="success">{{ $t('addBatch') }}</el-button>-->
<!--                  </el-upload>-->
<!--                </el-tooltip>-->
<!--                <el-button type="text" v-show="unsuccessfulNameList" @click="dialogVisible = true">导入失败列表</el-button>-->
<!--              </el-form-item>-->
<!--            </el-dropdown-item>-->
            <el-dropdown-item>
              <el-form-item>
                <el-button v-if="$hasPermission('inspect:outmanagement:delete')" type="danger" @click="deleteHandle()">{{ $t('deleteBatch') }}</el-button>
              </el-form-item>
            </el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown>
      </el-form>
      <el-table v-loading="dataListLoading" height="500px" ref="dataTable" :data="dataList" border @selection-change="dataListSelectionChangeHandle" style="width: 100%;">
        <el-table-column type="selection" header-align="center" align="center" width="50"></el-table-column>
<!--        <el-table-column prop="id" label="id" header-align="center" align="center"></el-table-column>-->
        <el-table-column prop="process" label="工序" header-align="center" align="center">
          <template slot-scope="scope">
            {{scope.row.process === 0 ? '品修' : scope.row.process === 1 ? '品检' : '品管'}}
          </template>
        </el-table-column>
        <el-table-column prop="designation" label="品号" header-align="center" align="center"></el-table-column>
<!--        <el-table-column prop="partId" label="品号id" header-align="center" align="center"></el-table-column>-->
<!--        <el-table-column prop="subBatchId" label="次批id" header-align="center" align="center"></el-table-column>-->
        <el-table-column prop="subBatchNumber" label="次批号" header-align="center" align="center">
          <template  slot-scope="scope">
            <div @click="openTimeLine(scope.row)">{{scope.row.subBatchNumber}}</div>
          </template>
        </el-table-column>
        <el-table-column prop="quantity" label="发货数量" header-align="center" align="center"></el-table-column>
        <el-table-column prop="consignmentTime" label="发货日期" header-align="center" align="center" width="200"></el-table-column>
        <el-table-column prop="consignmentStatus" label="发货类型" header-align="center" align="center">
          <template slot-scope="scope">
            <el-tag
                effect="dark"
                :type="scope.row.consignmentStatus === 0 ? '' : 'danger'">
            {{scope.row.consignmentStatus === 0 ? '正常' : '退货'}}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="storagePlace" label="当前位置" header-align="center" align="center">
          <template slot-scope="scope">
            <el-tag
              effect="dark"
              :type="scope.row.storagePlace === 0 ? 'info' : 'danger'">
              {{scope.row.storagePlace === 0 ? '厂内' : '厂外'}}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="remark" label="备注" header-align="center" align="center"></el-table-column>
<!--        <el-table-column prop="disabled" label="是否失效 0:有效; 1:失效 (逻辑删除)" header-align="center" align="center"></el-table-column>-->
<!--        <el-table-column prop="creator" label="创建者" header-align="center" align="center"></el-table-column>-->
<!--        <el-table-column prop="createDate" label="创建时间" header-align="center" align="center"></el-table-column>-->
<!--        <el-table-column prop="updater" label="更新者" header-align="center" align="center"></el-table-column>-->
<!--        <el-table-column prop="updateDate" label="更新时间" header-align="center" align="center"></el-table-column>-->
        <el-table-column :label="$t('handle')" fixed="right" header-align="center" align="center" width="150">
          <template slot-scope="scope">
            <el-dropdown>
              <el-button type="primary">
                功能<i class="el-icon-arrow-down el-icon--right"></i>
              </el-button>
              <el-dropdown-menu slot="dropdown">
                <el-dropdown-item>
                  <el-button v-if="$hasPermission('inspect:outmanagement:update')" type="text" size="small" @click="openUpdate(scope.row.id)">{{ $t('update') }}</el-button>
                </el-dropdown-item>
                <el-dropdown-item>
                  <el-button v-if="$hasPermission('inspect:outmanagement:delete')" type="text" size="small" @click="deleteHandle(scope.row.id)">{{ $t('delete') }}</el-button>
                </el-dropdown-item>
              </el-dropdown-menu>
            </el-dropdown>
          </template>
        </el-table-column>
      </el-table>
      <el-pagination
        :current-page="page"
        :page-sizes="[10, 20, 50, 100]"
        :page-size="limit"
        :total="total"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="pageSizeChangeHandle"
        @current-change="pageCurrentChangeHandle">
      </el-pagination>
      <!-- 弹窗, 新增 / 修改 -->
      <out-management-add v-if="addOrUpdateVisible" ref="addOrUpdate" @refreshDataList="getDataList"></out-management-add>
      <out-management-update v-if="addOrUpdateVisible1" ref="outManagementUpdate" @refreshDataList="getDataList"></out-management-update>
      <out-time-line-dialog v-if="addOrUpdateVisible2" ref="outTimeLine"></out-time-line-dialog>
    </div>
  </el-card>
</template>

<script>
import mixinViewModule from '@/mixins/view-module'
import OutManagementAdd from './outmanagement-add.vue'
import OutManagementUpdate from "@/views/modules/inspect/outmanagement-update.vue";
import OutTimeLineDialog from "@/views/modules/inspect/outTimeLineDialog.vue";
import Cookies from "js-cookie";
export default {
  mixins: [mixinViewModule],
  data () {
    return {
      message:'',
      headers: {'token': Cookies.get('token')},
      fileList: [{name: '', url: ''}],
      unsuccessfulNameList: '',//  导入失败名称列表
      mixinViewModuleOptions: {
        getDataListURL: '/inspect/outmanagement/page',
        getDataListIsPage: true,
        exportURL: '/inspect/outmanagement/export',
        deleteURL: '/inspect/outmanagement',
        deleteIsBatch: true,
        exportTemplateURL: '/inspect/outmanagement/export/template',
        addBatchUrl: window.SITE_CONFIG['apiURL'] + '/inspect/outmanagement/batchSave',
      },
      addOrUpdateVisible1:false,
      addOrUpdateVisible2:false,
      dataForm: {
        id: '',
        type:0,
        processType:'',
        consignmentTime:'',
        consignmentStatus:'',
        storagePlace:'',
        paramStr: '',
      },
      typeOption:[
        {
        label:'批号',
        value:0
        },
        {
          label:'品号',
          value:1
        },
      ],
      consignmentStatusOption: [
        {
          label: '正常',
          value: 0
        },
        {
          label: '退货',
          value: 1
        }
      ],
      processOption:[
        {
          label:'品修',
          value:0
        },
        {
          label:'品检',
          value:1
        }
      ],
      storagePlaceOption:[
        {
          label:'厂内',
          value:0
        },
        {
          label:'厂外',
          value:1
        },
      ],
      // 判断是否还在继续输入
      timer: null,
    }
  },
  components: {
    OutManagementAdd,
    OutManagementUpdate,
    OutTimeLineDialog
  },
  activated() {
    console.log("触发了")
    this.$nextTick(() => {
      if (this.$refs.dataTable) {
        this.$refs.dataTable.doLayout();
      }
    });
  },
  methods:{
    openUpdate(id){
      this.addOrUpdateVisible1 = true
      this.$nextTick(() =>{
        this.$refs.outManagementUpdate.dataForm.id = id
        this.$refs.outManagementUpdate.init()
      })
    },
    openTimeLine(row){
      this.addOrUpdateVisible2 = true
      this.$nextTick(() => {
        this.$refs.outTimeLine.subBatchId = row.subBatchId;
        this.$refs.outTimeLine.subBatchNumber = row.subBatchNumber;
        this.$refs.outTimeLine.init()
      })
    },
    //节流函数
    throttleFunction() {
      // 如果timer不为空就表明在持续输入,每次改变都清除定时，这样就可以实现只输入最后一次结果(注意每次间隔如果超过500毫秒就会输出)
      if (this.timer !== null) clearTimeout(this.timer)
      this.timer = setTimeout(() => {
        this.getDataList()
      }, 500)
    },
    beforeUpload(file) {
      this.message = this.$message({
        message: '上传中，请勿操作',
        center: true,
        duration: 0,
      });
    },
    handleChange(file, fileList) {
      this.fileList = fileList.slice(-3);

    },
    resultHandle(res) {
      if (res.code !== 0) {
      return this.$message.error(res.msg)
      }
      this.$message({
        message: this.$t('prompt.success'),
        type: 'success',
        duration: 500,
        onClose: () => {
          if (res.data) {
            this.unsuccessfulNameList = res.data
          }
          this.visible = false
          this.query()
        }
      })
    },
  }
}
</script>
