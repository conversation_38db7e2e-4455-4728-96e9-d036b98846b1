<template>
  <el-dialog :visible.sync="visible" :title="!dataForm.id ? $t('add') : $t('update')" :close-on-click-modal="false"
             :close-on-press-escape="false" append-to-body>
    <el-form :model="dataForm" :rules="dataRule" ref="dataForm" @keyup.enter.native="dataFormSubmitHandle()"
             :label-width="$i18n.locale === 'en-US' ? '120px' : '120px'">
      <div class="container">
        <!--        <el-form-item label="外发记录id" prop="outId">-->
        <!--          <el-input v-model="dataForm.outId" placeholder="外发记录id"></el-input>-->
        <!--        </el-form-item>-->
        <el-form-item label="品号" prop="partId">
          <el-select v-model="dataForm.partId" disabled>
            <el-option v-for="(item,index) in partOption"
                       :key="index"
                       :label="item.label"
                       :value="item.value">
            </el-option>
          </el-select>
        </el-form-item>
        <!--        <el-form-item label="次批" prop="subBatchId">-->
        <!--          <el-input v-model="dataForm.subBatchId" placeholder="次批id"></el-input>-->
        <!--        </el-form-item>-->
        <el-form-item label="次批号" prop="subBatchNumber">
          <el-input v-model="dataForm.subBatchNumber" disabled placeholder="次批号"></el-input>
        </el-form-item>
        <el-form-item label="交货数量" prop="quantity">
          <el-input v-model="dataForm.quantity" placeholder="交货数量"></el-input>
        </el-form-item>
        <el-form-item label="不良数" prop="defectQuantity">
          <el-input v-model="dataForm.defectQuantity" placeholder="不良数"></el-input>
        </el-form-item>
        <el-form-item label="交货日期" prop="deliveryTime">
          <el-date-picker
              style="width: 13vw"
              v-model="dataForm.deliveryTime"
              type="datetime"
              placeholder="选择日期时间"
              default-time="8:00:00"
              value-format="yyyy-MM-dd HH:mm:ss"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item label="检查人员编号" prop="inspectionNumber">
          <el-input v-model="dataForm.inspectionNumber" placeholder="检查人员编号"></el-input>
        </el-form-item>
        <el-form-item label="检查方式" prop="inspectionType">
          <el-select v-model="dataForm.inspectionType" clearable>
            <el-option v-for="(item,index) in inspectionTypeOption"
                       :key="index"
                       :label="item.label"
                       :value="item.value">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="当前检查状态" prop="checkMark">
          <el-select v-model="dataForm.checkMark" clearable>
            <el-option v-for="(item,index) in checkMarkOption"
                       :key="index"
                       :label="item.label"
                       :value="item.value">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="备注" >
          <el-input v-model="dataForm.remark" type="textarea" placeholder="备注"></el-input>
        </el-form-item>
      </div>
    </el-form>
    <template slot="footer">
      <el-button @click="visible = false">{{ $t('cancel') }}</el-button>
      <el-button type="primary" @click="dataFormSubmitHandle()">{{ $t('confirm') }}</el-button>
    </template>
  </el-dialog>
</template>

<script>
import debounce from 'lodash/debounce'
import mixinViewModule from '@/mixins/view-module'

export default {
  mixins: [mixinViewModule],
  data() {
    return {
      visible: false,
      //创建该页面时，不调用数据查询接口
      mixinViewModuleOptions: {
        createdIsNeed: false,
      },
      dataForm: {
        id: '',
        outId: '',
        partId: '',
        subBatchId: '',
        subBatchNumber: '',
        quantity: '',
        defectQuantity: '',
        deliveryTime: '',
        inspectionNumber: '',
        inspectionType: '',
        checkMark: '',
        remark: '',
        disabled: '',
        creator: '',
        createDate: '',
        updater: '',
        updateDate: ''
      },
      inspectionTypeOption: [
        {
          label: '抽检',
          value: 0
        },
        {
          label: '全检',
          value: 1
        },
      ],
      checkMarkOption:[
        {
          label: '未检',
          value: 0
        },
        {
          label: '已检',
          value: 1
        },
        {
          label: '待厂内二检',
          value: 2
        },
        {
          label: '待抽检',
          value: 3
        },
        {
          label: '退货',
          value: 4
        }
      ],
      partOption: [],
      outSubBatchOption: [],
    }
  },
  computed: {
    dataRule() {
      return {
        id: [
          {required: true, message: this.$t('validate.required'), trigger: 'blur'}
        ],
        outId: [
          {required: true, message: this.$t('validate.required'), trigger: 'blur'}
        ],
        partId: [
          {required: true, message: this.$t('validate.required'), trigger: 'blur'}
        ],
        subBatchId: [
          {required: true, message: this.$t('validate.required'), trigger: 'blur'}
        ],
        subBatchNumber: [
          {required: true, message: this.$t('validate.required'), trigger: 'blur'}
        ],
        quantity: [
          {required: true, message: this.$t('validate.required'), trigger: 'blur'}
        ],
        defectQuantity: [
          {required: true, message: this.$t('validate.required'), trigger: 'blur'}
        ],
        deliveryTime: [
          {required: true, message: this.$t('validate.required'), trigger: 'blur'}
        ],
        inspectionNumber: [
          {required: true, message: this.$t('validate.required'), trigger: 'blur'}
        ],
        inspectionType: [
          {required: true, message: this.$t('validate.required'), trigger: 'blur'}
        ],
        checkMark: [
          {required: true, message: this.$t('validate.required'), trigger: 'blur'}
        ],
        remark: [
          {required: true, message: this.$t('validate.required'), trigger: 'blur'}
        ],
      }

    }
  },
  methods: {
    init() {
      this.visible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].resetFields()
        this.getPartList()
        if (this.dataForm.id) {
          this.getInfo()
        }
      })
    },
    // 获取信息
    getInfo() {
      this.$http.get(`/inspect/outcollection/${this.dataForm.id}`).then(({data: res}) => {
        if (res.code !== 0) {
          return this.$message.error(res.msg)
        }
        this.dataForm = {
          ...this.dataForm,
          ...res.data
        }
      }).catch(() => {
      })
    },
    //获取品号列表
    getPartList() {
      this.$http.get('fabricate/part/getPartListByDesignation').then(({data: res}) => {
        if (res.code !== 0) {
          return this.$message.error(res.msg)
        }
        this.partOption = res.data.map((obj) => {
          return {
            label: obj.designation,
            value: obj.id,
            data: obj
          }
        })
      })
    },
    // 表单提交
    dataFormSubmitHandle: debounce(function () {
      this.$refs['dataForm'].validate((valid) => {
        if (!valid) {
          return false
        }
        let message = '';
        if (this.dataForm.id) {
          message = '此操作将修改该数据'
        } else {
          message = '此操作将新增一条数据'
        }
        this.$confirm(message + ', 是否继续?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
          center: true
        }).then(() => {
          this.$http[!this.dataForm.id ? 'post' : 'put']('/inspect/outcollection/', this.dataForm).then(({data: res}) => {
            if (res.code !== 0) {
              return this.$message.error(res.msg)
            }
            this.$message({
              message: this.$t('prompt.success'),
              type: 'success',
              duration: 500,
              onClose: () => {
                this.visible = false
                this.$emit('refreshDataList')
              }
            })
          }).catch(() => {
          })
        }).catch(() => {
          this.$message({
            type: 'info',
            message: '已取消'
          });
        });
      })
    }, 1000, {'leading': true, 'trailing': false})
  }
}
</script>
