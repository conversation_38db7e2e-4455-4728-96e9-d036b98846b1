<template>
  <el-dialog :visible.sync="visible" :title="!dataForm.id ? $t('add') : $t('update')" :close-on-click-modal="false"
             :close-on-press-escape="false" append-to-body>
    <el-form :model="dataForm" :rules="dataRule" ref="dataForm" @keyup.enter.native="dataFormSubmitHandle()"
             :label-width="$i18n.locale === 'en-US' ? '120px' : '120px'">
      <div class="container">
        <el-form-item label="工序" prop="process">
          <el-select v-model="dataForm.process">
            <el-option v-for="(item,index) in processOption"
                       :key="index"
                       :label="item.label"
                       :value="item.value">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="品号" prop="partId">
          <el-select v-model="dataForm.partId" disabled>
            <el-option v-for="(item,index) in partOption"
                       :key="index"
                       :label="item.label"
                       :value="item.value">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="次批" prop="subBatchId">
          <el-select v-model="dataForm.subBatchId" @change="changeBatchNumber" filterable clearable>
            <el-option v-for="(item,index) in subBatchOption"
                       :key="index"
                       :label="item.subBatchNumber"
                       :value="item.subBatchId">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="发货数量" prop="quantity">
          <el-input v-model="dataForm.quantity" placeholder="发货数量"></el-input>
        </el-form-item>
        <el-form-item label="发货日期" prop="consignmentTime">
          <el-date-picker
              v-model="dataForm.consignmentTime"
              type="datetime"
              placeholder="选择日期时间"
              default-time="18:00:00"
              value-format="yyyy-MM-dd HH:mm:ss"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item label="发货类型" prop="consignmentStatus">
          <el-select v-model="dataForm.consignmentStatus">
            <el-option v-for="(item,index) in consignmentStatusOption"
                       :key="index"
                       :label="item.label"
                       :value="item.value">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="当前位置" prop="storagePlace">
          <el-select v-model="dataForm.storagePlace">
            <el-option v-for="(item,index) in storagePlaceOption"
                       :key="index"
                       :label="item.label"
                       :value="item.value">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="备注">
          <el-input v-model="dataForm.remark" type="textarea" placeholder="备注"></el-input>
        </el-form-item>
      </div>
    </el-form>
    <template slot="footer">
      <el-button @click="visible = false">{{ $t('cancel') }}</el-button>
      <el-button type="primary" @click="dataFormSubmitHandle()">{{ $t('confirm') }}</el-button>
    </template>
  </el-dialog>
</template>

<script>
import debounce from 'lodash/debounce'
import mixinViewModule from '@/mixins/view-module'
import th from "element-ui/src/locale/lang/th";

export default {
  mixins: [mixinViewModule],
  data() {
    return {
      visible: false,
      //创建该页面时，不调用数据查询接口
      mixinViewModuleOptions: {
        createdIsNeed: false,
      },
      dataForm: {
        id: '',
        process: '',
        partId: '',
        designation: '',
        subBatchId: '',
        subBatchNumber: '',
        quantity: '',
        consignmentTime: '',
        consignmentStatus: '',
        storagePlace: '',
        remark: '',
        disabled: '',
        creator: '',
        createDate: '',
        updater: '',
        updateDate: ''
      },
      processOption: [
        {
          label: '品修',
          value: 0,
        },
        {
          label: '品检',
          value: 1,
        },
        {
          label: '品管',
          value: 2,
        }
      ],
      consignmentStatusOption: [
        {
          label: '正常',
          value: 0
        },
        {
          label: '退货',
          value: 1
        }
      ],
      storagePlaceOption: [
        {
          label: '厂内',
          value: 0
        },
        {
          label: '厂外',
          value: 1
        }
      ],
      partOption: [],
      subBatchOption: [],
    }
  },
  computed: {
    dataRule() {
      return {
        id: [
          {required: true, message: this.$t('validate.required'), trigger: 'blur'}
        ],
        process: [
          {required: true, message: this.$t('validate.required'), trigger: 'blur'}
        ],
        partId: [
          {required: true, message: this.$t('validate.required'), trigger: 'blur'}
        ],
        subBatchId: [
          {required: true, message: this.$t('validate.required'), trigger: 'blur'}
        ],
        subBatchNumber: [
          {required: true, message: this.$t('validate.required'), trigger: 'blur'}
        ],
        quantity: [
          {required: true, message: this.$t('validate.required'), trigger: 'blur'}
        ],
        consignmentTime: [
          {required: true, message: this.$t('validate.required'), trigger: 'blur'}
        ],
        consignmentStatus: [
          {required: true, message: this.$t('validate.required'), trigger: 'blur'}
        ],
        storagePlace: [
          {required: true, message: this.$t('validate.required'), trigger: 'blur'}
        ],
        remark: [
          {required: true, message: this.$t('validate.required'), trigger: 'blur'}
        ],
      }
    }
  },
  methods: {
    init() {
      this.visible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].resetFields()
        if (this.dataForm.id) {
          this.getPartList()
          this.getInfo()
        }
      })
    },
    // 获取信息
    getInfo() {
      this.$http.get(`/inspect/outmanagement/${this.dataForm.id}`).then(({data: res}) => {
        if (res.code !== 0) {
          return this.$message.error(res.msg)
        }
        this.dataForm = {
          ...this.dataForm,
          ...res.data
        }
        console.log(this.dataForm.partId)
        this.getSubBatchList(this.dataForm.partId)
      }).catch(() => {
      })
    },
    //获取次批列表
    getSubBatchList(partId) {
      this.$http.get(`batch/subbatch/getSubBatchListToInput?partId=${partId}`).then(({data: res}) => {
        if (res.code !== 0) {
          return this.$message.error(res.msg)
        }
        this.subBatchOption = res.data
      })
    },
    changeBatchNumber(){
      console.log('触发次批变更')
      if(this.dataForm.subBatchId){
        let filter = this.subBatchOption.filter(item => item.subBatchId === this.dataForm.subBatchId);
        this.dataForm.subBatchNumber = filter[0].subBatchNumber
        console.log(filter.subBatchNumber)
      }else {
        this.dataForm.subBatchNumber = ''
      }
    },
    //获取品号列表
    getPartList() {
      this.$http.get('fabricate/part/getPartListByDesignation').then(({data: res}) => {
        if (res.code !== 0) {
          return this.$message.error(res.msg)
        }
        this.partOption = res.data.map((obj) => {
          return {
            label: obj.designation,
            value: obj.id,
            data: obj
          }
        })
      })
    },
    // 表单提交
    dataFormSubmitHandle: debounce(function () {
      this.$refs['dataForm'].validate((valid) => {
        if (!valid) {
          return false
        }
        let message = '';
        if (this.dataForm.id) {
          message = '此操作将修改该数据'
        } else {
          message = '此操作将新增一条数据'
        }
        this.$confirm(message + ', 是否继续?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
          center: true
        }).then(() => {
          this.$http[!this.dataForm.id ? 'post' : 'put']('/inspect/outmanagement/', this.dataForm).then(({data: res}) => {
            if (res.code !== 0) {
              return this.$message.error(res.msg)
            }
            this.$message({
              message: this.$t('prompt.success'),
              type: 'success',
              duration: 500,
              onClose: () => {
                this.visible = false
                this.$emit('refreshDataList')
              }
            })
          }).catch(() => {
          })
        }).catch(() => {
          this.$message({
            type: 'info',
            message: '已取消'
          });
        });
      })
    }, 1000, {'leading': true, 'trailing': false})
  }
}
</script>
