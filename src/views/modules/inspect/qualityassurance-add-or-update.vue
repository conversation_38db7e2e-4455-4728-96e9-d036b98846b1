<template>
  <el-dialog :visible.sync="visible" :title="!dataForm.id ? $t('add') : $t('update')" :close-on-click-modal="false" :close-on-press-escape="false" append-to-body>
    <el-form :model="dataForm" :rules="dataRule" ref="dataForm" @keyup.enter.native="dataFormSubmitHandle()" :label-width="$i18n.locale === 'en-US' ? '120px' : '80px'">
      <div class="container">
        <el-form-item label="次批号" prop="subBatchId">
          <el-input v-model="dataForm.subBatchId" placeholder="次批号"></el-input>
        </el-form-item>
        <el-form-item label="抽检员" prop="spotCheckers">
          <el-input v-model="dataForm.spotCheckers" placeholder="抽检员"></el-input>
        </el-form-item>
        <el-form-item label="外发员工姓名" prop="outgoingName">
          <el-input v-model="dataForm.outgoingName" placeholder="外发员工姓名"></el-input>
        </el-form-item>
        <el-form-item label="外观" prop="appearance">
          <el-radio v-model="dataForm.appearance" label="OK">OK</el-radio>
          <el-radio v-model="dataForm.appearance" label="NG">NG</el-radio>
        </el-form-item>
        <el-form-item label="抽检日期" prop="samplingDate">
          <el-date-picker
              class="datePicker"
              v-model="dataForm.samplingDate"
              type="date"
              value-format="yyyy-MM-dd"
              placeholder="抽检日期">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="处理结果" prop="processTheResults">
          <el-radio v-model="dataForm.processTheResults" label="OK">OK</el-radio>
          <el-radio v-model="dataForm.processTheResults" label="NG">NG</el-radio>
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="dataForm.remark" placeholder="备注"></el-input>
        </el-form-item>
      </div>
    </el-form>
    <template slot="footer">
      <el-button @click="visible = false">{{ $t('cancel') }}</el-button>
      <el-button type="primary" @click="dataFormSubmitHandle()">{{ $t('confirm') }}</el-button>
    </template>
  </el-dialog>
</template>

<script>
import debounce from 'lodash/debounce'
import mixinViewModule from '@/mixins/view-module'
export default {
 data () {
    return {
      visible: false,
      //创建该页面时，不调用数据查询接口
      mixinViewModuleOptions: {
        createdIsNeed: false,
      },
      dataForm: {
        id: '',
        subBatchId: '',
        spotCheckers: '',
        outgoingName: '',
        appearance: '',
        samplingDate: '',
        processTheResults: '',
        remark: '',
        disabled: '',
        creator: '',
        createDate: '',
        updater: '',
        updateDate: ''
      }
    }
  },
  computed: {
    dataRule () {
      return {
          id: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          subBatchId: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          spotCheckers: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          appearance: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          samplingDate: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          processTheResults: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          remark: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
                }
    }
  },
  methods: {
    init () {
      this.visible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].resetFields()
        if (this.dataForm.id) {
          this.getInfo()
        }
      })
    },
    // 获取信息
    getInfo () {
      this.$http.get(`/inspect/qualityassurance/${this.dataForm.id}`).then(({ data: res }) => {
        if (res.code !== 0) {
          return this.$message.error(res.msg)
        }
        this.dataForm = {
          ...this.dataForm,
          ...res.data
        }
      }).catch(() => {})
    },
    // 表单提交
    dataFormSubmitHandle: debounce(function () {
      this.$refs['dataForm'].validate((valid) => {
        if (!valid) {
          return false
        }
        this.$http[!this.dataForm.id ? 'post' : 'put']('/inspect/qualityassurance/', this.dataForm).then(({ data: res }) => {
          if (res.code !== 0) {
            return this.$message.error(res.msg)
          }
          this.$message({
            message: this.$t('prompt.success'),
            type: 'success',
            duration: 500,
            onClose: () => {
              this.visible = false
              this.$emit('refreshDataList')
            }
          })
        }).catch(() => {})
      })
    }, 1000, { 'leading': true, 'trailing': false })
  }
}
</script>
