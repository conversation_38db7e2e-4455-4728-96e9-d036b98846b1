<template>
  <div>
    <el-dialog :visible.sync="visible" center width="37vw" append-to-body :title="subBatchNumber + '外发记录'">
      <el-timeline :reverse="false">
        <el-timeline-item v-for="(activity,index) in activities" :key="index" :timestamp="activity.timestamp" placement="top">
          <el-card>
            <h4>{{activity.title}}</h4>
            <p v-html="activity.content"></p>
          </el-card>
        </el-timeline-item>
      </el-timeline>
    </el-dialog>
  </div>
</template>

<script>
export default {
  name: "outTimeLineDialog",
  data(){
    return{
      visible:false,
      subBatchNumber:'',
      subBatchId:'',
      activities:[]
    }
  },
  methods:{
    init(){
      this.visible = true
      this.$nextTick(() =>{
        this.getData(this.subBatchId)
      })
    },
    getData(id){
      this.$http.get(`inspect/outinspectionrecords/getDataTimeline?subBatchId=${id}`).then(({data: res})=>{
        if(res.code !== 0){
          return this.$message.error(res.msg)
        }
        const data = res.data;
        // 对数据进行排序
        const sortedData = [...data].sort((a, b) => new Date(a.timestamp) - new Date(b.timestamp));
        // 将排序后的数据赋值给 this.activities
        this.activities = sortedData;
      })
    },
  }
}
</script>

<style scoped>

</style>