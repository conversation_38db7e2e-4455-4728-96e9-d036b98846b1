<template>
  <div>
    <div>
  <el-card shadow="never" class="aui-card--fill">
    <div class="mod-inspect__qualityinspectionnissanrecord}">
      <el-form :inline="true" :model="dataForm" @keyup.enter.native="getDataList()">
        <el-form-item>
          <el-tooltip content="请输入查询关键字" placement="top">
            <el-input v-model="dataForm.paramStr" placeholder="查询关键字" @input="throttleFunction" clearable>
              <el-button slot="append" icon="el-icon-search" @click="getDataList()"></el-button>
            </el-input>
          </el-tooltip>
        </el-form-item>
        <el-form-item>
          <el-button v-if="$hasPermission('inspect:qualityinspectionnissanrecord:save')" type="primary" @click="addOrUpdateHandle()">{{ $t('add') }}</el-button>
        </el-form-item>
        <el-form-item>
          <el-select v-model="dataForm.temporary" @change="getDataList()">
            <el-option :value="0" label="已存"></el-option>
            <el-option :value="1" label="暂存"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="summaryHandle()">品检汇总</el-button>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="dialogVisible1 = true">报表资料补充</el-button>
        </el-form-item>
        <el-dropdown  style="float:right;">
          <el-button type="primary">
            更多功能<i class="el-icon-arrow-down el-icon--right"></i>
          </el-button>
          <el-dropdown-menu slot="dropdown">
            <el-dropdown-item>
              <el-form-item>
                <el-button type="info" @click="exporthandle()">导出</el-button>
              </el-form-item>
            </el-dropdown-item>
            <el-dropdown-item>
              <el-form-item>
                <el-button v-if="$hasPermission('inspect:qualityinspectionnissanrecord:export')" type="info" @click="exportTemplateHandle()">
                  {{ $t('exportTemplate') }}
                </el-button>
              </el-form-item>
            </el-dropdown-item>
            <el-dropdown-item>
              <el-form-item>
                <el-tooltip content="下载模板填入信息后导入" placement="top">
                  <el-upload
                      v-if="$hasPermission('inspect:qualityinspectionnissanrecord:save')"
                      class="upload-demo"
                      :action="mixinViewModuleOptions.addBatchUrl"
                      :headers="headers"
                      :multiple="false"
                      :show-file-list="false"
                      :file-list="fileList"
                      :before-upload="beforeUpload"
                      :on-success="resultHandle"
                      :on-change="handleChange"
                      accept="'.xlsx','.xls'">
                    <el-button type="success">{{ $t('addBatch') }}</el-button>
                  </el-upload>
                </el-tooltip>
                <el-button type="text" v-show="unsuccessfulNameList" @click="dialogVisible = true">导入失败列表</el-button>
              </el-form-item>
            </el-dropdown-item>
            <el-dropdown-item>
              <el-form-item v-if="dataForm.temporary == 0">
                <el-button v-if="$hasPermission('inspect:qualityinspectionnissanrecord:delete')" type="danger" @click="deleteHandle()">{{ $t('deleteBatch') }}</el-button>
              </el-form-item>
              <el-form-item v-else>
                <el-button type="danger" @click="deleteStepStaging()">{{ $t('deleteBatch') }}</el-button>
              </el-form-item>
            </el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown>
      </el-form>
      <el-table v-loading="dataListLoading" height="600px" :data="dataList" border @selection-change="dataListSelectionChangeHandle" style="width: 100%;">
        <el-table-column type="selection" header-align="center" align="center" width="50"></el-table-column>
        <el-table-column prop="pickingDate" label="品检日期" header-align="center" align="center"></el-table-column>
<!--        <el-table-column prop="itemNumber" label="品名编号" header-align="center" align="center"></el-table-column>
        <el-table-column prop="designation" label="部品品番" header-align="center" align="center"></el-table-column>-->
<!--        <el-table-column prop="batchId" label="批号" header-align="center" align="center">
          <template slot-scope="scope">
            <div>
              {{$getDictLabel("size_category", scope.row.sizeCategory)+ scope.row.batchNumber}}
            </div>
          </template>
        </el-table-column>-->
        <el-table-column prop="subBatchNumber" label="次批号" header-align="center" align="center"></el-table-column>
        <el-table-column prop="qualityInspector" label="品检员" header-align="center" align="center">
          <template slot-scope="scope">
            <div>
              {{$getEmployeesList(scope.row.qualityInspector)}}
            </div>
          </template>
        </el-table-column>
<!--        <el-table-column prop="outgoingName" label="外发员工姓名" header-align="center" align="center"></el-table-column>-->
        <el-table-column prop="qcQuantity" label="品检数量" header-align="center" align="center"></el-table-column>
        <el-table-column prop="actualReceivedAmount" label="实际领取数" header-align="center" align="center"></el-table-column>
        <el-table-column prop="adverseNumber" label="不良数" header-align="center" align="center"></el-table-column>
        <el-table-column prop="performancePoints" label="绩效" header-align="center" align="center"></el-table-column>
        <el-table-column prop="remark" label="备注" header-align="center" align="center"></el-table-column>
        <el-table-column :label="$t('handle')" fixed="right" v-if="dataForm.temporary == 0" header-align="center" align="center" width="150px">
          <template slot-scope="scope">
            <el-dropdown>
              <el-button type="primary">
                功能<i class="el-icon-arrow-down el-icon--right"></i>
              </el-button>
              <el-dropdown-menu slot="dropdown">
                <el-dropdown-item>
                  <el-button v-if="$hasPermission('inspect:qualityinspectionnissanrecord:update')" type="text" size="small" @click="addOrUpdateHandle(scope.row.id)">{{ $t('update') }}</el-button>
                </el-dropdown-item>
                <el-dropdown-item>
                  <el-button v-if="$hasPermission('inspect:qualityinspectionnissanrecord:delete')" type="text" size="small" @click="deleteHandle(scope.row.id)">{{ $t('delete') }}</el-button>
                </el-dropdown-item>
              </el-dropdown-menu>
            </el-dropdown>
          </template>
        </el-table-column>
        <el-table-column :label="$t('handle')" fixed="right" v-else header-align="center" align="center" width="150px">
          <template slot-scope="scope">
            <el-dropdown>
              <el-dropdown-item>
                <el-button type="text" size="small" @click="deleteStepStaging(scope.row.snapshotId)">{{ $t('delete') }}</el-button>
              </el-dropdown-item>
            </el-dropdown>
          </template>
        </el-table-column>
      </el-table>
      <el-pagination
        :current-page="page"
        :page-sizes="[10, 20, 50, 100]"
        :page-size="limit"
        :total="total"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="pageSizeChangeHandle"
        @current-change="pageCurrentChangeHandle">
      </el-pagination>
      <!-- 弹窗, 新增 / 修改 -->
      <div>
        <add-or-update v-if="addOrUpdateVisible" ref="addOrUpdate" @refreshDataList="getDataList"></add-or-update>
        <excelexport-component v-if="excelExport" ref="excel"></excelexport-component>
      </div>
    </div>
  </el-card>
  </div>
    <div>
      <el-dialog :visible.sync="dialogVisible1"  title="品检报表资料补充">
        <el-form :model="statementFrom" ref="statementFrom" label-width="80px">
          <div style="display: flex">
          <div>
            <el-form-item label="选择批号">
           <el-select v-model="statementFrom.subBatchId"
                      :filter-method="customFilter"
                      filterable
                      clearable>
             <el-option
                 v-for="item in showSubBatchList"
                 :key="item.value"
                 :label="item.label"
                 :value="item.value">
             </el-option>
           </el-select>
            </el-form-item>
          </div>
            <div>
             <el-form-item label="品修员">
                          <el-select v-model="statementFrom.qualityRepairman"  clearable filterable>
                            <el-option v-for="(item,index) in employeeList"
                              :key="index"
                              :label="item.username + '(' + item.userCode + ')'"
                              :value="item.id"
                            >
                            </el-option>
                          </el-select>
                   </el-form-item>
             </div>
            <div>
              <el-form-item label="包装数量">
                <el-input v-model="statementFrom.quantity" placeholder=""></el-input>
              </el-form-item>
            </div>
          </div>
          <div style="display: flex">
            <div>
                   <el-form-item label="抽检日期">
                          <el-date-picker
                              v-model="statementFrom.samplingDate"
                              type="date"
                              value-format="yyyy-MM-dd"
                              placeholder="选择日期时间"
                              style="width: 93%">
                          </el-date-picker>
                        </el-form-item>
                      </div>
                      <div>
                        <el-form-item label="抽检员">
                          <el-select v-model="statementFrom.spotCheckers"  clearable filterable>
                            <el-option v-for="(item,index) in employeeList"
                                       :key="index"
                                       :label="item.username + '(' + item.userCode + ')'"
                                       :value="item.id"
                            >
                            </el-option>
                          </el-select>
                        </el-form-item>
                      </div>
          </div>
        </el-form>
        <template slot="footer">
          <el-button @click="dialogVisible1 = false">{{ $t('cancel') }}</el-button>
          <el-button type="primary" @click="dataFormSubmitHandle">{{ $t('confirm') }}</el-button>
        </template>
      </el-dialog>
    </div>
  </div>
</template>

<script>
import mixinViewModule from '@/mixins/view-module'
import AddOrUpdate from './qualityinspectionnissanrecord-add-or-update'
import ExcelexportComponent from "../../../components/shared-search/excelexport-component";
import Cookies from "js-cookie";
import {addDynamicRoute} from '@/router'
import {getSubBatchList} from "@/utils";
import th from "element-ui/src/locale/lang/th";
import debounce from "lodash/debounce";
export default {
  mixins: [mixinViewModule],
  data () {
    return {
      message:'',
      headers: {'token': Cookies.get('token')},
      fileList: [{name: '', url: ''}],
      unsuccessfulNameList: '',//  导入失败名称列表
      mixinViewModuleOptions: {
        getDataListURL: '/inspect/qualityinspectionnissanrecord/page',
        getDataListIsPage: true,
        exportURL: '/inspect/qualityinspectionnissanrecord/export',
        deleteURL: '/inspect/qualityinspectionnissanrecord',
        deleteStepStagingURL: '/notify/stepstaging',
        deleteIsBatch: true,
        deleteIsStepStagingBatchKey: 'snapshotId',   // 删除接口，批量状态下由那个key进行标记操作？比如：pid，uid...
        exportTemplateURL: 'inspect/qualityinspectionnissanrecord/export/template',
        addBatchUrl: window.SITE_CONFIG['apiURL'] + 'inspect/qualityinspectionnissanrecord/batchSave',
      },
      // 判断是否还在继续输入
      timer: null,
      dataForm: {
        id: '',
        paramStr: '',
        temporary: 0,
      },
      statementFrom:{
        qualityRepairman:'',
        subBatchId:'',
        samplingDate:'',
        spotCheckers:'',
        quantity:'',
      },
      subBatchList:[],
      showSubBatchList:[],
      employeeList:[],
      deptList:["1641701552458743810","1641701892159619073"],
      excelExport:false,
      dialogVisible1:false
    }
  },
  components: {
    AddOrUpdate,
    ExcelexportComponent
  },
  created() {
    this.getSubBatchList();
    this.getRepairAndInspect();
  },
  methods:{
    summaryHandle(){
      // 路由参数
      const routeParams = {
        routeName: `inspect__qualityinspectionsummary`,
        title: `品检汇总`,
        path: 'inspect/qualityinspectionsummary',
      }
      // 动态路由
      addDynamicRoute(routeParams, this.$router)
    },
    //自定义查询方法
    customFilter(val){
      let filterList = this.subBatchList.filter((item) => {
        return item.label.includes(val)
      });
      if(filterList.length > 200){
        this.showSubBatchList = filterList.slice(0,200)
      }else {
        this.showSubBatchList = filterList
      }
    },
    //获取次批列表
    getSubBatchList(){
        this.$http.get(`/batch/subbatch/querySuBatchNumber`).then(({data: res}) => {
          if (res.code !== 0) {
            return this.$message.error(res.msg);
          }
          this.subBatchList = res.data.map((obj) => {
            return {
              label: obj.subBatchNumber,
              value: obj.id,
              data: obj
            };
          });
          //过滤掉label为空的值
          let filter = this.subBatchList.filter(item => item.label !== undefined && item.label !== null );
          this.subBatchList = filter
          this.showSubBatchList = this.subBatchList.slice(-200)
        })
    },
    //获取品检品修人员列表
    getRepairAndInspect(){
      this.$http.post(`/sys/user/getUserListByDeptId`,this.deptList).then(({data: res}) =>{
        if (res.code !== 0) {
          return this.$message.error(res.msg)
        }
        this.employeeList = res.data;
      })
    },
    // 删除暂存数据
    deleteStepStaging(id){
      if (this.mixinViewModuleOptions.deleteIsBatch && !id && this.dataListSelections.length <= 0) {
        return this.$message({
          message: this.$t('prompt.deleteBatch'),
          type: 'warning',
          duration: 500
        })
      }
      this.$confirm(this.$t('prompt.info', {'handle': this.$t('delete')}), this.$t('prompt.title'), {
        confirmButtonText: this.$t('confirm'),
        cancelButtonText: this.$t('cancel'),
        type: 'warning'
      }).then(() => {
        this.$http.delete(
            `${this.mixinViewModuleOptions.deleteStepStagingURL}${this.mixinViewModuleOptions.deleteIsBatch ? '' : '/' + id}`,
            this.mixinViewModuleOptions.deleteIsBatch ? {
              'data': id ? [id] : this.dataListSelections.map(item => item[this.mixinViewModuleOptions.deleteIsStepStagingBatchKey])
            } : {}
        ).then(({data: res}) => {
          if (res.code !== 0) {
            return this.$message.error(res.msg)
          }
          this.$message({
            message: res.msg,
            type: 'success',
            duration: 500,
            onClose: () => {
              this.query()
            }
          })
        }).catch(() => {
        })
      }).catch(() => {
      })
    },
    // 表单提交
    dataFormSubmitHandle: debounce(function () {
      this.$refs['statementFrom'].validate((valid) => {
        if (!valid) {
          return false
        }
        this.$confirm('此操作将新增一条数据, 是否继续?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
          center: true
        }).then(() => {
          this.$http.post('/inspect/qualityinspectionnissanrecord/savaStatementFrom', this.statementFrom).then(({data: res}) => {
            if (res.code !== 0) {
              return this.$message.error(res.msg)
            }
            this.$message({
              message: this.$t('prompt.success'),
              type: 'success',
              duration: 500,
              onClose: () => {
                this.dialogVisible1 = false
                this.statementFrom = {
                  qualityRepairman:'',
                  subBatchId:'',
                  samplingDate:'',
                  spotCheckers:'',
                  quantity:'',
                }
              }
            })
          }).catch(() => {})
        }).catch(() => {
          this.$message({
            type: 'info',
            message: '已取消'
          });
        });
      })
    }, 1000, {'leading': true, 'trailing': false}),
    //节流函数
    throttleFunction() {
      // 如果timer不为空就表明在持续输入,每次改变都清除定时，这样就可以实现只输入最后一次结果(注意每次间隔如果超过500毫秒就会输出)
      if (this.timer !== null) clearTimeout(this.timer)
      this.timer = setTimeout(() => {
        this.getDataList()
      }, 500)
    },
    exporthandle(){
      this.excelExport = true
      this.$nextTick(()=>{
        this.$refs.excel.mixinViewModuleOptions.exportURL = this.mixinViewModuleOptions.exportURL
        this.$refs.excel.init();
      })
    },
    beforeUpload(file) {
      this.message = this.$message({
        message: '上传中，请勿操作',
        center: true,
        duration: 0,
      });
    },
    handleChange(file, fileList) {
      this.fileList = fileList.slice(-3);

    },
    resultHandle(res) {
      if (res.code !== 0) {
      return this.$message.error(res.msg)
      }
      this.$message({
        message: this.$t('prompt.success'),
        type: 'success',
        duration: 500,
        onClose: () => {
          if (res.data) {
            this.unsuccessfulNameList = res.data
          }
          this.visible = false
          this.query()
        }
      })
    },
  }
}
</script>
