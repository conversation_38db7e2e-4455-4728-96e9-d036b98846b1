<template>
  <el-dialog :visible.sync="visible" :title="!dataForm.id ? $t('add') : $t('update')" :close-on-click-modal="false" :close-on-press-escape="false" append-to-body>
    <el-form :model="dataForm" :rules="dataRule" ref="dataForm" @keyup.enter.native="dataFormSubmitHandle()" :label-width="$i18n.locale === 'en-US' ? '120px' : '80px'">
      <div class="container">
                                                <el-form-item label="日抽检id" prop="formingDailySamplingId">
              <el-input v-model="dataForm.formingDailySamplingId" placeholder="日抽检id"></el-input>
            </el-form-item>
                                        <el-form-item label="次批id" prop="subBatchId">
              <el-input v-model="dataForm.subBatchId" placeholder="次批id"></el-input>
            </el-form-item>
                                        <el-form-item label="生产次批" prop="subBatchNumber">
              <el-input v-model="dataForm.subBatchNumber" placeholder="生产次批"></el-input>
            </el-form-item>
                                        <el-form-item label="硬度" prop="hardness">
              <el-input v-model="dataForm.hardness" placeholder="硬度"></el-input>
            </el-form-item>
                                        <el-form-item label="首件" prop="firstPiece">
              <el-input v-model="dataForm.firstPiece" placeholder="首件"></el-input>
            </el-form-item>
                                        <el-form-item label="样品核对" prop="sampleVerification">
              <el-input v-model="dataForm.sampleVerification" placeholder="样品核对"></el-input>
            </el-form-item>
                                        <el-form-item label="巡检" prop="inspection">
              <el-input v-model="dataForm.inspection" placeholder="巡检"></el-input>
            </el-form-item>
                                        <el-form-item label="末件" prop="lastPiece">
              <el-input v-model="dataForm.lastPiece" placeholder="末件"></el-input>
            </el-form-item>
                                        <el-form-item label="检查时间" prop="inspectionTime">
              <el-input v-model="dataForm.inspectionTime" placeholder="检查时间"></el-input>
            </el-form-item>
                                        <el-form-item label="4M变更" prop="change">
              <el-input v-model="dataForm.change" placeholder="4M变更"></el-input>
            </el-form-item>
                                        <el-form-item label="取样数量" prop="samplingNumber">
              <el-input v-model="dataForm.samplingNumber" placeholder="取样数量"></el-input>
            </el-form-item>
                                        <el-form-item label="破裂" prop="rupture">
              <el-input v-model="dataForm.rupture" placeholder="破裂"></el-input>
            </el-form-item>
                                        <el-form-item label="包风" prop="bagWind">
              <el-input v-model="dataForm.bagWind" placeholder="包风"></el-input>
            </el-form-item>
                                        <el-form-item label="压痕" prop="indentation">
              <el-input v-model="dataForm.indentation" placeholder="压痕"></el-input>
            </el-form-item>
                                        <el-form-item label="死料" prop="deathPenalty">
              <el-input v-model="dataForm.deathPenalty" placeholder="死料"></el-input>
            </el-form-item>
                                        <el-form-item label="缺料" prop="lackOfMaterial">
              <el-input v-model="dataForm.lackOfMaterial" placeholder="缺料"></el-input>
            </el-form-item>
                                        <el-form-item label="拉伤" prop="strain">
              <el-input v-model="dataForm.strain" placeholder="拉伤"></el-input>
            </el-form-item>
                                        <el-form-item label="压毛边" prop="burr">
              <el-input v-model="dataForm.burr" placeholder="压毛边"></el-input>
            </el-form-item>
                                        <el-form-item label="起泡" prop="bubbling">
              <el-input v-model="dataForm.bubbling" placeholder="起泡"></el-input>
            </el-form-item>
                                        <el-form-item label="杂质" prop="impurities">
              <el-input v-model="dataForm.impurities" placeholder="杂质"></el-input>
            </el-form-item>
                                        <el-form-item label="剪伤" prop="cut">
              <el-input v-model="dataForm.cut" placeholder="剪伤"></el-input>
            </el-form-item>
                                        <el-form-item label="灌点凹" prop="concavePouringPoint">
              <el-input v-model="dataForm.concavePouringPoint" placeholder="灌点凹"></el-input>
            </el-form-item>
                                        <el-form-item label="料污" prop="materialPollution">
              <el-input v-model="dataForm.materialPollution" placeholder="料污"></el-input>
            </el-form-item>
                                        <el-form-item label="变形" prop="deformation">
              <el-input v-model="dataForm.deformation" placeholder="变形"></el-input>
            </el-form-item>
                                        <el-form-item label="露铁" prop="exposedIron">
              <el-input v-model="dataForm.exposedIron" placeholder="露铁"></el-input>
            </el-form-item>
                                        <el-form-item label="毛边" prop="burrs">
              <el-input v-model="dataForm.burrs" placeholder="毛边"></el-input>
            </el-form-item>
                                        <el-form-item label="颗粒" prop="particles">
              <el-input v-model="dataForm.particles" placeholder="颗粒"></el-input>
            </el-form-item>
                                        <el-form-item label="断柱子" prop="brokenColumn">
              <el-input v-model="dataForm.brokenColumn" placeholder="断柱子"></el-input>
            </el-form-item>
                                        <el-form-item label="不熟" prop="undercooked">
              <el-input v-model="dataForm.undercooked" placeholder="不熟"></el-input>
            </el-form-item>
                                        <el-form-item label="爆边" prop="burstEdge">
              <el-input v-model="dataForm.burstEdge" placeholder="爆边"></el-input>
            </el-form-item>
                                        <el-form-item label="孔堵" prop="holePlug">
              <el-input v-model="dataForm.holePlug" placeholder="孔堵"></el-input>
            </el-form-item>
                                        <el-form-item label="模伤" prop="moldDamage">
              <el-input v-model="dataForm.moldDamage" placeholder="模伤"></el-input>
            </el-form-item>
                                        <el-form-item label="灌点破" prop="brokenPouringPoint">
              <el-input v-model="dataForm.brokenPouringPoint" placeholder="灌点破"></el-input>
            </el-form-item>
                                        <el-form-item label="五彩" prop="colorful">
              <el-input v-model="dataForm.colorful" placeholder="五彩"></el-input>
            </el-form-item>
                                        <el-form-item label="针孔" prop="needleHole">
              <el-input v-model="dataForm.needleHole" placeholder="针孔"></el-input>
            </el-form-item>
                                        <el-form-item label="脱胶" prop="degumming">
              <el-input v-model="dataForm.degumming" placeholder="脱胶"></el-input>
            </el-form-item>
                                        <el-form-item label="异品" prop="differentProducts">
              <el-input v-model="dataForm.differentProducts" placeholder="异品"></el-input>
            </el-form-item>
                                        <el-form-item label="异物" prop="foreignMatter">
              <el-input v-model="dataForm.foreignMatter" placeholder="异物"></el-input>
            </el-form-item>
                                        <el-form-item label="黑点" prop="blackSpot">
              <el-input v-model="dataForm.blackSpot" placeholder="黑点"></el-input>
            </el-form-item>
                                        <el-form-item label="分模线粗" prop="coarsePartingLine">
              <el-input v-model="dataForm.coarsePartingLine" placeholder="分模线粗"></el-input>
            </el-form-item>
                                        <el-form-item label="发亮" prop="shiny">
              <el-input v-model="dataForm.shiny" placeholder="发亮"></el-input>
            </el-form-item>
                                        <el-form-item label="铁件不良" prop="ironPartsDefects">
              <el-input v-model="dataForm.ironPartsDefects" placeholder="铁件不良"></el-input>
            </el-form-item>
                                        <el-form-item label="胶歪" prop="glueCrooked">
              <el-input v-model="dataForm.glueCrooked" placeholder="胶歪"></el-input>
            </el-form-item>
                                        <el-form-item label="灌点大" prop="largePouringPoint">
              <el-input v-model="dataForm.largePouringPoint" placeholder="灌点大"></el-input>
            </el-form-item>
                                        <el-form-item label="不粘胶" prop="notStickyGlue">
              <el-input v-model="dataForm.notStickyGlue" placeholder="不粘胶"></el-input>
            </el-form-item>
                                        <el-form-item label="刻印" prop="engraving">
              <el-input v-model="dataForm.engraving" placeholder="刻印"></el-input>
            </el-form-item>
                                        <el-form-item label="毛边" prop="roughEdge">
              <el-input v-model="dataForm.roughEdge" placeholder="毛边"></el-input>
            </el-form-item>
                                        <el-form-item label="其他项目检查记录" prop="otherProjects">
              <el-input v-model="dataForm.otherProjects" placeholder="其他项目检查记录"></el-input>
            </el-form-item>
                                        <el-form-item label="不良率" prop="defectiveRate">
              <el-input v-model="dataForm.defectiveRate" placeholder="不良率"></el-input>
            </el-form-item>
                                        <el-form-item label="备注" prop="remark">
              <el-input v-model="dataForm.remark" placeholder="备注"></el-input>
            </el-form-item>
                                                                                                                  </div>
    </el-form>
    <template slot="footer">
      <el-button @click="visible = false">{{ $t('cancel') }}</el-button>
      <el-button type="primary" @click="dataFormSubmitHandle()">{{ $t('confirm') }}</el-button>
    </template>
  </el-dialog>
</template>

<script>
import debounce from 'lodash/debounce'
import mixinViewModule from '@/mixins/view-module'
export default {
  mixins:[mixinViewModule],
  data () {
    return {
      visible: false,
      //创建该页面时，不调用数据查询接口
      mixinViewModuleOptions: {
        createdIsNeed: false,
      },
      dataForm: {
        id: '',
        formingDailySamplingId: '',
        subBatchId: '',
        subBatchNumber: '',
        hardness: '',
        firstPiece: '',
        sampleVerification: '',
        inspection: '',
        lastPiece: '',
        inspectionTime: '',
        change: '',
        samplingNumber: '',
        rupture: '',
        bagWind: '',
        indentation: '',
        deathPenalty: '',
        lackOfMaterial: '',
        strain: '',
        burr: '',
        bubbling: '',
        impurities: '',
        cut: '',
        concavePouringPoint: '',
        materialPollution: '',
        deformation: '',
        exposedIron: '',
        burrs: '',
        particles: '',
        brokenColumn: '',
        undercooked: '',
        burstEdge: '',
        holePlug: '',
        moldDamage: '',
        brokenPouringPoint: '',
        colorful: '',
        needleHole: '',
        degumming: '',
        differentProducts: '',
        foreignMatter: '',
        blackSpot: '',
        coarsePartingLine: '',
        shiny: '',
        ironPartsDefects: '',
        glueCrooked: '',
        largePouringPoint: '',
        notStickyGlue: '',
        engraving: '',
        roughEdge: '',
        otherProjects: '',
        defectiveRate: '',
        remark: '',
        disabled: '',
        creator: '',
        createDate: '',
        updater: '',
        updateDate: ''
      }
    }
  },
  computed: {
    dataRule () {
      return {
          id: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          formingDailySamplingId: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          subBatchId: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          subBatchNumber: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          hardness: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          firstPiece: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          sampleVerification: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          inspection: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          lastPiece: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          inspectionTime: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          change: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          samplingNumber: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          rupture: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          bagWind: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          indentation: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          deathPenalty: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          lackOfMaterial: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          strain: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          burr: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          bubbling: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          impurities: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          cut: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          concavePouringPoint: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          materialPollution: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          deformation: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          exposedIron: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          burrs: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          particles: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          brokenColumn: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          undercooked: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          burstEdge: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          holePlug: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          moldDamage: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          brokenPouringPoint: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          colorful: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          needleHole: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          degumming: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          differentProducts: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          foreignMatter: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          blackSpot: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          coarsePartingLine: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          shiny: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          ironPartsDefects: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          glueCrooked: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          largePouringPoint: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          notStickyGlue: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          engraving: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          roughEdge: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          otherProjects: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          defectiveRate: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          remark: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
                }
    }
  },
  methods: {
    init () {
      this.visible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].resetFields()
        if (this.dataForm.id) {
          this.getInfo()
        }
      })
    },
    // 获取信息
    getInfo () {
      this.$http.get(`/inspect/formingdailysamplingdetails/${this.dataForm.id}`).then(({ data: res }) => {
        if (res.code !== 0) {
          return this.$message.error(res.msg)
        }
        this.dataForm = {
          ...this.dataForm,
          ...res.data
        }
      }).catch(() => {})
    },
    // 表单提交
    dataFormSubmitHandle: debounce(function () {
      this.$refs['dataForm'].validate((valid) => {
        if (!valid) {
          return false
        }
        this.$http[!this.dataForm.id ? 'post' : 'put']('/inspect/formingdailysamplingdetails/', this.dataForm).then(({ data: res }) => {
          if (res.code !== 0) {
            return this.$message.error(res.msg)
          }
          this.$message({
            message: this.$t('prompt.success'),
            type: 'success',
            duration: 500,
            onClose: () => {
              this.visible = false
              this.$emit('refreshDataList')
            }
          })
        }).catch(() => {})
      })
    }, 1000, { 'leading': true, 'trailing': false })
  }
}
</script>
