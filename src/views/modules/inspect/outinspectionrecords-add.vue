<template>
  <el-dialog :visible.sync="visible" title="新增检查记录" :close-on-click-modal="false"
             :close-on-press-escape="false" append-to-body>
    <el-form :model="dataForm" :rules="dataRule" ref="dataForm" @keyup.enter.native="dataFormSubmitHandle()"
             :label-width="$i18n.locale === 'en-US' ? '120px' : '120px'">
      <div class="container">
        <el-form-item label="品号" prop="partId">
          <el-select v-model="dataForm.partId" @change="getSubBatchByPart" filterable clearable>
            <el-option v-for="(item,index) in partOption"
                       :key="index"
                       :label="item.label"
                       :value="item.value">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="检查次批">
          <!--          <el-form-item label="次批号" prop="subBatchNumber">-->
          <!--            <el-input v-model="dataForm.subBatchNumber" placeholder="次批号"></el-input>-->
          <el-select v-model="subBatchIdList" @change="fillDataList" multiple clearable filterable>
            <el-option v-for="(item,index) in outCollectionOption"
                       :key="index"
                       :label="item.subBatchNumber"
                       :value="item.id">
            </el-option>
          </el-select>
          <el-button v-if="subBatchIdList.length !== 0" @click="visible1 = true" type="info" style="margin-left: 2vw">
            检查详情
          </el-button>
        </el-form-item>
<!--        <el-form-item label="备注" prop="remark">-->
<!--          <el-input type="textarea" v-model="dataForm.remark" placeholder="备注" style="width: 300px"></el-input>-->
<!--        </el-form-item>-->
      </div>
    </el-form>
    <template slot="footer">
      <el-button @click="visible = false">{{ $t('cancel') }}</el-button>
      <el-button type="primary" @click="dataFormSubmitHandle()">{{ $t('confirm') }}</el-button>
    </template>
    <el-dialog width="100vw" :visible.sync="visible1" append-to-body title="检查详情" :close-on-click-modal="false"
               center>
      <div v-for="(item, index) in dataForm.dataList" :key="index">
        <div style="display: flex; margin-bottom: 1vh">
          <div style="flex-grow: 3; display: flex; justify-content: center; align-items: center;">
            <div style="font-size: larger;">
              次批号:
              <el-input disabled v-model="item.subBatchNumber" style="width: 170px; font-size: large"></el-input>
            </div>
          </div>
          <div style="flex-grow: 2; display: flex; justify-content: center; align-items: center;">
            <div style="font-size: large">
              检查方式:
              <el-select v-model="item.inspectionType" style="width: 100px;" class="select-font">
                <el-option v-for="(item,index) in inspectionTypeOption"
                           style="font-size: large"
                           :key="index"
                           :label="item.label"
                           :value="item.value">
                </el-option>
              </el-select>
            </div>
          </div>
          <div style="flex-grow: 2; display: flex; justify-content: center; align-items: center;">
            <div style="font-size: large">
              检查数量:
              <el-input v-model="item.quantity" style="width: 120px; font-size: large"></el-input>
            </div>
          </div>
          <div style="flex-grow: 2; display: flex; justify-content: center; align-items: center;">
            <div style="font-size: large">
              检查日期:
              <el-date-picker
                  style="width: 180px"
                  v-model="item.inspectionTime"
                  type="datetime"
                  default-time="15:00:00"
                  value-format="yyyy-MM-dd HH:mm:ss">
              </el-date-picker>
            </div>
          </div>
          <div style="flex-grow: 2; display: flex; justify-content: center; align-items: center;">
            <div style="font-size: large">
              检查人员:
              <el-select v-model="item.inspector" @change="selectInspectorName(index)" style="width: 150px; font-size: large" filterable clearable>
                <el-option v-for="(item,index) in inspectorOption"
                           :key="index"
                           :label="item.label"
                           :value="item.value">
                </el-option>
              </el-select>
            </div>
          </div>
          <div style="flex-grow: 2; display: flex; justify-content: center; align-items: center;">
            <div style="font-size: large">
              判定结果:
              <el-radio-group v-model="item.judgementResult" style="font-size: large">
                <el-radio :label="0">OK</el-radio>
                <el-radio :label="1">NG</el-radio>
              </el-radio-group>
            </div>
          </div>
          <div v-if="item.judgementResult === 1" style="flex-grow: 2; display: flex; justify-content: center; align-items: center;">
            <div style="font-size: large">
              不良项:<el-input type="textarea" v-model="item.adverseItem" style="width: 160px;"></el-input>
            </div>
          </div>
          <div v-if="item.judgementResult === 1" style="flex-grow: 2; display: flex; justify-content: center; align-items: center;">
            <div style="font-size: large">
              处理方式:
<!--              <el-input type="textarea" v-model="item.processMode" style="width: 160px; font-size: large"></el-input>-->
              <el-autocomplete
                  style="width: 100px"
                  v-model="item.processMode"
                  :fetch-suggestions="querySearch"
              ></el-autocomplete>
            </div>
          </div>
        </div>
        <el-divider><i class="el-icon-edit"></i></el-divider>
      </div>
    </el-dialog>
  </el-dialog>
</template>

<script>
import debounce from 'lodash/debounce'
import mixinViewModule from '@/mixins/view-module'

export default {
  mixins: [mixinViewModule],
  data() {
    return {
      visible: false,
      visible1: false,
      //创建该页面时，不调用数据查询接口
      mixinViewModuleOptions: {
        createdIsNeed: false,
      },
      dataForm: {
        id: '',
        partId: '',
        dataList: [],
        // subBatchId: '',
        // subBatchNumber: '',
        // inspectionType: '',
        // inspectionTime: '',
        // inspector: '',
        // inspectorName: '',
        // judgementResult: '',
        // processMode: '',
        // performance: '',
        // performancePoint: '',
        // amount: '',
        // defectQuantity: '',
        // remark: '',
        // disabled: '',
        // creator: '',
        // createDate: '',
        // updater: '',
        // updateDate: ''
      },
      subBatchIdList: [],
      inspectorOption:[],
      partOption: [],
      outCollectionOption: [],
      deptId:['1641701892159619073'],
      processModeType:[
        {
          value:'退货'
        },
        {
          value:'厂内二检'
        }
      ],
      inspectionTypeOption: [
        {
          label: '抽检',
          value: 0
        },
        {
          label: '全检',
          value: 1
        },
      ],
    }
  },
  computed: {
    dataRule() {
      return {
        id: [
          {required: true, message: this.$t('validate.required'), trigger: 'blur'}
        ],
        partId: [
          {required: true, message: this.$t('validate.required'), trigger: 'blur'}
        ],
        subBatchId: [
          {required: true, message: this.$t('validate.required'), trigger: 'blur'}
        ],
        subBatchNumber: [
          {required: true, message: this.$t('validate.required'), trigger: 'blur'}
        ],
        inspectionType: [
          {required: true, message: this.$t('validate.required'), trigger: 'blur'}
        ],
        inspectionTime: [
          {required: true, message: this.$t('validate.required'), trigger: 'blur'}
        ],
        inspector: [
          {required: true, message: this.$t('validate.required'), trigger: 'blur'}
        ],
        inspectorName: [
          {required: true, message: this.$t('validate.required'), trigger: 'blur'}
        ],
        judgementResult: [
          {required: true, message: this.$t('validate.required'), trigger: 'blur'}
        ],
        processMode: [
          {required: true, message: this.$t('validate.required'), trigger: 'blur'}
        ],
        performance: [
          {required: true, message: this.$t('validate.required'), trigger: 'blur'}
        ],
        performancePoint: [
          {required: true, message: this.$t('validate.required'), trigger: 'blur'}
        ],
        amount: [
          {required: true, message: this.$t('validate.required'), trigger: 'blur'}
        ],
        defectQuantity: [
          {required: true, message: this.$t('validate.required'), trigger: 'blur'}
        ],
        remark: [
          {required: true, message: this.$t('validate.required'), trigger: 'blur'}
        ],
      }
    }
  },
  methods: {
    init() {
      this.visible = true
      this.$nextTick(() => {
        this.dataForm = {
          partId: '',
          dataList: []
        }
        this.subBatchIdList = []
        this.getPartList()
        this.getInspectorOption()

      })
    },
    querySearch(queryString, cb) {
      // 调用 callback 返回建议列表的数据
      cb(this.processModeType);
    },
    //获取品管人员列表
    getInspectorOption(){
      this.$http.post('/sys/user/getUserListByDeptId',this.deptId).then(({data: res}) =>{
        if(res.code !== 0){
          return this.$message.error(res.msg)
        }
        this.inspectorOption = res.data.map((obj) => {
          return{
            label:obj.userCode + '(' + obj.username + ')',
            value:obj.id,
            data:obj
          }
        })
      })
    },
    //获取品号列表
    getPartList() {
      this.$http.get('fabricate/part/getPartListByDesignation').then(({data: res}) => {
        if (res.code !== 0) {
          return this.$message.error(res.msg)
        }
        this.partOption = res.data.map((obj) => {
          return {
            label: obj.designation,
            value: obj.id,
            data: obj
          }
        })
      })
    },
    //选取次批时做的动作
    fillDataList() {
     // 创建一个映射对象，以便快速查找subBatchOption中的项目
      const outCollectionOptionMap = new Map(this.outCollectionOption.map(item => [item.id, item]));
      // 使用subBatchIdList的顺序创建一个新的数组，只包含存在于subBatchOption中的项目
      this.dataForm.dataList = this.subBatchIdList
          .filter(id => outCollectionOptionMap.has(id)) // 确保subBatchOption中有对应的id
          .map(id => ({...outCollectionOptionMap.get(id), 'inspectionTime': '', 'inspector': '', 'inspectorName': '',
            'judgementResult': '','adverseItem':'' ,'processMode': '', 'remark': ''}));
      // 如果需要保留原始的this.dataForm.subBatchList（即不覆盖），可以使用concat或者push等方法添加新元素
    },
    //选取检查人员做的动作
    selectInspectorName(index){
      const filter = this.inspectorOption.filter(item => this.dataForm.dataList[index].inspector === item.value);
      if(filter.length === 0){
        this.dataForm.dataList[index].inspectorName = ''
      }else {
        this.dataForm.dataList[index].inspectorName = filter[0].label
      }
    },
    //选取品号时查询次批
    getSubBatchByPart() {
      if (this.dataForm.partId) {
        this.getExaminableBatch(this.dataForm.partId)
      } else {
        this.outCollectionOption = []
      }
    },
    //获取可检查批次列表
    getExaminableBatch(partId) {
      this.$http.get(`inspect/outcollection/getExaminableBatch?partId=${partId}`).then(({data: res}) => {
        if (res.code !== 0) {
          return this.$message.error(res.msg)
        }
        this.outCollectionOption = res.data
      })
    },
    // 获取信息
    getInfo() {
      this.$http.get(`/inspect/outinspectionrecords/${this.dataForm.id}`).then(({data: res}) => {
        if (res.code !== 0) {
          return this.$message.error(res.msg)
        }
        this.dataForm = {
          ...this.dataForm,
          ...res.data
        }
      }).catch(() => {
      })
    },
    // 表单提交
    dataFormSubmitHandle: debounce(function () {
      this.$refs['dataForm'].validate((valid) => {
        if (!valid) {
          return false
        }
        if(this.subBatchIdList.length === 0){
          return this.$message.error("请选取次批")
        }
        let a = this.dataForm.dataList.some(item =>
            item.inspectionTime === null || item.inspectionTime === undefined || item.inspectionTime === ''
        );
        if (a) {
          return this.$message.error("请填写所有检查日期");
        }
        let b = this.dataForm.dataList.some(item =>
            item.inspector === null || item.inspector === undefined || item.inspector === ''
        );
        if (b) {
          return this.$message.error("请填写所有检查人员");
        }
        let c = this.dataForm.dataList.some(item =>
            item.judgementResult === null || item.judgementResult === undefined || item.judgementResult === ''
        );
        if (c) {
          return this.$message.error("请填写所有判定结果");
        }
        let e = this.dataForm.dataList.some(item =>
            (item.judgementResult === 1) &&
            (item.processMode === null || item.processMode === undefined || item.processMode === '')
        );
        if (e) {
          return this.$message.error("请补全处理结果");
        }
        let message = '';
        if (this.dataForm.id) {
          message = '此操作将修改该数据'
        } else {
          message = '此操作将新增一条数据'
        }
        this.$confirm(message + ', 是否继续?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
          center: true
        }).then(() => {
          this.$http[!this.dataForm.id ? 'post' : 'put']('/inspect/outinspectionrecords/', this.dataForm).then(({data: res}) => {
            if (res.code !== 0) {
              return this.$message.error(res.msg)
            }
            this.$message({
              message: this.$t('prompt.success'),
              type: 'success',
              duration: 500,
              onClose: () => {
                this.visible = false
                this.$emit('refreshDataList')
              }
            })
          }).catch(() => {
          })
        }).catch(() => {
          this.$message({
            type: 'info',
            message: '已取消'
          });
        });
      })
    }, 1000, {'leading': true, 'trailing': false})
  }
}
</script>

<style scoped>
.select-font ::v-deep .el-input__inner {
  font-size: large;
}
</style>
