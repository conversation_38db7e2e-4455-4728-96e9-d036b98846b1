<template>
  <el-dialog :visible.sync="visible" :title="!dataForm.id ? $t('add') : $t('update')" :close-on-click-modal="false" :close-on-press-escape="false" append-to-body>
    <el-form :model="dataForm" :rules="dataRule" ref="dataForm" @keyup.enter.native="dataFormSubmitHandle()" :label-width="$i18n.locale === 'en-US' ? '120px' : '80px'">
      <div class="container">
        <el-form-item label="通知类型" prop="notifyType">
          <ren-select v-model="dataForm.notifyType" dict-type="notify_type" placeholder="通知类型 0:业务流程通知;1:直接通知;2:异常通知"></ren-select>
        </el-form-item>
        <el-form-item label="发送者" prop="sender">
          <employee-component v-model="dataForm.sender" placeholder="发送者"></employee-component>
        </el-form-item>
        <el-form-item label="接收者" prop="receiver">
          <employee-component v-model="dataForm.receiver" :default-value="false" placeholder="接收者"></employee-component>
        </el-form-item>
<!--        <el-form-item label="通知状态" prop="notifyStatus">
           <ren-select v-model="dataForm.notifyStatus" dict-type="notify_status" placeholder="通知状态 0:未读;1:已读;2:已处理"></ren-select>
        </el-form-item>-->
        <el-form-item v-if="dataForm.id" label="发送时间" prop="sendTime">
          <span class="font_size">
            {{dataForm.sendTime}}
          </span>
<!--          <el-input v-model="dataForm.sendTime" placeholder="发送时间"></el-input>-->
        </el-form-item>
        <el-form-item v-if="dataForm.id" label="读取时间" prop="readingTime">
          <span class="font_size">
            {{dataForm.readingTime}}
          </span>
<!--          <el-input v-model="dataForm.readingTime" placeholder="读取时间"></el-input>-->
        </el-form-item>
      </div>
      <el-form-item label="通知内容" prop="notifyContent">
        <el-input type="textarea" :autosize="{ minRows: 2, maxRows: 4}" v-model="dataForm.notifyContent" placeholder="通知内容"></el-input>
      </el-form-item>
    </el-form>
    <template slot="footer">
      <el-button @click="visible = false">{{ $t('cancel') }}</el-button>
      <el-button type="primary" @click="dataFormSubmitHandle()">{{ $t('confirm') }}</el-button>
    </template>
  </el-dialog>
</template>

<script>
import debounce from 'lodash/debounce'
import mixinViewModule from '@/mixins/view-module'
export default {
  mixins:[mixinViewModule],
  data () {
    return {
      visible: false,
      //创建该页面时，不调用数据查询接口
      mixinViewModuleOptions: {
        createdIsNeed: false,
      },
      dataForm: {
        id: '',
        notifyType: 0,
        sender: '',
        receiver: '',
        notifyContent: '',
        notifyStatus: 0,
        sendTime: '',
        readingTime: '',
        remark: '',
        disabled: '',
        creator: '',
        createDate: '',
        updater: '',
        updateDate: ''
      }
    }
  },
  computed: {
    dataRule () {
      return {
          notifyType: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          sender: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          receiver: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
          notifyContent: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
      }
    }
  },
  methods: {
    init () {
      this.visible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].resetFields()
        if (this.dataForm.id) {
          this.getInfo()
        }
      })
    },
    // 获取信息
    getInfo () {
      this.$http.get(`/notify/notify/${this.dataForm.id}`).then(({ data: res }) => {
        if (res.code !== 0) {
          return this.$message.error(res.msg)
        }
        this.dataForm = {
          ...this.dataForm,
          ...res.data
        }
      }).catch(() => {})
    },
    // 表单提交
    dataFormSubmitHandle: debounce(function () {
      this.$refs['dataForm'].validate((valid) => {
        if (!valid) {
          return false
        }
        let message = '';
        if(this.dataForm.id){
          message = '此操作将修改该数据'
        }else {
          message = '此操作将新增一条数据'
          this.dataForm.sendTime = this.formatTimes(new Date())
        }
        this.$confirm(message+', 是否继续?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
          center: true
        }).then(() => {
          this.$http[!this.dataForm.id ? 'post' : 'put']('/notify/notify/', this.dataForm).then(({ data: res }) => {
            if (res.code !== 0) {
            return this.$message.error(res.msg)
            }
            this.$message({
          message: this.$t('prompt.success'),
            type: 'success',
                    duration: 500,
                    onClose: () => {
              this.visible = false
              this.$emit('refreshDataList')
            }
          })
          }).catch(() => {})
        }).catch(() => {
          this.$message({
            type: 'info',
            message: '已取消'
          });
        });
      })
    }, 1000, { 'leading': true, 'trailing': false })
  }
}
</script>
