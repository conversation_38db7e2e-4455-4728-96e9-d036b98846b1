<template>
  <el-card shadow="never" class="aui-card--fill">
    <div class="mod-notify__notify}">
      <el-form :inline="true" :model="dataForm" @keyup.enter.native="getDataList()">
        <el-form-item>
          <el-tooltip content="请输入查询关键字" placement="top">
            <el-input v-model="dataForm.paramStr" placeholder="查询关键字" @input="throttleFunction" clearable>
              <el-button slot="append" icon="el-icon-search" @click="getDataList()"></el-button>
            </el-input>
          </el-tooltip>
        </el-form-item>
        <el-form-item>
          <el-button v-if="$hasPermission('notify:notify:save')" type="primary" @click="addOrUpdateHandle()">{{ $t('add') }}</el-button>
        </el-form-item>
        <el-dropdown  style="float:right;">
          <el-button type="primary">
            更多功能<i class="el-icon-arrow-down el-icon--right"></i>
          </el-button>
          <el-dropdown-menu slot="dropdown">
            <el-dropdown-item>
              <el-form-item>
                <el-button type="info" @click="exportHandle()">{{ $t('export') }}</el-button>
              </el-form-item>
            </el-dropdown-item>
            <el-dropdown-item>
              <el-form-item>
                <el-button v-if="$hasPermission('notify:notify:export')" type="info" @click="exportTemplateHandle()">
                  {{ $t('exportTemplate') }}
                </el-button>
              </el-form-item>
            </el-dropdown-item>
            <el-dropdown-item>
              <el-form-item>
                <el-tooltip content="下载模板填入信息后导入" placement="top">
                  <el-upload
                          v-if="$hasPermission('notify:notify:save')"
                          class="upload-demo"
                          :action="mixinViewModuleOptions.addBatchUrl"
                          :headers="headers"
                          :multiple="false"
                          :show-file-list="false"
                          :file-list="fileList"
                          :before-upload="beforeUpload"
                          :on-success="resultHandle"
                          :on-change="handleChange"
                          accept="'.xlsx','.xls'">
                    <el-button type="success">{{ $t('addBatch') }}</el-button>
                  </el-upload>
                </el-tooltip>
                <el-button type="text" v-show="unsuccessfulNameList" @click="dialogVisible = true">导入失败列表</el-button>
              </el-form-item>
            </el-dropdown-item>
            <el-dropdown-item>
              <el-form-item>
                <el-button v-if="$hasPermission('notify:notify:delete')" type="danger" @click="deleteHandle()">{{ $t('deleteBatch') }}</el-button>
              </el-form-item>
            </el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown>
      </el-form>
      <el-table v-loading="dataListLoading" height="500px" :data="dataList" border @selection-change="dataListSelectionChangeHandle" style="width: 100%;">
        <el-table-column type="selection" header-align="center" align="center" width="50"></el-table-column>
        <el-table-column prop="notifyType" label="通知类型" header-align="center" align="center">
          <template slot-scope="scope">
            <div>
              {{$getDictLabel("notify_type", scope.row.notifyType)}}
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="sender" label="发送者" header-align="center" align="center">
          <template slot-scope="scope">
            {{$getEmployeesList(scope.row.sender)}}
          </template>
        </el-table-column>
        <el-table-column prop="receiver" label="接收者" header-align="center" align="center">
          <template slot-scope="scope">
            {{$getEmployeesList(scope.row.receiver)}}
          </template>
        </el-table-column>
        <el-table-column prop="notifyContent" show-overflow-tooltip label="通知内容" header-align="center" align="center">
          <template slot-scope="scope">
            <div @click="viewMessages(scope.row.id)">
              {{scope.row.notifyContent}}
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="notifyStatus" label="通知状态" header-align="center" align="center">
          <template slot-scope="scope">
            <div>
              {{$getDictLabel("notify_status", scope.row.notifyStatus)}}
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="sendTime" label="发送时间" header-align="center" align="center"></el-table-column>
        <el-table-column prop="readingTime" label="读取时间" header-align="center" align="center"></el-table-column>
        <el-table-column prop="remark" label="备注" header-align="center" align="center"></el-table-column>
        <el-table-column :label="$t('handle')" fixed="right" header-align="center" align="center" width="150">
          <template slot-scope="scope">
            <el-dropdown>
              <el-button type="primary">
                功能<i class="el-icon-arrow-down el-icon--right"></i>
              </el-button>
              <el-dropdown-menu slot="dropdown">
                <el-dropdown-item>
                  <el-button v-if="$hasPermission('notify:notify:update')" type="text" size="small" @click="addOrUpdateHandle(scope.row.id)">{{ $t('update') }}</el-button>
                </el-dropdown-item>
                <el-dropdown-item>
                  <el-button v-if="$hasPermission('notify:notify:delete')" type="text" size="small" @click="deleteHandle(scope.row.id)">{{ $t('delete') }}</el-button>
                </el-dropdown-item>
              </el-dropdown-menu>
            </el-dropdown>
          </template>
        </el-table-column>
      </el-table>
      <el-pagination
        :current-page="page"
        :page-sizes="[10, 20, 50, 100]"
        :page-size="limit"
        :total="total"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="pageSizeChangeHandle"
        @current-change="pageCurrentChangeHandle">
      </el-pagination>
      <!-- 弹窗, 新增 / 修改 -->
      <add-or-update v-if="addOrUpdateVisible" ref="addOrUpdate" @refreshDataList="getDataList"></add-or-update>
      <notify-read-only v-if="notifyReadOnlyVisible" ref="notifyReadOnly"></notify-read-only>
    </div>
  </el-card>
</template>


<script>
import mixinViewModule from '@/mixins/view-module'
import AddOrUpdate from './notify-add-or-update'
import Cookies from "js-cookie";
import {EventSourcePolyfill } from 'event-source-polyfill';
import notifyReadOnly from "./notify-read-only.vue";

export default {
  mixins: [mixinViewModule],
  data () {
    return {
      message:'',
      notifyReadOnlyVisible:false,
      headers: {'token': Cookies.get('token')},
      fileList: [{name: '', url: ''}],
      unsuccessfulNameList: '',//  导入失败名称列表
      mixinViewModuleOptions: {
        getDataListURL: '/notify/notify/page',
        getDataListIsPage: true,
        exportURL: '/notify/notify/export',
        deleteURL: '/notify/notify',
        deleteIsBatch: true,
        exportTemplateURL: '/notify/notify/export/template',
        addBatchUrl: window.SITE_CONFIG['apiURL'] + '/notify/notify/batchSave',
      },
      dataForm: {
        id: '',
        paramStr: '',
        receiver: window.SITE_CONFIG['users'].id
      },
      // 判断是否还在继续输入
      timer: null,
      eventSource: null,
    }
  },
  components: {
    notifyReadOnly,
    AddOrUpdate
  },
  mounted() {
    this.createSSE()
  },
  beforeDestroy() {
    if(this.eventSource){
      const userId =  String(window.SITE_CONFIG['users'].id)
      // 关闭SSE
      this.eventSource.close();
      // 通知后端关闭连接
      this.sseClose(userId)
      this.eventSource = null
      console.log("退出登录或关闭浏览器，关闭SSE连接~")
    }
  },
  methods:{
    createSSE(){
      if(window.EventSource){
        // 根据环境的不同，变更url
        const url = window.SITE_CONFIG["apiURL"]
        // 用户userId
        const userId =  String(window.SITE_CONFIG['users'].id)
        this.eventSource = new EventSourcePolyfill(
            `${url}/sse/subscribe/${userId}`, {
              // 设置重连时间
              heartbeatTimeout: 60 * 60 * 1000,
              // 添加token
              headers: {
                'token': `${Cookies.get('token')}`,
              },
            });
        this.eventSource.onopen = (e) => {
          console.log("已建立SSE连接~")
        }
        this.eventSource.onmessage = (e) => {
          console.log(e)
          this.massages(e.lastEventId)
          /*this.whetherWhere(e.lastEventId)*/
          console.log("已接受到消息:", e.data)
        }
        this.eventSource.onerror = (e) => {
          if (e.readyState == EventSource.CLOSED) {
            console.log("SSE连接关闭");
          } else if (this.eventSource.readyState == EventSource.CONNECTING) {
            console.log("SSE正在重连");
            //重新设置token
            this.eventSource.headers = {
              'token': `${Cookies.get('token')}`
            };
          } else {
            console.log('error', e);
          }
        };
      } else {
        console.log("你的浏览器不支持SSE~")
      }
    },
    whetherWhere(id){
      console.log(this.$route.path,'我的位置')
      // 判断用户是否在通知查看页面中
      if (this.$route.path === '/notify-notify') {
        console.log('在通知页面中')
        this.messageRead(id)
      }
    },
    // 消息已读
    messageRead(id){
      this.$http['post'](`/notify/notify/readNotify/${id}` ).then(({ data: res }) => {
        if (res.code !== 0) {
          return this.$message.error(res.msg)
        }
        this.getDataList()
      }).catch(() => {})
    },
    // 消息提示
    massages(id){
      console.log(id,'消息id')
      const h = this.$createElement;
      let notification = this.$notify.info({
        title: '通知',
        message:  h('i', { style: 'color: red'},'您有一条消息，请尽快查看!'),
        duration: 0,
        offset: 100,
        onClick: () => {
          this.viewMessages(id)
          notification.close()
        }
      });
    },
    // 查看消息
    viewMessages(id){
      this.notifyReadOnlyVisible = true
      this.$nextTick(()=>{
        this.$refs.notifyReadOnly.dataForm.id = id
        this.$refs.notifyReadOnly.init()
      })
      this.messageRead(id)
      /*this.$router.replace({ path: '/notify-notify'})*/
    },
    sseClose() {
      const url = window.SITE_CONFIG["apiURL"]
      // 用户userId
      const userId =  String(window.SITE_CONFIG['users'].id)
      this.eventSource.close();
      this.$http.get(`/sse/close/${userId}`).then(({ data: res }) => {
        if (res.code !== 0) {
          return this.$message.error(res.msg)
        }
        this.dataForm = {
          ...this.dataForm,
          ...res.data
        }
      }).catch(() => {})
      console.log("close");
    },
    //节流函数
    throttleFunction() {
      // 如果timer不为空就表明在持续输入,每次改变都清除定时，这样就可以实现只输入最后一次结果(注意每次间隔如果超过500毫秒就会输出)
      if (this.timer !== null) clearTimeout(this.timer)
      this.timer = setTimeout(() => {
        this.getDataList()
      }, 500)
    },
    beforeUpload(file) {
      this.message = this.$message({
        message: '上传中，请勿操作',
        center: true,
        duration: 0,
      });
    },
    handleChange(file, fileList) {
      this.fileList = fileList.slice(-3);

    },
    resultHandle(res) {
      if (res.code !== 0) {
      return this.$message.error(res.msg)
      }
      this.$message({
        message: this.$t('prompt.success'),
        type: 'success',
        duration: 500,
        onClose: () => {
          if (res.data) {
            this.unsuccessfulNameList = res.data
          }
          this.visible = false
          this.query()
        }
      })
    },
  }
}
</script>
