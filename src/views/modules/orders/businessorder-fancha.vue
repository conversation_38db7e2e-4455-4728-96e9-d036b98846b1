<template>
  <el-card shadow="never" class="aui-card--fill">
    <div class="mod-orders__businessorder__fancha}">
      <el-form :inline="true" :model="dataForm" @keyup.enter.native="getDataList()">
        <div class="container">
          <el-form-item label="营业制交单号：">
            <el-input v-model="dataMap.trackingNumber" placeholder="请输入营业制交单号" clearable></el-input>
          </el-form-item>
          <el-form-item label="客户代码：">
            <customer-component v-model="dataForm.customerId" placeholder="客户查询"></customer-component>
          </el-form-item>
          <el-form-item label="客户订单号：">
            <el-input v-model="dataForm.orderNumber" placeholder="客户订单号" clearable></el-input>
          </el-form-item>
          <el-form-item label="下单日期：">
            <el-date-picker
                v-model="dataForm.orderDate"
                type="date"
                format="yyyy 年 MM 月 dd 日"
                value-format="yyyy-MM-dd"
                placeholder="订单/预示日期">
            </el-date-picker>
          </el-form-item>
        </div>
        <div class="container">
          <el-form-item>
            <el-checkbox v-model="dataForm.checked">显示所有未番查订单</el-checkbox>
          </el-form-item>
        </div>
        <div class="container">
          <el-form-item label="作成：">
            <employee-component v-model="dataForm.make"></employee-component>
          </el-form-item>
<!--          <el-form-item label="建立本单：">
            <el-date-picker
                v-model="dataMap.createDate"
                type="date"
                format="yyyy 年 MM 月 dd 日"
                value-format="yyyy-MM-dd"
                placeholder="订单/预示日期">
            </el-date-picker>
          </el-form-item>-->
        </div>
      </el-form>
      <el-table v-loading="dataListLoading" height="400px"  ref="multipleTable" :data="dataList" border
                @selection-change="dataListSelectionChangeHandle" style="width: 100%;">
        <el-table-column type="selection" header-align="center" align="center" width="50"></el-table-column>
        <el-table-column prop="orderType" label="订单类型" header-align="center" align="center">
          <template slot-scope="scope">
            {{ $getDictLabel("business_order_type", scope.row.orderType) }}
          </template>
        </el-table-column>
<!--        <el-table-column prop="partNameCode" label="品名代码" header-align="center" align="center"></el-table-column>-->
        <el-table-column prop="customerCode" label="客户代码" header-align="center" align="center"></el-table-column>
        <el-table-column prop="designation" label="部品番号" header-align="center" align="center"></el-table-column>
        <el-table-column prop="materialQuality" label="材质" header-align="center" align="center"></el-table-column>
        <el-table-column prop="quantity" label="交制数量" header-align="center" align="center"></el-table-column>
        <el-table-column prop="deliveryDate" label="出货日期" header-align="center" align="center"></el-table-column>
        <el-table-column prop="tradingLocations" label="出货地点" header-align="center" align="center"></el-table-column>
        <el-table-column prop="summaryDescription" label="摘要说明" header-align="center" align="center"></el-table-column>
        <el-table-column prop="confirm" label="会签" header-align="center" align="center"></el-table-column>
        <el-table-column :label="$t('handle')" fixed="right" header-align="center" align="center" width="150">
          <template slot-scope="scope">
            <el-dropdown>
              <el-button type="primary">
                功能<i class="el-icon-arrow-down el-icon--right"></i>
              </el-button>
              <el-dropdown-menu slot="dropdown">
                <el-dropdown-item>
                  <el-button v-if="$hasPermission('orders:businessorder:update')" type="text" size="small"
                             @click="addOrUpdateHandle(scope.row.id)">{{ $t('update') }}
                  </el-button>
                </el-dropdown-item>
                <el-dropdown-item>
                  <el-button v-if="$hasPermission('orders:businessorder:delete')" type="text" size="small"
                             @click="deleteHandle(scope.row.id)">{{ $t('delete') }}
                  </el-button>
                </el-dropdown-item>
              </el-dropdown-menu>
            </el-dropdown>
          </template>
        </el-table-column>
      </el-table>
      <div class="container" STYLE="margin-top: 50px">
        <div class="font_size" style="margin-left: 10%">
          以上共选
          <span style="color: #409EFF;">{{count}}</span>
          项
        </div>
        <div style="margin-left: 30%">
          <el-button type="primary" @click="dataFormSubmitHandle()" round>确定生成订单番查</el-button>
        </div>
        <div style="margin-left: 30%">
          <el-button type="danger" @click="cancel()" round>取消</el-button>
        </div>
      </div>
      <!-- 弹窗, 新增 / 修改 -->
      <add-or-update v-if="addOrUpdateVisible" ref="addOrUpdate" @refreshDataList="getDataList"></add-or-update>
    </div>
  </el-card>
</template>

<script>
import mixinViewModule from '@/mixins/view-module'
import AddOrUpdate from './businessorder-add-or-update'
import Cookies from "js-cookie";
import debounce from "lodash/debounce";

export default {
  mixins: [mixinViewModule],
  data() {
    return {
      message: '',
      headers: {'token': Cookies.get('token')},
      fileList: [{name: '', url: ''}],
      unsuccessfulNameList: '',//  导入失败名称列表
      mixinViewModuleOptions: {
        getDataListURL: '/orders/businessorder/page',
        getDataListIsPage: true,
        exportURL: '/orders/businessorder/export',
        deleteURL: '/orders/businessorder',
        deleteIsBatch: true,
        exportTemplateURL: '/orders/businessorder/export/template',
        addBatchUrl: window.SITE_CONFIG['apiURL'] + '/orders/businessorder/batchSave',
      },
      dataForm: {
        customerId: '',
        orderNumber: '',
        orderDate: '',
        checked: true,
        /*createDate: this.formatDates(),*/
        make: '',
      },
      dataMap:{
        trackingNumber: this.$route.params.trackingNumber,
        dataFromList:[]
      },
      // 判断是否还在继续输入
      timer: null,
      count: 0,
      businessOrderList: [],
    }
  },
  components: {
    AddOrUpdate
  },
  watch:{
    dataForm:{
      handler(newVal,oldVal){
        this.getDataList()
      },
      deep:true
    }
  },
  methods: {
    // 多选
    dataListSelectionChangeHandle(val) {
      this.businessOrderList = val
      this.count = val.length
      for (let i = 0; i < this.businessOrderList.length; i++) {
        this.businessOrderList[i].trackingNumber = this.dataMap.trackingNumber
      }
      console.log(this.count,'总数')
      console.log(this.businessOrderList,'数据')
    },
    addOrUpdateHandle(id) {
      this.addOrUpdateVisible = true
      this.$nextTick(() => {
        this.$refs.addOrUpdate.dataForm.id = id
        if (id) {
          this.$refs.addOrUpdate.showHoldOrder = true
          this.$refs.addOrUpdate.checkOrder = false
        }
        this.$refs.addOrUpdate.init()
      })
    },
    // 取消
    cancel(){
      this.businessOrderList = []
      this.count = 0
      this.$refs.multipleTable.clearSelection();
    },
    // 表单提交
    dataFormSubmitHandle: debounce(function () {
      this.$confirm('此操作将生成订单审查, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
        center: true
      }).then(() => {
        this.$http['post']('/orders/businessorderreview', this.businessOrderList).then(({ data: res }) => {
          if (res.code !== 0) {
            return this.$message.error(res.msg)
          }
          this.$message({
            message: this.$t('prompt.success'),
            type: 'success',
            duration: 500,
            onClose: () => {
              this.businessOrderList = []
              this.count = 0
              this.getDataList()
              this.$router.push({name:'orders-businessorderreview'})
            }
          })
        }).catch(() => {})
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '已取消'
        });
      });
    }, 1000, { 'leading': true, 'trailing': false }),
    //节流函数
    throttleFunction() {
      // 如果timer不为空就表明在持续输入,每次改变都清除定时，这样就可以实现只输入最后一次结果(注意每次间隔如果超过500毫秒就会输出)
      if (this.timer !== null) clearTimeout(this.timer)
      this.timer = setTimeout(() => {
        this.getDataList()
      }, 500)
    },
    beforeUpload(file) {
      this.message = this.$message({
        message: '上传中，请勿操作',
        center: true,
        duration: 0,
      });
    },
    handleChange(file, fileList) {
      this.fileList = fileList.slice(-3);
    },
    resultHandle(res) {
      if (res.code !== 0) {
        return this.$message.error(res.msg)
      }
      this.$message({
        message: this.$t('prompt.success'),
        type: 'success',
        duration: 500,
        onClose: () => {
          if (res.data) {
            this.unsuccessfulNameList = res.data
          }
          this.visible = false
          this.query()
        }
      })
    },
  }
}
</script>
