<template>
  <el-dialog :visible.sync="visible" :title="!dataForm.id ? '新增订单审查' : '修改订单审查'" :close-on-click-modal="false"
             :close-on-press-escape="false" append-to-body>
    <el-form :model="dataForm" :rules="dataRule" ref="dataForm" @keyup.enter.native="dataFormSubmitHandle()"
             :label-width="$i18n.locale === 'en-US' ? '120px' : '80px'">
      <div class="container">
        <el-form-item label="客户代码" prop="customerId">
          <customer-component v-model="dataForm.customerId" placeholder="客户代码"></customer-component>
        </el-form-item>
        <el-form-item label="部品品番" prop="partId">
          <part-number-component v-model="dataForm.partId" placeholder="部品品番"></part-number-component>
        </el-form-item>
        <el-form-item label="客户订单" prop="businessOrderId">
          <business-order-component v-model="dataForm.businessOrderId"
                                    @orderData="orderData"
                                    placeholder="客户订单"></business-order-component>
        </el-form-item>
        <el-form-item label="制交单号" prop="trackingNumber">
          <el-input v-model="dataForm.trackingNumber" placeholder="制交单号"></el-input>
        </el-form-item>
        <el-form-item label="材质" prop="materialQuality">
          <el-input v-model="dataForm.materialQuality" placeholder="材质"></el-input>
        </el-form-item>
        <el-form-item label="孔数" prop="numberOfHoles">
          <el-input v-model="dataForm.numberOfHoles" placeholder="孔数"></el-input>
        </el-form-item>
        <el-form-item label="生产数量" prop="quantity">
          <el-input-number :controls="false" v-model="dataForm.quantity" placeholder="生产数量"></el-input-number>
        </el-form-item>
        <el-form-item label="出货日期" prop="shippingDate">
          <el-date-picker
              v-model="dataForm.deliveryDate"
              type="date"
              format="yyyy 年 MM 月 dd 日"
              value-format="yyyy-MM-dd"
              placeholder="出货日期">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="生产地点" prop="originOfProduction">
          <el-input v-model="dataForm.originOfProduction" placeholder="生产地点"></el-input>
        </el-form-item>
        <el-form-item label="库存状态" prop="stockStatus">
          <ren-select v-model="dataForm.stockStatus" placeholder="库存状态" dict-type="stock_status"></ren-select>
        </el-form-item>
        <el-form-item label="包装规定" prop="packagingRegulations">
          <el-input v-model="dataForm.packagingRegulations" placeholder="包装规定"></el-input>
        </el-form-item>
        <el-form-item label="订单类型" prop="orderType">
          <ren-select v-model="dataForm.orderType" placeholder="订单类型" dict-type="business_order_type"></ren-select>
        </el-form-item>
        <el-form-item label="生产确认" prop="productionConfirm">
          <ren-select v-model="dataForm.productionConfirm" placeholder="生产确认"
                      dict-type="production_confirm"></ren-select>
        </el-form-item>
      </div>
      <el-form-item label="备注" prop="remark">
        <el-input v-model="dataForm.remark" placeholder="备注"></el-input>
      </el-form-item>
    </el-form>
    <template slot="footer">
      <el-button @click="visible = false">{{ $t('cancel') }}</el-button>
      <el-button type="primary" @click="dataFormSubmitHandle()">{{ $t('confirm') }}</el-button>
    </template>
  </el-dialog>
</template>

<script>
import debounce from 'lodash/debounce'
import mixinViewModule from '@/mixins/view-module'

export default {
  mixins: [mixinViewModule],
  data() {
    return {
      visible: false,
      //创建该页面时，不调用数据查询接口
      mixinViewModuleOptions: {
        createdIsNeed: false,
      },
      dataForm: {
        id: '',
        customerId: '',
        partId: '',
        businessOrderId: '',
        trackingNumber: '',
        materialQuality: '',
        numberOfHoles: '',
        quantity: '',
        shippingDate: '',
        originOfProduction: '',
        stockStatus: '',
        packagingRegulations: '',
        orderType: '',
        productionConfirm: 0,
        remark: '',
        disabled: '',
        creator: '',
        createDate: '',
        updater: '',
        updateDate: ''
      }
    }
  },
  computed: {
    dataRule() {
      return {
        customerId: [
          {required: true, message: this.$t('validate.required'), trigger: 'blur'}
        ],
        partId: [
          {required: true, message: this.$t('validate.required'), trigger: 'blur'}
        ],
        businessOrderId: [
          {required: true, message: this.$t('validate.required'), trigger: 'blur'}
        ],
        trackingNumber: [
          {required: true, message: this.$t('validate.required'), trigger: 'blur'}
        ],
        materialQuality: [
          {required: true, message: this.$t('validate.required'), trigger: 'blur'}
        ],
        quantity: [
          {required: true, message: this.$t('validate.required'), trigger: 'blur'}
        ],
        stockStatus: [
          {required: true, message: this.$t('validate.required'), trigger: 'blur'}
        ],
        packagingRegulations: [
          {required: true, message: this.$t('validate.required'), trigger: 'blur'}
        ],
        orderType: [
          {required: true, message: this.$t('validate.required'), trigger: 'blur'}
        ],
        productionConfirm: [
          {required: true, message: this.$t('validate.required'), trigger: 'blur'}
        ],
      }
    }
  },
  methods: {
    orderData(data){
      this.dataForm.orderType = data.orderType
    },
    init() {
      this.visible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].resetFields()
        if (this.dataForm.id) {
          this.getInfo()
        }
      })
    },
    // 获取信息
    getInfo() {
      this.$http.get(`/orders/businessorderreview/${this.dataForm.id}`).then(({data: res}) => {
        if (res.code !== 0) {
          return this.$message.error(res.msg)
        }
        this.dataForm = {
          ...this.dataForm,
          ...res.data
        }
      }).catch(() => {
      })
    },
    // 表单提交
    dataFormSubmitHandle: debounce(function () {
      this.$refs['dataForm'].validate((valid) => {
        if (!valid) {
          return false
        }
        let message = '';
        if (this.dataForm.id) {
          message = '此操作将修改该数据'
        } else {
          message = '此操作将新增一条数据'
        }
        this.$confirm(message + ', 是否继续?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
          center: true
        }).then(() => {
          this.$http[!this.dataForm.id ? 'post' : 'put']('/orders/businessorderreview/', this.dataForm).then(({data: res}) => {
            if (res.code !== 0) {
              return this.$message.error(res.msg)
            }
            this.$message({
              message: this.$t('prompt.success'),
              type: 'success',
              duration: 500,
              onClose: () => {
                this.visible = false
                this.$emit('refreshDataList')
              }
            })
          }).catch(() => {
          })
        }).catch(() => {
          this.$message({
            type: 'info',
            message: '已取消'
          });
        });
      })
    }, 1000, {'leading': true, 'trailing': false})
  }
}
</script>
