<template>
  <el-card shadow="never" class="aui-card--fill">
    <div class="mod-orders__ordersummarycontrol}">
      <el-form :inline="true" :model="dataForm" @keyup.enter.native="getDataList()">
        <el-form-item>
          <el-button type="primary" @click="queryFunction()">
            查询功能
          </el-button>
        </el-form-item>
        <el-form-item>
          <el-tooltip content="请输入查询关键字" placement="top">
            <el-input v-model="dataForm.designation" placeholder="查询关键字" @input="throttleFunction" clearable>
              <el-button slot="append" icon="el-icon-search" @click="getDataList()"></el-button>
            </el-input>
          </el-tooltip>
        </el-form-item>
        <el-form-item>
<!--          <el-button v-if="$hasPermission('orders:ordersummarycontrol:save')" type="primary" @click="addOrUpdateHandle()">{{ $t('add') }}</el-button>-->
        </el-form-item>
        <el-dropdown  style="float:right;">
          <el-button type="primary">
            更多功能<i class="el-icon-arrow-down el-icon--right"></i>
          </el-button>
          <el-dropdown-menu slot="dropdown">
            <el-dropdown-item>
              <el-form-item>
                <el-button type="info" @click="exportHandle()">{{ $t('export') }}</el-button>
              </el-form-item>
              <el-form-item>
                <el-button type="info" @click="exportHandleDay">日报</el-button>
              </el-form-item>
              <el-form-item>
                <el-button type="info" @click="exportHandleWeek()">周报</el-button>
              </el-form-item>
              <el-form-item>
                <el-button type="info" @click="exportHandleMonth()">月报</el-button>
              </el-form-item>
              <el-form-item>
                <el-button type="info" @click="exportHandleQuarter()">季报</el-button>
              </el-form-item>
              <el-form-item>
                <el-button type="info" @click="exportHandleYear()">年报</el-button>
              </el-form-item>
            </el-dropdown-item>
            <el-dropdown-item>
              <el-form-item>
                <el-button v-if="$hasPermission('orders:ordersummarycontrol:export')" type="info" @click="exportTemplateHandle()">
                  {{ $t('exportTemplate') }}
                </el-button>
              </el-form-item>
            </el-dropdown-item>
            <el-dropdown-item>
              <el-form-item>
                <el-button v-if="$hasPermission('orders:ordersummarycontrol:delete')" type="danger" @click="deleteHandle()">{{ $t('deleteBatch') }}</el-button>
              </el-form-item>
            </el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown>
      </el-form>
      <el-table v-loading="dataListLoading" height="500px" :data="dataList" border @selection-change="dataListSelectionChangeHandle" style="width: 100%;">
        <el-table-column type="selection" header-align="center" align="center" width="50"></el-table-column>
<!--        <el-table-column prop="productName" label="品番编号" header-align="center" align="center"></el-table-column>-->
        <el-table-column prop="partCode" label="品番编号" header-align="center" align="center"></el-table-column>
        <el-table-column prop="customerCode" label="客户代码" header-align="center" align="center"></el-table-column>
<!--        <el-table-column prop="designation" label="客户品名" header-align="center" align="center"></el-table-column>-->
<!--        <el-table-column prop="productName" label="客户品名" header-align="center" align="center"></el-table-column>-->
        <el-table-column prop="orderQuantity" label="订单累计数" header-align="center" align="center"></el-table-column>
        <el-table-column prop="predictQuantity" label="预示订单累计数" header-align="center" align="center"></el-table-column>
        <el-table-column prop="shipmentQuantity" label="出货累计数" header-align="center" align="center"></el-table-column>
        <el-table-column :label="$t('handle')" fixed="right" header-align="center" align="center" width="150">
          <template slot-scope="scope">
            <el-dropdown>
              <el-button type="primary">
                功能<i class="el-icon-arrow-down el-icon--right"></i>
              </el-button>
              <el-dropdown-menu slot="dropdown">
                <el-dropdown-item>
                  <el-button v-if="$hasPermission('orders:ordersummarycontrol:update')" type="text" size="small" @click="addOrUpdateHandle(scope.row.id)">{{ $t('update') }}</el-button>
                </el-dropdown-item>
                <el-dropdown-item>
                  <el-button v-if="$hasPermission('orders:ordersummarycontrol:delete')" type="text" size="small" @click="deleteHandle(scope.row.id)">{{ $t('delete') }}</el-button>
                </el-dropdown-item>
              </el-dropdown-menu>
            </el-dropdown>
          </template>
        </el-table-column>
      </el-table>
      <el-pagination
        :current-page="page"
        :page-sizes="[10, 20, 50, 100]"
        :page-size="limit"
        :total="total"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="pageSizeChangeHandle"
        @current-change="pageCurrentChangeHandle">
      </el-pagination>
      <!-- 弹窗, 新增 / 修改 -->
      <add-or-update v-if="addOrUpdateVisible" ref="addOrUpdate" @refreshDataList="getDataList"></add-or-update>
      <order-tracking v-if="orderTrackingVisible" ref="orderTracking" @inquire="inquire"></order-tracking>
    </div>

    <dialog class="dialog" v-if="dayDisplay">
      <div v-if="dayDisplay" class="block">
        <span class="demonstration">默认</span>
        <el-date-picker
            v-model="value1"
            type="date"
            placeholder="选择日期"
            @click="handleClick"
        >
        </el-date-picker>
      </div>
      <el-button @click="dayFunction">确定</el-button>
    </dialog>
    <dialog class="dialog" v-if="weekDisplay">
      <div v-if="weekDisplay" class="block">
        <span class="demonstration">默认</span>
        <el-date-picker
            v-model="week"
            type="week"
            format="yyyy 第 WW 周"
            placeholder="选择周">
        </el-date-picker>
      </div>
      <el-button @click="weekFunction">确定</el-button>
    </dialog>
    <dialog class="dialog" v-if="monthDisplay">
      <div v-if="monthDisplay" class="block">
        <span class="demonstration">默认</span>
        <el-date-picker
            v-model="month"
            type="month"

            placeholder="选择月">
        </el-date-picker>
      </div>
      <el-button @click="monthFunction">确定</el-button>
    </dialog>
    <dialog class="dialog" v-if="quarterDisplay">
      <div v-if="quarterDisplay" class="block">
        <span class="demonstration">默认</span>
<!--        <el-date-picker
            v-model="quarter"
            type="quarter"

            placeholder="选择季度">
        </el-date-picker>-->
        <QuarterPicker
            type="quarter"
            :date="quarter"
            :min="minDate"
            :max="maxDate"
            :featureDisabled="options.featureDisabled"
            :todayDisabled="options.todayDisabled"
            :disabled="options.disabled"
            :clearable="options.clearable"
            @change="handleQuarterPickerChange"
        />
      </div>
      <el-button @click="quarterFunction">确定</el-button>
    </dialog>
    <dialog class="dialog" v-if="yearDisplay">
      <div v-if="yearDisplay" class="block">
        <span class="demonstration">默认</span>
        <el-date-picker
            v-model="year"
            type="year"

            placeholder="选择年">
        </el-date-picker>
      </div>
      <el-button @click="yearFunction">确定</el-button>
    </dialog>

  </el-card>

</template>

<script>
import mixinViewModule from '@/mixins/view-module'
import AddOrUpdate from './ordersummarycontrol-add-or-update'
import Cookies from "js-cookie";
import qs from "qs";
import QuarterPicker from "../../../components/quarterPicker.vue";
import OrderTracking from "./order-tracking.vue";
export default {
  mixins: [mixinViewModule],
  data () {
    return {
      message:'',
      headers: {'token': Cookies.get('token')},
      fileList: [{name: '', url: ''}],
      unsuccessfulNameList: '',//  导入失败名称列表
      mixinViewModuleOptions: {
        getDataListURL: '/orders/ordersummarycontrol/page',
        getDataListIsPage: true,
        exportURL: '/orders/ordersummarycontrol/export',
        exportURLDay: '/orders/ordersummarycontrol/exportDay',
        exportURLWeek: '/orders/ordersummarycontrol/exportWeek',
        exportURLMonth: '/orders/ordersummarycontrol/exportMonth',
        exportURLQuarter: '/orders/ordersummarycontrol/exportQuarter',
        exportURLYear: '/orders/ordersummarycontrol/exportYear',
        deleteURL: '/orders/ordersummarycontrol',
        deleteIsBatch: true,
        exportTemplateURL: '/orders/ordersummarycontrol/export/template',
      },
      dataForm: {
        id: '',
        designation:'',
        paramStr: '',
        date:'',
      },
      // 判断是否还在继续输入
      timer: null,
      dayDisplay:false,
      weekDisplay:false,
      monthDisplay:false,
      quarterDisplay:false,
      yearDisplay:false,
      value1:'',
      week:'',
      month:'',
      quarter:'',
      year:'',
      minDate:'',
      maxDate:'',
      options: {

      },
      orderTrackingVisible: false,
    }
  },
  components: {
    QuarterPicker,
    AddOrUpdate,
    OrderTracking
  },
  methods:{
    inquire(data) {
      this.dataForm = {
        ...this.dataForm,
        ...data
      }
      this.getDataList()
    },
    queryFunction() {
      this.orderTrackingVisible = true;
      this.$nextTick(() => {
        this.$refs.orderTracking.init();
      })
    },
    handleQuarterPickerChange({ date }) {
      this.quarter = date
console.log("--------"+date)
      this.$message({
        message: '触发查询请求',
        type: 'success',
      })
    },
    dayFunction(){
      this.dayDisplay=false
      const date = new Date(this.value1);
      date.setDate(date.getDate() + 1);
      this.dataForm.date=date
      console.log("========="+this.value1)
      var params = qs.stringify({
        'token': Cookies.get('token'),
        ...this.dataForm
      })
      window.location.href = `${window.SITE_CONFIG['apiURL']}${this.mixinViewModuleOptions.exportURLDay}?${params}`
    },
    weekFunction(){
      this.weekDisplay=false
      this.dataForm.date=this.week
      console.log("====week====="+this.week)
      var params = qs.stringify({
        'token': Cookies.get('token'),
        ...this.dataForm
      })
      window.location.href = `${window.SITE_CONFIG['apiURL']}${this.mixinViewModuleOptions.exportURLWeek}?${params}`
    },
    monthFunction(){
      this.monthDisplay=false
      this.dataForm.date=this.month
      console.log("====month====="+this.month)
      var params = qs.stringify({
        'token': Cookies.get('token'),
        ...this.dataForm
      })
      window.location.href = `${window.SITE_CONFIG['apiURL']}${this.mixinViewModuleOptions.exportURLMonth}?${params}`
    },
    quarterFunction(){
      this.quarterDisplay=false
      this.dataForm.date=this.quarter
      console.log("====quarter====="+this.quarter)
      var params = qs.stringify({
        'token': Cookies.get('token'),
        ...this.dataForm
      })
      window.location.href = `${window.SITE_CONFIG['apiURL']}${this.mixinViewModuleOptions.exportURLQuarter}?${params}`
    },
    yearFunction(){
      this.yearDisplay=false
      this.dataForm.date=this.year
      console.log("====year====="+this.year)
      var params = qs.stringify({
        'token': Cookies.get('token'),
        ...this.dataForm
      })
      window.location.href = `${window.SITE_CONFIG['apiURL']}${this.mixinViewModuleOptions.exportURLYear}?${params}`
    },
    handleClick() {
      this.dateDisplay=false
      console.log('Date picker clicked!');
      // 在这里处理点击事件
    },
    exportHandleDay(){
      this.dayDisplay = true
      /*var params = qs.stringify({
        'token': Cookies.get('token'),
        ...this.dataForm
      })
      window.location.href = `${window.SITE_CONFIG['apiURL']}${this.mixinViewModuleOptions.exportURL}?${params}`*/
    },
    exportHandleWeek(){
      this.weekDisplay = true
      /*var params = qs.stringify({
        'token': Cookies.get('token'),
        ...this.dataForm
      })
      window.location.href = `${window.SITE_CONFIG['apiURL']}${this.mixinViewModuleOptions.exportURL}?${params}`*/
    },
    exportHandleMonth(){
      this.monthDisplay = true
      /*var params = qs.stringify({
        'token': Cookies.get('token'),
        ...this.dataForm
      })
      window.location.href = `${window.SITE_CONFIG['apiURL']}${this.mixinViewModuleOptions.exportURL}?${params}`*/
    },
    exportHandleQuarter(){
      this.quarterDisplay = true
      /*var params = qs.stringify({
        'token': Cookies.get('token'),
        ...this.dataForm
      })
      window.location.href = `${window.SITE_CONFIG['apiURL']}${this.mixinViewModuleOptions.exportURL}?${params}`*/
    },
    exportHandleYear(){
      this.yearDisplay = true
      /*var params = qs.stringify({
        'token': Cookies.get('token'),
        ...this.dataForm
      })
      window.location.href = `${window.SITE_CONFIG['apiURL']}${this.mixinViewModuleOptions.exportURL}?${params}`*/
    },
    //节流函数
    throttleFunction() {
      // 如果timer不为空就表明在持续输入,每次改变都清除定时，这样就可以实现只输入最后一次结果(注意每次间隔如果超过500毫秒就会输出)
      if (this.timer !== null) clearTimeout(this.timer)
      this.timer = setTimeout(() => {
        this.getDataList()
      }, 500)
    },
    beforeUpload(file) {
      this.message = this.$message({
        message: '上传中，请勿操作',
        center: true,
        duration: 0,
      });
    },
    handleChange(file, fileList) {
      this.fileList = fileList.slice(-3);

    },
    resultHandle(res) {
      if (res.code !== 0) {
      return this.$message.error(res.msg)
      }
      this.$message({
        message: this.$t('prompt.success'),
        type: 'success',
        duration: 500,
        onClose: () => {
          if (res.data) {
            this.unsuccessfulNameList = res.data
          }
          this.visible = false
          this.query()
        }
      })
    },
  }
}
</script>
<style>
.modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
}
.dialog {
  position: fixed;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
}
</style>
