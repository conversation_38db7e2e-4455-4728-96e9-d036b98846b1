<template>
  <el-card shadow="never" class="aui-card--fill">
    <div class="mod-orders__businessorder}">
      <el-form :inline="true" :model="dataForm" @keyup.enter.native="getDataList()">
        <el-form-item>
          <el-tooltip content="请输入客户订单编号" placement="top">
            <el-input v-model="dataForm.orderNumber" placeholder="客户订单编号" clearable>
              <el-button slot="append" icon="el-icon-search" @click="getDataList()"></el-button>
            </el-input>
          </el-tooltip>
        </el-form-item>
        <el-form-item>
          <customer-component v-model="dataForm.customerId" placeholder="客户查询"></customer-component>
        </el-form-item>
        <el-form-item>
          <el-input v-model="dataForm.designation" placeholder="客户品名查询" @input="throttleFunction"
                    clearable></el-input>
        </el-form-item>
        <el-form-item>
          <el-date-picker
              v-model="dataForm.orderDate"
              type="date"
              format="yyyy 年 MM 月 dd 日"
              value-format="yyyy-MM-dd"
              placeholder="下单日期">
          </el-date-picker>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="queryFunction()">
            查询功能
          </el-button>
        </el-form-item>
        <el-form-item>
          <el-button v-if="$hasPermission('orders:businessorder:save')" type="primary" @click="addOrUpdateHandle()">
            {{ $t('add') }}
          </el-button>
        </el-form-item>
        <el-form-item>
          <el-button v-if="$hasPermission('orders:businessorder:save')" type="primary" @click="makeAndDeliver()">
            制交单
          </el-button>
        </el-form-item>
        <el-dropdown style="float:right;">
          <el-button type="primary">
            更多功能<i class="el-icon-arrow-down el-icon--right"></i>
          </el-button>
          <el-dropdown-menu slot="dropdown">
            <el-dropdown-item>
              <el-form-item>
                <el-button type="info" @click="exportHandle()">{{ $t('export') }}</el-button>
              </el-form-item>
            </el-dropdown-item>
            <el-dropdown-item>
              <el-form-item>
                <el-button v-if="$hasPermission('orders:businessorder:export')" type="info"
                           @click="exportTemplateHandle()">
                  {{ $t('exportTemplate') }}
                </el-button>
              </el-form-item>
            </el-dropdown-item>
            <el-dropdown-item>
              <el-form-item>
                <el-tooltip content="下载模板填入信息后导入" placement="top">
                  <el-upload
                      v-if="$hasPermission('orders:businessorder:save')"
                      class="upload-demo"
                      :action="mixinViewModuleOptions.addBatchUrl"
                      :headers="headers"
                      :multiple="false"
                      :show-file-list="false"
                      :file-list="fileList"
                      :before-upload="beforeUpload"
                      :on-success="resultHandle"
                      :on-change="handleChange"
                      accept="'.xlsx','.xls'">
                    <el-button type="success">{{ $t('addBatch') }}</el-button>
                  </el-upload>
                </el-tooltip>
                <el-button type="text" v-show="unsuccessfulNameList" @click="dialogVisible = true">导入失败列表
                </el-button>
              </el-form-item>
            </el-dropdown-item>
            <el-dropdown-item>
              <el-form-item>
                <el-button v-if="$hasPermission('orders:businessorder:delete')" type="danger" @click="deleteHandle()">
                  {{ $t('deleteBatch') }}
                </el-button>
              </el-form-item>
            </el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown>
      </el-form>
      <el-table v-loading="dataListLoading" height="500px" :data="dataList" border
                @selection-change="dataListSelectionChangeHandle" style="width: 100%;">
        <el-table-column type="selection" header-align="center" align="center" width="50"></el-table-column>
<!--        <el-table-column prop="orderDate" label="下单日期" header-align="center" align="center"></el-table-column>-->
        <el-table-column prop="orderType" label="订单类型" header-align="center" align="center">
          <template slot-scope="scope">
            {{ $getDictLabel("business_order_type", scope.row.orderType) }}
          </template>
        </el-table-column>
        <el-table-column prop="partCode" label="品号代码" header-align="center" align="center"></el-table-column>
        <el-table-column prop="partNameCode" label="品号" header-align="center" align="center"></el-table-column>
        <el-table-column prop="orderNumber" label="订单/预示号" header-align="center" align="center"><template slot-scope="scope">
          <el-link @click="showFullOrderNumber(scope.row.orderNumber)" type="primary">{{scope.row.orderNumber==null?scope.row.orderNumber:scope.row.orderNumber.slice(0, 6)}}</el-link>

        </template></el-table-column>
        <el-dialog :visible.sync="visible2" title="详情订单/预示号" width="550px" center :append-to-body="true">
          <div style="display: flex; justify-content: center;">
            <div style="display: flex; justify-content: center;">{{this.detailData2}}
            </div>
          </div>
        </el-dialog>
        <el-table-column prop="trackingNumber" label="制交单号" header-align="center" align="center"></el-table-column>
        <el-table-column prop="customerCode" label="客户代码" header-align="center" align="center"></el-table-column>
<!--        <el-table-column prop="designation" label="客户番号" header-align="center" align="center"></el-table-column>-->
        <!--        <el-table-column prop="designation" label="客户品名" header-align="center" align="center"></el-table-column>-->
<!--        <el-table-column prop="partNameCode" label="品名代码" header-align="center" align="center"></el-table-column>-->
        <el-table-column prop="materialQuality" label="材质" header-align="center" align="center"></el-table-column>
        <el-table-column prop="quantity" label="交制数量" header-align="center" align="center"></el-table-column>
        <el-table-column prop="orderDate" label="下单日期" header-align="center" align="center"></el-table-column>
        <el-table-column prop="deliveryDate" label="客户交期" header-align="center" align="center"></el-table-column>
        <el-table-column prop="tradingLocations" label="交货地址" header-align="center" align="center"><template slot-scope="scope">
          <el-link @click="showFullLocation(scope.row.tradingLocations)" type="primary">{{processData(scope.row.tradingLocations)}}</el-link>

        </template></el-table-column>
        <el-dialog :visible.sync="visible1" title="详情地址" width="550px" center :append-to-body="true">
          <div style="display: flex; justify-content: center;">
            <el-table :data="detailData" style="width: 480px;">
              <el-table-column prop="subBatchNumber" label="地址" width="530">{{this.detailData[0]}}</el-table-column>
<!--              <el-table-column prop="quantity" label="数量" width="230"></el-table-column>-->

            </el-table>
          </div>
        </el-dialog>
        <el-table-column prop="transferToFactory" label="转给厂务" header-align="center" align="center">{{dataForm.transferToFactory}}</el-table-column>
        <el-table-column prop="workNumber" label="工号" header-align="center" align="center"></el-table-column>
        <el-table-column prop="summaryDescription" label="摘要说明" show-overflow-tooltip header-align="center" align="center"></el-table-column>
        <el-table-column prop="make" label="做成" header-align="center" align="center">
          <template slot-scope="scope">
            {{$getEmployeesList(scope.row.make)}}
          </template>
        </el-table-column>
        <el-table-column :label="$t('handle')" fixed="right" header-align="center" align="center" width="150">
          <template slot-scope="scope">
            <el-dropdown>
              <el-button type="primary">
                功能<i class="el-icon-arrow-down el-icon--right"></i>
              </el-button>
              <el-dropdown-menu slot="dropdown">
                <el-dropdown-item>
                  <el-button v-if="$hasPermission('orders:businessorder:update')" type="text" size="small"
                             @click="addOrUpdateHandle(scope.row.id)">{{ $t('update') }}
                  </el-button>
                </el-dropdown-item>
                <el-dropdown-item>
                  <el-button v-if="$hasPermission('orders:businessorder:delete')" type="text" size="small"
                             @click="deleteHandle(scope.row.id)">{{ $t('delete') }}
                  </el-button>
                </el-dropdown-item>
              </el-dropdown-menu>
            </el-dropdown>
          </template>
        </el-table-column>
      </el-table>
      <el-pagination
          :current-page="page"
          :page-sizes="[10, 20, 50, 100]"
          :page-size="limit"
          :total="total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="pageSizeChangeHandle"
          @current-change="pageCurrentChangeHandle">
      </el-pagination>
      <!-- 弹窗, 新增 / 修改 -->
      <add-or-update v-if="addOrUpdateVisible" ref="addOrUpdate" @refreshDataList="getDataList"></add-or-update>
      <order-tracking v-if="orderTrackingVisible" ref="orderTracking" @inquire="inquire"></order-tracking>
    </div>
  </el-card>
</template>

<script>
import mixinViewModule from '@/mixins/view-module'
import AddOrUpdate from './businessorder-add-or-update'
import OrderTracking from "./order-tracking.vue";
import Cookies from "js-cookie";
import {addDynamicRoute} from '@/router'

export default {
  mixins: [mixinViewModule],
  data() {
    return {
      visible1: false,
      visible2: false,
      message: '',
      headers: {'token': Cookies.get('token')},
      fileList: [{name: '', url: ''}],
      unsuccessfulNameList: '',//  导入失败名称列表
      mixinViewModuleOptions: {
        getDataListURL: '/orders/businessorder/page',
        getDataListIsPage: true,
        exportURL: '/orders/businessorder/export',
        deleteURL: '/orders/businessorder',
        deleteIsBatch: true,
        exportTemplateURL: '/orders/businessorder/export/template',
        addBatchUrl: window.SITE_CONFIG['apiURL'] + '/orders/businessorder/batchSave',
      },
      dataForm: {
        orderNumber: '',
        customerId: '',
        designation: '',
        orderDate: '',
        transferToFactory:'未转',
        workNumber:'',
      },
      // 判断是否还在继续输入
      timer: null,
      orderTrackingVisible: false,
      detailData: [],
      detailData1: '',
      detailData2: '',
    }
  },
  components: {
    AddOrUpdate,
    OrderTracking
  },
  watch: {
    dataForm: {
      handler(newVal, oldVal) {
        this.getDataList()
      },
      deep: true
    }
  },
  methods: {
    //查看订单号详情
    showFullOrderNumber(boxId){
      //获取该包装详情
      this.visible2 = true
      this.detailData2=boxId;
    },
    //查看包装详情
    showFullLocation(boxId){
      //获取该包装详情
      this.visible1 = true
      while (this.detailData.length) {
        this.detailData.pop();
      }
      // this.detailData.remove()
      this.detailData.push(boxId)
      this.detailData1=boxId;
    },
    processData(data) {
      if(data!=null){
        // this.dataForm.multipleBoxId = data;
        // var data1 = data.split(",");
        // return data1.length-1;
        return data.slice(0, 5);
      }else{
        return 0;
      }
      // return data.map(item => ({
      //   ...item,
      //   ageDisplay: item.age + '岁' // 例如，将年龄显示为“XX岁”的形式
      // }));
    },
    inquire(data) {
      this.dataForm = {
        ...this.dataForm,
        ...data
      }
      this.getDataList()
    },
    queryFunction() {
      this.orderTrackingVisible = true;
      this.$nextTick(() => {
        this.$refs.orderTracking.init();
      })
    },
    makeAndDeliver() {
      let trackingNumber = 'SAR0001';
      // 路由参数
      const routeParams = {
        routeName: `${this.$route.name}__fancha`,
        title: `订单番查制交单建立`,
        path: 'orders/businessorder-fancha',
        params: {
          trackingNumber: trackingNumber
        }
      }
      // 动态路由
      addDynamicRoute(routeParams, this.$router)
    },
    addOrUpdateHandle(id) {
      this.addOrUpdateVisible = true
      this.$nextTick(() => {
        this.$refs.addOrUpdate.dataForm.id = id
        if (id) {
          this.$refs.addOrUpdate.showHoldOrder = true
          this.$refs.addOrUpdate.checkOrder = false
        }
        this.$refs.addOrUpdate.init()
      })
    },
    //节流函数
    throttleFunction() {
      // 如果timer不为空就表明在持续输入,每次改变都清除定时，这样就可以实现只输入最后一次结果(注意每次间隔如果超过500毫秒就会输出)
      if (this.timer !== null) clearTimeout(this.timer)
      this.timer = setTimeout(() => {
        this.getDataList()
      }, 500)
    },
    beforeUpload(file) {
      this.message = this.$message({
        message: '上传中，请勿操作',
        center: true,
        duration: 0,
      });
    },
    handleChange(file, fileList) {
      this.fileList = fileList.slice(-3);

    },
    resultHandle(res) {
      if (res.code !== 0) {
        return this.$message.error(res.msg)
      }
      this.$message({
        message: this.$t('prompt.success'),
        type: 'success',
        duration: 500,
        onClose: () => {
          if (res.data) {
            this.unsuccessfulNameList = res.data
          }
          this.visible = false
          this.query()
        }
      })
    },
  }
}
</script>
