<template>
  <div class="tech-dashboard">
    <!-- 頂部歡迎區域 -->
    <div class="welcome-section">
      <div class="welcome-content">
        <div class="greeting-text" v-if="greeting">
          <h1 class="main-greeting">
            {{greeting}}，
            <span class="user-name">
              <span v-if="user.surname">{{user.surname}}</span>
              <span v-if="user.name">{{user.name}}</span>
              <span v-else>{{user.username}}</span>
            </span>
          </h1>
          <div class="time-display">
            <i class="time-icon">🕐</i>
            <span class="current-time">{{nowTime}}</span>
          </div>
        </div>
        <div class="tech-decoration">
          <div class="circuit-line"></div>
          <div class="pulse-dot"></div>
        </div>
      </div>
    </div>

    <!-- 權限模組區域 -->
    <div class="modules-section">
      <div class="section-header">
        <h2 class="section-title">
          <span class="title-icon">🔐</span>
          系統權限模組
        </h2>
        <div class="title-underline"></div>
      </div>

      <div class="department-container" v-for="(department, index) in dataForm" :key="index">
        <div class="department-header">
          <h3 class="department-name">{{department.parentName}}</h3>
          <div class="department-count">{{department.menuList ? department.menuList.length : 0}} 個模組</div>
        </div>

        <div class="modules-grid">
          <div
            v-for="(module, moduleIndex) in department.menuList"
            :key="moduleIndex"
            class="module-card"
            :class="{ 'form-module': isFormModule(module) }"
            :style="{ animationDelay: `${moduleIndex * 0.1}s` }"
          >
            <!-- 普通模組顯示 -->
            <div v-if="!isFormModule(module)" @click="jumpToYourComponent(module.url)" class="normal-module">
              <div class="module-icon-container">
                <svg class="module-icon" aria-hidden="true">
                  <use :xlink:href="`#${module.icon}`"></use>
                </svg>
                <div class="icon-glow"></div>
              </div>
              <div class="module-info">
                <h4 class="module-name">{{module.name}}</h4>
                <div class="module-status">
                  <span class="status-dot"></span>
                  <span class="status-text">可用</span>
                </div>
              </div>
            </div>

            <!-- 表單模組顯示 -->
            <div v-else class="form-module-content">
              <div class="form-module-header">
                <h4 class="form-module-title">{{module.name}}</h4>
              </div>
              <div class="form-options">
                <div class="form-option" @click="handleFormAction(module, 'add')">
                  <div class="form-option-icon">
                    <svg class="form-icon" aria-hidden="true">
                      <use xlink:href="#add"></use>
                    </svg>
                  </div>
                  <span class="form-option-text">新增</span>
                </div>
                <div class="form-option" @click="handleFormAction(module, 'search')">
                  <div class="form-option-icon">
                    <svg class="form-icon" aria-hidden="true">
                      <use xlink:href="#search"></use>
                    </svg>
                  </div>
                  <span class="form-option-text">查詢</span>
                </div>
              </div>
            </div>

            <div class="module-overlay"></div>
          </div>
        </div>
      </div>
    </div>

    <!-- 背景裝飾 -->
    <div class="bg-decoration">
      <div class="grid-pattern"></div>
      <div class="floating-particles">
        <div class="particle" v-for="n in 20" :key="n"></div>
      </div>
    </div>
  </div>
</template>

<script>
import el from "element-ui/src/locale/lang/el";

export default {
  name: "module-layout",
  data(){
    return{
      nowTime:'',
      greeting:'',
      user:'',
      rights:'',
      dataForm: {

      }
    }
  },
  created() {
    this.$users.$on('getUser', this.getUser);
  },
  beforeDestroy() {
    this.$users.$off('getUser', this.getUser);
  },
  mounted(){
    this.$users.$on('getUser', this.getUser);
    let time  = setInterval(()=>{
      this.getNowDate()
    },1000)
    this.getUser()
  },
  methods:{

    jumpToYourComponent(url) {
      let newUrl = url.replace(/\//, '-')
      this.$router.push(newUrl)
    },

    // 判斷是否為表單模組
    isFormModule(module) {
      // 根據模組名稱或URL判斷是否為表單模組
      const formKeywords = ['表單', '表单', 'form', '新增', '添加', '管理', '維護', '维护']
      return formKeywords.some(keyword =>
        module.name.toLowerCase().includes(keyword.toLowerCase()) ||
        (module.url && module.url.toLowerCase().includes(keyword.toLowerCase()))
      )
    },

    // 處理表單操作
    handleFormAction(module, action) {
      let url = module.url
      if (action === 'add') {
        // 新增操作，可以添加特定的參數或路由
        url = url + '/add'
      } else if (action === 'search') {
        // 查詢操作，跳轉到列表頁面
        url = url
      }

      let newUrl = url.replace(/\//, '-')
      this.$router.push(newUrl)
    },
    //获取用户以及权限信息
    getUser(){
      let obtain = setInterval(()=>{
        let user = JSON.parse(localStorage.getItem("userData"));
        let rights = JSON.parse(localStorage.getItem("userRights"));
        console.log("获取的用户数据：",user)
        console.log("获取的用户数据this.user：",this.user)
        console.log("获取的用户权限：",rights)
        this.getRights(user.id)
        this.getUsers(user.id)
        if(user){
          clearInterval(obtain)
        }
      },1000)
    },
    getUsers(userId){
      this.$http.get(`/sys/user/${userId}`).then(({ data: res }) => {
        if (res.code !== 0) {
          return this.$message.error(res.msg)
        }
        this.user = res.data;
        console.log("用户数据为：",res.data)
        sessionStorage.setItem("users", JSON.stringify(res.data))
      }).catch(() => {})
    },
    getRights(userId){
      this.$http['get'](`/sys/menu/getUserRights/`+userId ).then(({ data: res }) => {
        if (res.code !== 0) {
          return this.$message.error(res.msg)
        }
        console.log("count:",res.data)
        this.dataForm = res.data
      }).catch(() => {})
    },
    //获取当前时间
    getNowDate() {
      var date = new Date();
      var sign2 = ":";
      var year = date.getFullYear() // 年
      var month = date.getMonth() + 1; // 月
      var day = date.getDate(); // 日
      var hour = date.getHours(); // 时
      var minutes = date.getMinutes(); // 分
      var seconds = date.getSeconds() //秒
      var weekArr = ['星期天','星期一', '星期二', '星期三', '星期四', '星期五', '星期六'];
      var week = weekArr[date.getDay()];
      let greet = ''

      if(hour < 8 && hour > 6){
        greet = '早上好'
      }else if (hour < 12){
        greet = '上午好'
      }else if (hour < 13){
        greet = '中午好'
      }else if (hour < 18){
        greet = '下午好'
      }else{
        greet = '晚上好'
      }
      this.greeting = greet

      // 给一位数的数据前面加 “0”
      if (month >= 1 && month <= 9) {
        month = "0" + month;
      }
      if (day >= 0 && day <= 9) {
        day = "0" + day;
      }
      if (hour >= 0 && hour <= 9) {
        hour = "0" + hour;
      }
      if (minutes >= 0 && minutes <= 9) {
        minutes = "0" + minutes;
      }
      if (seconds >= 0 && seconds <= 9) {
        seconds = "0" + seconds;
      }

      this.nowTime =  year + "年" + month + "月" + day + "号 " + week + ' ' + hour + sign2 + minutes + sign2 + seconds;
    },
  }
}
</script>

<style scoped>
/* 主容器 - 科技感背景 */
.tech-dashboard {
  min-height: 100vh;
  background: linear-gradient(135deg, #0c0c0c 0%, #1a1a2e 50%, #16213e 100%);
  color: #ffffff;
  position: relative;
  overflow-x: hidden;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

/* 歡迎區域 */
.welcome-section {
  padding: 2rem 2rem 1rem;
  position: relative;
  z-index: 2;
}

.welcome-content {
  max-width: 1200px;
  margin: 0 auto;
  position: relative;
}

.greeting-text {
  text-align: center;
  margin-bottom: 1rem;
}

.main-greeting {
  font-size: 1.8rem;
  font-weight: 300;
  margin: 0 0 0.8rem 0;
  background: linear-gradient(45deg, #00d4ff, #00ff88, #ff0080);
  background-size: 200% 200%;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  animation: gradientShift 3s ease-in-out infinite;
}

.user-name {
  font-weight: 600;
  text-shadow: 0 0 20px rgba(0, 212, 255, 0.5);
}

.time-display {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  font-size: 0.9rem;
  color: #a0a0a0;
}

.time-icon {
  font-size: 1.2rem;
  animation: pulse 2s infinite;
}

.current-time {
  font-family: 'Courier New', monospace;
  background: rgba(0, 212, 255, 0.1);
  padding: 0.3rem 0.8rem;
  border-radius: 20px;
  border: 1px solid rgba(0, 212, 255, 0.3);
}

/* 科技裝飾元素 */
.tech-decoration {
  position: absolute;
  top: 50%;
  right: 2rem;
  transform: translateY(-50%);
}

.circuit-line {
  width: 100px;
  height: 2px;
  background: linear-gradient(90deg, transparent, #00d4ff, transparent);
  position: relative;
  animation: circuitFlow 2s linear infinite;
}

.pulse-dot {
  width: 12px;
  height: 12px;
  background: #00d4ff;
  border-radius: 50%;
  position: absolute;
  right: -6px;
  top: -5px;
  animation: pulse 1.5s infinite;
  box-shadow: 0 0 20px #00d4ff;
}

/* 模組區域 */
.modules-section {
  padding: 2rem;
  max-width: 1400px;
  margin: 0 auto;
  position: relative;
  z-index: 2;
}

.section-header {
  text-align: center;
  margin-bottom: 3rem;
}

.section-title {
  font-size: 1.5rem;
  font-weight: 300;
  margin: 0 0 1rem 0;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
}

.title-icon {
  font-size: 1.5rem;
}

.title-underline {
  width: 100px;
  height: 3px;
  background: linear-gradient(90deg, #00d4ff, #00ff88);
  margin: 0 auto;
  border-radius: 2px;
}

/* 部門容器 */
.department-container {
  margin-bottom: 3rem;
  background: rgba(255, 255, 255, 0.02);
  border-radius: 20px;
  padding: 2rem;
  border: 1px solid rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  position: relative;
  overflow: hidden;
}

.department-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, transparent, #00d4ff, transparent);
}

.department-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.department-name {
  font-size: 1.2rem;
  font-weight: 500;
  margin: 0;
  color: #00d4ff;
  text-shadow: 0 0 10px rgba(0, 212, 255, 0.3);
}

.department-count {
  background: rgba(0, 212, 255, 0.2);
  color: #00d4ff;
  padding: 0.3rem 0.8rem;
  border-radius: 15px;
  font-size: 0.9rem;
  border: 1px solid rgba(0, 212, 255, 0.3);
}

/* 模組網格 */
.modules-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
  gap: 1rem;
  padding: 1rem 0;
  justify-items: center;
}

/* 當有表單模組時調整網格 */
.modules-grid:has(.form-module) {
  grid-template-columns: repeat(auto-fit, minmax(100px, 220px));
  justify-content: center;
}

/* 模組卡片 */
.module-card {
  width: 100px;
  height: 100px;
  background: linear-gradient(145deg, rgba(255, 255, 255, 0.08), rgba(255, 255, 255, 0.03));
  border: 1px solid rgba(0, 212, 255, 0.2);
  border-radius: 12px;
  padding: 0.8rem;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  backdrop-filter: blur(10px);
  animation: slideInUp 0.6s ease-out forwards;
  opacity: 0;
  transform: translateY(30px);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  box-shadow:
    0 4px 15px rgba(0, 0, 0, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

/* 表單模組特殊樣式 */
.module-card.form-module {
  width: 220px;
  height: 100px;
  padding: 0.6rem;
  background: linear-gradient(145deg, rgba(0, 212, 255, 0.08), rgba(0, 255, 136, 0.05));
  border: 1px solid rgba(0, 212, 255, 0.3);
}

/* 普通模組容器 */
.normal-module {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.module-card:hover {
  transform: translateY(-4px) scale(1.05);
  border-color: rgba(0, 212, 255, 0.8);
  background: linear-gradient(145deg, rgba(0, 212, 255, 0.1), rgba(0, 255, 136, 0.05));
  box-shadow:
    0 8px 25px rgba(0, 0, 0, 0.4),
    0 0 20px rgba(0, 212, 255, 0.4),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

.module-card.form-module:hover {
  transform: translateY(-3px) scale(1.02);
  background: linear-gradient(145deg, rgba(0, 212, 255, 0.15), rgba(0, 255, 136, 0.08));
  border-color: rgba(0, 212, 255, 0.6);
}

/* 表單模組內容 */
.form-module-content {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.form-module-header {
  text-align: center;
  margin-bottom: 0.5rem;
}

.form-module-title {
  font-size: 0.8rem;
  font-weight: 600;
  margin: 0;
  color: #00d4ff;
  text-shadow: 0 0 8px rgba(0, 212, 255, 0.4);
}

/* 表單選項容器 */
.form-options {
  display: flex;
  justify-content: space-around;
  align-items: center;
  gap: 0.5rem;
}

.form-option {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 0.4rem 0.6rem;
  background: rgba(0, 212, 255, 0.1);
  border: 1px solid rgba(0, 212, 255, 0.3);
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  min-width: 45px;
  backdrop-filter: blur(5px);
}

.form-option:hover {
  background: rgba(0, 212, 255, 0.2);
  border-color: rgba(0, 212, 255, 0.6);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 212, 255, 0.3);
}

.form-option-icon {
  margin-bottom: 0.2rem;
}

.form-icon {
  width: 16px;
  height: 16px;
  fill: #00d4ff;
  transition: all 0.3s ease;
}

.form-option:hover .form-icon {
  fill: #ffffff;
  transform: scale(1.1);
}

.form-option-text {
  font-size: 0.65rem;
  color: #ffffff;
  font-weight: 500;
  text-align: center;
  line-height: 1;
}

.module-card:hover .icon-glow {
  opacity: 1;
  transform: scale(1.2);
}

.module-card:hover .module-overlay {
  opacity: 1;
}

/* 模組圖標容器 */
.module-icon-container {
  position: relative;
  width: 40px;
  height: 40px;
  margin: 0 auto 0.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
}

.module-icon {
  width: 24px;
  height: 24px;
  fill: #00d4ff;
  transition: all 0.3s ease;
  position: relative;
  z-index: 2;
}

.icon-glow {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 40px;
  height: 40px;
  background: radial-gradient(circle, rgba(0, 212, 255, 0.4) 0%, transparent 70%);
  border-radius: 50%;
  opacity: 0;
  transition: all 0.3s ease;
}

/* 模組信息 */
.module-info {
  text-align: center;
}

.module-name {
  font-size: 0.75rem;
  font-weight: 500;
  margin: 0 0 0.3rem 0;
  color: #ffffff;
  line-height: 1.2;
  text-align: center;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  max-width: 80px;
}

.module-status {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.2rem;
  font-size: 0.65rem;
  color: #a0a0a0;
}

.status-dot {
  width: 4px;
  height: 4px;
  background: #00ff88;
  border-radius: 50%;
  animation: pulse 2s infinite;
}

.status-text {
  font-size: 0.6rem;
}

/* 模組覆蓋層 */
.module-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg, rgba(0, 212, 255, 0.1), rgba(0, 255, 136, 0.1));
  opacity: 0;
  transition: opacity 0.3s ease;
  border-radius: 16px;
}

/* 背景裝飾 */
.bg-decoration {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 1;
}

.grid-pattern {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image:
    linear-gradient(rgba(0, 212, 255, 0.03) 1px, transparent 1px),
    linear-gradient(90deg, rgba(0, 212, 255, 0.03) 1px, transparent 1px);
  background-size: 50px 50px;
  animation: gridMove 20s linear infinite;
}

.floating-particles {
  position: absolute;
  width: 100%;
  height: 100%;
}

.particle {
  position: absolute;
  width: 2px;
  height: 2px;
  background: #00d4ff;
  border-radius: 50%;
  animation: float 6s infinite linear;
}

.particle:nth-child(odd) {
  background: #00ff88;
  animation-duration: 8s;
}

.particle:nth-child(3n) {
  background: #ff0080;
  animation-duration: 10s;
}

/* 動畫定義 */
@keyframes gradientShift {
  0%, 100% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
}

@keyframes pulse {
  0%, 100% { opacity: 1; transform: scale(1); }
  50% { opacity: 0.7; transform: scale(1.1); }
}

@keyframes circuitFlow {
  0% { background-position: -100px 0; }
  100% { background-position: 100px 0; }
}

@keyframes slideInUp {
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes gridMove {
  0% { transform: translate(0, 0); }
  100% { transform: translate(50px, 50px); }
}

@keyframes float {
  0% {
    transform: translateY(100vh) translateX(0);
    opacity: 0;
  }
  10% {
    opacity: 1;
  }
  90% {
    opacity: 1;
  }
  100% {
    transform: translateY(-100px) translateX(100px);
    opacity: 0;
  }
}

/* 響應式設計 */
@media (max-width: 1200px) {
  .modules-grid {
    grid-template-columns: repeat(auto-fill, 100px);
    gap: 1rem;
    justify-content: center;
  }

  .modules-grid:has(.form-module) {
    grid-template-columns: repeat(auto-fit, minmax(100px, 200px));
  }

  .module-card.form-module {
    width: 200px;
  }

  .main-greeting {
    font-size: 1.6rem;
  }

  .tech-decoration {
    display: none;
  }
}

@media (max-width: 768px) {
  .welcome-section {
    padding: 1.5rem 1rem;
  }

  .modules-section {
    padding: 1.5rem 1rem;
  }

  .modules-grid {
    grid-template-columns: repeat(auto-fill, 100px);
    gap: 0.8rem;
    justify-content: center;
  }

  .modules-grid:has(.form-module) {
    grid-template-columns: repeat(auto-fit, minmax(100px, 180px));
  }

  .module-card.form-module {
    width: 180px;
    height: 90px;
    padding: 0.5rem;
  }

  .form-module-title {
    font-size: 0.75rem;
  }

  .form-option {
    padding: 0.3rem 0.4rem;
    min-width: 40px;
  }

  .form-icon {
    width: 14px;
    height: 14px;
  }

  .form-option-text {
    font-size: 0.6rem;
  }

  .main-greeting {
    font-size: 1.4rem;
  }

  .section-title {
    font-size: 1.3rem;
  }

  .department-container {
    padding: 1.2rem;
  }

  .department-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
  }

  .module-card {
    width: 100px;
    height: 100px;
    padding: 0.6rem;
  }
}

@media (max-width: 480px) {
  .modules-grid {
    grid-template-columns: repeat(auto-fill, 100px);
    gap: 0.6rem;
    justify-content: center;
  }

  .modules-grid:has(.form-module) {
    grid-template-columns: 1fr;
    max-width: 300px;
    margin: 0 auto;
  }

  .module-card.form-module {
    width: 100%;
    max-width: 280px;
    height: 80px;
    padding: 0.4rem;
  }

  .form-options {
    gap: 0.3rem;
  }

  .form-option {
    padding: 0.25rem 0.3rem;
    min-width: 35px;
  }

  .form-icon {
    width: 12px;
    height: 12px;
  }

  .form-option-text {
    font-size: 0.55rem;
  }

  .form-module-title {
    font-size: 0.7rem;
    margin-bottom: 0.3rem;
  }

  .main-greeting {
    font-size: 1.2rem;
  }

  .time-display {
    flex-direction: column;
    gap: 0.3rem;
  }

  .current-time {
    font-size: 0.8rem;
  }

  .module-card {
    width: 100px;
    height: 100px;
    padding: 0.5rem;
  }

  .module-name {
    font-size: 0.7rem;
    max-width: 75px;
  }
}

/* 粒子動畫隨機位置 */
.particle:nth-child(1) { left: 10%; animation-delay: 0s; }
.particle:nth-child(2) { left: 20%; animation-delay: 1s; }
.particle:nth-child(3) { left: 30%; animation-delay: 2s; }
.particle:nth-child(4) { left: 40%; animation-delay: 3s; }
.particle:nth-child(5) { left: 50%; animation-delay: 4s; }
.particle:nth-child(6) { left: 60%; animation-delay: 5s; }
.particle:nth-child(7) { left: 70%; animation-delay: 0.5s; }
.particle:nth-child(8) { left: 80%; animation-delay: 1.5s; }
.particle:nth-child(9) { left: 90%; animation-delay: 2.5s; }
.particle:nth-child(10) { left: 15%; animation-delay: 3.5s; }
.particle:nth-child(11) { left: 25%; animation-delay: 4.5s; }
.particle:nth-child(12) { left: 35%; animation-delay: 5.5s; }
.particle:nth-child(13) { left: 45%; animation-delay: 0.2s; }
.particle:nth-child(14) { left: 55%; animation-delay: 1.2s; }
.particle:nth-child(15) { left: 65%; animation-delay: 2.2s; }
.particle:nth-child(16) { left: 75%; animation-delay: 3.2s; }
.particle:nth-child(17) { left: 85%; animation-delay: 4.2s; }
.particle:nth-child(18) { left: 95%; animation-delay: 5.2s; }
.particle:nth-child(19) { left: 5%; animation-delay: 0.8s; }
.particle:nth-child(20) { left: 95%; animation-delay: 1.8s; }

/* 滾動條樣式 */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
}

::-webkit-scrollbar-thumb {
  background: linear-gradient(45deg, #00d4ff, #00ff88);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(45deg, #00ff88, #ff0080);
}
</style>
