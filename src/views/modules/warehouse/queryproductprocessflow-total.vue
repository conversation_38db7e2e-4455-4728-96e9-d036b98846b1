<template>
  <el-dialog :visible.sync="visible" title="流转" :close-on-click-modal="false" :close-on-press-escape="false" append-to-body>
    <el-table :data="dataLists" border style="width: 100%;">
      <el-table-column label="批号" fixed header-align="center" align="center">
        <template slot-scope="scope">
          <div>
            {{$getDictLabel("size_category",scope.row.sizeCategory) + scope.row.batchNumber+'-'+scope.row.subBatchNumber}}
          </div>
        </template>
      </el-table-column>
      <el-table-column prop="transferOutQuantity" label="转出数量" header-align="center" align="center"></el-table-column>
      <el-table-column prop="receiveQuantity" label="接收数量" header-align="center" align="center"></el-table-column>
      <el-table-column prop="itemCategory" label="项目类别" header-align="center" align="center">
        <template slot-scope="scope">
          {{ $getDictLabel("item_category", scope.row.itemCategory) }}
        </template>
      </el-table-column>
      <el-table-column prop="transferUnit" label="转出单位" header-align="center" align="center">
        <template slot-scope="scope">
          {{ $getDictLabel("product_category", scope.row.transferUnit) }}
        </template>
      </el-table-column>

      <el-table-column prop="transferee" label="转出者(工号)" header-align="center" align="center">
        <template slot-scope="scope">
          {{ $getEmployeesList(scope.row.transferee)}}
        </template>
      </el-table-column>
      <el-table-column prop="receivingDepartment" label="接收单位" header-align="center" align="center">
        <template slot-scope="scope">
          {{ $getDictLabel("product_category", scope.row.receivingDepartment) }}
        </template>
      </el-table-column>
      <el-table-column prop="recipient" label="接收者(工号)" header-align="center" align="center">
        <template slot-scope="scope">
          {{ $getEmployeesList(scope.row.recipient)}}
        </template>
      </el-table-column>
      <el-table-column prop="receivingTime" label="接收时间" header-align="center" align="center"></el-table-column>
      <el-table-column prop="storageLocation" label="存放位置" header-align="center" align="center"></el-table-column>
      <el-table-column prop="productCategoryTakeOver" label="产品货别" header-align="center" align="center">
        <template slot-scope="scope">
          {{ $getDictLabel("product_category", scope.row.receivingDepartment) }}
        </template>
      </el-table-column>
    </el-table>
    <template slot="footer">
      <el-button @click="visible = false">{{ $t('cancel') }}</el-button>
      <el-button type="primary" @click="circulationHandle()">{{ $t('confirm') }}</el-button>
    </template>
  </el-dialog>
</template>

<script>
import debounce from 'lodash/debounce'
import {getDictLabel} from "@/utils/index"
import mixinViewModule from '@/mixins/view-module'
export default {
 data () {
    return {
      visible: false,
      //创建该页面时，不调用数据查询接口
      mixinViewModuleOptions: {
        createdIsNeed: false,
      },

      dataLists:[],
    }
  },
  computed: {
    dataRule () {
      return {}
    }
  },
  mounted() {

  },
  watch:{
  },
  methods: {
    init(){
      this.visible = true
    },
    // 表单提交
    circulationHandle: debounce(function () {
      this.$confirm('此操作将新增流转记录, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
        center: true
      }).then(() => {
        this.$http['post']('/batch/productprocessflow/batchAddFlowRecords', this.dataLists).then(({ data: res }) => {
          if (res.code !== 0) {
            return this.$message.error(res.msg)
          }
          this.$message({
            message: this.$t('prompt.success'),
            type: 'success',
            duration: 500,
            onClose: () => {
              this.visible = false
              this.$emit('refreshDataList')
            }
          })
        }).catch(() => {})
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '已取消'
        });
      });
    }, 1000, { 'leading': true, 'trailing': false }),
  }
}
</script>

<style lang="scss" scoped>
::v-deep .birthday .el-input__inner {
  background-color: #e8e889 !important;
  color: black !important;
}
::v-deep .input-green .el-input__inner{
  background-color: #88e388 !important; /* 设置输入框的背景色为绿色 */
  color: black !important;
}
</style>

