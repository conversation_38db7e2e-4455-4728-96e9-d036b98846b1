<template>
  <el-card shadow="never" class="aui-card--fill">
    <div class="mod-sys__dept">
      <!--      <el-tooltip content="下载模板填入用户信息后导入" placement="top">-->
      <el-upload
          v-if="$hasPermission('sys:user:save')"
          class="upload-demo"
          :action="mixinViewModuleOptions.fileUpload"
          :headers="headers"
          :multiple="false"
          :show-file-list="false"
          :file-list="fileList"
          :before-upload="beforeUpload"
          :on-success="resultHandle"
          :on-change="handleChange"
          accept="'.xlsx','.xls'">
        <el-button type="success">{{ $t('upload.button') }}</el-button>
      </el-upload>
    </div>
  </el-card>
</template>

<script>
import mixinViewModule from '@/mixins/view-module'
import Cookies from "js-cookie";

export default {
  mixins: [mixinViewModule],
  data() {
    return {
      message: {},
      progress: 0,
      headers: {'token': Cookies.get('token')},
      fileList: [{name: '', url: ''}],
      mixinViewModuleOptions: {
        getDataListURL: '/sys/dept/list',
        deleteURL: '/sys/dept',
        fileUpload: window.SITE_CONFIG['apiURL'] + '/file/upload',
      }
    }
  },

  methods: {
    beforeUpload(file) {
      this.message = this.$message({
        message: '上传中',
        type: 'info',
        duration: 0,
        iconClass: 'el-icon-loading',
      })
    },
    handleChange(file, fileList) {
      this.fileList = fileList.slice(-3);
    },
    resultHandle(res) {
      this.message.close();
      if (res.code !== 0) {
        return this.$message.error(res.msg)
      }
      this.$message({
        message: this.$t('prompt.success'),
        type: 'success',
        duration: 500,
        onClose: () => {
          this.visible = false
          this.query()
        }
      })
    },
  },

}
</script>
