<template>
  <el-dialog :visible.sync="visible" :title="!dataForm.id ? $t('add') : $t('update')" :close-on-click-modal="false" :close-on-press-escape="false" append-to-body>
    <el-form :model="dataForm" :rules="dataRule" ref="dataForm" @keyup.enter.native="dataFormSubmitHandle()" :label-width="$i18n.locale === 'en-US' ? '120px' : '80px'">
          <el-form-item label="上级文件夹ID,顶级文件夹为0" prop="parentId">
          <el-input v-model="dataForm.parentId" placeholder="上级文件夹ID,顶级文件夹为0"></el-input>
      </el-form-item>
          <el-form-item label="真实文件id" prop="realId">
          <el-input v-model="dataForm.realId" placeholder="真实文件id"></el-input>
      </el-form-item>
          <el-form-item label="文件名" prop="filename">
          <el-input v-model="dataForm.filename" placeholder="文件名"></el-input>
      </el-form-item>
          <el-form-item label="是否是文件夹 （0 否 1 是）" prop="folderFlag">
          <el-input v-model="dataForm.folderFlag" placeholder="是否是文件夹 （0 否 1 是）"></el-input>
      </el-form-item>
          <el-form-item label="文件大小展示字符" prop="fileSizeDesc">
          <el-input v-model="dataForm.fileSizeDesc" placeholder="文件大小展示字符"></el-input>
      </el-form-item>
          <el-form-item label="文件类型（1 普通文件 2 压缩文件 3 excel 4 word 5 pdf 6 txt 7 图片 8 音频 9 视频 10 ppt 11 源码文件 12 csv）" prop="fileType">
          <el-input v-model="dataForm.fileType" placeholder="文件类型（1 普通文件 2 压缩文件 3 excel 4 word 5 pdf 6 txt 7 图片 8 音频 9 视频 10 ppt 11 源码文件 12 csv）"></el-input>
      </el-form-item>
          <el-form-item label="失效（0 否 1 是）" prop="disabled">
          <el-input v-model="dataForm.disabled" placeholder="失效（0 否 1 是）"></el-input>
      </el-form-item>
              <el-form-item label="更新人" prop="updater">
          <el-input v-model="dataForm.updater" placeholder="更新人"></el-input>
      </el-form-item>
          <el-form-item label="更新时间" prop="updateDate">
          <el-input v-model="dataForm.updateDate" placeholder="更新时间"></el-input>
      </el-form-item>
      </el-form>
    <template slot="footer">
      <el-button @click="visible = false">{{ $t('cancel') }}</el-button>
      <el-button type="primary" @click="dataFormSubmitHandle()">{{ $t('confirm') }}</el-button>
    </template>
  </el-dialog>
</template>

<script>
import debounce from 'lodash/debounce'
export default {
  data () {
    return {
      visible: false,
      dataForm: {
        id: '',
        parentId: '',
        realId: '',
        filename: '',
        folderFlag: '',
        fileSizeDesc: '',
        fileType: '',
        disabled: '',
        creator: '',
        createDate: '',
        updater: '',
        updateDate: ''
      }
    }
  },
  computed: {
    dataRule () {
      return {
        parentId: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        realId: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        filename: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        folderFlag: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        fileSizeDesc: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        fileType: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        disabled: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        updater: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        updateDate: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ]
      }
    }
  },
  methods: {
    init () {
      this.visible = true
      this.$nextTick(() => {
        this.$refs['dataForm'].resetFields()
        if (this.dataForm.id) {
          this.getInfo()
        }
      })
    },
    // 获取信息
    getInfo () {
      this.$http.get(`/file/file/${this.dataForm.id}`).then(({ data: res }) => {
        if (res.code !== 0) {
          return this.$message.error(res.msg)
        }
        this.dataForm = {
          ...this.dataForm,
          ...res.data
        }
      }).catch(() => {})
    },
    // 表单提交
    dataFormSubmitHandle: debounce(function () {
      this.$refs['dataForm'].validate((valid) => {
        if (!valid) {
          return false
        }
        this.$http[!this.dataForm.id ? 'post' : 'put']('/file/file/', this.dataForm).then(({ data: res }) => {
          if (res.code !== 0) {
            return this.$message.error(res.msg)
          }
          this.$message({
            message: this.$t('prompt.success'),
            type: 'success',
            duration: 500,
            onClose: () => {
              this.visible = false
              this.$emit('refreshDataList')
            }
          })
        }).catch(() => {})
      })
    }, 1000, { 'leading': true, 'trailing': false })
  }
}
</script>
