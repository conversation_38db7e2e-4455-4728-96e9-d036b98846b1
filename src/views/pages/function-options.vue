<template>
  <div class="aui-wrapper aui-page__login">
    <div class="aui-content__wrapper" style="border: 1px red solid">
      <div class="div-content">
        <div class="non-flex">
          <div>
            <p v-if="greeting">
              {{greeting}}，
              <b v-if="user.surname">
                <span v-if="user.surname">{{user.surname}}</span>
                <span v-if="user.name">{{user.name}}</span>
              </b>
              <b v-else>{{user.username}}</b>
              <span>现在时间是{{nowTime}}</span>
            </p>
          </div>
          <h2>育群ERP系统功能选项</h2>
        </div>
        <div class="non-flex">
          <div class="container">
            <div class="four"  v-for="item in dataForm">
              <div @click="jumpToYourComponent(item.url)" class="button-like-div">
                <p>{{item.name}}</p>
              </div>
            </div>
          </div>
        </div>
        <div class="non-flex">
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import Cookies from 'js-cookie'
import debounce from 'lodash/debounce'
import { getUUID } from '@/utils'
export default {
  data () {
    return {
      nowTime:'',
      greeting:'',
      user:'',
      rights:'',
      dataForm: {

      }
    }
  },
  computed: {
    dataRule () {
      return {
        username: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        password: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        captcha: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ]
      }
    }
  },
  created() {
    this.$users.$on('getUser', this.getUser);
  },
  beforeDestroy() {
    this.$users.$off('getUser', this.getUser);
  },
  mounted(){
    this.$users.$on('getUser', this.getUser);
    let time  = setInterval(()=>{
      this.getNowDate()
    },1000)
    this.getUser()
  },
  methods:{
    routingReturn(){
      this.$router.push('new-page')
    },
    jumpToYourComponent(url) {
      let newUrl = url.replace(/\//, '-')
      this.$router.push(newUrl)
    },
    //获取用户以及权限信息
    getUser(){
      let obtain = setInterval(()=>{
        let user = JSON.parse(localStorage.getItem("userData"));
        let rights = JSON.parse(localStorage.getItem("userRights"));
        console.log("获取的用户数据：",user)
        console.log("获取的用户数据this.user：",this.user)
        console.log("获取的用户权限：",rights)
        this.getRights(user.id)
        this.getUsers(user.id)
        if(user){
          clearInterval(obtain)
        }
      },1000)
    },
    getUsers(userId){
      this.$http.get(`/sys/user/${userId}`).then(({ data: res }) => {
        if (res.code !== 0) {
          return this.$message.error(res.msg)
        }
        this.user = res.data;
        console.log("用户数据为：",res.data)
        sessionStorage.setItem("users", JSON.stringify(res.data))
      }).catch(() => {})
    },
    getRights(userId){
      this.$http['get'](`/sys/menu/getUserRights/`+userId ).then(({ data: res }) => {
        if (res.code !== 0) {
          return this.$message.error(res.msg)
        }
        console.log("count:",res.data)
        this.dataForm = res.data
      }).catch(() => {})
    },
    //获取当前时间
    getNowDate() {
      var date = new Date();
      var sign2 = ":";
      var year = date.getFullYear() // 年
      var month = date.getMonth() + 1; // 月
      var day = date.getDate(); // 日
      var hour = date.getHours(); // 时
      var minutes = date.getMinutes(); // 分
      var seconds = date.getSeconds() //秒
      var weekArr = ['星期天','星期一', '星期二', '星期三', '星期四', '星期五', '星期六'];
      var week = weekArr[date.getDay()];
      let greet = ''

      if(hour < 8 && hour > 6){
        greet = '早上好'
      }else if (hour < 12){
        greet = '上午好'
      }else if (hour < 13){
        greet = '中午好'
      }else if (hour < 18){
        greet = '下午好'
      }else{
        greet = '晚上好'
      }
      this.greeting = greet

      // 给一位数的数据前面加 “0”
      if (month >= 1 && month <= 9) {
        month = "0" + month;
      }
      if (day >= 0 && day <= 9) {
        day = "0" + day;
      }
      if (hour >= 0 && hour <= 9) {
        hour = "0" + hour;
      }
      if (minutes >= 0 && minutes <= 9) {
        minutes = "0" + minutes;
      }
      if (seconds >= 0 && seconds <= 9) {
        seconds = "0" + seconds;
      }

      this.nowTime =  year + "年" + month + "月" + day + "号 " + week + ' ' + hour + sign2 + minutes + sign2 + seconds;
    },
  }
}
</script>

<style scoped>
h1, h2, h3, h4, h5, h6 {
  text-align: center;
}
.div-content {
  width: 60%;
  background-color: #fff;
  position: relative;
  margin-left: auto;
  margin-right: auto;
}
.button-like-div {
  display: inline-block;
  background-color: #007bff;
  color: #fff;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  text-align: center;
  text-decoration: none;
  font-size: 16px;
  width: 150px; /* 设置固定的宽度 */
  height: 50px; /* 设置固定的高度 */
}

.button-like-div:hover {
  background-color: #0056b3;
}

.button-like-div:active {
  background-color: #004080;
}
</style>
