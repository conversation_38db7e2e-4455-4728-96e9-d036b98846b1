<template>
  <div class="scan-page">
    <!-- 返回导航栏 -->
    <van-nav-bar title="扫描二维码/条形码" left-text="取消" left-arrow
                 fixed class="scan-index-bar" @click-left="clickIndexLeft()"
    ></van-nav-bar>
    <!-- 扫码区域 -->
    <video ref="video" id="video" class="scan-video" autoplay></video>
    <!-- 扫描线 -->
    <!-- 提示语 -->
    <div v-show="tipShow" class="scan-tip">{{ tipMsg }}</div>
  </div>
</template>

<script>
import { BrowserMultiFormatReader } from '@zxing/library';
import {Dialog} from "element-ui";


export default {
  name: 'QrcodeScanning',  // 扫码页面
  data() {
    return {
/*      codeReader: null,*/
      tipShow: false,  // 是否展示提示
      tipMsg: '',  // 提示文本内容
      scanText: '',  // 扫码结果文本内容
      videoElement: null,
      codeReader: new BrowserMultiFormatReader(),
    }
  },
  created() {
    this.openScan();
  },
  watch: {
    '$route'(to, from) {
      if(to.path == '/ScanCodePage'){  // 当处于该页面时
        this.openScan();
      }
    }
  },
  destroyed(){
    this.codeReader.reset();
    this.codeReader = null;
  },
  methods: {
    scanCode(firstDeviceId) {
      this.codeReader.getVideoInputDevices().then(videoInputDevices => {
        this.codeReader.decodeFromInputVideoDeviceContinuously(firstDeviceId, 'video', (result, err) => {
          if (result) {
            // 扫描成功，处理扫描结果
            alert(result.text);
          }
        });
      });
    },
    continueOpeningCamera() {
      this.codeReader.getVideoInputDevices().then(videoDevices => {
        this.tipMsg = '正在调用摄像头...';
        this.tipShow = true;
        console.log('get-videoDevices', videoDevices);
        alert('get-videoDevices'+videoDevices);
        // 默认获取摄像头列表里的最后一个设备id，通过几部测试机发现一般前置摄像头位于列表里的前面几位，所以一般获取最后一个的是后置摄像头
        let firstDeviceId = videoDevices[videoDevices.length - 1].deviceId;
        // 一般获取了手机的摄像头列表里不止一个，有的手机摄像头高级多层，会有变焦摄像头等情况，需要做处理
        if (videoDevices.length > 1) {
          // 一般通过判断摄像头列表项里的 label 字段，'camera2 0, facing back' 字符串含有 'back' 和 '0'，大部分机型是这样，如果有些机型没有，那就还是默认获取最后一个
          firstDeviceId = videoDevices.find(el => { return el.label.toLowerCase().includes('back') }) ?
              videoDevices.find(el => { return el.label.toLowerCase().includes('back') }).deviceId :
              videoDevices[videoDevices.length - 1].deviceId;
        }
        console.log('get-firstDeviceId', firstDeviceId);
        alert('get-firstDeviceId'+firstDeviceId);
        this.decodeFromInputVideoFunc(firstDeviceId);
      }).catch(err => {

        this.tipShow = false;
        console.error(err);
        alert(err);
      });
    },

    async openScan() {  // 初始化摄像头
      this.codeReader = await new BrowserMultiFormatReader();

      navigator.mediaDevices.getUserMedia({video: true })
          .then((stream) => {
           /* this.bar()*/
            // 用户已经授予了摄像头和麦克风的权限
            this.continueOpeningCamera()
            // 停止所有轨道
            /*stream.getTracks().forEach(function(track) {
              track.stop();
            });*/
          })
          .catch(function(err) {
            // 用户拒绝了权限请求或者发生了其他错误
            alert(err)
            console.log(err)
          });

    },
    bar(){
      // 获取视频和画布元素
      var video = document.getElementById('video');
      var canvas = document.getElementById('canvas');
      var ctx = canvas.getContext('2d');

      // 扫描线的初始位置和速度
      var y = 0;
      var speed = 1;

      video.srcObject = stream;
      video.play();

      // 绘制函数
      function draw() {
        // 将视频帧绘制到画布上
        ctx.drawImage(video, 0, 0, canvas.width, canvas.height);

        // 绘制扫描线
        ctx.fillStyle = '#07C';
        ctx.fillRect(0, y, canvas.width, 10);

        // 移动扫描线
        y += speed;
        if (y > canvas.height) {
          y = 0;
        }

        // 在下一帧继续绘制
        requestAnimationFrame(draw);
      }

      draw();
    },
    decodeFromInputVideoFunc(firstDeviceId) {  // 使用摄像头扫描
      this.codeReader.reset(); // 重置
      this.codeReader.decodeFromInputVideoDeviceContinuously(firstDeviceId, 'video', (result, err) => {
        this.tipMsg = '正在尝试识别...';
        if (result) {
          console.log('扫码结果', result);
          this.scanText = result.text;
          console.log(this.scanText,'扫码结果')
          if (this.scanText) {
            this.tipShow = false;
            Dialog.confirm({})
            Dialog.confirm({  // 获取到扫码结果进行弹窗提示，这部分接下去的代码根据需要，读者自行编写了
              title: '扫码结果',
              message: this.scanText,
            }).then(() => {  // 点击确认

            }).catch(() => {  // 点击取消

            });
            console.log('这里来')
          }
        }
      });
    },
    clickIndexLeft(){  // 返回上一页
      this.$destroy();
      this.$router.go(-1);
      // window.location.href = document.referrer;
    }
  }
}
</script>

<style lang="scss">
video {
  width: 100%;
  height: auto;
}
.scan-index-bar{
  background-image: linear-gradient( -45deg, #42a5ff ,#59cfff);
  .van-nav-bar__title, .van-nav-bar__arrow, .van-nav-bar__text{
    color: #fff !important;
  }
  height: 30%;
}
.scan-page{
  min-height: 100vh;
  background-color: #363636;
  overflow-y: hidden;
  .scan-video{
    height: 85vh;
  }
  .scan-tip{
    width: 100vw;
    text-align: center;
    color: white;
    font-size: 5vw;
  }
}
</style>

