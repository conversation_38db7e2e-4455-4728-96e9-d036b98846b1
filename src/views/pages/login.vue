<template>
  <div class="aui-wrapper aui-page__login">
    <div class="aui-content__wrapper">
      <main class="aui-content">
        <div class="login-header">
          <h2 class="login-brand">{{ $t('brand.lg') }}</h2>
        </div>
        <div class="login-body">
          <h3 class="login-title">{{ $t('login.title') }}</h3>
          <el-form :model="dataForm" :rules="dataRule" ref="dataForm" @keyup.enter.native="dataFormSubmitHandle()" status-icon>
            <el-form-item prop="username">
              <el-input v-model="dataForm.username" :placeholder="$t('login.username')">
                <span slot="prefix" class="el-input__icon">
                  <svg class="icon-svg" aria-hidden="true"><use xlink:href="#icon-user"></use></svg>
                </span>
              </el-input>
            </el-form-item>
            <el-form-item prop="password">
              <el-input v-model="dataForm.password" type="password" show-password :placeholder="$t('login.password')">
                <span slot="prefix" class="el-input__icon">
                  <svg class="icon-svg" aria-hidden="true"><use xlink:href="#icon-lock"></use></svg>
                </span>
              </el-input>
            </el-form-item>
<!--            <el-form-item prop="captcha">-->
<!--              <el-row :gutter="20">-->
<!--                <el-col :span="14">-->
<!--                  <el-input v-model="dataForm.captcha" :placeholder="$t('login.captcha')" clearable>-->
<!--                    <span slot="prefix" class="el-input__icon">-->
<!--                      <svg class="icon-svg" aria-hidden="true"><use xlink:href="#icon-safetycertificate"></use></svg>-->
<!--                    </span>-->
<!--                  </el-input>-->
<!--                </el-col>-->
<!--                <el-col :span="10" class="login-captcha">-->
<!--                  <img :src="captchaPath" @click="getCaptcha()">-->
<!--                </el-col>-->
<!--              </el-row>-->
<!--            </el-form-item>-->
            <el-form-item>
              <el-button type="primary" @click="dataFormSubmitHandle()" class="w-percent-100">{{ $t('login.title') }}</el-button>
            </el-form-item>
          </el-form>
        </div>
        <div class="login-footer">
        </div>
      </main>
    </div>
  </div>
</template>

<script>
import Cookies from 'js-cookie'
import debounce from 'lodash/debounce'
import publicFunctions from '@/mixins/public-functions'
import { getUUID } from '@/utils'
export default {
  mixins:[publicFunctions],
  data () {
    return {
      captchaPath: '',
      dataForm: {
        username: '',
        password: '',
        uuid: '',
        captcha: ''
      }
    }
  },
  computed: {
    dataRule () {
      return {
        username: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        password: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        // captcha: [
        //   { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        // ]
      }
    }
  },
  // created () {
  //   this.getCaptcha()
  // },
  methods: {
    // 获取验证码
    // getCaptcha () {
    //   this.dataForm.uuid = getUUID()
    //   this.captchaPath = `${window.SITE_CONFIG['apiURL']}/captcha?uuid=${this.dataForm.uuid}`
    //   console.log(this.captchaPath,'验证码')
    // },
    // 表单提交
    dataFormSubmitHandle: debounce(function () {
      this.$refs['dataForm'].validate((valid) => {
        if (!valid) {
          return false
        }
        this.$http.post('/login', this.dataForm).then(({ data: res }) => {
          if (res.code !== 0) {
            // this.getCaptcha()
            return this.$message.error(res.msg)
          }
          Cookies.set('token', res.data.token)
          Cookies.set('userId',res.data.userId)
          if(this.equipment()){
            this.$router.replace({ name: 'home' })
          }else {
            this.$router.replace({ name: 'select-input' })
            /*this.$router.replace({ name: 'new-page' })*/
          }

        }).catch(() => {})
      })
    }, 1000, { 'leading': true, 'trailing': false })
  }
}
</script>

<style lang="scss">
/* 针对手机屏幕的样式 */
@media screen and (max-width: 768px) {
  .aui-page__login {
    .login-body,
    .login-footer {
      width: 100%;
    }
    .login-captcha {
      text-align: left;
      > img {
        width: 136px;
      }
    }
    &--right-vertical {
      .login-header {
        display: none;
      }
    }
  }
}
</style>
