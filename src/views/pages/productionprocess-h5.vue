<template>
  <el-dialog width="70%" :visible.sync="visible"  @close="handleClose" :title="!dataForm.id ? '新增批次生产流程卡' : '修改批次生产流程卡'" :close-on-click-modal="false" :close-on-press-escape="false" append-to-body  :before-close="closure">
    <el-form :model="dataForm" :rules="dataRule" ref="dataForm" @keyup.enter.native="dataFormSubmitHandle()" :label-width="$i18n.locale === 'en-US' ? '150px' : '80px'">
      <div class="container">
        <!--        <div>
                  <el-form-item label="部品代码" prop="code">
                    <el-input v-model="dataForm.code" placeholder="部品代码"></el-input>
                  </el-form-item>
                </div>-->

        <div>
          <el-form-item label="部品品名" prop="designation">
            <part-number-component  v-model="dataForm.partId"></part-number-component>
          </el-form-item>
        </div>

<!--        <div>
          <el-form-item label="客户代码" prop="customerCode">
            <span class="font_size">{{dataForm.customerCode}}</span>
          </el-form-item>
        </div>-->
      </div>
      <div class="container">
        <div>
          <el-form-item label="指令编号" prop="manufacturingInstructions">
            <manufacturing-instructions-component v-model="dataForm.manufacturingInstructions"></manufacturing-instructions-component>
          </el-form-item>
        </div>

        <div>
          <el-form-item label="批号" prop="batchNumber">
            <div style="display: flex; align-items: center;">
              <batch-under-component v-model="dataForm.batchId" :display-or-not="false"></batch-under-component>
              <span style="margin: 0 10px;">—</span>
              <el-input v-model="dataForm.subBatchNumber" style="width: 80px;display: inline-block;text-align: left"></el-input>
            </div>
          </el-form-item>
        </div>
      </div>

      <div class="container">
        <div>
          <el-form-item label="项目类别" prop="itemCategory">
            <ren-select v-model="dataForm.itemCategory" placeholder="请选择项目类别" dict-type="item_category"></ren-select>
          </el-form-item>
        </div>

        <div>
          <el-form-item label="产品状态" prop="itemCategory">
            <ren-select v-model="dataForm.productStatus" placeholder="产品状态" dict-type="management_category"></ren-select>
          </el-form-item>
        </div>
      </div>

      <div class="container">
        <div>
          <el-form-item label="技术员" prop="technician">
            <employee-component v-model="dataForm.technician" placeholder="工号"></employee-component>
          </el-form-item>
        </div>

        <div>
          <el-form-item label="机台编号" prop="machineId">
          <machine-component v-model="dataForm.machineId" placeholder="机台编号"></machine-component>
<!--            <el-input :class="showError ? 'error':''" v-model="dataForm.machineNumber" placeholder="机台编号"></el-input>
            <span v-if="showError" style="color: red;font-size: 1px">请填写机台编号</span>-->
          </el-form-item>
        </div>

<!--        <div>
          <el-form-item label="模具编号" prop="moldNumber">
            <el-input v-model="dataForm.moldNumber" placeholder="机台编号"></el-input>
          </el-form-item>
        </div>-->
      </div>

      <div class="container">
        <div>
          <el-form-item label="预估生产量" prop="estimatedProductionVolume" >
            <el-input v-model="dataForm.estimatedProductionVolume" placeholder="预估生产量"></el-input>
          </el-form-item>
        </div>

        <div>
          <el-form-item label="生产日期" prop="manufacturingTime">
            <el-date-picker
                class="datePicker"
                v-model="dataForm.manufacturingTime"
                type="datetime"
                value-format="yyyy-MM-dd HH:mm:ss"
                style="width: 100%; height: 100%; position: absolute; top: 0; left: 0;"
                placeholder="生产日期(含时分)">
            </el-date-picker>
          </el-form-item>
        </div>
      </div>

      <div v-if="informationVisible">
        <div class="container divColor" v-if="false">
          <el-divider content-position="center">
            <span style="font-size: 20px; font-weight: bold;">标准加硫条件</span>
          </el-divider>
          <div class="box">
            <p>
              上模：<span></span>
              中模：<span></span>
            </p>
            <p>
              下模：<span></span>
            </p>
            <p>
              时间：<span></span>秒
              压力：<span></span>秒
            </p>
            <p>
              料重：<span></span>
              单重：<span></span>
            </p>
            <p>
              模具检查频率：<span></span>次
            </p>
          </div>
          <div class="box">
            <p>
              <el-image src="" ></el-image>
            </p>
            <p>
              <span>切换作业</span>
            </p>
          </div>
        </div>

        <div class="container" v-if="false">
          <div>
            <el-form-item label="镶件" prop="insertNumber">
              <el-input v-model="dataForm.insertNumber" placeholder="N"></el-input>
            </el-form-item>
          </div>

          <div>
            <el-form-item label="镶件名称" prop="insertName">
              <el-input v-model="dataForm.insertName" placeholder="镶件名称"></el-input>
            </el-form-item>
          </div>

          <div>
            <el-form-item label="镶件批号" prop="insertNumber">
              <el-input v-model="dataForm.insertNumber" placeholder="镶件批号"></el-input>
            </el-form-item>
          </div>
        </div>
        <div class="container" v-if="false">
          <div>
            <el-form-item label="喷涂日期" prop="sprayDate">
              <el-date-picker
                  style="width: 200px"
                  v-model="dataForm.sprayDate"
                  type="datetime"
                  value-format="yyyy-MM-dd HH:mm:ss"
                  placeholder="喷涂日期(含时分)">
              </el-date-picker>
            </el-form-item>
          </div>

          <div>
            <el-form-item label="领料确认" prop="pickingConfirmation">
              <el-input v-model="dataForm.pickingConfirmation" placeholder="领料确认者"></el-input>
            </el-form-item>
          </div>
        </div>


        <div class="container">
          <div class="four">
            <el-form-item label="生产总模数" prop="productionModulus">
              <el-input v-model="dataForm.productionModulus" placeholder="生产总模数"></el-input>
            </el-form-item>
          </div>

          <div class="four">
            <el-form-item label="模具孔数" prop="dieHoleNumber">
              <el-input v-model="dataForm.dieHoleNumber" placeholder="模具孔数"></el-input>
            </el-form-item>
          </div>

          <div class="four">
            <el-form-item label="第几模开始" prop="whichModeToStart">
              <el-input v-model="dataForm.whichModeToStart" placeholder="第几模开始"></el-input>
            </el-form-item>
          </div>

          <div class="four">
            <el-form-item label="实际生产孔数" prop="actualProductionHoles">
              <el-input v-model="dataForm.actualProductionHoles" placeholder="实际生产孔数"></el-input>
            </el-form-item>
          </div>
        </div>

        <div class="container">
          <div class="four">
            <el-form-item label="标准产能" prop="standardCapacity">
              <el-input v-model="dataForm.standardCapacity" placeholder="标准产能"></el-input>
            </el-form-item>
          </div>

          <div class="four">
            <el-form-item label="实际产能" prop="actualCapacity">
              <el-input v-model="dataForm.actualCapacity" placeholder="实际产能"></el-input>
            </el-form-item>
          </div>

          <div class="four">
            <el-form-item label="不良数" prop="undesirable">
              <el-input v-model="dataForm.undesirable" placeholder="不良数"></el-input>
            </el-form-item>
          </div>
          <div class="four">
            <el-form-item label="不良率" prop="defectiveRate">
              <el-input v-model="dataForm.defectiveRate" placeholder="不良率"></el-input>
            </el-form-item>
          </div>
        </div>

        <div class="container">
          <div class="five divCon">
            <el-button class="el-buttons" type="primary" @click="requisitionVisible = true" round>材料记录</el-button>
          </div>

          <div class="five divCon">
            <el-button class="el-buttons" type="primary" round>始业点检</el-button>
          </div>

          <div class="five divCon">
            <el-button class="el-buttons" type="primary" round>异常处理</el-button>
          </div>

          <div class="five divCon">
            <el-button class="el-buttons" type="primary" round>切换作业</el-button>
          </div>

          <div class="five divCon">
            <el-button class="el-buttons" type="danger" round>放弃</el-button>
          </div>
        </div>
        <br/>
        <br/>
        <br/>
        <div class="container" v-if="inspectionInstructionsVisible">
          <inspection-instructions @calculatedValueFather="modulusAndDefectRate"
                                   :inputSubBatchId="subBatchId"
                                   :standardYield="dataForm.standardCapacity"
                                   :productionProgress="dataForm.productionProgress"
                                   :estimatedCompletion="dataForm.estimatedCompletion"
                                   :dieHoleNumber="dataForm.dieHoleNumber"
                                   :object-information="dataForm"
                                   :permission-hiding="permissionHiding"
                                   style=" width: 100%;height: auto;"></inspection-instructions>
        </div>
        <br/>
        <br/>
        <br/>
        <div class="container"  v-if="requisitionVisible">
          <div class="four">
            <el-form-item label="量产前三批" prop="massProduction">
              <ren-radio-group v-model="dataForm.massProduction" dict-type="yes_no"></ren-radio-group>
            </el-form-item>
          </div>

          <div class="four">
            <el-form-item label="领料日期" prop="requisitionDate">
              <el-date-picker
                  class="datePicker"
                  v-model="dataForm.requisitionDate"
                  type="datetime"
                  value-format="yyyy-MM-dd HH:mm:ss"
                  placeholder="领料日期(含时分)">
              </el-date-picker>
            </el-form-item>
          </div>
        </div>

        <div class="container" v-if="requisitionVisible">
          <div class="four">
            <el-form-item label="领料" prop="requisition">
              <el-input v-model="dataForm.requisition" placeholder="领料"></el-input>
            </el-form-item>
          </div>

          <div class="four">
            <el-form-item label="回头料" prop="backToMaterial">
              <el-input v-model="dataForm.backToMaterial" placeholder="回头料"></el-input>
            </el-form-item>
          </div>

          <div class="four">
            <el-form-item label="净重" prop="netWeight">
              <el-input v-model="dataForm.netWeight" placeholder="净重"></el-input>
            </el-form-item>
          </div>

          <div class="four">
            <el-form-item label="留料" prop="distillate">
              <el-input v-model="dataForm.distillate" placeholder="留料"></el-input>
            </el-form-item>
          </div>
        </div>
      </div>

      </el-form>
    <div>
      <el-button type="primary" @click="confirmInformation('machineId')" round v-if="displayButton">确认信息</el-button>
      <el-button @click="closure">{{ $t('cancel') }}</el-button>
      <el-button type="primary" @click="dataFormSubmitHandle()" v-if="!displayButton">{{ $t('confirm') }}</el-button>
    </div>
  </el-dialog>
</template>

<script>
import debounce from 'lodash/debounce'
import InspectionInstructions from "../modules/batch/inspectioninstructions.vue";


export default {

  props: {
    inputDataFrom: {
      type:Object,
      default: null,
    },
    permissionHiding:{
      type:Boolean,
      default:true
    }
  },
  data () {
    return {
      //默认值
      defaultValue:'',
      formingId:'',
      displayButton:true,
      visible: false,
      showError: false,
      inspectionInstructionsVisible: false, //显示检查
      inspectionVisible: false,
      informationVisible: false, //显示信息
      requisitionVisible:false,
      operation:false,
      subBatchId:'',
      dataForm: {
        id: '',
        manufacturingOrderId: '',
        manufacturingInstructions: '',
        partId: '',
        designation: '',
        estimatedProductionVolume: '',
        batchId: '',
        batchNumber: '',
        sizeCategory: 0,
        manufacturingTime:this.formatTimes(new Date()),
        code:'',
        customerId: '',
        customerCode: '',
        machineId: '',
        moldId: '',
        moldNumber: '',
        itemCategory: 0,
        productStatus: 0,
        subBatchNumber:'',
        numberOfMoldHoles:'',

        //成形
        subBatchId: '',
        insertNumber: 'N',
        insertName: '',
        sprayDate: '',
        pickingConfirmation: '',
        pickingDate: '',
        formingClass: '',
        vulcanizationDate: '',
        sulfurizationTemperature:  '',
        machinePressure: '',
        dieHoleNumber: 20,
        productionModulus: '',
        productionProgress: '',
        estimatedCompletion: '',
        undesirable: 0,
        defectiveRate: 0,
        actualCapacity: 0,
        standardCapacity: 2000,
        whichModeToStart: '',
        actualProductionHoles: '',
        moldingDate: '',
        technician: '',

        //生产领料
        massProduction: '',
        requisitionDate: this.formatTimes(new Date()),
        requisition: '',
        backToMaterial: '',
        netWeight: '',
        distillate: '',

        remark: '',
      }
    }
  },
  components:{
    MachineComponent,
    InspectionInstructions
  },
  computed: {
    dataRule () {
      return {
        insertNumber: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        insertName: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        sprayDate: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
        machineId: [
          { required: true, message: this.$t('validate.required'), trigger: 'blur' }
        ],
      }
    }
  },

  watch:{
    inputDataFrom:{
        handler: function (val) {
          //dataForm 所具有的属性就赋值，否则不赋值
          for (const key in this.inputDataFrom) {
            if (this.inputDataFrom.hasOwnProperty(key)) {
              if (this.dataForm.hasOwnProperty(key)) {
                this.dataForm[key] = this.inputDataFrom[key];
              }
            }
          }
         if(this.inputDataFrom){
           this.$nextTick(()=>{
             if(!this.inputDataFrom.batchId){
               this.dataForm.batchId = this.inputDataFrom.id
             }
            /* this.dataForm.sizeCategory = getDictLabel('size_category',this.inputDataFrom.sizeCategory)*/
             if(!this.dataForm.id){
               this.dataForm.id = ''
             }
           })
         }
        },
        immediate: true,
        deep: true
    },
    "dataForm.batchId"(value){
      if(value){
        this.$http.get(`/batch/subbatch/maxSubBatchNumber/${this.dataForm.batchId}`).then(({ data: res }) => {
          if (res.code !== 0) {
            return this.$message.error(res.msg)
          }
          this.dataForm.subBatchNumber = Number(res.data.subBatchNumber)
        }).catch(() => {})
      }
    }
  },
  mounted() {
  },
  methods: {
    handleClose() {
      // 触发自定义事件
      this.$emit("dialog-closed");
    },
    myFunction() {
      if(this.dataForm.subBatchId && !this.dataForm.id){
        this.$http[!this.dataForm.id ? 'post' : 'put']('/batch/forming/', this.dataForm).then(({ data: res }) => {
          if (res.code !== 0) {
            return this.$message.error(res.msg)
          }
          this.$message({
            message: this.$t('prompt.success'),
            type: 'success',
            duration: 500,
            onClose: () => {
              this.init()
              this.informationVisible = false
              this.visible = false
              if(!this.dataForm.id){
                this.$router.push({name: 'batch-subbatch',query:{batchId:this.dataForm.batchId}})
              }
              this.$emit('refreshDataList')
            }
          })
        }).catch(() => {})
      }
    },
    // 获取系统当前日期及时间
    formatTimes(date){
      let year = date.getFullYear();
      let month = date.getMonth() + 1;
      let day = date.getDate();
      let hours = date.getHours();
      let minutes = date.getMinutes();
      let seconds = date.getSeconds();

      month = month < 10 ? "0" + month : month;
      day = day < 10 ? "0" + day : day;
      hours = hours < 10 ? "0" + hours : hours;
      minutes = minutes < 10 ? "0" + minutes : minutes;
      seconds = seconds < 10 ? "0" + seconds : seconds;

      return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
    },
    //模数以及不良率、不良数、实际生产量
    modulusAndDefectRate(productionModulus,defectiveRate,actualCapacity,undesirable,productionProgress,estimatedCompletion){
      this.dataForm.productionModulus = productionModulus
      this.dataForm.defectiveRate = defectiveRate
      this.dataForm.undesirable = undesirable
      this.dataForm.actualCapacity = actualCapacity
      this.dataForm.estimatedCompletion = estimatedCompletion
      this.dataForm.productionProgress = productionProgress
    },
    //关闭页面
    closure(){
      this.informationVisible = false
      this.visible = false
    },
    transfereeSelectEmployeeId(employeeId,cb){
      this.$http.get(`/sys/user/selectEmployeeId/`+employeeId).then(({data: res}) => {
        console.log("res",res)
        if (res.code !== 0) {
          return this.$message.error(res.msg)
        }
        const list = res.data.map((obj) => {
          console.log("obj",obj)
          return {
            technician: obj.employeeId,
            value: obj.employeeId + '(' + obj.surname+obj.name + ')',
          }
        })
        this.timeout = setTimeout(() => {
          cb(list);
        }, 300 * Math.random());
      }).catch(() => {
      })
    },
    SelectEmployeeIdTransferee(item) {
      this.dataForm.technician = item.technician
    },
    //确认信息之后的操作
    confirmInformation(field){
        if (this.dataForm[field]) {
          !//生成次批号
          this.$http['post']('/batch/subbatch/', this.dataForm).then(({ data: res }) => {
            if (res.code !== 0) {
              return this.$message.error(res.msg)
            }
            this.subBatchId = res.data.id
            this.inspectionInstructionsVisible = true
            this.dataForm.subBatchId = res.data.id
            this.dataForm.subBatchNumber = res.data.subBatchNumber
          }).catch(() => {})
          this.showError = false;
          //显示数据
          this.informationVisible = true

          this.displayButton = false;
        } else {
          // 字段未填写，提示错误信息
          this.showError = true;
        }
    },
    init () {
      console.log('进行初始化')
      this.visible = true
      this.$nextTick(() => {
        this.formingId = this.dataForm.id
        this.$refs['dataForm'].resetFields()
        this.dataForm.id = this.formingId
        if (this.dataForm.id) {
          this.getInfo()
        }
      })
    },
    // 获取信息
    getInfo () {
      this.$http.get(`/batch/forming/${this.dataForm.id}`).then(({ data: res }) => {
        if (res.code !== 0) {
          return this.$message.error(res.msg)
        }
        this.dataForm = {
          ...this.dataForm,
          ...res.data
        }
        if(res.data.subBatchId){
          this.subBatchId = res.data.subBatchId
          this.inspectionInstructionsVisible = true
        }

      }).catch(() => {})
    },
    // 表单提交
    dataFormSubmitHandle: debounce(function () {
      this.$refs['dataForm'].validate((valid) => {
        if (!valid) {
          return false
        }
        let message = '';
        if(this.dataForm.id){
          message = '此操作将修改该数据'
        }else {
          message = '此操作将新增一条数据'
        }
        this.$confirm(message+', 是否继续?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
          center: true
        }).then(() => {
          this.$http[!this.dataForm.id ? 'post' : 'put']('/batch/forming/', this.dataForm).then(({ data: res }) => {
            if (res.code !== 0) {
              return this.$message.error(res.msg)
            }
            this.$message({
              message: this.$t('prompt.success'),
              type: 'success',
              duration: 500,
              onClose: () => {
                this.init()
                this.informationVisible = false
                this.visible = false
                if(!this.dataForm.id){
                  this.$router.push({name: 'batch-subbatch',query:{batchId:this.dataForm.batchId}})
                }
                this.$emit('refreshDataList')
              }
            })
          }).catch(() => {})
        }).catch(() => {
          this.$message({
            type: 'info',
            message: '已取消'
          });
        });
      })
    }, 1000, { 'leading': true, 'trailing': false })
  }
}
</script>
<style>
  .divCon{
    display: flex;
    justify-content: center;
    align-items: center;
  }
  .divColor{
    background-color: #def5be;
    border-radius: 10px; /* 设置圆角半径 */
    padding: 10px; /* 设置内边距 */
  }
  /*按钮大小*/
  .el-buttons {
    width: 120px; /* 设置宽度 */
    height: 45px; /* 设置高度 */
  }
  .error {
    border: 1px solid red;
  }
</style>
