<template>
  <div>
    <el-divider content-position="center">
      <span class="font_size">选项输入</span>
    </el-divider>
    <div class="container">
      <div class="div-display" @click="jumpToYourComponent('productprocessflow-h5')">
        工序流转
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: "selectInput",
  data(){
    return{

    }
  },
  computed: {

  },
  methods:{
    jumpToYourComponent(url) {
      this.$router.replace({ name: url })
    },
  }
}
</script>

<style scoped>
.div-display{
  padding: 10px 20px;
  border: none;
  border-radius: 4px;
  background-color: blueviolet;
  width: 100px;
  height: 100px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  text-align: center;
}
.div-display:hover{
  transform: scale(1.1); /* 放大 10% */
  background-color: gray; /* 变亮一点 */
}
</style>
