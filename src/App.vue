<template>
  <transition name="el-fade-in-linear">
    <router-view />
  </transition>
</template>


<style>
  .parent {
    display: grid;
    grid-template-columns: 1fr 1fr 1fr 1fr; /* 定义4列网格 */
    gap: 10px; /* 设置网格间距 */
  }

  .child {
    background-color: #ddd;
    padding: 20px;
    text-align: center;
  }

  @media (max-width: 768px) {
    .parent {
      display: flex;
      flex-wrap: wrap; /* 当屏幕尺寸小于768px时，子元素自动换行 */
    }
  }
  .el-table{
    overflow: auto;
  }
  .el-table__header-wrapper,.el-table__body-wrapper,.el-table__footer-wrapper{overflow:visible;}
  .el-table__body-wrapper{
    overflow-x:visible !important;
  }
  /* 这个是为了解决前面样式覆盖之后伪类带出来的竖线 */
  .el-table::after{
    position: relative;
  }
  /*设置字体大小自适应视窗以及加粗*/
  .font_size{
    font-size: 1.8vw;
    font-size: 1.8vh;
    font-weight: bold;
  }
  /*设置数量文本框大小*/
  .input_size{
    min-width: 60px;
  }
  .el-dialog{
    /*min-width: 800px;*/
  }
  /*设置时间框宽度*/
  .datePicker {
    min-width: 100px!important;
    width: 100%!important;
  }
  .el-autocomplete-suggestion li {
    width: 500px;
  }

  .el-table th.gutter{
    display: table-cell!important;
  }
  .el-table__fixed {
    height: 100% !important;
  }
  .el-table__fixed-right {
    height: 100% !important;
  }
  /*flex布局*/
  .container {
    display: flex;
    flex-wrap: wrap;
  }
  /*flex布局附带居中*/
  .containers {
    display: flex;
    justify-content: center;
    align-items: center;
  }
  /*两个子元素一行*/
  .box {
       width: calc(50% - 10px);
       margin: 5px;
     }
  @media screen and (max-width: 767px) {
    .container {
      display: block;  /* Change container display to block */
    }
    .box {
      width: 100%;
    }
  }

  /*三个子元素一行*/
  .item {
    width: calc(33.33% - 10px);
    margin: 5px;
  }

  @media screen and (max-width: 768px) {
    .container {
      display: block;  /* Change container display to block */
    }
    .item {
      width: 100%;  /* Change item width to 100% */
    }
  }

  @media (min-width: 768px) and (max-width: 1023px) {
    .container {
      display: block;  /* Change container display to block */
    }
    .item {
      width: calc(50% - 10px);
    }
  }

  @media (min-width: 1024px) {
    .item {
      width: calc(33.33% - 10px);
    }
  }
  /*四个子元素一行*/
  .four{
    width: calc(25% - 10px);
    margin: 5px;
  }
  @media screen and (max-width: 768px) {
    .container {
      display: block;  /* Change container display to block */
    }
    .four {
      width: calc(50% - 10px);;  /* Change item width to 100% */
    }
  }
  @media screen and (max-width: 553px) {
    .container {
      display: block;  /* Change container display to block */
    }
    .four {
      width: calc(50% - 10px);;  /* Change item width to 100% */
    }
  }
  /*五个子元素一行*/
  .five {
    width: calc(20% - 10px);
    margin: 5px;
  }

  @media screen and (max-width: 768px) {
    .container {
      display: block;  /* Change container display to block */
    }
    .five {
      width: 100%;  /* Change item width to 100% */
    }
  }
</style>
<script>
import Cookies from 'js-cookie'
import { messages } from '@/i18n'
export default {
  watch: {
    '$i18n.locale': 'i18nHandle'
  },
  created () {
    this.i18nHandle(this.$i18n.locale)
  },
  methods: {
    i18nHandle (val, oldVal) {
      Cookies.set('language', val)
      document.querySelector('html').setAttribute('lang', val)
      document.title = messages[val].brand.lg
      // 非登录页面，切换语言刷新页面
      if (this.$route.name !== 'login' && oldVal) {
        window.location.reload()
      }
    }
  }
}
</script>
